app:
  version: ${version}
  julia:
    threads: 8
  xva-proxy:
    s3:
      bucket: xva-msg.qa.solumxplain.com
      region: eu-west-2
    temp-dir: /tmp/xplain
    julia-dir: /xva/bin/
    topics:
      results-topic: xva-results
      valuations-topic: xva-requests
spring:
  jackson:
    default-property-inclusion: non_null
  kafka:
    consumer:
      group-id: xplain-xva-proxy
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: com.solum.xplain.xva.proxy.messages.XvaCalculationRequestDeserializer
      properties:
        spring.json.trusted.packages: "com.solum.xplain.*"
      enable-auto-commit: false
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.solum.xplain.xva.proxy.messages.XvaCalculationResponseSerializer
      retries: 5
    jaas:
      enabled: true
    listener:
      concurrency: 4
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="\${KAFKA_USERNAME}" password="\${KAFKA_PASSWORD}";
server:
  port: 8070
