package com.solum.xplain.xva.proxy.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.nio.file.Path;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@ConfigurationProperties(prefix = "app.xva-proxy")
@Validated
public class XvaProperties {
  @Valid private S3Properties s3;

  @NotNull private Path tempDir;

  @NotNull private String juliaDir;
}
