plugins {
  id 'java'
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'com.google.cloud.tools.jib' version "${jibVersion}"
}


bootJar {
  launchScript()
}

compileJava {
  options.compilerArgs << '-Amapstruct.unmappedTargetPolicy=ERROR'
  options.compilerArgs << '-Amapstruct.defaultComponentModel=spring'
}

jib {
  from {
    image = 'eclipse-temurin:17-jre'
  }
  to {
    image = '575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-xva-proxy'
  }
  container {
    ports = ['8080']
    environment = [
      'JULIA_NUM_THREADS': '8'
    ]
  }
  extraDirectories {
    paths {
      path {
        from = "../../XVA_compiled_for_linux/"
        into = '/xva'
      }
    }
    permissions = [
      '/xva/bin/*': '700'
    ]
  }
}

processResources {
  filesMatching('application.yml') {
    expand(project.properties)
  }
}

dependencies {
  implementation project(":xplain-xva:xva-messages")
  implementation "org.springframework.kafka:spring-kafka"
  implementation('org.springframework.boot:spring-boot-starter-actuator')
  implementation('org.springframework.boot:spring-boot-starter-security')
  implementation('org.springframework.boot:spring-boot-starter-web')
  implementation('org.springframework.boot:spring-boot-starter-validation')

  implementation("com.google.guava:guava:${guavaVersion}")

  implementation "io.atlassian.fugue:fugue:${fugueVersion}"
  implementation "io.atlassian.fugue:fugue-extensions:${fugueVersion}"

  implementation("software.amazon.awssdk:s3")
  implementation 'net.logstash.logback:logstash-logback-encoder:8.1'

  testImplementation('org.springframework.boot:spring-boot-starter-test')
  testImplementation('org.springframework.security:spring-security-test')
  testImplementation "org.spockframework:spock-core:${spockVersion}"
  testImplementation "org.spockframework:spock-spring:${spockVersion}"
}
