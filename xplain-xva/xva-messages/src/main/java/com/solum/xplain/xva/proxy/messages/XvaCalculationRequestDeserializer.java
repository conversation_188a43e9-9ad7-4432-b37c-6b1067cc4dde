package com.solum.xplain.xva.proxy.messages;

import com.solum.xplain.shared.utils.kafka.ProtostuffDeserializer;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.stereotype.Component;

@Component
public class XvaCalculationRequestDeserializer
    extends ErrorHandlingDeserializer<XvaCalculationRequest> {

  public XvaCalculationRequestDeserializer() {
    super(ProtostuffDeserializer.of(XvaCalculationRequest.class));
  }
}
