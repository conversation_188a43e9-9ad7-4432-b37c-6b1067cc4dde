package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class XvaControlFile {

  @JsonProperty("BuildCurvesFromRates")
  private final boolean buildCurvesFromRates;

  @JsonProperty("BuildSurvProbsFromSpreads")
  private final boolean buildSurvivalProbsFromSpreads;

  @JsonProperty("CounterpartiesToProcess")
  private final List<String> counterpartiesToProcess;

  @JsonProperty("DoPV")
  private final boolean doPV;

  @JsonProperty("DoCVA")
  private final boolean doCVA;

  @JsonProperty("DoPFE")
  private final boolean doPFE;

  @JsonProperty("PartitionByNetSet")
  private final boolean partitionByNetSet;

  @JsonProperty("PartitionByTrade")
  private final boolean partitionByTrade;

  @JsonProperty("UseSobol")
  private final boolean useSobol;

  @JsonProperty("TimeGap")
  private final double timeGap;

  @JsonProperty("PFEPercentile")
  private final double pfePercentile;

  @JsonProperty("NumSims")
  private final int numSims;

  @JsonProperty("NumSimsCVA")
  private final int numSimsCVA;

  @JsonProperty("SelfPartyName")
  private final String selfPartyName;

  @JsonProperty("WhatIfPartyName")
  private final String whatIfPartyName;

  @JsonProperty("SavePaths")
  private final boolean savePaths;

  @JsonProperty("OnValuationErrors")
  private final String onValuationErrors;

  @JsonProperty("TradeFile")
  private final String tradeFile;

  @JsonProperty("MarketFile")
  private final String marketFile;

  @JsonProperty("InputModelFile")
  private final String inputModelFile;

  @JsonProperty("ResultsFile")
  private final String resultsFile;

  @JsonProperty("OutputModelFile")
  private final String outputModelFile;

  @JsonProperty("AllowScalarsInResults")
  private final boolean allowScalarsInResults;

  public static XvaControlFileBuilder builderWithDefaults() {
    return XvaControlFile.builder()
        .doPV(true)
        .doCVA(true)
        .doPFE(true)
        .savePaths(true)
        .useSobol(true)
        .buildSurvivalProbsFromSpreads(true)
        .whatIfPartyName("WHATIF")
        .selfPartyName("SELF")
        .onValuationErrors("Continue");
  }
}
