package com.solum.xplain.valuation.messages.calibration.vols;

import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilities;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class ValuationCalibratedCaplets implements Serializable {
  private final IborCapletFloorletVolatilities volatilities;
  private final List<ValuationFxShiftedCaplets> fxShiftedCaplets;
}
