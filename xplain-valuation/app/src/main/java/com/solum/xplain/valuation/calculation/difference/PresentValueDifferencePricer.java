package com.solum.xplain.valuation.calculation.difference;

import static com.google.common.math.DoubleMath.fuzzyEquals;
import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;
import static java.lang.Math.abs;
import static org.apache.commons.lang3.math.NumberUtils.DOUBLE_ZERO;

import com.solum.xplain.valuation.metrics.ClientMetrics;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.lang.Nullable;

@ToString
@EqualsAndHashCode
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PresentValueDifferencePricer {

  public static final PresentValueDifferencePricer DIFFERENCE_PRICER =
      new PresentValueDifferencePricer();

  public DifferenceMetrics calculate(
      @Nullable Double presentValue, @Nullable Double dv01, @Nullable ClientMetrics clientMetrics) {
    var clientPv = clientMetrics == null ? null : clientMetrics.getPresentValue();
    if (clientPv == null || presentValue == null) {
      return DifferenceMetrics.empty();
    } else {
      double absoluteDifference = clientPv - presentValue;
      Double relativeDifference = null;
      Double absRelativeDifference = null;
      if (clientPv != 0) {
        relativeDifference = absoluteDifference / clientPv;
        absRelativeDifference = abs(relativeDifference);
      }

      Double clientPvPvDv01 =
          Optional.ofNullable(dv01)
              .filter(val -> !fuzzyEquals(val, DOUBLE_ZERO, ONE_BASIS_POINT))
              .map(val -> (presentValue - clientPv) / val)
              .orElse(null);

      return new DifferenceMetrics(
          clientPvPvDv01,
          absoluteDifference,
          relativeDifference,
          abs(absoluteDifference),
          absRelativeDifference);
    }
  }
}
