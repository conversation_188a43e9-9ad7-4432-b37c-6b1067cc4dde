package com.solum.xplain.valuation.calculation.capfloor;

import static com.opengamma.strata.basics.currency.CurrencyAmount.zero;
import static com.solum.xplain.valuation.calculation.Constants.DAY_FRACTION_OF_YEAR;
import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;
import static com.solum.xplain.valuation.calculation.breakeven.SwapParRateCalculationsFactory.parRateCalculation;
import static com.solum.xplain.valuation.metrics.CashFlowMetrics.ofPayments;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.currency.MultiCurrencyAmount;
import com.opengamma.strata.market.sensitivity.PointSensitivity;
import com.opengamma.strata.pricer.DiscountingPaymentPricer;
import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilities;
import com.opengamma.strata.pricer.capfloor.VolatilityIborCapFloorProductPricer;
import com.opengamma.strata.pricer.capfloor.VolatilityIborCapFloorTradePricer;
import com.opengamma.strata.pricer.capfloor.VolatilityIborCapletFloorletPeriodPricer;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.capfloor.IborCapFloorTrade;
import com.opengamma.strata.product.capfloor.ResolvedIborCapFloorLeg;
import com.opengamma.strata.product.capfloor.ResolvedIborCapFloorTrade;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.valuation.calculation.TradeCalculationUtils;
import com.solum.xplain.valuation.calculation.TradeCalculator;
import com.solum.xplain.valuation.calculation.TradeUtils;
import com.solum.xplain.valuation.calculation.ValuationOptions;
import com.solum.xplain.valuation.calculation.sensitivity.Dv01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.Spot01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.Spot01CalculationResult;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve;
import com.solum.xplain.valuation.messages.calibration.rates.FxShiftedValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.vols.ValuationCalibratedCaplets;
import com.solum.xplain.valuation.messages.calibration.vols.ValuationFxShiftedCaplets;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import com.solum.xplain.valuation.metrics.CashFlowMetrics;
import com.solum.xplain.valuation.metrics.Metrics;
import com.solum.xplain.valuation.metrics.OptionMetrics;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class CapFloorTradeCalculator implements TradeCalculator {

  private static final VolatilityIborCapFloorTradePricer TRADE_PRICER =
      VolatilityIborCapFloorTradePricer.DEFAULT;
  private static final VolatilityIborCapFloorProductPricer PRODUCT_PRICER =
      VolatilityIborCapFloorProductPricer.DEFAULT;
  private static final DiscountingPaymentPricer PREMIUM_PRICER = DiscountingPaymentPricer.DEFAULT;
  private static final VolatilityIborCapletFloorletPeriodPricer PERIOD_PRICER =
      VolatilityIborCapletFloorletPeriodPricer.DEFAULT;

  private final IborCapFloorTrade trade;
  private final ValuationOptions options;
  private final Supplier<Map<String, CalibrationCurve>> curvesSupplier;
  private final RatesProvider ratesProvider;
  private final List<FxShiftedValuationCurveRates> fxShiftedRates;
  private final IborCapletFloorletVolatilities volatilities;
  private final List<ValuationFxShiftedCaplets> fxShiftedCaplets;

  public CapFloorTradeCalculator(
      IborCapFloorTrade trade,
      ValuationOptions options,
      Supplier<Map<String, CalibrationCurve>> curvesSupplier,
      ValuationCurveRates rates,
      ValuationCalibratedCaplets caplets) {
    this.trade = trade;
    this.options = options;
    this.curvesSupplier = curvesSupplier;
    this.ratesProvider = rates.getRatesProvider();
    this.fxShiftedRates = rates.getFxShiftedRates();
    this.volatilities = caplets.getVolatilities();
    this.fxShiftedCaplets = caplets.getFxShiftedCaplets();
  }

  @Override
  public Metrics calculate(ReferenceData refData) {
    var reportingCcy = options.getReportingCcy();
    var tradeCcy = options.getTradeCcy();
    var resolvedTrade = trade.resolve(refData);
    ResolvedIborCapFloorLeg capFloorLeg = resolvedTrade.getProduct().getCapFloorLeg();
    var productPv = productPv(resolvedTrade);
    double productPvInRepCcy = productPv.convertedTo(reportingCcy, ratesProvider).getAmount();
    var productPvAmount = productPv.convertedTo(tradeCcy, ratesProvider);
    double productPvInTradeCcy = productPvAmount.getAmount();

    var premiumPv = premiumPv(resolvedTrade);
    double premiumPvInRepCcy = premiumPv.convertedTo(reportingCcy, ratesProvider).getAmount();
    double premiumPvInTradeCcy = premiumPv.getAmount();

    var cfMetrics = cashFlowMetrics(resolvedTrade, capFloorLeg);
    var breakEvenMetrics =
        TradeCalculationUtils.eitherBreakevenMetrics(
            resolvedTrade, () -> breakevenMetrics(refData, capFloorLeg));

    var pv = productPvInRepCcy + premiumPvInRepCcy - cfMetrics.tZeroNetReportingCcy();
    var pvPayLegCcy = productPvInTradeCcy + premiumPvInTradeCcy - cfMetrics.tZeroNet();
    var metricsBuilder =
        Metrics.builder(TradeUtils.getClientPv(trade))
            .localCcy(tradeCcy.getCode())
            .presentValue(pv)
            .presentValuePayLegCurrency(pvPayLegCcy)
            .cashFlowMetrics(cfMetrics)
            .breakevenMetrics(breakEvenMetrics)
            .optionMetrics(optionMetrics(resolvedTrade, volatilities));

    if (capFloorLeg.getPayReceive().equals(PayReceive.PAY)) {
      double plusPremium = premiumPvInTradeCcy < 0 ? premiumPvInTradeCcy : 0;
      metricsBuilder.payLegPV(productPvInTradeCcy + plusPremium - cfMetrics.tZeroPay());
    } else {
      double plusPremium = premiumPvInTradeCcy > 0 ? premiumPvInTradeCcy : 0;
      metricsBuilder.receiveLegPV(productPvInTradeCcy + plusPremium - cfMetrics.tZeroRec());
    }

    if (options.isCalculateSensitivities()) {
      var dv01Calculation = dv01Calculation(resolvedTrade);
      metricsBuilder.with(dv01Calculation.calculate(ratesProvider));
      var spot01Calculation = calculateSpot01(productPvAmount, resolvedTrade);
      metricsBuilder.with(spot01Calculation);
    }
    return metricsBuilder.build();
  }

  private MultiCurrencyAmount productPv(ResolvedIborCapFloorTrade resolvedTrade) {
    return PRODUCT_PRICER.presentValue(resolvedTrade.getProduct(), ratesProvider, volatilities);
  }

  private CurrencyAmount premiumPv(ResolvedIborCapFloorTrade resolvedTrade) {
    return resolvedTrade
        .getPremium()
        .map(p -> PREMIUM_PRICER.presentValue(p, ratesProvider))
        .orElse(zero(options.getTradeCcy()));
  }

  private CashFlowMetrics cashFlowMetrics(
      ResolvedIborCapFloorTrade resolvedTrade, ResolvedIborCapFloorLeg capFloorLeg) {
    var currCash =
        TRADE_PRICER
            .currentCash(resolvedTrade, ratesProvider, volatilities)
            .convertedTo(capFloorLeg.getCurrency(), ratesProvider);
    CurrencyAmount payCash = currCash.getAmount() < 0 ? currCash : null;
    CurrencyAmount recCash = currCash.getAmount() > 0 ? currCash : null;
    return ofPayments(
        payCash, recCash, null, options.getReportingCcy(), options.getTradeCcy(), ratesProvider);
  }

  private BreakevenMetrics breakevenMetrics(
      ReferenceData refData, ResolvedIborCapFloorLeg capFloorLeg) {
    var parRate = parRateCalculation(trade).calculate(refData, ratesProvider);
    var impliedVol =
        PERIOD_PRICER.impliedVolatility(capFloorLeg.getFinalPeriod(), ratesProvider, volatilities);
    return BreakevenMetrics.newOf(parRate, impliedVol);
  }

  private OptionMetrics optionMetrics(
      ResolvedIborCapFloorTrade resolvedTrade, IborCapletFloorletVolatilities vols) {
    var p = resolvedTrade.getProduct();
    var delta =
        PRODUCT_PRICER.presentValueDelta(p, ratesProvider, vols).multipliedBy(ONE_BASIS_POINT);
    var gamma =
        PRODUCT_PRICER
            .presentValueGamma(p, ratesProvider, vols)
            .multipliedBy(ONE_BASIS_POINT)
            .multipliedBy(ONE_BASIS_POINT);
    var theta =
        PRODUCT_PRICER
            .presentValueTheta(resolvedTrade.getProduct(), ratesProvider, vols)
            .multipliedBy(DAY_FRACTION_OF_YEAR);

    var vegaAmount =
        PRODUCT_PRICER
            .presentValueSensitivityModelParamsVolatility(p, ratesProvider, vols)
            .multipliedBy(ONE_BASIS_POINT)
            .build()
            .getSensitivities()
            .stream()
            .map(ps -> ps.convertedTo(options.getTradeCcy(), ratesProvider))
            .map(PointSensitivity::getSensitivity)
            .reduce(Double::sum)
            .orElse(0d);

    var vega = CurrencyAmount.of(options.getTradeCcy(), vegaAmount);

    return OptionMetrics.builder()
        .pvDeltaLocalCcy(delta.getAmounts().first().getAmount())
        .pvDelta(delta.convertedTo(options.getReportingCcy(), ratesProvider).getAmount())
        .pvGammaLocalCcy(gamma.getAmounts().first().getAmount())
        .pvGamma(gamma.convertedTo(options.getReportingCcy(), ratesProvider).getAmount())
        .pvThetaLocalCcy(theta.getAmounts().first().getAmount())
        .pvTheta(theta.convertedTo(options.getReportingCcy(), ratesProvider).getAmount())
        .pvVegaLocalCcy(vega.getAmount())
        .pvVega(vega.convertedTo(options.getReportingCcy(), ratesProvider).getAmount())
        .build();
  }

  private Dv01Calculation dv01Calculation(ResolvedIborCapFloorTrade trade) {
    var sensitivities =
        TRADE_PRICER.presentValueSensitivityRates(trade, ratesProvider, volatilities);
    return new Dv01Calculation(
        options.getReportingCcy(), options.getTradeCcy(), sensitivities, curvesSupplier.get());
  }

  private Spot01CalculationResult calculateSpot01(
      CurrencyAmount dirtyPv, ResolvedIborCapFloorTrade resolvedTrade) {
    var spot01Calculation = Spot01Calculation.fromRatesProvider(ratesProvider, fxShiftedRates);
    return spot01Calculation.calculateSpot01(
        dirtyPv,
        (currencyPair, provider) -> {
          var shiftedVols =
              fxShiftedCaplets.stream()
                  .filter(v -> v.getCurrencyPair().equals(currencyPair))
                  .findFirst()
                  .orElseThrow(() -> missingShiftedCapletsException(currencyPair));

          return PRODUCT_PRICER
              .presentValue(resolvedTrade.getProduct(), provider, shiftedVols.getVolatilities())
              .convertedTo(dirtyPv.getCurrency(), provider);
        });
  }

  private IllegalArgumentException missingShiftedCapletsException(CurrencyPair currencyPair) {
    return new IllegalArgumentException("No shifted caplet vols for " + currencyPair);
  }
}
