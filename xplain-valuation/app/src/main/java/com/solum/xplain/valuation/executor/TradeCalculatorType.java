package com.solum.xplain.valuation.executor;

import com.solum.xplain.valuation.messages.trade.constant.ValuationProductType;

public enum TradeCalculatorType {
  SWAP,
  FXFWD,
  FXSWAP,
  FXCOLLAR,
  SWAPTION,
  CAP_FLOOR,
  CDS,
  CREDIT_INDEX,
  FXOPT,
  LOAN_NOTE,
  FRA;

  public static TradeCalculatorType fromProductType(ValuationProductType type) {
    return switch (type) {
      case IRS, XCCY, INFLATION -> SWAP;
      case SWAPTION -> SWAPTION;
      case FXFWD -> FXFWD;
      case FXSWAP -> FXSWAP;
      case FXCOLLAR -> FXCOLLAR;
      case FXOPT -> FXOPT;
      case CAP_FLOOR -> CAP_FLOOR;
      case CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> CREDIT_INDEX;
      case CDS -> CDS;
      case LOAN_NOTE -> LOAN_NOTE;
      case FRA -> FRA;
    };
  }
}
