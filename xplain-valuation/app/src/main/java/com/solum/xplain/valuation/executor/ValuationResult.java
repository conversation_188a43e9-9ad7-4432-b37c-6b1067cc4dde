package com.solum.xplain.valuation.executor;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.valuation.metrics.Metrics;
import java.util.List;
import lombok.Data;

@Data
public class ValuationResult {

  private final Metrics metrics;
  private final List<String> validationWarnings;

  public List<String> warnings() {
    return ImmutableList.<String>builder()
        .addAll(metrics.getWarnings())
        .addAll(validationWarnings)
        .build();
  }
}
