package com.solum.xplain.valuation.executor;

import static com.solum.xplain.valuation.calculation.TradeCalculationUtils.options;

import com.solum.xplain.valuation.calculation.loan.LoanDetails;
import com.solum.xplain.valuation.calculation.loan.LoanNoteTradeCalculator;
import com.solum.xplain.valuation.calibration.CalibrationCacheService;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.mapper.LoanNoteTradeMapper;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class LoanNoteValuationExecutorResolver implements TradeValuationExecutorResolver {

  private final CalibrationCacheService cacheService;
  private final LoanNoteTradeMapper tradeMapper;

  @Override
  public TradeCalculatorType calculatorType() {
    return TradeCalculatorType.LOAN_NOTE;
  }

  @Override
  public Either<ValuationError, TradeValuationExecutor> executor(ValuationRequest request) {
    var reference = request.getTradeDetails().getLoanNoteTradeDetails().getReference();

    return Steps.begin(cacheService.marketDataFxRates(request))
        .then(() -> cacheService.bondCurve(request, reference))
        .yield(
            (ratesProvider, bondCurve) ->
                new LoanNoteTradeCalculator(
                    tradeMapper.toStrataTrade(request),
                    LoanDetails.fromValuationRequest(request),
                    ratesProvider,
                    bondCurve,
                    options(request)))
        .map(TradeValuationExecutor::newOf);
  }
}
