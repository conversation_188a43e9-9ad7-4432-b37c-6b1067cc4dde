package com.solum.xplain.valuation.calculation;

import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.ResolvedTrade;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import java.util.function.Supplier;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeCalculationUtils {

  private static final String BREAK_EVEN_ERROR_MESSAGE_TEMPLATE =
      "Error calculating breakeven metrics. %s";

  public static Either<ValuationError, BreakevenMetrics> eitherBreakevenMetrics(
      ResolvedTrade resolvedTrade, Supplier<BreakevenMetrics> metricsSupplier) {
    return Checked.now(metricsSupplier::get)
        .toEither()
        .leftMap(err -> String.format(BREAK_EVEN_ERROR_MESSAGE_TEMPLATE, err.getMessage()))
        .leftMap(err -> formatError(resolvedTrade, err))
        .leftMap(ValuationError::new);
  }

  public static String formatError(ResolvedTrade resolvedTrade, String error) {
    return String.format("%s: %s", tradeId(resolvedTrade), error);
  }

  public static ValuationOptions options(ValuationRequest request) {
    return ValuationOptions.newOf(
        Currency.of(request.getTradeDetails().getInfo().getTradeCurrency()),
        Currency.of(request.getReportingCurrency()),
        request.isCalculateSensitivities(),
        request.getTradeDetails().getInfo().getXplainResolvedTradeUnderlying());
  }

  private static String tradeId(ResolvedTrade resolvedTrade) {
    return resolvedTrade.getInfo().getId().map(StandardId::getValue).orElse(null);
  }
}
