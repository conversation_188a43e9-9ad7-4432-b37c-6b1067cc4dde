package com.solum.xplain.valuation.calculation.swap;

import com.opengamma.strata.product.swap.RatePaymentPeriod;
import com.opengamma.strata.product.swap.ResolvedSwapLeg;
import java.time.LocalDate;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LegAccrualDaysCalculator {

  public static final LegAccrualDaysCalculator DEFAULT = new LegAccrualDaysCalculator();

  public int accruedDays(ResolvedSwapLeg swapLeg, LocalDate valuationDate) {
    return swapLeg
        .findPaymentPeriod(valuationDate)
        .filter(RatePaymentPeriod.class::isInstance)
        .map(RatePaymentPeriod.class::cast)
        .map(p -> calculateAccrualDays(p, valuationDate))
        .orElse(0);
  }

  private int calculateAccrualDays(RatePaymentPeriod period, LocalDate valuationDate) {
    var startDate = period.getStartDate();
    return Math.toIntExact(valuationDate.toEpochDay() - startDate.toEpochDay());
  }
}
