package com.solum.xplain.valuation.calculation;

import com.opengamma.strata.product.AttributeType;
import com.opengamma.strata.product.Trade;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeUtils {
  public static final AttributeType<Double> CLIENT_PV = AttributeType.of("CLIENT_PV");

  @Nullable
  public static Double getClientPv(Trade trade) {
    return trade == null ? null : trade.getInfo().findAttribute(CLIENT_PV).orElse(null);
  }
}
