package com.solum.xplain.valuation.calculation.breakeven;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.swap.SwapTrade;
import lombok.AllArgsConstructor;

/**
 * Implementation of {@link SwapParRateCalculation} for calculating the par rate of a trade with a
 * fixed leg.
 */
@AllArgsConstructor
class FixedLegSwapParRateCalculation implements SwapParRateCalculation {

  private final SwapTrade trade;

  @Override
  public Double calculate(ReferenceData referenceData, RatesProvider provider) {
    var parRatePricer = DiscountingSwapProductParRatePricerFactory.parRatePricer(referenceData);
    var resolvedTrade = trade.resolve(referenceData);
    try {
      double parRate = parRatePricer.parRate(resolvedTrade.getProduct(), provider);
      if (Double.isNaN(parRate)) {
        return null;
      }
      return parRate;
    } catch (IllegalArgumentException e) {
      return null;
    }
  }
}
