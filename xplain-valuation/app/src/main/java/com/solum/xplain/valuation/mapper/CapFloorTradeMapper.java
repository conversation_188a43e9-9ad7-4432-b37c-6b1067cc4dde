package com.solum.xplain.valuation.mapper;

import static com.solum.xplain.valuation.mapper.StrataMappingUtils.adjustment;
import static com.solum.xplain.valuation.mapper.TradeMapperUtils.parseIborIndex;
import static com.solum.xplain.valuation.mapper.TradeMapperUtils.tradePositionLeg;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.capfloor.IborCapFloor;
import com.opengamma.strata.product.capfloor.IborCapFloorLeg;
import com.opengamma.strata.product.capfloor.IborCapFloorTrade;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class CapFloorTradeMapper {

  private static final Integer CAPFLOOR_PAYMENT_OFFSET_DAYS = 0;
  private static final StubConvention CAPFLOOR_STUB_CONVENTION = StubConvention.NONE;

  public IborCapFloorTrade toStrataTrade(ValuationRequest request) {
    var trade = request.getTradeDetails();
    var calendarId = HolidayCalendarId.of(trade.getCalendar());
    var adjustment = adjustment(calendarId, trade.getBusinessDayConvention());
    var capFloorLeg = tradePositionLeg(trade);

    var iborIndex = parseIborIndex(capFloorLeg.getIndex(), capFloorLeg.getIsOffshore());

    var builder = IborCapFloorLeg.builder();
    builder.payReceive(trade.getPositionType().toPayReceive());
    builder.currency(Currency.of(capFloorLeg.getCurrency()));
    builder.paymentSchedule(
        PeriodicSchedule.builder()
            .startDate(trade.getStartDate())
            .endDate(trade.getEndDate())
            .frequency(Frequency.of(iborIndex.getTenor().getPeriod()))
            .stubConvention(CAPFLOOR_STUB_CONVENTION)
            .businessDayAdjustment(adjustment)
            .build());
    builder.paymentDateOffset(
        DaysAdjustment.builder()
            .days(CAPFLOOR_PAYMENT_OFFSET_DAYS)
            .calendar(calendarId)
            .adjustment(adjustment)
            .build());
    builder.notional(ValueSchedule.of(capFloorLeg.getNotional()));

    builder.calculation(rateCalculation(iborIndex, capFloorLeg));

    var optionDetails = trade.getOptionTradeDetails();
    var capFloorType = optionDetails.getCapFloorType();
    capFloorType.valueSchedule(builder, optionDetails.getStrike());

    var premium = TradeMapperUtils.premium(optionDetails);

    return IborCapFloorTrade.builder()
        .product(IborCapFloor.of(builder.build()))
        .premium(StrataMappingUtils.toPayment(premium, calendarId).orElse(null))
        .info(TradeInfoMapper.INSTANCE.toTradeInfo(trade.getInfo(), request.getExternalTradeId()))
        .build();
  }

  private IborRateCalculation rateCalculation(
      IborIndex index, ValuationTradeLegDetails legDetails) {
    var daysAdjustment =
        Optional.ofNullable(legDetails.getFixingDateOffsetDays())
            .map(
                days ->
                    DaysAdjustment.builder()
                        .days(days)
                        .calendar(index.getFixingDateOffset().getCalendar())
                        .adjustment(index.getFixingDateOffset().getAdjustment())
                        .build()
                        .normalized())
            .orElse(index.getFixingDateOffset());
    var valueSchedule =
        legDetails.getInitialValue() == null
            ? null
            : ValueSchedule.of(legDetails.getInitialValue());
    return IborRateCalculation.builder()
        .dayCount(DayCount.of(legDetails.getDayCount()))
        .index(index)
        .fixingDateOffset(daysAdjustment)
        .spread(valueSchedule)
        .build();
  }
}
