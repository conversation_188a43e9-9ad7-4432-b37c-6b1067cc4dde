package com.solum.xplain.valuation.refdata;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.ReferenceDataId;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.valuation.messages.calibration.CalibrationCacheType;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/** This service allows for non-standard reference data to be looked up from a distributed cache. */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReferenceDataCacheService {
  private final DataGrid dataGrid;

  @NonNull
  public ReferenceData getReferenceData(ValuationRequest request) {
    String calculationId = request.getCalculationId();
    try {
      var cachedRefData =
          (Map<? extends ReferenceDataId<?>, ?>)
              dataGrid
                  .getKeyValueCache(CalibrationCacheType.REF_DATA.toString())
                  .get(calculationId);
      Optional<ReferenceData> customReferenceData =
          Optional.ofNullable(cachedRefData)
              .map(ReferenceData::of)
              .map(x -> x.combinedWith(ReferenceData.standard()));
      return customReferenceData.orElseThrow(
          () -> {
            log.error(missingReferenceData(calculationId));
            return new IllegalStateException(missingReferenceData(calculationId));
          });
    } catch (Exception e) {
      log.error(missingReferenceData(calculationId), e);
      throw new IllegalStateException(missingReferenceData(calculationId), e);
    }
  }

  private String missingReferenceData(String calculationId) {
    return String.format("%s: Reference data missing", calculationId);
  }
}
