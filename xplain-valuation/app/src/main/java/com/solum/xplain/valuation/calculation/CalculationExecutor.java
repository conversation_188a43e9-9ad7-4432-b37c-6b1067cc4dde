package com.solum.xplain.valuation.calculation;

import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.executor.TradeValuationExecutorResolverFactory;
import com.solum.xplain.valuation.executor.ValuationResult;
import com.solum.xplain.valuation.messages.metrics.ValuationMetricsDto;
import com.solum.xplain.valuation.messages.metrics.ValuationResponse;
import com.solum.xplain.valuation.messages.metrics.ValuationStatus;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import com.solum.xplain.valuation.metrics.BsonValuationMetricsMapper;
import com.solum.xplain.valuation.metrics.MetricsMapper;
import com.solum.xplain.valuation.refdata.ReferenceDataCacheService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class CalculationExecutor {

  private static final Logger LOG = getLogger(CalculationExecutor.class);

  private final TradeValuationExecutorResolverFactory resolverFactory;
  private final MetricsMapper metricsMapper;
  private final ReferenceDataCacheService referenceDataCacheService;
  private final String appVersion;
  private final BsonValuationMetricsMapper bsonValuationMetricsMapper;

  public CalculationExecutor(
      TradeValuationExecutorResolverFactory resolverFactory,
      MetricsMapper metricsMapper,
      ReferenceDataCacheService referenceDataCacheService,
      BsonValuationMetricsMapper bsonValuationMetricsMapper,
      @Value("${app.version}") String appVersion) {
    this.resolverFactory = resolverFactory;
    this.metricsMapper = metricsMapper;
    this.referenceDataCacheService = referenceDataCacheService;
    this.bsonValuationMetricsMapper = bsonValuationMetricsMapper;
    this.appVersion = appVersion;
  }

  public <T extends ValuationRequest> ValuationResponse valuate(T request) {
    try {
      ReferenceData referenceData = referenceDataCacheService.getReferenceData(request);
      return resolverFactory
          .getResolver(request.getProductType())
          .flatMap(r -> r.executor(request))
          .flatMap(executor -> executor.execute(referenceData))
          .fold(err -> ofError(err, request), metrics -> ofResult(metrics, request));
    } catch (Exception e) {
      LOG.error("Error valuating trade", e);
      return ofError(new ValuationError(e.getMessage()), request);
    }
  }

  private ValuationResponse ofError(ValuationError error, ValuationRequest request) {
    var response = response(request);
    response.setErrorMessage(error.getDescription());
    response.setStatus(ValuationStatus.ERROR);
    return response;
  }

  private ValuationResponse ofResult(ValuationResult result, ValuationRequest request) {
    var response = response(request);
    try {
      ValuationMetricsDto metrics = metricsMapper.toValuationMetrics(result.getMetrics());
      byte[] metricsSerialized = bsonValuationMetricsMapper.toBsonBytes(metrics);
      response.setValuationMetricsBytes(metricsSerialized);
      response.setWarnings(result.warnings());
      response.setStatus(ValuationStatus.OK);
      return response;
    } catch (Exception e) {
      LOG.error("Error serialising metrics", e);
      return ofError(new ValuationError(e.getMessage()), request);
    }
  }

  private ValuationResponse response(ValuationRequest request) {
    var response = new ValuationResponse();
    response.setSimulationId(request.getSimulationId());
    response.setSimulationType(request.getSimulationType());
    response.setCalculationId(request.getCalculationId());
    response.setValuationDate(request.getValuationDate());
    response.setPortfolioId(request.getPortfolioId());
    response.setTradeId(request.getTradeId());
    response.setExternalTradeId(request.getExternalTradeId());
    response.setDescription(request.getDescription());

    response.setTradeDetails(request.getTradeDetails());
    response.setProductType(request.getProductType());

    response.setDiscountingCcy(request.getDiscountingCcy());
    response.setAppVersion(appVersion);
    return response;
  }
}
