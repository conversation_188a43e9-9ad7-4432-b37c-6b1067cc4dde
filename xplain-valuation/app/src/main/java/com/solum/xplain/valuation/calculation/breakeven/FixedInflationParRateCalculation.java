package com.solum.xplain.valuation.calculation.breakeven;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.swap.SwapLegType;
import com.opengamma.strata.product.swap.SwapTrade;
import lombok.AllArgsConstructor;

@AllArgsConstructor
class FixedInflationParRateCalculation implements SwapParRateCalculation {

  private final SwapTrade trade;

  @Override
  public Double calculate(ReferenceData referenceData, RatesProvider provider) {
    var resolvedTrade = trade.resolve(referenceData);
    var fixedLeg = getSwapLeg(resolvedTrade, SwapLegType.FIXED);
    var inflationLeg = getSwapLeg(resolvedTrade, SwapLegType.INFLATION);
    var pricer =
        DiscountingSwapProductParRatePricerFactory.parRatePricer(referenceData).getLegPricer();

    double fixedPv = pricer.presentValue(fixedLeg, provider).getAmount();
    double inflationPv = pricer.presentValue(inflationLeg, provider).getAmount();

    return Math.abs(inflationPv / fixedPv) * fixedRate(trade);
  }
}
