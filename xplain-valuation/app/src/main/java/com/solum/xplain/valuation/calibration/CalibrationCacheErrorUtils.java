package com.solum.xplain.valuation.calibration;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CalibrationCacheErrorUtils {

  private static final String MISSING_RATES = "%s: Required rates missing";
  private static final String MISSING_CREDIT_RATES = "%s: Required credit rates missing";
  private static final String MISSING_BOND_CURVES = "%s: Required bond curve %s missing";
  private static final String MISSING_VOLS = "%s: Required %s volatilities missing";
  private static final String MISSING_CAPFLOORS = "%s: Required %s capfloor volatilities missing";
  private static final String MISSING_FX_RATES = "%s: Required FX rate provider missing";

  public static String missingRates(ValuationRequest request) {
    return String.format(MISSING_RATES, request.getExternalTradeId());
  }

  public static String missingCreditRates(ValuationRequest request) {
    return String.format(MISSING_CREDIT_RATES, request.getExternalTradeId());
  }

  public static String missingVols(ValuationRequest request, RateIndex index) {
    return String.format(MISSING_VOLS, request.getExternalTradeId(), index);
  }

  public static String missingFxVols(ValuationRequest request, CurrencyPair pair) {
    return String.format(MISSING_VOLS, request.getExternalTradeId(), pair);
  }

  public static String missingCapFloors(ValuationRequest request, IborIndex iborIndex) {
    return String.format(MISSING_CAPFLOORS, request.getExternalTradeId(), iborIndex);
  }

  public static String missingBondCurve(ValuationRequest request, String reference) {
    return String.format(MISSING_BOND_CURVES, request.getExternalTradeId(), reference);
  }

  public static String missingFxRates(ValuationRequest request) {
    return String.format(MISSING_FX_RATES, request.getExternalTradeId());
  }
}
