package com.solum.xplain.valuation.calculation.sensitivity.spot01;

import com.solum.xplain.valuation.metrics.AssignableToMetrics;
import com.solum.xplain.valuation.metrics.Metrics.MetricsBuilder;
import com.solum.xplain.valuation.metrics.Spot01TradeValue;
import java.util.List;

public record Spot01CalculationResult(List<Spot01TradeValue> spotO1Values)
    implements AssignableToMetrics {

  @Override
  public MetricsBuilder assignToMetrics(MetricsBuilder builder) {
    return builder.spot01TradeValues(spotO1Values);
  }
}
