package com.solum.xplain.valuation.executor;

import static com.solum.xplain.valuation.calculation.TradeCalculationUtils.options;

import com.solum.xplain.valuation.calculation.capfloor.CapFloorTradeCalculator;
import com.solum.xplain.valuation.calibration.CalibrationCacheService;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.mapper.CapFloorTradeMapper;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import org.springframework.stereotype.Component;

@Component
public class CapFloorValuationExecutorResolver implements TradeValuationExecutorResolver {

  private final CalibrationCacheService cacheService;
  private final CapFloorTradeMapper tradeMapper;

  public CapFloorValuationExecutorResolver(
      CalibrationCacheService cacheService, CapFloorTradeMapper tradeMapper) {
    this.cacheService = cacheService;
    this.tradeMapper = tradeMapper;
  }

  @Override
  public TradeCalculatorType calculatorType() {
    return TradeCalculatorType.CAP_FLOOR;
  }

  @Override
  public Either<ValuationError, TradeValuationExecutor> executor(ValuationRequest request) {
    var capFloor = tradeMapper.toStrataTrade(request);
    var iborIndex = capFloor.getProduct().getCapFloorLeg().getIndex();

    return Steps.begin(cacheService.calibrationRates(request))
        .then(() -> cacheService.capFloors(request, iborIndex))
        .yield(
            (calibratedRates, caplets) ->
                new CapFloorTradeCalculator(
                    capFloor,
                    options(request),
                    cacheService.curvesSupplier(request),
                    calibratedRates,
                    caplets))
        .map(TradeValuationExecutor::newOf);
  }
}
