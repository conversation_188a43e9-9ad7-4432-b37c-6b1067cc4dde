package com.solum.xplain.valuation.calculation.breakeven;

import static java.lang.String.format;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.ResolvedSwapLeg;
import com.opengamma.strata.product.swap.ResolvedSwapTrade;
import com.opengamma.strata.product.swap.SwapLeg;
import com.opengamma.strata.product.swap.SwapLegType;
import com.opengamma.strata.product.swap.SwapTrade;
import io.atlassian.fugue.Iterables;

public interface SwapParRateCalculation {

  Double calculate(ReferenceData referenceData, RatesProvider provider);

  default ResolvedSwapLeg getSwapLeg(ResolvedSwapTrade trade, SwapLegType type) {
    return Iterables.first(trade.getProduct().getLegs(type))
        .toOptional()
        .orElseThrow(() -> new IllegalArgumentException(format("Required %s leg not found", type)));
  }

  default SwapLeg getSwapLeg(SwapTrade trade, SwapLegType type) {
    return Iterables.first(trade.getProduct().getLegs(type))
        .toOptional()
        .orElseThrow(() -> new IllegalArgumentException(format("Required %s leg not found", type)));
  }

  default double fixedRate(SwapTrade trade) {
    return trade.getProduct().getLegs(SwapLegType.FIXED).stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .map(RateCalculationSwapLeg::getCalculation)
        .filter(FixedRateCalculation.class::isInstance)
        .map(FixedRateCalculation.class::cast)
        .map(FixedRateCalculation::getRate)
        .map(ValueSchedule::getInitialValue)
        .findFirst()
        .orElseThrow(() -> new IllegalArgumentException("Trade must have fixed rate"));
  }
}
