package com.solum.xplain.valuation.metrics;

import static java.util.Comparator.comparing;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.FxRateProvider;
import com.opengamma.strata.market.amount.CashFlows;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class CashFlowValues {

  private final List<CashFlowValue> values;

  public static CashFlowValues payValues(
      CashFlows c, Currency calculationCurrency, FxRateProvider ratesProvider) {
    var values =
        c.getCashFlows().stream()
            .map(cashFlow -> CashFlowValue.payValue(cashFlow, calculationCurrency, ratesProvider))
            .toList();
    return new CashFlowValues(values);
  }

  public static CashFlowValues receiveValues(
      CashFlows c, Currency calculationCurrency, FxRateProvider ratesProvider) {
    var values =
        c.getCashFlows().stream()
            .map(
                cashFlow ->
                    CashFlowValue.receiveValue(cashFlow, calculationCurrency, ratesProvider))
            .toList();
    return new CashFlowValues(values);
  }

  public static CashFlowValues empty() {
    return new CashFlowValues(Collections.emptyList());
  }

  public CashFlowValues combinedWith(CashFlowValues other) {
    return new CashFlowValues(
        ImmutableList.<CashFlowValue>builder().addAll(values).addAll(other.values).build());
  }

  public CashFlowValues compact() {
    return new CashFlowValues(
        values.stream()
            .collect(
                Collectors.groupingBy(
                    CashFlowValue::cashflowKey, Collectors.reducing(CashFlowValue::reduce)))
            .values()
            .stream()
            .filter(Optional::isPresent)
            .map(Optional::get)
            .sorted(comparing(CashFlowValue::getPaymentDate))
            .toList());
  }

  public Optional<CashFlowValue> firstCashFlow() {
    return values.isEmpty() ? Optional.empty() : Optional.of(values.get(0));
  }
}
