package com.solum.xplain.valuation.calculation.fx;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.pricer.fx.DiscountingFxSingleProductPricer;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.fx.ResolvedFxSingle;
import com.opengamma.strata.product.fx.ResolvedFxSwap;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * Utility class for FX trade calculation metrics with adjustments for underlying currency
 * direction.
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FxUnderlyingCalculationUtils {

  /**
   * Calculates par rate using the {@param fxSingleProductPricer}, and applies adjustments based on
   * the relative directions of the xplain and opengamma trade underlying fields.
   *
   * @param fxSingleProductPricer the pricer
   * @param strataTradeUnderlying the underlying of the strata trade based to the pricers
   * @param xplainTradeUnderlyingCurrencyPair the currency pair of the xplain-native trade
   * @param ratesProvider the rates provider
   * @return the underlying-adjusted par rate
   */
  public static Double parRate(
      DiscountingFxSingleProductPricer fxSingleProductPricer,
      ResolvedFxSingle strataTradeUnderlying,
      String xplainTradeUnderlyingCurrencyPair,
      RatesProvider ratesProvider) {
    if (ratesProvider.getValuationDate().isAfter(strataTradeUnderlying.getPaymentDate())) {
      return null;
    }

    var parRate =
        fxSingleProductPricer
            .forwardFxRate(strataTradeUnderlying, ratesProvider)
            .fxRate(strataTradeUnderlying.getCurrencyPair());
    var sameTradeUnderlyingDirection =
        sameXplainNativeTradeUnderlying(
            strataTradeUnderlying.getCurrencyPair().toString(), xplainTradeUnderlyingCurrencyPair);

    return sameTradeUnderlyingDirection ? parRate : 1.0d / parRate;
  }

  /**
   * Calculates par rate using the {@param fxSingleProductPricer}, and applies adjustments based on
   * the relative directions of the xplain and opengamma trade underlying fields. Par rate is
   * calculated separately for the near and far legs.
   *
   * @param fxSingleProductPricer the pricer
   * @param strataTradeUnderlying the underlying of the strata trade based to the pricers
   * @param xplainTradeUnderlyingCurrencyPair the currency pair of the xplain-native trade
   * @param ratesProvider the rates provider
   * @return the underlying-adjusted par rate
   */
  public static Double parRate(
      DiscountingFxSingleProductPricer fxSingleProductPricer,
      ResolvedFxSwap strataTradeUnderlying,
      String xplainTradeUnderlyingCurrencyPair,
      RatesProvider ratesProvider) {
    return parRate(
        fxSingleProductPricer,
        strataTradeUnderlying
            .getFarLeg(), // Par rate for FX Swaps should be calculated using Far Leg
        xplainTradeUnderlyingCurrencyPair,
        ratesProvider);
  }

  /**
   * Calculates fx spot using the {@param fxSingleProductPricer}, and applies adjustments based on
   * the relative directions of the xplain and opengamma trade underlying fields.
   *
   * @param strataTradeUnderlyingCurrencyPair the underlying of the strata trade based to the
   *     pricers
   * @param xplainTradeUnderlyingCurrencyPair the currency pair of the xplain-native trade
   * @param ratesProvider the rates provider
   * @return the underlying-adjusted par rate
   */
  public static Double fxSpot(
      CurrencyPair strataTradeUnderlyingCurrencyPair,
      String xplainTradeUnderlyingCurrencyPair,
      RatesProvider ratesProvider) {
    var sameTradeUnderlyingDirection =
        sameXplainNativeTradeUnderlying(
            strataTradeUnderlyingCurrencyPair.toString(), xplainTradeUnderlyingCurrencyPair);
    var fxSpot = ratesProvider.fxRate(strataTradeUnderlyingCurrencyPair);
    return sameTradeUnderlyingDirection ? fxSpot : 1.0d / fxSpot;
  }

  /**
   * Trade underlying for an Xplain-native FXFWD/FXO trade is defined as
   * baseCurrency/counterCurrency, without FX currency priority restriction on the currency
   * direction (e.g., base currency could be SEK, and counter currency could be USD). However, in
   * opengamma, the currency direction must conform to the fx priority ordering restrictions. <br>
   * <br>
   * To produce outputs with opengamma-compatible trades, we must convert particular metrics (such
   * as fx spot, which is the fx rate correpsponding to the underlying fx pair) so that they are in
   * line with the xplain-native trade underlying currency direction. For example, the opengamma
   * trade fx spot would be quoted as (USD/SEK), but in Xplain it should be reported as (SEK/USD).
   * <br>
   * <br>
   * This method returns a flag which allows us to determine whether to flip particular metrics to
   * achieve this.
   *
   * @return true if xplain-native trade has underlying with the same currency direction as
   *     this.trade
   */
  private static boolean sameXplainNativeTradeUnderlying(
      String strataTradeUnderlyingCurrencyPair, String xplainTradeUnderlyingCurrencyPair) {
    return strataTradeUnderlyingCurrencyPair.equals(xplainTradeUnderlyingCurrencyPair);
  }
}
