package com.solum.xplain.valuation.resolver


import com.solum.xplain.extensions.validation.TradeMarketDataValidator
import com.solum.xplain.valuation.calculation.credit.CreditIndexTradeCalculator
import com.solum.xplain.valuation.calibration.CalibrationCacheService
import com.solum.xplain.valuation.executor.CreditIndexValuationExecutorResolver
import com.solum.xplain.valuation.mapper.CreditIndexTradeMapper
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import io.atlassian.fugue.Either
import spock.lang.Specification

class CreditIndexCalculationResolverTest extends Specification {
  CalibrationCacheService cacheService = Mock()
  CreditIndexTradeMapper tradeMapper = Mock()

  CreditIndexValuationExecutorResolver listener = new CreditIndexValuationExecutorResolver(cacheService, tradeMapper)

  def "should resolve credit index calculator"() {
    setup:
    def request = new ValuationRequest(
      tradeDetails: new ValuationTradeDetails(info: new ValuationTradeInfo(tradeCurrency: "EUR")),
      reportingCurrency: "USD"
      )
    1 * cacheService.calibrationRates(request) >> Either.right(Mock(ValuationCurveRates))
    1 * cacheService.creditRates(request) >> Either.right(Mock(ValuationCreditRates))


    when:
    def result = listener.executor(request)
    then:
    result.isRight()
    result.getOrNull().calculator instanceof CreditIndexTradeCalculator
    result.getOrNull().validator == TradeMarketDataValidator.EMPTY_VALIDATOR
  }
}
