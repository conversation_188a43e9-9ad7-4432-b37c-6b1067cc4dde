package com.solum.xplain.valuation.mapper

import com.opengamma.strata.product.fx.FxSingleTrade
import com.opengamma.strata.product.fx.FxSwapTrade
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import java.time.LocalDate
import spock.lang.Specification

class FxSwapTradeMapperTest extends Specification {

  def "should map FxSwapTrade"() {
    setup:
    def trade = new FxSwapTradeMapper().toStrataTrade(request())
    expect:
    trade != null
    trade instanceof FxSwapTrade
    trade.product.nearLeg.getBaseCurrencyAmount().getAmount() == 1
    trade.product.nearLeg.getBaseCurrencyAmount().getCurrency().getCode() == "EUR"
    trade.product.nearLeg.getCounterCurrencyAmount().getAmount() == -2
    trade.product.nearLeg.getCounterCurrencyAmount().getCurrency().getCode() == "USD"
    trade.product.nearLeg.getPaymentDate() == LocalDate.parse("2016-01-01")
    trade.product.farLeg.getBaseCurrencyAmount().getAmount() == -1.5
    trade.product.farLeg.getBaseCurrencyAmount().getCurrency().getCode() == "EUR"
    trade.product.farLeg.getCounterCurrencyAmount().getAmount() == 2
    trade.product.farLeg.getCounterCurrencyAmount().getCurrency().getCode() == "USD"
    trade.product.farLeg.getPaymentDate() == LocalDate.parse("2017-01-01")
  }

  static ValuationRequest request() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      receiveLeg: new ValuationTradeLegDetails(
      currency: "EUR",
      nearNotional: 1,
      notional: -1.5
      ),
      payLeg: new ValuationTradeLegDetails(
      currency: "USD",
      nearNotional: -2,
      notional: 2
      ),
      startDate: LocalDate.parse("2016-01-01"),
      endDate: LocalDate.parse("2017-01-01"),
      calendar: "EUTAFX",
      businessDayConvention: "ModifiedFollowing",
      info: TradeUtils.tradeInfo()
      )
      )
  }
}
