package com.solum.xplain.valuation.mapper

import com.solum.xplain.valuation.messages.trade.ValuationAdjustablePayment
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import java.time.LocalDate

class TradeUtils {

  static ValuationAdjustablePayment payment() {
    new ValuationAdjustablePayment(
      amount: 2,
      date: LocalDate.parse("2016-01-01"),
      convention: "ModifiedFollowing",
      currency: "EUR"
      )
  }

  static ValuationTradeInfo tradeInfo() {
    new ValuationTradeInfo(
      )
  }
}
