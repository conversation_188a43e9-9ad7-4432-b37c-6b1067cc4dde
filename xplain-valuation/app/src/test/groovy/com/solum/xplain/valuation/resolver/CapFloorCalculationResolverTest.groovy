package com.solum.xplain.valuation.resolver

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M

import com.solum.xplain.extensions.validation.TradeMarketDataValidator
import com.solum.xplain.valuation.calculation.TradeSamples
import com.solum.xplain.valuation.calculation.capfloor.CapFloorTradeCalculator
import com.solum.xplain.valuation.calibration.CalibrationCacheService
import com.solum.xplain.valuation.executor.CapFloorValuationExecutorResolver
import com.solum.xplain.valuation.mapper.CapFloorTradeMapper
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates
import com.solum.xplain.valuation.messages.calibration.vols.ValuationCalibratedCaplets
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class CapFloorCalculationResolverTest extends Specification {
  CalibrationCacheService cacheService = Mock()
  CapFloorTradeMapper tradeMapper = Mock()

  CapFloorValuationExecutorResolver listener = new CapFloorValuationExecutorResolver(cacheService, tradeMapper)

  def "should resolve capfloor calculator"() {
    setup:
    def request = new ValuationRequest(
      tradeDetails: new ValuationTradeDetails(info: new ValuationTradeInfo(tradeCurrency: "EUR")),
      reportingCurrency: "USD"
      )

    1 * tradeMapper.toStrataTrade(request) >> TradeSamples.capFloorTrade(LocalDate.now())
    1 * cacheService.calibrationRates(request) >> Either.right(Mock(ValuationCurveRates))
    1 * cacheService.capFloors(request, EUR_EURIBOR_3M) >> Either.right(Mock(ValuationCalibratedCaplets))
    when:
    def result = listener.executor(request)
    then:
    result.isRight()
    result.getOrNull().calculator instanceof CapFloorTradeCalculator
    result.getOrNull().validator == TradeMarketDataValidator.EMPTY_VALIDATOR
  }
}
