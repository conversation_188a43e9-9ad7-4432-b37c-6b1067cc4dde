package com.solum.xplain.valuation.mapper

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING
import static com.opengamma.strata.basics.date.HolidayCalendarIds.AUSY
import static java.time.LocalDate.now

import com.opengamma.strata.basics.currency.AdjustablePayment
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.basics.date.AdjustableDate
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.solum.xplain.valuation.messages.trade.ValuationAdjustablePayment
import spock.lang.Specification

class StrataMappingUtilsTest extends Specification {
  def "should throw error if premium not present"() {
    when:
    StrataMappingUtils.premium(null, AUSY)
    then:
    thrown IllegalArgumentException
  }

  def "should correctly map to payment"() {
    setup:
    def payment = StrataMappingUtils.toPayment(form, AUSY)
    expect:
    payment == expected
    where:
    form          | expected
    null          | Optional.empty()
    paymentForm() | Optional.of(adjustablePayment())
  }

  static ValuationAdjustablePayment paymentForm() {
    return ValuationAdjustablePayment.newOf(now(), "Following", 0d, "EUR")
  }

  static AdjustablePayment adjustablePayment() {
    AdjustablePayment.of(CurrencyAmount.of(Currency.EUR, 0d), AdjustableDate.of(now(), BusinessDayAdjustment.of(FOLLOWING, AUSY)))
  }

  def "should correctly map to business day adjustment"() {
    setup:
    def bda = StrataMappingUtils.adjustment(AUSY, convention)
    expect:
    bda == expected
    where:
    convention  | expected
    "Following" | BusinessDayAdjustment.of(FOLLOWING, AUSY)
    null        | BusinessDayAdjustment.NONE
  }
}
