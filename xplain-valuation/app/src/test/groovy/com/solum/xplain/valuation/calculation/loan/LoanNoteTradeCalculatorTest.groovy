package com.solum.xplain.valuation.calculation.loan


import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.curve.NodalCurve
import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.TradeSamples
import com.solum.xplain.valuation.calculation.ValuationOptions
import java.time.LocalDate
import spock.lang.Specification

class LoanNoteTradeCalculatorTest extends Specification {
  def "should calculate loan note metrics"() {
    setup:

    def nodalCurve = Mock(NodalCurve)
    nodalCurve.yValue(24617) >> 0.**********

    def calculator = new LoanNoteTradeCalculator(
      TradeSamples.fixedCouponBond(LocalDate.of(2017, 5, 24)),
      new LoanDetails(0.0115d, 100d),
      MarketDataFxRateProvider.of(MarketDataSample.ogMarketData().toBuilder().valuationDate(LocalDate.of(2023, 2, 7)).build()),
      nodalCurve,
      ValuationOptions.newOf(GBP, USD, true, "GB-Bump-DMO")
      )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())


    then:
    metrics != null
    metrics.localCcy == USD.getCode()
    metrics.loanMetrics.gvtYTM == 0.**********
    that metrics.loanMetrics.modifiedDuration, closeTo(11.20, 0.01)
    that metrics.loanMetrics.duration, closeTo(11.46, 0.01)
    that metrics.loanMetrics.bondYTM, closeTo(0.04800, 0.000001)
    that metrics.presentValuePercent, closeTo(0.79433828, 0.000001)
    that metrics.presentValue, closeTo(1021302.2, 0.1)
    that metrics.presentValuePayLegCurrency, closeTo(794338.2, 0.1)

    that metrics.cleanPresentValuePercent, closeTo(0.788661485, 0.000001)
    that metrics.cleanPresentValue, closeTo(1014003.47, 0.1)
    that metrics.cleanPresentValueLocalCcy, closeTo(788661.48, 0.01)

    that metrics.accruedPercent, closeTo(0.00567679, 0.000001)
    that metrics.accrued, closeTo(7298.8, 0.1)
    that metrics.accruedLocalCcy, closeTo(5676.7, 0.1)
  }

  def "should calculate loan note metrics for JP_SIMPLE yield convention"() {
    setup:

    def nodalCurve = Mock(NodalCurve)
    nodalCurve.yValue(24617) >> 0.**********

    def calculator = new LoanNoteTradeCalculator(
      TradeSamples.fixedCouponBond(LocalDate.of(2017, 5, 24), FixedCouponBondYieldConvention.JP_SIMPLE, DayCounts.ACT_ACT_ISDA),
      new LoanDetails(0.0115d, 100d),
      MarketDataFxRateProvider.of(MarketDataSample.ogMarketData().toBuilder().valuationDate(LocalDate.of(2023, 2, 7)).build()),
      nodalCurve,
      ValuationOptions.newOf(GBP, USD, true, "GB-Bump-DMO")
      )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())


    then:
    metrics != null
    metrics.localCcy == USD.getCode()
    metrics.loanMetrics.gvtYTM == 0.**********
    that metrics.loanMetrics.modifiedDuration, closeTo(8.41, 0.01)
    metrics.loanMetrics.duration == null
    that metrics.loanMetrics.bondYTM, closeTo(0.04800, 0.000001)
    that metrics.presentValuePercent, closeTo(0.83101878, 0.000001)
    that metrics.presentValue, closeTo(1068463.4, 0.1)
    that metrics.presentValuePayLegCurrency, closeTo(831018.7, 0.1)

    that metrics.cleanPresentValuePercent, closeTo(0.82538864, 0.000001)
    that metrics.cleanPresentValue, closeTo(1061224.58, 0.1)
    that metrics.cleanPresentValueLocalCcy, closeTo(825388.64, 0.01)

    that metrics.accruedPercent, closeTo(0.00563013, 0.000001)
    that metrics.accrued, closeTo(7238.8, 0.1)
    that metrics.accruedLocalCcy, closeTo(5630.1, 0.1)
  }
}
