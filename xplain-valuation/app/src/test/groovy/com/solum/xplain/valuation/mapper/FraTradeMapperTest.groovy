package com.solum.xplain.valuation.mapper

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.product.fra.FraDiscountingMethod
import com.opengamma.strata.product.fra.FraTrade
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import com.solum.xplain.extensions.enums.PositionType
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class FraTradeMapperTest extends Specification {

  @Unroll
  def "should map FraTrade for #buySell"() {
    setup:
    def trade = new FraTradeMapper().toStrataTrade(request(indexType, curr, "AUD-BBSW-6M"))

    expect:
    trade != null
    trade instanceof FraTrade

    trade.product.getStartDate() == LocalDate.parse("2016-01-01")
    trade.product.getEndDate() == LocalDate.parse("2017-01-01")
    trade.product.getBuySell().isBuy() == (buySell == "buy")
    trade.product.getCurrency().getCode() == "AUD"
    trade.product.getNotional() == 1000000.0
    trade.product.getFixedRate() == 0.05
    trade.product.getIndex() == IborIndices.AUD_BBSW_6M
    trade.product.getBusinessDayAdjustment().get().convention ==  MODIFIED_FOLLOWING

    trade.product.getDiscounting() == FraDiscountingMethod.of("AFMA")

    where:
    buySell | indexType   | curr
    "buy"   | "FIXED"     | "AUD"
    "sell"  | "OVERNIGHT" | "AUD"
  }

  def "should map FraTrade for #discounting"() {
    setup:
    def trade = new FraTradeMapper().toStrataTrade(request(indexType, curr, ibor_index))

    expect:
    trade != null
    trade instanceof FraTrade

    trade.product.getStartDate() == LocalDate.parse("2016-01-01")
    trade.product.getEndDate() == LocalDate.parse("2017-01-01")
    trade.product.getBuySell().isBuy()
    trade.product.getCurrency().getCode() == curr
    trade.product.getNotional() == 1000000.0
    trade.product.getFixedRate() == 0.05
    trade.product.getIndex() == IborIndex.of(ibor_index)
    trade.product.getDiscounting() == FraDiscountingMethod.of(discounting)

    where:
    indexType   | curr  | discounting | ibor_index
    "FIXED"     | "AUD" | "AFMA"      | "AUD-BBSW-6M"
    "FIXED"     | "NZD" | "AFMA"      | "NZD-BKBM-3M"
    "FIXED"     | "EUR" | "ISDA"      | "EUR-EURIBOR-3M"
  }

  def "should return correct FraDiscounting"() {
    setup:
    def discount = new FraTradeMapper().getFraDiscountingMethod(Currency.parse(curr))

    expect:
    discount!= null
    discount instanceof FraDiscountingMethod

    discount == FraDiscountingMethod.of(discounting)

    where:
    curr  | discounting
    "AUD" | "AFMA"
    "NZD" | "AFMA"
    "EUR" | "ISDA"
  }

  static ValuationRequest request(String type, String curr, String index) {
    def positionType = type == "FIXED" ? PositionType.BUY : PositionType.SELL

    def payLeg,receiveLeg
    var fixedLeg = new ValuationTradeLegDetails(
      type: "FIXED",
      currency: curr,
      initialValue: 0.05,
      notional: 1000000,
      dayCount: "Act/360",
      )
    var iborLeg = new ValuationTradeLegDetails(
      type: "IBOR",
      currency: curr,
      index: index,
      notional: 1000000,
      dayCount: "Act/360",
      )

    if (positionType == PositionType.BUY) {
      payLeg = fixedLeg
      receiveLeg = iborLeg
    } else {
      // positionType == "sell"
      payLeg = iborLeg
      receiveLeg = fixedLeg
    }

    return new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      positionType: positionType,
      startDate: LocalDate.parse("2016-01-01"),
      endDate: LocalDate.parse("2017-01-01"),
      businessDayConvention: "ModifiedFollowing",
      payLeg: payLeg,
      receiveLeg: receiveLeg,
      info: new ValuationTradeInfo(tradeCurrency: "NZD"),
      )
      )
  }
}
