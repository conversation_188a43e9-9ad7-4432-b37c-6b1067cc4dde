package com.solum.xplain.valuation.calculation.sensitivity

import static com.solum.xplain.extensions.calibrator.XplainCreditCurveCalibrator.D_ZHRATES_D_SWAP_RATES_JACOBIAN

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.market.curve.Curve
import com.opengamma.strata.market.curve.CurveInfoType
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix
import com.opengamma.strata.market.param.CurrencyParameterSensitivities
import com.opengamma.strata.market.param.CurrencyParameterSensitivity
import com.opengamma.strata.market.sensitivity.PointSensitivities
import com.opengamma.strata.math.impl.matrix.OGMatrixAlgebra
import com.opengamma.strata.pricer.credit.CreditRatesProvider
import com.opengamma.strata.pricer.credit.IsdaCdsTradePricer
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.TradeSamples
import com.solum.xplain.valuation.calculation.credit.CreditRatesSample
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve
import spock.lang.Specification

class XplainCreditMarketQuoteSensitivityCalculatorTest extends Specification {

  def calculator = new XplainCreditMarketQuoteSensitivityCalculator()
  def STANDRARD_ID = CreditRatesSample.CDS_STANDARD_ID
  def CURRENCY = Currency.EUR
  def TRADE_PRICER = IsdaCdsTradePricer.DEFAULT
  def REF_DATA = ReferenceData.standard()
  def cdsTrade = TradeSamples.cdsTrade(MarketDataSample.VAL_DT.plusMonths(1)).resolve(REF_DATA)

  def "should calculate parameter sensitivities"() {
    setup:
    CreditRatesProvider creditRatesProvider = CreditRatesSample.creditRatesProvider(CreditRatesSample.CDS_STANDARD_ID).creditRatesProvider
    PointSensitivities pointSensitivities = TRADE_PRICER.parSpreadSensitivity(cdsTrade, creditRatesProvider, REF_DATA)
    CurrencyParameterSensitivities creditRatesSensitivity = creditRatesProvider.parameterSensitivity(pointSensitivities)
    Map<String, CalibrationCurve> curves = CreditRatesSample.creditCalibrationCurves()

    when:
    CurrencyParameterSensitivities outputSensitivities = calculator.sensitivity(creditRatesSensitivity, creditRatesProvider, curves)

    then:
    outputSensitivities.size() == 2

    outputSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR).getSensitivity() == DoubleArray.of(0.9884592948925218, 0.9884592948925218)
    outputSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR).getSensitivity() == DoubleArray.of(1.6054385720336994)
  }

  def "should combine result with sensitivities calculated using ∂ZHRates/∂SwapRates matrix"() {
    setup:
    CreditRatesProvider creditRatesProvider = CreditRatesSample.creditRatesProvider(CreditRatesSample.CDS_STANDARD_ID).creditRatesProvider
    PointSensitivities pointSensitivities = TRADE_PRICER.parSpreadSensitivity(cdsTrade, creditRatesProvider, REF_DATA)
    CurrencyParameterSensitivities currencyParameterSensitivities = creditRatesProvider.parameterSensitivity(pointSensitivities)
    CurrencyParameterSensitivity paramSens = currencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR)
    Curve creditCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(STANDRARD_ID, CURRENCY).survivalProbabilities).curve
    DoubleArray marketQuoteSens = calculator.calculateMarketQuoteSensMatrix(creditCurve.metadata.getInfo(D_ZHRATES_D_SWAP_RATES_JACOBIAN), paramSens)

    when:
    CurrencyParameterSensitivities combinedCurrencyParameterSensitivities = calculator.combineZeroHazardSwapRateSensitivities(creditCurve, paramSens, currencyParameterSensitivities, creditRatesProvider)

    then:
    combinedCurrencyParameterSensitivities.getSensitivities().size() == 2
    combinedCurrencyParameterSensitivities.getSensitivities().get(0) == currencyParameterSensitivities.getSensitivities().get(0)
    combinedCurrencyParameterSensitivities.getSensitivities().get(1) != currencyParameterSensitivities.getSensitivities().get(1)
    combinedCurrencyParameterSensitivities.getSensitivities().get(1).getSensitivity() == currencyParameterSensitivities.getSensitivities().get(1).getSensitivity().combine(marketQuoteSens, (a, b) -> a + b)
  }

  def "should throw error if ∂ZHRates/∂Rates Jacobian is missing"() {
    setup:
    CreditRatesProvider creditRatesProvider = CreditRatesSample.creditRatesProvider(CreditRatesSample.CDS_STANDARD_ID, false).creditRatesProvider
    PointSensitivities pointSensitivities = TRADE_PRICER.parSpreadSensitivity(cdsTrade, creditRatesProvider, REF_DATA)
    CurrencyParameterSensitivities currencyParameterSensitivities = creditRatesProvider.parameterSensitivity(pointSensitivities)
    CurrencyParameterSensitivity paramSens = currencyParameterSensitivities.getSensitivities().get(0)
    Curve creditCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(STANDRARD_ID, CURRENCY).survivalProbabilities).curve

    when:
    calculator.combineZeroHazardSwapRateSensitivities(creditCurve, paramSens, currencyParameterSensitivities, creditRatesProvider)

    then:
    IllegalArgumentException exception = thrown()
    exception.message == "Market Quote sensitivity requires ∂ZHRates/∂Rates Jacobian calibration information"
  }

  def "should combine multiple currency parameter sensitivities split by curve"() {
    setup:
    CreditRatesProvider creditRatesProvider = CreditRatesSample.creditRatesProvider(CreditRatesSample.CDS_STANDARD_ID).creditRatesProvider
    PointSensitivities pointSensitivities = TRADE_PRICER.parSpreadSensitivity(cdsTrade, creditRatesProvider, REF_DATA)
    CurrencyParameterSensitivities currencyParameterSensitivities = creditRatesProvider.parameterSensitivity(pointSensitivities)
    Curve creditCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(STANDRARD_ID, CURRENCY).survivalProbabilities).curve
    Curve discountCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.discountFactors(Currency.EUR)).curve

    JacobianCalibrationMatrix discountCurveMatrix = discountCurve.metadata.getInfo(CurveInfoType.JACOBIAN)
    JacobianCalibrationMatrix creditCurveCreditRatesMatrix = creditCurve.metadata.getInfo(CurveInfoType.JACOBIAN)
    JacobianCalibrationMatrix creditCurveZhrSwapRatesMatrix = creditCurve.metadata.getInfo(D_ZHRATES_D_SWAP_RATES_JACOBIAN)

    CurrencyParameterSensitivity discountCurveSens = currencyParameterSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR)
    CurrencyParameterSensitivity creditCurveSens = currencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR)

    DoubleArray marketQuoteSensArray = calculator.calculateMarketQuoteSensMatrix(discountCurveMatrix, discountCurveSens)
    DoubleArray marketQuoteSensArrayCredit = calculator.calculateMarketQuoteSensMatrix(creditCurveCreditRatesMatrix, creditCurveSens)
    DoubleArray marketQuoteSensArrayCreditWithSwapRateMatrix = calculator.calculateMarketQuoteSensMatrix(creditCurveZhrSwapRatesMatrix, creditCurveSens)

    // 1st time, for discount curve
    when:
    CurrencyParameterSensitivities outputCurrencyParameterSensitivities = calculator.splitBetweenCurvesAndCombineResult(
    currencyParameterSensitivities,
    discountCurveMatrix,
    marketQuoteSensArray,
    creditRatesProvider,
    discountCurveSens
    )
    def savedDiscountCurveSens = outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR).getSensitivity()

    then:
    outputCurrencyParameterSensitivities.size() == 2
    savedDiscountCurveSens == discountCurveSens.getSensitivity().combine(marketQuoteSensArray, (a, b) -> a + b)
    outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR).getSensitivity() == creditCurveSens.getSensitivity()

    // 2nd time, with credit curve (using market quote sens array with creditRates jacobian matrix)
    when:
    outputCurrencyParameterSensitivities = calculator.splitBetweenCurvesAndCombineResult(
    outputCurrencyParameterSensitivities,
    creditCurveCreditRatesMatrix,
    marketQuoteSensArrayCredit,
    creditRatesProvider,
    creditCurveSens
    )
    def savedCreditCurveSens = outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR)

    then:
    // check discount curve sensitivities unchanged
    outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR).getSensitivity() == savedDiscountCurveSens

    outputCurrencyParameterSensitivities.size() == 2
    savedCreditCurveSens.getSensitivity() == creditCurveSens.getSensitivity().combine(marketQuoteSensArrayCredit, (a, b) -> a + b)

    // 3rd time, with credit curve (using market quote sens array with swapRates jacobian matrix) - should update sensitivities to discount curve
    when:
    outputCurrencyParameterSensitivities = calculator.splitBetweenCurvesAndCombineResult(
    outputCurrencyParameterSensitivities,
    creditCurveZhrSwapRatesMatrix,
    marketQuoteSensArrayCreditWithSwapRateMatrix,
    creditRatesProvider,
    savedCreditCurveSens
    )

    then:
    outputCurrencyParameterSensitivities.size() == 2

    // check discount curve sensitivities changed
    outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR).getSensitivity() != savedDiscountCurveSens
    outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR discount"), Currency.EUR).getSensitivity() == savedDiscountCurveSens.combine(marketQuoteSensArrayCreditWithSwapRateMatrix, (a, b) -> a + b)

    // check credit cruve sensitivities unchanged
    savedCreditCurveSens.getSensitivity() == outputCurrencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR).getSensitivity()
  }

  def "should calculate market quote sensitivities"() {
    setup:
    CreditRatesProvider creditRatesProvider = CreditRatesSample.creditRatesProvider(CreditRatesSample.CDS_STANDARD_ID).creditRatesProvider
    PointSensitivities pointSensitivities = TRADE_PRICER.parSpreadSensitivity(cdsTrade, creditRatesProvider, REF_DATA)
    CurrencyParameterSensitivities currencyParameterSensitivities = creditRatesProvider.parameterSensitivity(pointSensitivities)
    CurrencyParameterSensitivity paramSens = currencyParameterSensitivities.getSensitivity(CurveName.of("EUR"), Currency.EUR)
    Curve creditCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(STANDRARD_ID, CURRENCY).survivalProbabilities).curve
    JacobianCalibrationMatrix jacobianCalibrationMatrix = creditCurve.metadata.getInfo(D_ZHRATES_D_SWAP_RATES_JACOBIAN)

    when:
    DoubleArray output = calculator.calculateMarketQuoteSensMatrix(jacobianCalibrationMatrix, paramSens)

    then:
    DoubleArray calculatedMatrix = (DoubleArray) new OGMatrixAlgebra().multiply(paramSens.getSensitivity(), jacobianCalibrationMatrix.getJacobianMatrix())
    output.equals(calculatedMatrix)
  }
}
