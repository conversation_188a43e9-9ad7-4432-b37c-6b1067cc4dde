package com.solum.xplain.valuation.calibration.vols

import com.opengamma.strata.pricer.option.TenorRawOptionData
import com.solum.xplain.valuation.messages.calibration.vols.SkewType
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceConfig
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceForCalculation
import io.atlassian.fugue.Either
import spock.lang.Specification

class VolatilityProviderFactoryTest extends Specification {
  SabrVolatilitiesDataFactory sabrFactory = Mock()
  SkewVolatilitiesDataFactory skewFactory = Mock()
  VolatilityProviderFactory factory = new VolatilityProviderFactory(sabrFactory, skewFactory)

  def "should correctly resolve ATM volatility provider"() {
    setup:
    def surfaceConfig = new SurfaceConfig(
      skewType: SkewType.ATM,
      )
    def surfaceCalculation = new SurfaceForCalculation(
      config: surfaceConfig,
      nodes: []
      )
    when:
    def result = factory.resolveProvider(surfaceCalculation)
    then:
    result.isRight()
    result.getOrNull() instanceof AtmVolatilitiesProvider
  }

  def "should correctly resolve SABR volatility provider"() {
    setup:
    def surfaceConfig = new SurfaceConfig(
      skewType: SkewType.MONEYNESS,
      sabr: true,
      sabrBeta: 1,
      sabrShift: 1
      )
    def surfaceCalculation = new SurfaceForCalculation(
      config: surfaceConfig,
      nodes: [],
      skews: [(BigDecimal.ONE): []]
      )
    1 * sabrFactory.buildSabrData(_, _) >> Either.right(TenorRawOptionData.of([:]))
    when:
    def result = factory.resolveProvider(surfaceCalculation)
    then:
    result.isRight()
    result.getOrNull() instanceof SabrVolatilitiesProvider
  }

  def "should correctly resolve Skew volatility provider"() {
    setup:
    def surfaceConfig = new SurfaceConfig(
      skewType: SkewType.STRIKE,
      sabr: false,
      )
    def surfaceCalculation = new SurfaceForCalculation(
      config: surfaceConfig,
      nodes: [],
      skews: [(BigDecimal.ONE): []]
      )
    1 * skewFactory.buildSkewInterpolations(_) >> Either.right([])
    when:
    def result = factory.resolveProvider(surfaceCalculation)
    then:
    result.isRight()
    result.getOrNull() instanceof SkewVolatilitiesProvider
  }
}
