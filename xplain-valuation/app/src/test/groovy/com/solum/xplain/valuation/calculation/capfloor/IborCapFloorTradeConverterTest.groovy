package com.solum.xplain.valuation.calculation.capfloor

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.valuation.calculation.TradeSamples.capFloorTrade

import com.opengamma.strata.product.swap.IborRateCalculation
import com.opengamma.strata.product.swap.NotionalSchedule
import com.opengamma.strata.product.swap.RateCalculationSwapLeg
import com.opengamma.strata.product.swap.SwapLegType
import com.solum.xplain.valuation.calculation.MarketDataSample
import spock.lang.Specification

class IborCapFloorTradeConverterTest extends Specification {

  def "should correctly convert CAP_FLOOR to swap"() {
    setup:
    def capfloor = capFloorTrade(MarketDataSample.VAL_DT.plusMonths(1))
    def swapTrade = IborCapFloorTradeConverter.of(capfloor).toSwapTrade()

    expect:
    def swap = swapTrade.product
    def payLeg = (RateCalculationSwapLeg) swap.payLeg.orElseThrow()
    payLeg.type == SwapLegType.FIXED
    payLeg.notionalSchedule == NotionalSchedule.of(EUR, 20d)
    payLeg.accrualSchedule == capfloor.product.capFloorLeg.paymentSchedule

    def receiveLeg = (RateCalculationSwapLeg) swap.receiveLeg.orElseThrow()
    receiveLeg.type == SwapLegType.IBOR
    receiveLeg.accrualSchedule == capfloor.product.capFloorLeg.paymentSchedule
    def iborCalc = (IborRateCalculation) receiveLeg.calculation
    iborCalc == capfloor.product.capFloorLeg.calculation
  }
}
