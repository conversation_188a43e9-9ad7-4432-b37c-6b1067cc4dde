package com.solum.xplain.valuation.calculation.credit

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.valuation.calculation.MarketDataSample.VAL_DT

import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.array.DoubleMatrix
import com.opengamma.strata.collect.tuple.Pair
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.ConstantNodalCurve
import com.opengamma.strata.market.curve.CurveInfoType
import com.opengamma.strata.market.curve.CurveMetadata
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveParameterSize
import com.opengamma.strata.market.curve.Curves
import com.opengamma.strata.market.curve.DefaultCurveMetadata
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.pricer.credit.ConstantRecoveryRates
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities
import com.solum.xplain.extensions.calibrator.XplainCreditCurveCalibrator
import com.solum.xplain.extensions.utils.StandardIdUtils
import com.solum.xplain.valuation.messages.calibration.credits.FxShiftedValuationCreditCurveRates
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurveType
import java.time.LocalDate

class CreditRatesSample {
  static StandardId CDS_STANDARD_ID = StandardIdUtils.curveIdStandardId("SELF_EUR_SNRFOR_CR14")
  static StandardId CREDIT_INDEX_STANDARD_ID = StandardIdUtils.curveIdStandardId("CDX_NA_HY_S1_V1")

  static Map<String, CalibrationCurve> creditCalibrationCurves() {
    return [
      "EUR discount": new CalibrationCurve(name: "EUR discount", type: CalibrationCurveType.IR_INDEX),
      "EUR"         : new CalibrationCurve(name: "EUR", type: CalibrationCurveType.CREDIT),
    ]
  }

  static ValuationCreditRates creditRatesProvider(StandardId standardId, boolean withDscCurveJacobian = true, LocalDate vd = VAL_DT, DoubleArray creditCurveYValues = DoubleArray.of(1.0, 0.2)) {
    def creditsProvider = ImmutableCreditRatesProvider
      .builder()
      .valuationDate(vd)
      .discountCurves([(EUR): discountFactors(vd)])
      .recoveryRateCurves([(standardId): ConstantRecoveryRates.of(standardId, vd, 0.01)])
      .creditCurves([(Pair.of(standardId, EUR)): LegalEntitySurvivalProbabilities.of(standardId, eurDiscountFactors(withDscCurveJacobian, vd, creditCurveYValues))])
      .build()
    return new ValuationCreditRates(creditsProvider, [])
  }

  static ValuationCreditRates creditIndexRatesProvider(StandardId standardId, LocalDate vd = VAL_DT, DoubleArray creditCurveYValues = DoubleArray.of(1.0, 0.2)) {
    def creditsProvider = ImmutableCreditRatesProvider
      .builder()
      .valuationDate(vd)
      .discountCurves([(EUR): discountFactors(vd)])
      .recoveryRateCurves([(standardId): ConstantRecoveryRates.of(standardId, vd, 0.01)])
      .creditCurves([(Pair.of(standardId, EUR)): LegalEntitySurvivalProbabilities.of(standardId, eurDiscountCdsIndexFactors(vd, creditCurveYValues))])
      .build()
    return new ValuationCreditRates(creditsProvider, [])
  }

  static ValuationCreditRates creditIndexShiftedRatesProvider(StandardId standardId) {
    def rates = creditIndexRatesProvider(standardId)
    def shiftedRates = creditIndexRatesProvider(standardId)

    return new ValuationCreditRates(rates.creditRatesProvider, [new FxShiftedValuationCreditCurveRates(CurrencyPair.of(EUR, USD), shiftedRates.creditRatesProvider)])
  }

  static IsdaCreditDiscountFactors eurDiscountFactors(boolean withDscCurveJacobian = false, LocalDate vd = VAL_DT, DoubleArray yValues = DoubleArray.of(1.0, 0.2)) {
    if (!withDscCurveJacobian) {
      return factors(Curves.zeroRates("EUR", IborIndices.EUR_EURIBOR_1M.dayCount), vd, yValues)
    }
    return factorsWithDiscountCurveJacobian(Curves.zeroRates("EUR", IborIndices.EUR_EURIBOR_1M.dayCount), vd, yValues)
  }

  static IsdaCreditDiscountFactors eurDiscountCdsIndexFactors(LocalDate vd = VAL_DT, DoubleArray creditCurveYValues = DoubleArray.of(1.0, 0.2)) {
    factorsWithDiscountCurveJacobian(Curves.zeroRates("EUR", IborIndices.EUR_EURIBOR_1M.dayCount)
      .withInfo(CurveInfoType.CDS_INDEX_FACTOR, 0.5d), vd, creditCurveYValues)
  }

  static IsdaCreditDiscountFactors factors(CurveMetadata metadata, LocalDate vd = VAL_DT, DoubleArray yValues = DoubleArray.of(1.0, 0.2)) {
    def jacobian = JacobianCalibrationMatrix.of(
      [CurveParameterSize.of(CurveName.of("EUR"), 2)],
      DoubleMatrix.copyOf([[1.0d, 1.0d], [2.0d, 2.0d]] as double[][])
      )
    def curve = InterpolatedNodalCurve.of(
      metadata
      .withInfo(CurveInfoType.JACOBIAN, jacobian),
      DoubleArray.of(2.0, 3.0),
      yValues,
      CurveInterpolators.PRODUCT_LINEAR,
      CurveExtrapolators.FLAT,
      CurveExtrapolators.PRODUCT_LINEAR
      )
    return IsdaCreditDiscountFactors.of(EUR, vd, curve)
  }

  static IsdaCreditDiscountFactors factorsWithDiscountCurveJacobian(CurveMetadata metadata, LocalDate vd = VAL_DT, DoubleArray yValues = DoubleArray.of(1.0, 0.2)) {
    def jacobian = JacobianCalibrationMatrix.of(
      [CurveParameterSize.of(CurveName.of("EUR"), 2)],
      DoubleMatrix.copyOf([[1.0d, 1.0d], [2.0d, 2.0d]] as double[][])
      )
    def jacobianDiscountCurve = JacobianCalibrationMatrix.of(
      [CurveParameterSize.of(CurveName.of("EUR discount"), 1)],
      DoubleMatrix.copyOf([[1.5d], [1.1d]] as double[][])
      )
    def curve = InterpolatedNodalCurve.of(
      metadata
      .withInfo(CurveInfoType.JACOBIAN, jacobian)
      .withInfo(XplainCreditCurveCalibrator.D_ZHRATES_D_SWAP_RATES_JACOBIAN, jacobianDiscountCurve),
      DoubleArray.of(2.0, 3.0),
      yValues,
      CurveInterpolators.PRODUCT_LINEAR,
      CurveExtrapolators.FLAT,
      CurveExtrapolators.PRODUCT_LINEAR
      )
    return IsdaCreditDiscountFactors.of(EUR, vd, curve)
  }

  static IsdaCreditDiscountFactors discountFactors(LocalDate vd = VAL_DT) {
    def jacobian = JacobianCalibrationMatrix.of(
      [CurveParameterSize.of(CurveName.of("EUR discount"), 1)],
      DoubleMatrix.of(1, 1, 1d))

    def constantCurve = ConstantNodalCurve.of(
      DefaultCurveMetadata.builder().curveName("EUR discount")
      .xValueType(ValueType.YEAR_FRACTION)
      .yValueType(ValueType.ZERO_RATE)
      .dayCount(IborIndices.EUR_EURIBOR_1M.dayCount)
      .jacobian(jacobian)
      .build(), 0, 0.01)
    return IsdaCreditDiscountFactors.of(EUR, vd, constantCurve)
  }
}
