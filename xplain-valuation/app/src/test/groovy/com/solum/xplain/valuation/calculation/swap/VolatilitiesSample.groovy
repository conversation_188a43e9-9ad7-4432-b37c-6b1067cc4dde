package com.solum.xplain.valuation.calculation.swap

import static com.opengamma.strata.market.curve.interpolator.CurveInterpolators.LINEAR
import static com.opengamma.strata.market.surface.Surfaces.normalVolatilityByExpiryStrike

import com.opengamma.strata.basics.date.DayCount
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.surface.ConstantSurface
import com.opengamma.strata.market.surface.InterpolatedNodalSurface
import com.opengamma.strata.market.surface.SurfaceMetadata
import com.opengamma.strata.market.surface.Surfaces
import com.opengamma.strata.market.surface.interpolator.GridSurfaceInterpolator
import com.opengamma.strata.pricer.model.SabrInterestRateParameters
import com.opengamma.strata.pricer.model.SabrVolatilityFormula
import com.opengamma.strata.pricer.swaption.NormalSwaptionExpiryStrikeVolatilities
import com.opengamma.strata.pricer.swaption.SabrParametersSwaptionVolatilities
import com.opengamma.strata.pricer.swaption.SwaptionSurfaceExpiryStrikeParameterMetadata
import com.opengamma.strata.pricer.swaption.SwaptionSurfaceExpiryTenorParameterMetadata
import com.opengamma.strata.pricer.swaption.SwaptionVolatilities
import com.opengamma.strata.pricer.swaption.SwaptionVolatilitiesName
import com.opengamma.strata.product.swap.type.FixedIborSwapConventions
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calibration.vols.SabrVolatilitiesProvider
import java.time.ZoneId

class VolatilitiesSample {

  static SwaptionVolatilities skewOisVols() {
    def parameterMetadata = [
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(1.0138888888888888, 0.01, "1Yx0.01"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(1.0138888888888888, 0.1, "1Yx0.1"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(2.033333333333333, 0.01, "2Yx0.01"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(2.033333333333333, 0.1, "2Yx0.1")
    ]
    SurfaceMetadata metadata = normalVolatilityByExpiryStrike("EUR OIS VOLS", DayCount.of("Act/360"))
      .withParameterMetadata(parameterMetadata)
    def surface = InterpolatedNodalSurface.of(metadata,
      DoubleArray.copyOf(1.0138888888888888, 1.0138888888888888, 2.033333333333333, 2.033333333333333),
      DoubleArray.copyOf(0.01, 0.1, 0.01, 0.1),
      DoubleArray.copyOf(0.009, 0.009, 0.009, 0.009),
      GridSurfaceInterpolator.of(LINEAR, LINEAR))
    NormalSwaptionExpiryStrikeVolatilities.of(
      FixedOvernightSwapConventions.EUR_FIXED_1Y_EONIA_OIS,
      MarketDataSample.VAL_DT.atStartOfDay(ZoneId.systemDefault()),
      surface
      )
  }

  static SwaptionVolatilities skewVols() {
    def parameterMetadata = [
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(1.0138888888888888, 0.01, "1Yx0.01"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(1.0138888888888888, 0.1, "1Yx0.1"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(2.033333333333333, 0.01, "2Yx0.01"),
      SwaptionSurfaceExpiryStrikeParameterMetadata.of(2.033333333333333, 0.1, "2Yx0.1")
    ]
    SurfaceMetadata metadata = normalVolatilityByExpiryStrike("EUR 3M VOLS", DayCount.of("Act/360"))
      .withParameterMetadata(parameterMetadata)
    def surface = InterpolatedNodalSurface.of(metadata,
      DoubleArray.copyOf(1.0138888888888888, 1.0138888888888888, 2.033333333333333, 2.033333333333333),
      DoubleArray.copyOf(0.01, 0.1, 0.01, 0.1),
      DoubleArray.copyOf(0.009, 0.009, 0.009, 0.009),
      GridSurfaceInterpolator.of(LINEAR, LINEAR))
    NormalSwaptionExpiryStrikeVolatilities.of(
      FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M,
      MarketDataSample.VAL_DT.atStartOfDay(ZoneId.systemDefault()),
      surface
      )
  }


  static SwaptionVolatilities sabrVols() {
    def surfaceName = "EUR-3M-VOLS"
    def parameterMetadata = [
      SwaptionSurfaceExpiryTenorParameterMetadata.of(1, 0.01, "P1Yx1Y"),
      SwaptionSurfaceExpiryTenorParameterMetadata.of(1, 0.1, "P1Yx2Y"),
      SwaptionSurfaceExpiryTenorParameterMetadata.of(2.0054794520547947, 0.01, "P2Yx1Y"),
      SwaptionSurfaceExpiryTenorParameterMetadata.of(2.0054794520547947, 0.1, "P2Yx2Y")
    ]
    def timeToExpiryArray = DoubleArray.of(1.0, 1.0, 2.0054794520547947, 2.0054794520547947)
    def timeTenorArray = DoubleArray.of(1.0, 2.0, 1.0, 2.0)
    def alphaArray = DoubleArray.of(0.03728612033207624, 0.12249733081711753, 0.05416358990763201, 0.13144017686989137)
    def rhoArray = DoubleArray.of(0.785048904616588, 0.999, 0.6703526326282458, 0.999)
    def nuArray = DoubleArray.of(2.499999999999967, 2.5, 2.5, 2.5)
    def dayCount = DayCounts.ACT_365F
    def interpolator = GridSurfaceInterpolator.of(LINEAR, LINEAR)
    def dataSensitivityAlpha = [
      DoubleArray.of(0.8354705417931606, 1.0532510909852932, 0.4546208255791312),
      DoubleArray.of(1.5330612977591145, 1.6826810469298061, 1.6583429155114433),
      DoubleArray.of(0.5690098354129458, 0.7002109953090607, 0.49675381347716113),
      DoubleArray.of(1.2844103167296715E-4, 1.5706102932546088E-4, 1.548428670791611E-4)
    ]

    def dataSensitivityRho = [
      DoubleArray.of(-18.936870102501654, -13.461439023382686, -28.743699578988352),
      DoubleArray.of(0.0, 0.0, 0.0),
      DoubleArray.of(-8.453571615396209, -7.847926744974736, -10.656659409092269),
      DoubleArray.of(0.0, 0.0, 0.0)
    ]

    def dataSensitivityNu = [
      DoubleArray.of(2.3059310471281917E-26, 1.801444891988651E-26, 3.212749039190756E-26),
      DoubleArray.of(0.0, 0.0, 0.0),
      DoubleArray.of(0.0, 0.0, 0.0),
      DoubleArray.of(0.0, 0.0, 0.0)
    ]
    def betaSurface = ConstantSurface.of(SabrVolatilitiesProvider.BETA_META, 0.5)
    def shiftSurface = ConstantSurface.of("SABR-Shift", 0.03)
    SurfaceMetadata metadataAlpha = Surfaces.sabrParameterByExpiryTenor(
      surfaceName + "-Alpha", dayCount, ValueType.SABR_ALPHA)
      .withParameterMetadata(parameterMetadata)
    SurfaceMetadata metadataRho = Surfaces.sabrParameterByExpiryTenor(
      surfaceName + "-Rho", dayCount, ValueType.SABR_RHO)
      .withParameterMetadata(parameterMetadata)
    SurfaceMetadata metadataNu = Surfaces.sabrParameterByExpiryTenor(
      surfaceName + "-Nu", dayCount, ValueType.SABR_NU)
      .withParameterMetadata(parameterMetadata)
    InterpolatedNodalSurface alphaSurface = InterpolatedNodalSurface
      .of(metadataAlpha, timeToExpiryArray, timeTenorArray, alphaArray, interpolator)
    InterpolatedNodalSurface rhoSurface = InterpolatedNodalSurface
      .of(metadataRho, timeToExpiryArray, timeTenorArray, rhoArray, interpolator)
    InterpolatedNodalSurface nuSurface = InterpolatedNodalSurface
      .of(metadataNu, timeToExpiryArray, timeTenorArray, nuArray, interpolator)
    SabrInterestRateParameters params = SabrInterestRateParameters.of(
      alphaSurface, betaSurface, rhoSurface, nuSurface, shiftSurface, SabrVolatilityFormula.hagan())

    return SabrParametersSwaptionVolatilities.builder()
      .name(SwaptionVolatilitiesName.of(surfaceName))
      .convention(FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M)
      .valuationDateTime(MarketDataSample.VAL_DT.atStartOfDay(ZoneId.systemDefault()))
      .parameters(params)
      .dataSensitivityAlpha(dataSensitivityAlpha)
      .dataSensitivityRho(dataSensitivityRho)
      .dataSensitivityNu(dataSensitivityNu)
      .build()
  }
}
