package com.solum.xplain.core.common.versions;

import com.solum.xplain.core.common.value.HasVersionForm;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * Interface for mapping from a form to a versioned entity.
 *
 * @param <FORM> A POJO or record class that implements HasVersionForm (i.e. implements
 *     getVersionForm() or versionForm() method)
 * @param <ENTITY> Target class that extends VersionedEntity
 */
public interface VersionedFormMapper<FORM extends HasVersionForm, ENTITY extends VersionedEntity> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  ENTITY fromForm(FORM form, @MappingTarget ENTITY previousEntity);
}
