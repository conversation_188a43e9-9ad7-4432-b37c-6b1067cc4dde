package com.solum.xplain.core.common.versions.embedded.update;

import static com.solum.xplain.core.common.versions.State.ARCHIVED;
import static com.solum.xplain.core.common.versions.State.DELETED;
import static java.util.List.of;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import java.util.List;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor(access = AccessLevel.NONE)
public class EntityUpdateResult<V, T extends EmbeddedVersionEntity<V>> {

  private final T entity;
  private final boolean isNew;
  private final boolean isRemoved;
  private final List<EmbeddedVersion<V>> newVersions;
  private final List<EmbeddedVersion<V>> allVersions;

  EntityUpdateResult(T entity, List<EmbeddedVersion<V>> newVersions) {
    this.entity = entity;
    this.newVersions = newVersions;
    this.allVersions = CollectionUtils.join(entity.getVersions(), newVersions);
    this.isNew = newVersions.size() == this.allVersions.size();
    this.isRemoved =
        newVersions.stream().anyMatch(v -> of(DELETED, ARCHIVED).contains(v.getState()));
  }
}
