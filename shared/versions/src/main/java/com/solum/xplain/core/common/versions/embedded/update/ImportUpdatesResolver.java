package com.solum.xplain.core.common.versions.embedded.update;

import static com.solum.xplain.core.common.csv.NewVersionFormV2Utils.fromImportOptions;
import static java.time.LocalDate.ofEpochDay;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Builder
@EqualsAndHashCode
@Getter
public class ImportUpdatesResolver<V, E extends EmbeddedVersionEntity<V>> {

  private final ImportOptions options;
  private final Map<E, V> entitiesToAppend;
  private final Map<EntityForUpdate<V, E>, V> entitiesToReplace;
  private final List<EntityForUpdate<V, E>> entitiesToArchive;

  public List<EntityUpdateResult<V, E>> appendEntities(EntityUpdateOperations<V, E> update) {
    if (isEmpty(entitiesToAppend)) {
      return List.of();
    }
    var vf = fromImportOptions(options, ofEpochDay(0), options::getFutureVersionsAction);
    return entitiesToAppend.entrySet().stream()
        .map(e -> update.createEntity(e.getKey(), e.getValue(), vf))
        .toList();
  }

  public List<EntityUpdateResult<V, E>> replaceEntities(EntityUpdateOperations<V, E> update) {
    if (isEmpty(entitiesToReplace)) {
      return List.of();
    }
    var vf = fromImportOptions(options, options::getFutureVersionsAction);
    return entitiesToReplace.entrySet().stream()
        .map(e -> update.updateEntity(e.getKey(), e.getValue(), vf))
        .toList();
  }

  public List<EntityUpdateResult<V, E>> archiveEntities(EntityUpdateOperations<V, E> update) {
    if (isEmpty(entitiesToArchive)) {
      return List.of();
    }
    var vf = fromImportOptions(options, options::getMissingEntriesFutureVersionsAction);
    return entitiesToArchive.stream().map(e -> update.archiveEntity(e, vf)).toList();
  }

  public List<V> allAffectedValues() {
    var builder = ImmutableList.<V>builder();
    if (!isEmpty(entitiesToAppend)) {
      builder.addAll(
          entitiesToAppend.keySet().stream()
              .flatMap(e -> e.getVersions().stream().map(EmbeddedVersion::getValue))
              .toList());
      builder.addAll(entitiesToAppend.values());
    }
    if (!isEmpty(entitiesToReplace)) {
      builder.addAll(
          entitiesToReplace.keySet().stream()
              .flatMap(e -> e.getEntity().getVersions().stream().map(EmbeddedVersion::getValue))
              .toList());
      builder.addAll(entitiesToReplace.values());
    }
    if (!isEmpty(entitiesToArchive)) {
      builder.addAll(
          entitiesToArchive.stream()
              .flatMap(e -> e.getEntity().getVersions().stream().map(EmbeddedVersion::getValue))
              .toList());
    }
    return builder.build();
  }
}
