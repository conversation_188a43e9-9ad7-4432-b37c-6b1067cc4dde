package com.solum.xplain.core.common.versions;

import static com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository.hasEntityId;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import org.springframework.data.mongodb.core.query.Criteria;

public interface VersionedEntityQueries<T /*extends VersionedEntity*/> {

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default DateList futureVersionsByEntityId(String entityId, LocalDate stateDate) {
    var searchCriteria = hasEntityId(entityId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default List<T> findAllActiveItems(LocalDate stateDate) {
    return findAllActiveItems(BitemporalDate.newOf(stateDate));
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default Either<ErrorItem, T> findOneActiveItem(String entityId, LocalDate stateDate) {
    return findOneActiveItem(entityId, BitemporalDate.newOf(stateDate));
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default Either<ErrorItem, EntityId> archive(
      String entityId, LocalDate versionDate, ArchiveEntityForm f) {
    return archive(entityId, BitemporalDate.newOf(versionDate), f);
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default Either<ErrorItem, EntityId> delete(String entityId, LocalDate versionDate) {
    return delete(entityId, BitemporalDate.newOf(versionDate));
  }

  /**
   * @deprecated SXSD-9639 : use equivalent function with {@link BitemporalDate} instead.
   */
  @Deprecated
  default DateList futureVersionsByCriteria(Criteria uniquenessCriteria, LocalDate stateDate) {
    return futureVersionsByCriteria(uniquenessCriteria, BitemporalDate.newOf(stateDate));
  }

  default DateList futureVersionsByEntityId(String entityId, BitemporalDate stateDate) {
    var searchCriteria = hasEntityId(entityId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  /**
   * Returns the active item for the given entityIds and stateDate. If the item is not found, an
   * error will be returned if lenient is false.
   *
   * @param entityIds the ids to find
   * @param stateDate the date to check for active items
   * @param lenient if true, no error will be returned if any items are not found
   */
  Either<ErrorItem, List<T>> findManyActiveItems(
      Collection<String> entityIds, BitemporalDate stateDate, boolean lenient);

  /**
   * Returns the active item for the given entityIds and stateDate. If the item is not found, an
   * error will be returned
   *
   * @param entityIds the ids to find
   * @param stateDate the date to check for active items
   */
  default Either<ErrorItem, List<T>> findManyActiveItems(
      List<String> entityIds, BitemporalDate stateDate) {
    return findManyActiveItems(entityIds, stateDate, false);
  }

  List<T> findAllActiveItems(BitemporalDate stateDate);

  Either<ErrorItem, T> findOneActiveItem(String entityId, BitemporalDate stateDate);

  Either<ErrorItem, EntityId> archive(
      String entityId, BitemporalDate versionDate, ArchiveEntityForm f);

  Either<ErrorItem, EntityId> delete(String entityId, BitemporalDate versionDate);

  DateList futureVersionsByCriteria(Criteria uniquenessCriteria, BitemporalDate stateDate);
}
