package com.solum.xplain.core.common.versions.daterange;

import com.solum.xplain.core.common.versions.MajorVersioned;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;

@Data
@FieldNameConstants
public abstract class DateRangeVersionedEntity implements MajorVersioned {
  @Id private String id;
  private String entityId;

  private String comment;
  private LocalDate version;
  private LocalDate validFrom;
  private LocalDate validTo;
  private LocalDateTime recordFrom;
  private LocalDateTime recordTo;
  private State state;

  private List<DateRangeVersionValidity> validities;

  private AuditUser modifiedBy;
  private LocalDateTime modifiedAt;
}
