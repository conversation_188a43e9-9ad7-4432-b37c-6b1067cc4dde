package com.solum.xplain.core.common.versions.embedded.update


import com.solum.xplain.core.common.versions.embedded.TestEmbeddedVersionEntity
import spock.lang.Specification

class EntityUpdateResultTest extends Specification {

  def "should create EntityUpdateResult"() {
    setup:
    def existingVersion = TestEmbeddedVersionEntity.ROOT1
    def entity = new TestEmbeddedVersionEntity([existingVersion])
    def newVersion = TestEmbeddedVersionEntity.LATEST2

    when:
    def result = new EntityUpdateResult(entity, [newVersion])

    then:
    result.getNewVersions() == [newVersion]
    result.getAllVersions().containsAll([newVersion, existingVersion])
    !result.isNew()
    result.isRemoved()
  }
}
