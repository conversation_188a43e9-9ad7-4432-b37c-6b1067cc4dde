package com.solum.xplain.core.common.versions.embedded

import static com.solum.xplain.core.common.versions.State.ACTIVE
import static com.solum.xplain.core.common.versions.State.ARCHIVED
import static com.solum.xplain.core.common.versions.State.DELETED
import static com.solum.xplain.core.common.versions.embedded.TestEmbeddedVersionEntity.version
import static java.time.LocalDate.ofEpochDay

import java.time.LocalDateTime
import java.time.LocalTime
import spock.lang.Specification
import spock.lang.Unroll

class EmbeddedVersionEntityTest extends Specification {

  @Unroll
  "should match version by #stateDate #result"() {
    setup:
    def root1 = version(ARCHIVED, ofEpochDay(0), rootDateAtHour(0), "1")
    def root2 = version(ACTIVE, ofEpochDay(0), rootDateAtHour(1), "2")
    def deleted1 = version(ARCHIVED, ofEpochDay(9), rootDateAtHour(2), "3")
    def deleted2 = version(DELETED, ofEpochDay(9), rootDateAtHour(3), "4")
    def activeAt15LatestRecordDate = version(ACTIVE, ofEpochDay(15), rootDateAtHour(22), "5")
    def activeAt20 = version(ACTIVE, ofEpochDay(20), rootDateAtHour(4), "6")
    def entity = new TestEmbeddedVersionEntity([root1, root2, deleted1, deleted2, activeAt15LatestRecordDate, activeAt20])

    expect:
    entity.versionAt(stateDate).map({ it.getValue() }).orElse(null) == result

    where:
    stateDate      | result
    ofEpochDay(0)  | "2"
    ofEpochDay(1)  | "2"
    ofEpochDay(10) | "2"
    ofEpochDay(15) | "5"
    ofEpochDay(18) | "5"
    ofEpochDay(20) | "6"
    ofEpochDay(25) | "6"
  }

  @Unroll
  "should match version exact by #stateDate #result"() {
    setup:
    def entity = TestEmbeddedVersionEntity.testEntity()

    expect:
    entity.exactVersionAt(stateDate).orElse(null) == result

    where:
    stateDate      | result
    ofEpochDay(0)  | TestEmbeddedVersionEntity.ROOT2
    ofEpochDay(1)  | null
    ofEpochDay(9)  | null
    ofEpochDay(10) | null
  }

  @Unroll
  "should check future version exist by #stateDate #result"() {
    setup:
    def root = version(ARCHIVED, ofEpochDay(0), rootDateAtHour(0), "1")
    def active = version(ACTIVE, ofEpochDay(5), rootDateAtHour(4), "2")
    def deleted = version(DELETED, ofEpochDay(9), rootDateAtHour(3), "3")
    def entity = new TestEmbeddedVersionEntity([root, active, deleted])

    expect:
    entity.hasFutureVersion(stateDate) == result

    where:
    stateDate     | result
    ofEpochDay(0) | true
    ofEpochDay(4) | true
    ofEpochDay(5) | false
    ofEpochDay(6) | false
  }

  private LocalDateTime rootDateAtHour(int hour) {
    return LocalDateTime.of(ofEpochDay(0), LocalTime.of(hour, 0))
  }
}
