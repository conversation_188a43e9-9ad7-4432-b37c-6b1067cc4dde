package com.solum.xplain.core.common.versions.embedded


import groovy.transform.EqualsAndHashCode
import org.springframework.data.mongodb.core.mapping.Document

@Document
@EqualsAndHashCode
class TestEmbeddedVersionForm implements ResolvableEmbeddedVersionValue<String, String> {

  private String entityId
  private String value

  @Override
  String toVersionValue() {
    return value
  }

  @Override
  String getEntityId() {
    return entityId
  }
}
