package com.solum.xplain.core.common.versions.embedded.update

import static com.solum.xplain.core.common.value.FutureVersionsAction.DELETE
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE
import static com.solum.xplain.core.common.versions.State.ACTIVE
import static com.solum.xplain.core.common.versions.State.ARCHIVED
import static com.solum.xplain.core.common.versions.State.DELETED
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.embedded.TestEmbeddedVersionEntity
import com.solum.xplain.core.users.AuditUser
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.function.Supplier
import org.springframework.data.domain.AuditorAware
import spock.lang.Specification
import spock.lang.Unroll

class EntityUpdateOperationsTest extends Specification {

  private static final LocalDate newDate = ofEpochDay(2)
  private static final LocalDate futureDate = ofEpochDay(10)
  private static final LocalDateTime time = ROOT_DATE.atStartOfDay()
  private static final AuditUser user = new AuditUser()

  AuditorAware<AuditUser> auditUserAuditorAware = Mock()
  Supplier<LocalDateTime> timeSupplier = Mock()
  EntityUpdateOperations operations = new EntityUpdateOperations<String, TestEmbeddedVersionEntity>(auditUserAuditorAware, timeSupplier)

  def setup() {
    auditUserAuditorAware.getCurrentAuditor() >> Optional.of(user)
    1 * timeSupplier.get() >> time
  }

  @Unroll
  "should create entity #options #result"() {
    setup:
    def entity = new TestEmbeddedVersionEntity([])
    def value = "value"

    expect:
    operations.createEntity(entity, value, options).getEntity() == result

    where:
    options                                              | result
    new NewVersionFormV2("c", ROOT_DATE, null, null)     | new TestEmbeddedVersionEntity([TestEmbeddedVersionEntity.version(ROOT_DATE, time, ACTIVE, "c", user, "value")])
    new NewVersionFormV2("c", ofEpochDay(2), null, null) | new TestEmbeddedVersionEntity([
      TestEmbeddedVersionEntity.version(ROOT_DATE, time, ARCHIVED, "c", user, "value"),
      TestEmbeddedVersionEntity.version(ofEpochDay(2), time, ACTIVE, "c", user, "value")
    ])
  }

  @Unroll
  def "should update entity #currState #newValue #options #newVersions"() {
    setup:
    def version = TestEmbeddedVersionEntity.version(ROOT_DATE, time, currState, null, user, "oldValue")
    def versionFuture1 = TestEmbeddedVersionEntity.version(futureDate, time, ACTIVE, null, user, "futureValue")
    def versionFuture2 = TestEmbeddedVersionEntity.version(futureDate, time.plusDays(1L), ACTIVE, null, user, "futureValueLatest")
    def entity = new TestEmbeddedVersionEntity([version, versionFuture1, versionFuture2])

    def entityForUpdate = new EntityForUpdate<>(entity, version)

    expect:
    def result = operations.updateEntity(entityForUpdate, newValue, options)
    result.getNewVersions() == newVersions

    where:
    currState | newValue   | options                                           | newVersions
    ACTIVE    | "newValue" | new NewVersionFormV2("newC", null, null, null)    | [(TestEmbeddedVersionEntity.version(ROOT_DATE, time, ACTIVE, "newC", user, "newValue"))]
    ACTIVE    | "newValue" | new NewVersionFormV2("newC", newDate, null, null) | [(TestEmbeddedVersionEntity.version(newDate, time, ACTIVE, "newC", user, "newValue"))]
    ACTIVE    | "oldValue" | new NewVersionFormV2("newC", null, null, null)    | []
    ARCHIVED  | "oldValue" | new NewVersionFormV2("newC", null, null, null)    | [(TestEmbeddedVersionEntity.version(ROOT_DATE, time, ACTIVE, "newC", user, "oldValue"))]
    ACTIVE    | "newValue" | new NewVersionFormV2("newC", null, null, DELETE)  | [
      (TestEmbeddedVersionEntity.version(ROOT_DATE, time, ACTIVE, "newC", user, "newValue")),
      (TestEmbeddedVersionEntity.version(futureDate, time, DELETED, null, user, "futureValueLatest"))
    ]
  }

  @Unroll
  def "should archive entity #currState #options #newVersions"() {
    setup:
    def version = TestEmbeddedVersionEntity.version(ROOT_DATE, time, currState, null, user, "oldValue")
    def versionFuture1 = TestEmbeddedVersionEntity.version(futureDate, time, ACTIVE, null, user, "futureValue")
    def versionFuture2 = TestEmbeddedVersionEntity.version(futureDate, time.plusDays(1L), ACTIVE, null, user, "futureValueLatest")
    def entity = new TestEmbeddedVersionEntity([version, versionFuture1, versionFuture2])

    def entityForUpdate = new EntityForUpdate<>(entity, version)

    expect:
    def result = operations.archiveEntity(entityForUpdate, options)
    result.getNewVersions() == newVersions

    where:
    currState | options                                           | newVersions
    ACTIVE    | new NewVersionFormV2("newC", null, null, null)    | [(TestEmbeddedVersionEntity.version(ROOT_DATE, time, ARCHIVED, "newC", user, "oldValue"))]
    ACTIVE    | new NewVersionFormV2("newC", newDate, null, null) | [(TestEmbeddedVersionEntity.version(newDate, time, ARCHIVED, "newC", user, "oldValue"))]
    ARCHIVED  | new NewVersionFormV2("newC", null, null, null)    | []
    DELETED   | new NewVersionFormV2("newC", null, null, null)    | []
    ACTIVE    | new NewVersionFormV2("newC", null, null, DELETE)  | [
      (TestEmbeddedVersionEntity.version(ROOT_DATE, time, ARCHIVED, "newC", user, "oldValue")),
      (TestEmbeddedVersionEntity.version(futureDate, time, DELETED, null, user, "futureValueLatest"))
    ]
  }

  @Unroll
  def "should delete entity #currState #currValidFrom #newVersions"() {
    setup:
    def version = TestEmbeddedVersionEntity.version(currValidFrom, time, currState, null, user, "oldValue")
    def entity = new TestEmbeddedVersionEntity([version])

    def entityForUpdate = new EntityForUpdate<>(entity, version)

    expect:
    def result = operations.deleteEntity(entityForUpdate, currValidFrom)
    result.getNewVersions() == newVersions

    where:
    currState | currValidFrom         | newVersions
    ACTIVE    | ROOT_DATE.plusDays(1) | [(TestEmbeddedVersionEntity.version(ROOT_DATE.plusDays(1), time, DELETED, null, user, "oldValue"))]
    ARCHIVED  | ROOT_DATE.plusDays(1) | [(TestEmbeddedVersionEntity.version(ROOT_DATE.plusDays(1), time, DELETED, null, user, "oldValue"))]
    DELETED   | ROOT_DATE.plusDays(1) | []
    ACTIVE    | ROOT_DATE             | []
  }
}
