package com.solum.xplain.extensions.calendar

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.HolidayCalendarIds
import java.time.LocalDate
import java.time.Month
import spock.lang.Specification

class ValuationDateReferenceDataTest extends Specification {

  def "should wrap reference data to valuation date reference data"() {
    given:
    def christmas = LocalDate.of(2023, Month.DECEMBER, 25)
    def refData = ValuationDateReferenceData.wrap(ReferenceData.standard(), [christmas])

    when:
    def gblo = refData.getValue(HolidayCalendarIds.GBLO)
    def nohols = refData.getValue(HolidayCalendarIds.NO_HOLIDAYS)

    then:
    ValuationDateHolidayCalendar.isInstance(gblo)
    !gblo.isHoliday(christmas)

    ValuationDateHolidayCalendar.isInstance(nohols)
    !nohols.isHoliday(christmas)
  }
}
