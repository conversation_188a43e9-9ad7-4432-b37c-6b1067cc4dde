package com.solum.xplain.extensions.utils

import spock.lang.Specification

class StandardIdUtilsTest extends Specification {

  def "should return curve standard id"() {
    setup:
    def id = StandardIdUtils.curveIdStandardId("curveName")

    expect:
    id.scheme == "CurveId"
    id.value == "curveName"
  }

  def "should return market data standard id"() {
    setup:
    def id = StandardIdUtils.marketDataStandardId("KEY")

    expect:
    id.scheme == "XPL"
    id.value == "KEY"
  }

  def "should return trade standard id"() {
    setup:
    def id = StandardIdUtils.tradeStandardId("externalId")

    expect:
    id.scheme == "TradeId"
    id.value == "externalId"
  }

  def "should return counterparty standard id"() {
    setup:
    def id = StandardIdUtils.counterpartyStandardId("counterparty")

    expect:
    id.scheme == "Counterparty"
    id.value == "counterparty"
  }
}
