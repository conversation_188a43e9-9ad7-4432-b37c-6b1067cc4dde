package com.solum.xplain.extensions.pricers

import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.HolidayCalendarId
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.market.explain.ExplainKey
import com.opengamma.strata.market.explain.ExplainMap
import com.opengamma.strata.pricer.rate.RateComputationFn
import com.opengamma.strata.product.rate.OvernightAveragedRateComputation
import java.time.LocalDate
import spock.lang.Specification

class XplainApproxForwardOvernightAveragedRateComputationFnTest extends Specification {
  static START_DATE = LocalDate.parse("2021-02-08")
  static END_DATE = LocalDate.parse("2022-02-08")
  static VALUATION_DATE = LocalDate.parse("2021-02-10")

  RateComputationFn<OvernightAveragedRateComputation> FUNCTION = new XplainApproxForwardOvernightAveragedRateComputationFn(ReferenceData.standard())

  def "should calculate rate"() {
    setup:
    def rates = CalibrationRatesSample.gbpRatesProvider(VALUATION_DATE)
    def computation = OvernightAveragedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates), closeTo(0.00048099d, 0.000001d)
  }

  def "should calculate rate on saturday"() {
    setup:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .put(LocalDate.parse("2021-02-11"), 0.000484)
      .put(LocalDate.parse("2021-02-12"), 0.000485)
      .build()

    var valuationDate = LocalDate.parse("2021-02-14") //Saturday
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate)
      .toBuilder()
      .timeSeries([(OvernightIndices.GBP_SONIA): overrideTimeseries])
      .build()
    def computation = OvernightAveragedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, START_DATE, valuationDate, rates), closeTo(0.00048316d, 0.00000001d)
  }

  def "should calculate rate when start date is fixing calendar holiday"() {
    setup:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-04-06"), 0.00048)
      .put(LocalDate.parse("2023-04-10"), 0.00048)
      .build()

    var valuationDate = LocalDate.parse("2023-04-11")
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate, Currency.USD, "USD-SOFR", OvernightIndices.USD_SOFR)
      .toBuilder()
      .timeSeries([(OvernightIndices.USD_SOFR): overrideTimeseries])
      .build()

    def computation =  OvernightAveragedRateComputation.builder()
      .index(OvernightIndices.USD_SOFR)
      .fixingCalendar(HolidayCalendarId.of("USGS").resolve(ReferenceData.standard()))
      .startDate(LocalDate.parse("2023-04-07")) // fixing calendar (USGS) holiday
      .endDate(LocalDate.parse("2024-04-07"))
      .rateCutOffDays(1)
      .build()

    expect:
    that FUNCTION.rate(computation, LocalDate.parse("2023-04-07"), valuationDate, rates), closeTo(0.00048d, 0.00000001d)
  }

  def "should calculate rate when end date is fixing calendar holiday"() {
    setup:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2022-04-06"), 0.00048)
      .put(LocalDate.parse("2022-04-07"), 0.00048)
      .build()

    var valuationDate = LocalDate.parse("2022-04-07")
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate, Currency.USD, "USD-SOFR", OvernightIndices.USD_SOFR)
      .toBuilder()
      .timeSeries([(OvernightIndices.USD_SOFR): overrideTimeseries])
      .build()

    def computation =  OvernightAveragedRateComputation.builder()
      .index(OvernightIndices.USD_SOFR)
      .fixingCalendar(HolidayCalendarId.of("USGS").resolve(ReferenceData.standard()))
      .startDate(LocalDate.parse("2022-04-07")) // fixing calendar (USGS) holiday
      .endDate(LocalDate.parse("2023-04-07"))
      .rateCutOffDays(1)
      .build()
    expect:
    that FUNCTION.rate(computation, LocalDate.parse("2022-04-07"), LocalDate.parse("2023-04-07"), rates), closeTo(0.00565499d, 0.00000001d)
  }

  def "should calculate rate sensitivities"() {
    setup:
    def rates = CalibrationRatesSample.gbpRatesProvider(VALUATION_DATE)
    def computation = OvernightAveragedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      VALUATION_DATE,
      ReferenceData.standard()
      )

    expect:
    def sensitivities = FUNCTION.rateSensitivity(computation, START_DATE, END_DATE, rates).build()
    sensitivities.sensitivities.size() == 1
    that sensitivities.sensitivities[0].sensitivity, closeTo(0.988860045, 0.0001d)
  }

  def "should calculate rate with explain"() {
    setup:
    def rates = CalibrationRatesSample.gbpRatesProvider(VALUATION_DATE)
    def explain = ExplainMap.builder()
    def computation = OvernightAveragedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.explainRate(computation, START_DATE, VALUATION_DATE, rates, explain), closeTo(0.00048099d, 0.000001d)
    that explain.build().get(ExplainKey.COMBINED_RATE).get(), closeTo(0.00048099d, 0.000001d)
  }
}
