package com.solum.xplain.extensions.xccyiborois


import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_3M
import static com.opengamma.strata.basics.index.IborIndices.CZK_PRIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.HUF_BUBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.MXN_TIIE_4W
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.NZD_BKBM_3M
import static com.opengamma.strata.basics.index.IborIndices.PLN_WIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.SEK_STIBOR_3M
import static com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR
import static com.opengamma.strata.basics.schedule.Frequency.P3M
import static com.opengamma.strata.product.swap.OvernightAccrualMethod.COMPOUNDED
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.AUSY_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.CZPR_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.DKCO_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.HUBU_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.MXMC_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.NOOS_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.NZBD_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.PLWA_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.SEST_USNY

import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.swap.CompoundingMethod
import spock.lang.Specification

class StandardXCcyIborOvernightSwapConventionsTest extends Specification {

  def "should return correctly constructed AUD_BBSW_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.AUD_BBSW_3M_USD_SOFR
    def ibor_index = AUD_BBSW_3M
    def overnight_index = USD_SOFR
    def calendar_on = AUSY_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed CZK_PRIBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.CZK_PRIBOR_3M_USD_SOFR
    def ibor_index = CZK_PRIBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = CZPR_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed NZD_BKBM_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.NZD_BKBM_3M_USD_SOFR
    def ibor_index = NZD_BKBM_3M
    def overnight_index = USD_SOFR
    def calendar_on = NZBD_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed DKK_CIBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.DKK_CIBOR_3M_USD_SOFR
    def ibor_index = DKK_CIBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = DKCO_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed HUF_BUBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.HUF_BUBOR_3M_USD_SOFR
    def ibor_index = HUF_BUBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = HUBU_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed MXN_TIIE_4W_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.MXN_TIIE_4W_USD_SOFR
    def ibor_index = MXN_TIIE_4W
    def overnight_index = USD_SOFR
    def calendar_on = MXMC_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.FLAT

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed NOK_NIBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.NOK_NIBOR_3M_USD_SOFR
    def ibor_index = NOK_NIBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = NOOS_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed PLN_WIBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.PLN_WIBOR_3M_USD_SOFR
    def ibor_index = PLN_WIBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = PLWA_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }

  def "should return correctly constructed SEK_STIBOR_3M_USD_SOFR convention"() {
    when:
    def convention = StandardXCcyIborOvernightSwapConventions.SEK_STIBOR_3M_USD_SOFR
    def ibor_index = SEK_STIBOR_3M
    def overnight_index = USD_SOFR
    def calendar_on = SEST_USNY

    then:
    convention.getIborLeg().getIndex() == ibor_index
    convention.getIborLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getIborLeg().getPaymentFrequency() == P3M
    convention.getIborLeg().getCompoundingMethod() == CompoundingMethod.NONE

    convention.getOvernightLeg().getIndex() == overnight_index
    convention.getOvernightLeg().getAccrualMethod() == COMPOUNDED
    convention.getOvernightLeg().getAccrualFrequency() == P3M
    convention.getOvernightLeg().getPaymentFrequency()== P3M
    convention.getOvernightLeg().getPaymentDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
    convention.getOvernightLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getOvernightLeg().getRateCutOffDays() == 1

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, calendar_on)
  }
}
