package com.solum.xplain.extensions.xccyfixedois

import static com.opengamma.strata.basics.date.Tenor.TENOR_6M
import static com.opengamma.strata.product.common.BuySell.BUY
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.INR_FIXED_6M_USD_SOFR
import static java.time.Period.ZERO

import com.opengamma.strata.basics.ReferenceData
import java.time.LocalDate
import spock.lang.Specification

class XCcyFixedOvernightSwapConventionTest extends Specification{

  private static final ImmutableXCcyFixedOvernightSwapConvention DEFAULT_CONV = INR_FIXED_6M_USD_SOFR as ImmutableXCcyFixedOvernightSwapConvention

  def "should correctly resolve of"() {
    setup:
    def conv = XCcyFixedOvernightSwapConvention.of("INR-FIXED-6M-USD-SOFR")

    expect:
    conv.equals(INR_FIXED_6M_USD_SOFR)
  }

  def "should correctly create trade"() {
    setup:
    def tradeDate = LocalDate.of(2022, 1, 1)

    def swap = DEFAULT_CONV.createTrade(tradeDate, ZERO, TENOR_6M, BUY, 10,10, 1, ReferenceData.standard())

    expect:
    swap.getInfo().getTradeDate().get() == tradeDate
    swap.getProduct() != null
  }
}
