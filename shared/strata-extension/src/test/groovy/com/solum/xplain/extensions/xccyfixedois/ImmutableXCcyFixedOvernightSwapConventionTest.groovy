package com.solum.xplain.extensions.xccyfixedois

import static com.opengamma.strata.basics.currency.Currency.INR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.product.common.BuySell.BUY
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.INR_FIXED_6M_USD_SOFR

import com.opengamma.strata.product.TradeInfo
import com.opengamma.strata.product.common.PayReceive
import com.opengamma.strata.product.swap.NotionalSchedule
import com.opengamma.strata.product.swap.RateCalculationSwapLeg
import com.opengamma.strata.product.swap.SwapLegType
import java.time.LocalDate
import spock.lang.Specification

class ImmutableXCcyFixedOvernightSwapConventionTest extends Specification {

  private static final ImmutableXCcyFixedOvernightSwapConvention DEFAULT_CONV = INR_FIXED_6M_USD_SOFR as ImmutableXCcyFixedOvernightSwapConvention


  def "should correctly create trade"() {
    setup:
    def tradeDate = LocalDate.of(2022, 1, 1)
    def tradeInfo = TradeInfo.of(tradeDate)

    def swap = DEFAULT_CONV.toTrade(tradeInfo, tradeDate, tradeDate.plusMonths(1), BUY, 10, 10, 1)

    expect:
    swap.getInfo().getTradeDate().get() == tradeDate
    swap.getProduct() != null
    swap.getProduct().getLegs().size() == 2
    swap.getProduct().getLegs()[0] instanceof RateCalculationSwapLeg

    def leg1 = swap.getProduct().getLegs()[0] as RateCalculationSwapLeg
    leg1.getType() == SwapLegType.FIXED
    leg1.getCurrency() == INR
    leg1.getPayReceive() == PayReceive.PAY
    leg1.getNotionalSchedule() == NotionalSchedule.of(INR, 10).toBuilder().finalExchange(true).initialExchange(true).build()

    def leg2 = swap.getProduct().getLegs()[1] as RateCalculationSwapLeg
    leg2.getType() == SwapLegType.OVERNIGHT
    leg2.getCurrency() == USD
    leg2.getPayReceive() == PayReceive.RECEIVE
    leg2.getNotionalSchedule() == NotionalSchedule.of(USD, 10).toBuilder().finalExchange(true).initialExchange(true).build()
  }
}
