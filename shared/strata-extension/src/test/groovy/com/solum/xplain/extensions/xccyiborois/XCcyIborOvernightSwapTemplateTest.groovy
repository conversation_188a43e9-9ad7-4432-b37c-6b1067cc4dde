package com.solum.xplain.extensions.xccyiborois

import static com.opengamma.strata.basics.date.Tenor.TENOR_1M
import static com.opengamma.strata.product.common.BuySell.BUY
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.AUD_BBSW_3M_USD_SOFR

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class XCcyIborOvernightSwapTemplateTest extends Specification {

  def "should correctly construct template"() {
    setup:
    def template = XCcyIborOvernightSwapTemplate.of(TENOR_1M, AUD_BBSW_3M_USD_SOFR)

    expect:
    template.getTenor() == TENOR_1M
    template.getConvention() == AUD_BBSW_3M_USD_SOFR
    template.getCurrencyPair() == CurrencyPair.of(Currency.AUD, Currency.USD)
  }

  def "should correctly create trade"() {
    setup:
    def convention = Mock(XCcyIborOvernightSwapConvention)
    def template = XCcyIborOvernightSwapTemplate.of(TENOR_1M, convention)
    def date = LocalDate.of(2020, 1, 1)
    1 * convention.createTrade(date, Period.ZERO, TENOR_1M, BUY, 100, 200, 3, ReferenceData.standard()) >> null

    def res = template.createTrade(date, BUY, 100, 200, 3, ReferenceData.standard())

    expect:
    res == null
  }
}
