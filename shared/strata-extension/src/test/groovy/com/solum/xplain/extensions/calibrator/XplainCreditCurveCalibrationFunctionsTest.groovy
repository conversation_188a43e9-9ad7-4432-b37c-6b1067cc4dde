package com.solum.xplain.extensions.calibrator

import static com.opengamma.strata.basics.currency.Currency.USD
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.market.curve.NodalCurve
import com.opengamma.strata.pricer.common.PriceType
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider
import com.opengamma.strata.pricer.credit.IsdaCdsTradePricer
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors
import com.opengamma.strata.product.credit.ResolvedCdsTrade
import com.opengamma.strata.product.credit.type.CdsQuoteConvention
import java.time.LocalDate
import spock.lang.Specification

class XplainCreditCurveCalibrationFunctionsTest extends Specification {

  ReferenceData REF_DATA = CreditTradesSample.REF_DATA
  LocalDate VALUATION_DATE = CreditTradesSample.VALUATION_DATE
  StandardId LEGAL_ENTITY = CreditTradesSample.LEGAL_ENTITY
  Currency CALIBRATION_CURRENCY = USD
  CdsQuoteConvention QUOTE_CONVENTION = CdsQuoteConvention.PAR_SPREAD
  double NUM_BASIS_POINTS = 0.01

  def "should correctly return all trades' PV sensitivity to ZC rate at index"() {
    setup:
    int index = 0
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    DoubleArray discountCurveZcRates = ((IsdaCreditDiscountFactors) creditRatesProvider.discountFactors(USD)).getCurve().getYValues()
    ResolvedCdsTrade cdsTrade = cdsTrade()

    DoubleArray shiftedDiscountCurveZcRates = XplainCreditCurveCalibrationFunctions.shiftElement(discountCurveZcRates, index)
    double firstElement = XplainCreditCurveCalibrationFunctions.getTradeZeroCouponRateSensitivity(tradePricer, cdsTrade, CALIBRATION_CURRENCY, creditRatesProvider, QUOTE_CONVENTION, shiftedDiscountCurveZcRates, REF_DATA)

    when:
    DoubleArray expSensitivityArray = XplainCreditCurveCalibrationFunctions.dPVDZeroCouponRates(
      tradePricer,
      creditRatesProvider,
      discountCurveZcRates,
      cdsTrade,
      QUOTE_CONVENTION,
      CALIBRATION_CURRENCY,
      REF_DATA
      )

    then:
    expSensitivityArray.toArray() == [
      2.8420507613979854E-8,
      -1.0210200740434773E-8,
      4.772569422994977E-8,
      9.677474516189834E-8,
      1.4450226432094484E-7,
      3.130109643700863E-7,
      3.7349301013067665E-7,
      -5.291152793684973E-7,
      -2.474163560139964E-6,
      -5.408850752008921E-6,
      -6.4706374397482955E-6,
      -1.473732326451316E-6,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ]
    firstElement == expSensitivityArray.get(0)
  }

  def "should correctly return a trade's PV sensitivity to all ZH rates"() {
    setup:
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve creditCurve = ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD).getSurvivalProbabilities()).getCurve()
    DoubleArray creditCurveZhRates = creditCurve.getYValues()
    ResolvedCdsTrade cdsTrade = cdsTrade()

    double firstElement = XplainCreditCurveCalibrationFunctions.getTradeZeroHazardRateSensitivity(
      tradePricer,
      cdsTrade,
      LEGAL_ENTITY,
      CALIBRATION_CURRENCY,
      creditCurve,
      QUOTE_CONVENTION,
      creditRatesProvider,
      XplainCreditCurveCalibrationFunctions.shiftElement(creditCurveZhRates, 0),
      REF_DATA
      )

    DoubleArray output = XplainCreditCurveCalibrationFunctions.dPVDZeroHazardRates(
      tradePricer,
      creditRatesProvider,
      LEGAL_ENTITY,
      creditCurve,
      creditCurveZhRates,
      cdsTrade,
      QUOTE_CONVENTION,
      CALIBRATION_CURRENCY,
      REF_DATA
      )
    DoubleArray expectedOutput = DoubleArray.of(1.5525535732030529E-6, 1.9118162455922243E-6, 4.575571466158835E-6,
      6.9209218966914685E-6, 9.460603087041443E-6, 1.7372510031921087E-4, 2.586651593944334E-4, 0.0)

    expect:
    output.size() == expectedOutput.size()

    that output.get(0), closeTo(expectedOutput.get(0), 1e-13)
    that output.get(1), closeTo(expectedOutput.get(1), 1e-13)
    that output.get(2), closeTo(expectedOutput.get(2), 1e-13)
    that output.get(3), closeTo(expectedOutput.get(3), 1e-13)
    that output.get(4), closeTo(expectedOutput.get(4), 1e-13)
    that output.get(5), closeTo(expectedOutput.get(5), 1e-13)
    that output.get(6), closeTo(expectedOutput.get(6), 1e-13)
    that output.get(7), closeTo(expectedOutput.get(7), 1e-13)

    firstElement == output.get(0)
  }

  def "should correctly return price delta with zero coupon rate shift"() {
    setup:
    int index = 0
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve discountCurve =
      ((IsdaCreditDiscountFactors) creditRatesProvider.discountFactors(USD)).getCurve()
    DoubleArray shiftedCurveZcRates = XplainCreditCurveCalibrationFunctions.shiftElement(discountCurve.getYValues(), index)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider = XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroCouponRate(CALIBRATION_CURRENCY, creditRatesProvider, shiftedCurveZcRates)
    ResolvedCdsTrade cdsTrade = cdsTrade()

    double originalPrice = tradePricer.price(cdsTrade, creditRatesProvider, PriceType.CLEAN, REF_DATA)
    def shiftedPrice = tradePricer.price(cdsTrade, shiftedCreditRatesProvider, PriceType.CLEAN, REF_DATA)

    def result = XplainCreditCurveCalibrationFunctions.getTradeZeroCouponRateSensitivity(
      tradePricer, cdsTrade, CALIBRATION_CURRENCY, creditRatesProvider, QUOTE_CONVENTION,
      shiftedCurveZcRates, REF_DATA)

    expect:
    that result, closeTo((shiftedPrice - originalPrice) / NUM_BASIS_POINTS, 1e-8)
  }

  def "should correctly return price delta with zero hazard rate shift"() {
    setup:
    int index = 0
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve creditCurve =
      ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD)
      .getSurvivalProbabilities()).getCurve()
    DoubleArray shiftedCurveZhRates = XplainCreditCurveCalibrationFunctions.shiftElement(creditCurve.getYValues(), index)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider = XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroHazardRate(CALIBRATION_CURRENCY, LEGAL_ENTITY, creditCurve, creditRatesProvider, shiftedCurveZhRates)
    ResolvedCdsTrade cdsTrade = cdsTrade()

    double originalPrice = tradePricer.price(cdsTrade, creditRatesProvider, PriceType.CLEAN, REF_DATA)
    def shiftedPrice = tradePricer.price(cdsTrade, shiftedCreditRatesProvider, PriceType.CLEAN, REF_DATA)

    double result = XplainCreditCurveCalibrationFunctions.getTradeZeroHazardRateSensitivity(
      tradePricer, cdsTrade, LEGAL_ENTITY, CALIBRATION_CURRENCY, creditCurve, QUOTE_CONVENTION,
      creditRatesProvider, shiftedCurveZhRates, REF_DATA)

    expect:
    that result, closeTo((shiftedPrice - originalPrice) / NUM_BASIS_POINTS, 1e-8)
  }

  def "should correctly calculate price shifts with no shift"() {
    setup:
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider originalCreditRatesProvider = creditRatesProvider(VALUATION_DATE)
    ResolvedCdsTrade cdsTrade = cdsTrade()

    when:
    ImmutableCreditRatesProvider unShiftedCreditRatesProvider = creditRatesProvider(VALUATION_DATE)
    double expectedPriceShift = 0d
    double actualPriceShift =
      XplainCreditCurveCalibrationFunctions.calculateTradeSensitivityValue(tradePricer, cdsTrade,
      originalCreditRatesProvider,
      unShiftedCreditRatesProvider,
      QUOTE_CONVENTION,
      REF_DATA)

    then:
    expectedPriceShift == actualPriceShift
  }

  def "should correctly calculate price shifts with shift"() {
    setup:
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider originalCreditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve originalCreditCurve =
      ((IsdaCreditDiscountFactors) originalCreditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD)
      .getSurvivalProbabilities()).getCurve()
    DoubleArray originalYValues = originalCreditCurve.getYValues()
    ResolvedCdsTrade cdsTrade = cdsTrade()

    double unShiftedPrice =
      tradePricer.price(cdsTrade, originalCreditRatesProvider, PriceType.CLEAN, REF_DATA)
    DoubleArray shiftedCurveZhRates = XplainCreditCurveCalibrationFunctions.shiftElement(
      originalYValues,
      0)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider =
      XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroHazardRate(CALIBRATION_CURRENCY, LEGAL_ENTITY,
      originalCreditCurve, originalCreditRatesProvider, shiftedCurveZhRates)
    double actualPriceShift =
      XplainCreditCurveCalibrationFunctions.calculateTradeSensitivityValue(tradePricer, cdsTrade,
      originalCreditRatesProvider,
      shiftedCreditRatesProvider,
      QUOTE_CONVENTION,
      REF_DATA)
    double shiftedPrice = tradePricer.price(cdsTrade, shiftedCreditRatesProvider, PriceType.CLEAN, REF_DATA)

    expect:
    that actualPriceShift, closeTo((shiftedPrice - unShiftedPrice) / NUM_BASIS_POINTS, 1e-8)
  }

  def "should correctly calculate par spread shifts with shift"() {
    setup:
    IsdaCdsTradePricer tradePricer = new IsdaCdsTradePricer()
    ImmutableCreditRatesProvider originalCreditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve originalCreditCurve =
      ((IsdaCreditDiscountFactors) originalCreditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD)
      .getSurvivalProbabilities()).getCurve()
    DoubleArray originalYValues = originalCreditCurve.getYValues()
    ResolvedCdsTrade cdsTrade = cdsTrade()

    double unShiftedParSpread =
      tradePricer.parSpread(cdsTrade, originalCreditRatesProvider, REF_DATA)
    DoubleArray shiftedCurveZhRates = XplainCreditCurveCalibrationFunctions.shiftElement(
      originalYValues,
      0)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider =
      XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroHazardRate(CALIBRATION_CURRENCY, LEGAL_ENTITY,
      originalCreditCurve, originalCreditRatesProvider, shiftedCurveZhRates)
    double actualParSpreadShift =
      XplainCreditCurveCalibrationFunctions.calculateTradeSensitivityValue(
      tradePricer,
      cdsTrade,
      originalCreditRatesProvider,
      shiftedCreditRatesProvider,
      CdsQuoteConvention.QUOTED_SPREAD,
      REF_DATA)
    double shiftedParSpread = tradePricer.parSpread(cdsTrade, shiftedCreditRatesProvider, REF_DATA)

    expect:
    that actualParSpreadShift, closeTo((shiftedParSpread - unShiftedParSpread) / NUM_BASIS_POINTS, 1e-8)
  }

  def "should correctly return credit rates provider with shifted ZC rate"() {
    setup:
    int index = 0
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve originalDiscountCurve =
      ((IsdaCreditDiscountFactors) creditRatesProvider.discountFactors(USD)).getCurve()
    DoubleArray originalYValues = originalDiscountCurve.getYValues()

    when:
    DoubleArray shiftedCurveZcRates = XplainCreditCurveCalibrationFunctions.shiftElement(
      originalYValues,
      index)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider =
      XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroCouponRate(CALIBRATION_CURRENCY, creditRatesProvider,
      shiftedCurveZcRates)
    NodalCurve newDiscountCurve =
      ((IsdaCreditDiscountFactors) shiftedCreditRatesProvider.discountFactors(USD)).getCurve()
    DoubleArray newYValues = newDiscountCurve.getYValues()

    then:
    newYValues != originalYValues
    newYValues.get(index) != originalYValues.get(index)
    for (int i = 1; i < originalYValues.size(); i++) {
      newYValues.get(i) == originalYValues.get(i)
    }
    newDiscountCurve.getXValues() == originalDiscountCurve.getXValues()
    newDiscountCurve.getMetadata() == originalDiscountCurve.getMetadata()

    for (int i = 0; i < originalDiscountCurve.getParameterCount(); i++) {
      newDiscountCurve.getParameterMetadata(i) == originalDiscountCurve.getParameterMetadata(i)
    }
    creditRatesProvider.recoveryRates(LEGAL_ENTITY) == shiftedCreditRatesProvider.recoveryRates(LEGAL_ENTITY)
    creditRatesProvider.toBuilder().get("creditCurves") == shiftedCreditRatesProvider.toBuilder().get("creditCurves")
  }

  def "should correctly return credit rates provider with shifted ZH rate"() {
    int index = 0
    ImmutableCreditRatesProvider creditRatesProvider = creditRatesProvider(VALUATION_DATE)
    NodalCurve originalCreditCurve =
      ((IsdaCreditDiscountFactors) creditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD)
      .getSurvivalProbabilities()).getCurve()
    DoubleArray originalYValues = originalCreditCurve.getYValues()

    when:
    DoubleArray shiftedCurveZcRates = XplainCreditCurveCalibrationFunctions.shiftElement(
      originalYValues,
      index)
    ImmutableCreditRatesProvider shiftedCreditRatesProvider =
      XplainCreditCurveCalibrationFunctions.generateRatesProviderWithShiftedZeroHazardRate(CALIBRATION_CURRENCY, LEGAL_ENTITY,
      originalCreditCurve,
      creditRatesProvider, shiftedCurveZcRates)
    NodalCurve newCreditCurve =
      ((IsdaCreditDiscountFactors) shiftedCreditRatesProvider.survivalProbabilities(LEGAL_ENTITY, USD)
      .getSurvivalProbabilities()).getCurve()
    DoubleArray newYValues = newCreditCurve.getYValues()

    then:
    newYValues != originalYValues
    newYValues.get(index) != originalYValues.get(index)
    for (int i = 1; i < originalYValues.size(); i++) {
      newYValues.get(i) == originalYValues.get(i)
    }

    newCreditCurve.getXValues() == originalCreditCurve.getXValues()
    newCreditCurve.getMetadata() == originalCreditCurve.getMetadata()
    for (int i = 0; i < originalCreditCurve.getParameterCount(); i++) {
      newCreditCurve.getParameterMetadata(i) == originalCreditCurve.getParameterMetadata(i)
    }
    creditRatesProvider.recoveryRates(LEGAL_ENTITY) == shiftedCreditRatesProvider.recoveryRates(LEGAL_ENTITY)
    creditRatesProvider.toBuilder().get("discountCurves") == shiftedCreditRatesProvider.toBuilder().get("discountCurves")
  }

  def "should correctly return shifted rates"() {
    setup:
    DoubleArray originalRates = DoubleArray.of(1.0d, 1.0d, 1.0d, 1.0d)
    int index = 0
    DoubleArray shiftedRates = XplainCreditCurveCalibrationFunctions.shiftElement(originalRates, index)

    expect:
    shiftedRates.get(0) == 1.000001d
    shiftedRates.get(1) == 1.0d
    shiftedRates.get(2) == 1.0d
    shiftedRates.get(3) == 1.0d
  }

  static creditRatesProvider(LocalDate valuationDate) {
    CreditRatesSample.creditRatesProvider(valuationDate)
  }

  static cdsTrade(String endDate = "2020-10-20") {
    return CreditTradesSample.cdsTrade(endDate)
  }
}
