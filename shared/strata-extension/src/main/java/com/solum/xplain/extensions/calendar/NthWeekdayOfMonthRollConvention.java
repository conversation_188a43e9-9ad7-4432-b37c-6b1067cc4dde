package com.solum.xplain.extensions.calendar;

import static java.time.format.TextStyle.SHORT;
import static java.util.Locale.ENGLISH;

import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.collect.ArgChecker;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

public class NthWeekdayOfMonthRollConvention implements RollConvention {

  private static final String NAME_PATTERN = "%sDay%s";
  private final int dayNumber;
  private final DayOfWeek dayOfWeek;

  NthWeekdayOfMonthRollConvention(int dayNumber, DayOfWeek dayOfWeek) {
    this.dayNumber = dayNumber;
    this.dayOfWeek = dayOfWeek;
  }

  @Override
  public LocalDate adjust(LocalDate date) {
    ArgChecker.notNull(date, "date");
    return date.with(TemporalAdjusters.dayOfWeekInMonth(dayNumber, dayOfWeek));
  }

  @Override
  public String getName() {
    return String.format(NAME_PATTERN, dayNumber, dayOfWeek.getDisplayName(SHORT, ENGLISH));
  }
}
