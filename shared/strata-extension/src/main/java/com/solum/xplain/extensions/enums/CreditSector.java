package com.solum.xplain.extensions.enums;

public enum CreditSector {
  BASIC_MATERIALS("Basic Materials"),
  CONSUMER_GOODS("Consumer Goods"),
  CONSUMER_SERVICES("Consumer Services"),
  ENERGY("Energy"),
  FINANCIALS("Financials"),
  GOVERNMENT("Government"),
  HEALTHCARE("Healthcare"),
  INDUSTRIALS("Industrials"),
  TECHNOLOGY("Technology"),
  TELCO("Telecommunications Services"),
  DIVERSIFIED("Diversified"),
  MUNICIPALITIES("Municipalities"),
  UTILITIES("Utilities"),
  UNDEFINED("Undefined");

  private final String label;

  CreditSector(String label) {
    this.label = label;
  }

  public String getLabel() {
    return label;
  }
}
