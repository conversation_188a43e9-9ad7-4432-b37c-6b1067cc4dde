package com.solum.xplain.extensions.calendar;

import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.NonNull;

@AllArgsConstructor
public class ValuationDateHolidayCalendar implements HolidayCalendar {

  private final HolidayCalendar calendar;
  private final List<LocalDate> holidayOverrides;

  public ValuationDateHolidayCalendar(HolidayCalendar calendar, @NonNull LocalDate date) {
    this.calendar = calendar;
    this.holidayOverrides = List.of(date);
  }

  @Override
  public boolean isHoliday(LocalDate date) {
    if (holidayOverrides.contains(date)) {
      return false;
    }
    return calendar.isHoliday(date);
  }

  @Override
  public HolidayCalendarId getId() {
    return calendar.getId();
  }
}
