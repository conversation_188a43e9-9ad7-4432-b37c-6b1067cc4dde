package com.solum.xplain.extensions.validation;

import static com.solum.xplain.extensions.calendar.ValuationDateReferenceData.wrap;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.pricer.rate.OvernightIndexRates;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.SwapLeg;
import com.solum.xplain.extensions.index.OffshoreIndices;
import io.atlassian.fugue.Checked;
import java.util.List;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class SwapMarketDataValidator implements TradeMarketDataValidator {

  private static final String MISSING_FIXING_AND_FALLBACK_FIXING_TEMPLATE =
      "Fixing for %s not found on %s or fallback date %s";
  private static final String MISSING_FIXING_TEMPLATE =
      "Fixing for %s not found on %s, using fixing from %s";

  private final RatesProvider ratesProvider;
  private final String tradeId;
  private final List<SwapLeg> swaplegs;

  public SwapMarketDataValidator(
      RatesProvider ratesProvider, String tradeId, List<SwapLeg> swaplegs) {
    this.ratesProvider = ratesProvider;
    this.tradeId = tradeId;
    this.swaplegs = swaplegs;
  }

  @Override
  public List<String> validate(ReferenceData referenceData) {
    ReferenceData valuationDateReferenceData =
        wrap(referenceData, ratesProvider.getValuationDate());
    return swaplegs.stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .map(RateCalculationSwapLeg::getCalculation)
        .filter(OvernightRateCalculation.class::isInstance)
        .map(OvernightRateCalculation.class::cast)
        .map(
            rateCalculation ->
                validateOvernightPreviousFixingValue(rateCalculation, valuationDateReferenceData))
        .flatMap(Optional::stream)
        .map(this::formatError)
        .toList();
  }

  private Optional<String> validateOvernightPreviousFixingValue(
      OvernightRateCalculation rateCalculation, ReferenceData referenceData) {

    return overnightIndexRates(rateCalculation)
        .flatMap(rates -> validateFixings(rates, rateCalculation, referenceData));
  }

  private Optional<OvernightIndexRates> overnightIndexRates(OvernightRateCalculation calculation) {
    return Checked.now(() -> ratesProvider.overnightIndexRates(calculation.getIndex()))
        .toOptional();
  }

  private Optional<String> validateFixings(
      OvernightIndexRates rates,
      OvernightRateCalculation rateCalculation,
      ReferenceData referenceData) {
    var calendar = rateCalculation.getIndex().getFixingCalendar().resolve(referenceData);
    var previousDate = calendar.previous(ratesProvider.getValuationDate());
    var fixings = rates.getFixings();
    var previousDateFixing = fixings.get(previousDate);

    var fallbackDate = calendar.previous(previousDate);
    var fallbackDateFixing = fixings.get(fallbackDate);

    if (previousDateFixing.isEmpty()) {
      var missingFixingTemplate =
          fallbackDateFixing.isPresent()
              ? MISSING_FIXING_TEMPLATE
              : MISSING_FIXING_AND_FALLBACK_FIXING_TEMPLATE;
      return Optional.of(
          String.format(
              missingFixingTemplate,
              OffshoreIndices.convertFromOffshore(rateCalculation.getIndex()),
              previousDate,
              fallbackDate));
    }

    return Optional.empty();
  }

  private String formatError(String errorMsg) {
    return String.format("%s: %s", tradeId, errorMsg);
  }
}
