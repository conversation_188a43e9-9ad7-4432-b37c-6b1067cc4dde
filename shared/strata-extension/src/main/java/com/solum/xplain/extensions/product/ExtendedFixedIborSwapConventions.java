package com.solum.xplain.extensions.product;

import static com.opengamma.strata.basics.currency.Currency.CAD;
import static com.opengamma.strata.basics.currency.Currency.CNH;
import static com.opengamma.strata.basics.currency.Currency.CNY;
import static com.opengamma.strata.basics.currency.Currency.KRW;
import static com.opengamma.strata.basics.currency.Currency.MXN;
import static com.opengamma.strata.basics.currency.Currency.MYR;
import static com.opengamma.strata.basics.currency.Currency.THB;
import static com.opengamma.strata.basics.currency.Currency.TWD;
import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;
import static com.opengamma.strata.basics.date.DayCounts.ACT_360;
import static com.opengamma.strata.basics.date.DayCounts.ACT_365F;
import static com.opengamma.strata.basics.date.DayCounts.THIRTY_360_ISDA;
import static com.opengamma.strata.basics.date.DayCounts.THIRTY_E_360;
import static com.opengamma.strata.basics.date.DayCounts.THIRTY_U_360;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.AUSY;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CATO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CHZU;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CZPR;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.DKCO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.EUTA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.GBLO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.HUBU;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.JPTO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.MXMC;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.NOOS;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.PLWA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.SEST;
import static com.opengamma.strata.basics.schedule.Frequency.P12M;
import static com.opengamma.strata.basics.schedule.Frequency.P13W;
import static com.opengamma.strata.basics.schedule.Frequency.P1M;
import static com.opengamma.strata.basics.schedule.Frequency.P26W;
import static com.opengamma.strata.basics.schedule.Frequency.P3M;
import static com.opengamma.strata.basics.schedule.Frequency.P4W;
import static com.opengamma.strata.basics.schedule.Frequency.P6M;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.AEDU;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.CNBE;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.CNBE_HKHK;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.CNBE_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_JPTO;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.HKHK;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.ILTA;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.KRSE;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.KRSE_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.MXMC_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.MYKL;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.MYKL_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.NZAU_NZWE;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.QADO;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.RUMO;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.SARI;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.SGSI;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.THBA;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.THBA_USNY;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.TRIS;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.TWTA;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.TWTA_USNY;

import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.IborIndices;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.ImmutableFixedIborSwapConvention;
import com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies;
import com.solum.xplain.extensions.index.OffshoreIndices;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@SuppressWarnings("unused")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExtendedFixedIborSwapConventions {

  public static final FixedIborSwapConvention GBP_FIXED_6M_LIBOR_1M =
      makeConvention(
          "GBP-FIXED-6M-LIBOR-1M",
          IborIndices.GBP_LIBOR_1M,
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO));

  public static final FixedIborSwapConvention GBP_FIXED_6M_LIBOR_12M =
      makeConvention(
          "GBP-FIXED-6M-LIBOR-12M",
          IborIndices.GBP_LIBOR_12M,
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_EURIBOR_1M =
      makeConvention(
          "EUR-FIXED-1Y-EURIBOR-1M",
          IborIndices.EUR_EURIBOR_1M,
          DayCounts.THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_EURIBOR_12M =
      makeConvention(
          "EUR-FIXED-1Y-EURIBOR-12M",
          IborIndices.EUR_EURIBOR_12M,
          DayCounts.THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention AUD_FIXED_3M_BBSW_3M =
      makeConvention(
          "AUD-FIXED-3M-BBSW-3M",
          IborIndices.AUD_BBSW_3M,
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, AUSY),
          1);

  public static final FixedIborSwapConvention AUD_FIXED_6M_BBSW_6M =
      make6MConvention(
          "AUD-FIXED-6M-BBSW-6M",
          IborIndices.AUD_BBSW_6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, AUSY),
          1);

  public static final FixedIborSwapConvention CHF_FIXED_1Y_LIBOR_1M =
      makeConvention(
          "CHF-FIXED-1Y-LIBOR-1M",
          IborIndices.CHF_LIBOR_1M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU));

  public static final FixedIborSwapConvention CHF_FIXED_1Y_LIBOR_12M =
      makeConvention(
          "CHF-FIXED-1Y-LIBOR-12M",
          IborIndices.CHF_LIBOR_12M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU));

  public static final FixedIborSwapConvention CHF_FIXED_1Y_LIBOR_6M =
      makeConvention(
          "CHF-FIXED-1Y-LIBOR-6M",
          IborIndices.CHF_LIBOR_6M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU));
  public static final FixedIborSwapConvention CAD_FIXED_6M_CDOR_3M =
      ImmutableFixedIborSwapConvention.of(
          "CAD-FIXED-6M-CDOR-3M",
          FixedRateSwapLegConvention.of(
              CAD,
              DayCounts.ACT_365F,
              Frequency.P6M,
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CATO)),
          IborRateSwapLegConvention.builder()
              .index(IborIndices.CAD_CDOR_3M)
              .accrualFrequency(P3M)
              .paymentFrequency(Frequency.P6M)
              .compoundingMethod(CompoundingMethod.FLAT)
              .stubConvention(StubConvention.SMART_INITIAL)
              .build());

  public static final FixedIborSwapConvention CAD_FIXED_6M_CDOR_12M =
      makeConvention(
          "CAD-FIXED-6M-CDOR-12M",
          IborIndices.CAD_CDOR_12M,
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CATO));

  public static final FixedIborSwapConvention NOK_FIXED_1Y_NIBOR_6M =
      makeConvention(
          "NOK-FIXED-1Y-NIBOR-6M",
          IborIndices.NOK_NIBOR_6M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, NOOS));

  public static final FixedIborSwapConvention NOK_FIXED_1Y_NIBOR_3M =
      makeConvention(
          "NOK-FIXED-1Y-NIBOR-3M",
          IborIndices.NOK_NIBOR_3M,
          THIRTY_E_360,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, NOOS));

  public static final FixedIborSwapConvention AED_FIXED_1Y_EIBOR_3M =
      makeConvention(
          "AED-FIXED-1Y-EIBOR-3M",
          IborIndex.of("AED-EIBOR-3M"),
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, AEDU));

  public static final FixedIborSwapConvention PLN_FIXED_1Y_WIBOR_6M =
      makeConvention(
          "PLN-FIXED-1Y-WIBOR-6M",
          IborIndices.PLN_WIBOR_6M,
          DayCounts.ACT_ACT_ISDA,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, PLWA));

  public static final FixedIborSwapConvention QAR_FIXED_1Y_QIBOR_3M =
      makeConvention(
          "QAR-FIXED-1Y-QIBOR-3M",
          IborIndex.of("QAR-QIBOR-3M"),
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, QADO));

  public static final FixedIborSwapConvention SAR_FIXED_1Y_SAIBOR_3M =
      makeConvention(
          "SAR-FIXED-1Y-SAIBOR-3M",
          IborIndex.of("SAR-SAIBOR-3M"),
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(FOLLOWING, SARI));
  public static final FixedIborSwapConvention DKK_FIXED_1Y_CIBOR_3M =
      makeConvention(
          "DKK-FIXED-1Y-CIBOR-3M",
          IborIndices.DKK_CIBOR_3M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, DKCO));

  public static final FixedIborSwapConvention DKK_FIXED_1Y_CIBOR_6M =
      makeConvention(
          "DKK-FIXED-1Y-CIBOR-6M",
          IborIndices.DKK_CIBOR_6M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, DKCO));

  public static final FixedIborSwapConvention SEK_FIXED_1Y_STIBOR_3M =
      makeConvention(
          "SEK-FIXED-1Y-STIBOR-3M",
          IborIndices.SEK_STIBOR_3M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SEST));

  public static final FixedIborSwapConvention CZK_FIXED_1Y_PRIBOR_6M =
      makeConvention(
          "CZK-FIXED-1Y-PRIBOR-6M",
          IborIndices.CZK_PRIBOR_6M,
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CZPR));

  public static final FixedIborSwapConvention CZK_FIXED_1Y_PRIBOR_3M =
      makeConvention(
          "CZK-FIXED-1Y-PRIBOR-3M",
          IborIndices.CZK_PRIBOR_3M,
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CZPR));

  public static final FixedIborSwapConvention ILS_FIXED_1Y_TLBOR_3M =
      makeConvention(
          "ILS-FIXED-1Y-TLBOR-3M",
          IborIndex.of("ILS-TLBOR-3M"),
          DayCounts.ACT_365F,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, ILTA));

  public static final FixedIborSwapConvention NZD_FIXED_6M_BKBM_3M =
      make6MConvention(
          "NZD-FIXED-6M-BKBM-3M",
          IborIndices.NZD_BKBM_3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, NZAU_NZWE),
          2);

  public static final FixedIborSwapConvention PLN_FIXED_1Y_WIBOR_3M =
      makeConvention(
          "PLN-FIXED-1Y-WIBOR-3M",
          IborIndices.PLN_WIBOR_3M,
          DayCounts.ACT_ACT_ISDA,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, PLWA));

  public static final FixedIborSwapConvention RUB_FIXED_1Y_MOSPRIME_3M =
      make3MConvention(
          "RUB-FIXED-1Y-MOSPRIME-3M",
          IborIndex.of("RUB-MOSPRIME-3M"),
          DayCounts.ACT_ACT_ISDA,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, RUMO),
          1,
          -1);

  public static final FixedIborSwapConvention ZAR_FIXED_3M_JIBAR_3M =
      makeConvention(
          "ZAR-FIXED-3M-JIBAR-3M",
          IborIndices.ZAR_JIBAR_3M,
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.ZAJO));

  public static final FixedIborSwapConvention CNY_FIXED_3M_REPO_1W =
      ImmutableFixedIborSwapConvention.of(
          "CNY-FIXED-3M-REPO-1W",
          FixedRateSwapLegConvention.of(
              CNY, DayCounts.ACT_365F, P3M, BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE)),
          IborRateSwapLegConvention.builder()
              .index(IborIndex.of("CNY-REPO-1W"))
              .stubConvention(StubConvention.SMART_FINAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.FLAT)
              .build());

  public static final FixedIborSwapConvention CNY_FIXED_3M_REPO_1W_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "CNY-FIXED-3M-REPO-1W-OFFSHORE",
          FixedRateSwapLegConvention.of(
              CNY,
              DayCounts.ACT_365F,
              P3M,
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.CNY_REPO_OFFSHORE_1W)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE_USNY))
              .stubConvention(StubConvention.SMART_FINAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.FLAT)
              .build());

  public static final FixedIborSwapConvention CNY_FIXED_3M_SHIBOR_3M =
      ImmutableFixedIborSwapConvention.of(
          "CNY-FIXED-3M-SHIBOR-3M",
          FixedRateSwapLegConvention.of(
              CNY, DayCounts.ACT_365F, P3M, BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE)),
          IborRateSwapLegConvention.builder()
              .index(IborIndex.of("CNY-SHIBOR-3M"))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build());

  public static final FixedIborSwapConvention CNH_FIXED_3M_HIBOR_3M =
      ImmutableFixedIborSwapConvention.of(
          "CNH-FIXED-3M-HIBOR-3M",
          FixedRateSwapLegConvention.of(
              CNH, ACT_360, P3M, BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE_HKHK)),
          IborRateSwapLegConvention.builder()
              .index(IborIndex.of("CNH-HIBOR-3M"))
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CNBE_HKHK))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build());

  public static final FixedIborSwapConvention HKD_FIXED_3M_HIBOR_3M =
      makeConvention(
          "HKD-FIXED-3M-HIBOR-3M",
          IborIndex.of("HKD-HIBOR-3M"),
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HKHK));

  public static final FixedIborSwapConvention HUF_FIXED_1Y_BUBOR_6M =
      makeConvention(
          "HUF-FIXED-1Y-BUBOR-6M",
          IborIndices.HUF_BUBOR_6M,
          DayCounts.ACT_365F,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HUBU));

  public static final FixedIborSwapConvention HUF_FIXED_3M_BUBOR_3M =
      makeConvention(
          "HUF-FIXED-3M-BUBOR-3M",
          IborIndices.HUF_BUBOR_3M,
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HUBU));

  public static final FixedIborSwapConvention KRW_FIXED_3M_CD_13W =
      makeConvention(
          "KRW-FIXED-3M-CD-13W",
          IborIndex.of("KRW-CD-13W"),
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, KRSE));

  public static final FixedIborSwapConvention KRW_FIXED_3M_CD_13W_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "KRW-FIXED-3M-CD-13W-OFFSHORE",
          FixedRateSwapLegConvention.of(
              KRW,
              DayCounts.ACT_365F,
              P3M,
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, KRSE_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.KRW_CD_OFFSHORE_13W)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, KRSE_USNY))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build(),
          DaysAdjustment.ofBusinessDays(1, KRSE));

  public static final FixedIborSwapConvention MXN_FIXED_4W_TIIE_4W =
      makeConvention(
          "MXN-FIXED-4W-TIIE-4W",
          IborIndices.MXN_TIIE_4W,
          DayCounts.ACT_360,
          P4W,
          BusinessDayAdjustment.of(FOLLOWING, MXMC));

  public static final FixedIborSwapConvention MXN_FIXED_4W_TIIE_4W_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "MXN-FIXED-4W-TIIE-4W-OFFSHORE",
          FixedRateSwapLegConvention.of(
              MXN, ACT_360, P4W, BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MXMC_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.MXN_TIIE_OFFSHORE_4W)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MXMC_USNY))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P4W)
              .compoundingMethod(CompoundingMethod.NONE)
              .build(),
          DaysAdjustment.ofBusinessDays(1, MXMC));

  public static final FixedIborSwapConvention MXN_FIXED_13W_TIIE_13W =
      makeConvention(
          "MXN-FIXED-13W-TIIE-13W",
          IborIndices.MXN_TIIE_13W,
          DayCounts.ACT_360,
          P13W,
          BusinessDayAdjustment.of(FOLLOWING, MXMC));

  public static final FixedIborSwapConvention MXN_FIXED_26W_TIIE_26W =
      makeConvention(
          "MXN-FIXED-26W-TIIE-26W",
          IborIndices.MXN_TIIE_26W,
          DayCounts.ACT_360,
          P26W,
          BusinessDayAdjustment.of(FOLLOWING, MXMC));

  public static final FixedIborSwapConvention MYR_FIXED_3M_KLIBOR_3M =
      makeConvention(
          "MYR-FIXED-3M-KLIBOR-3M",
          IborIndex.of("MYR-KLIBOR-3M"),
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MYKL));

  public static final FixedIborSwapConvention MYR_FIXED_3M_KLIBOR_3M_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "MYR-FIXED-3M-KLIBOR-3M-OFFSHORE",
          FixedRateSwapLegConvention.of(
              MYR, ACT_365F, P3M, BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MYKL_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.MYR_KLIBOR_OFFSHORE_3M)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MYKL_USNY))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build(),
          DaysAdjustment.ofBusinessDays(0, MYKL));

  public static final FixedIborSwapConvention THB_FIXED_6M_THBFIX_6M =
      makeConvention(
          "THB-FIXED-6M-THBFIX-6M",
          IborIndex.of("THB-THBFIX-6M"),
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, THBA));

  public static final FixedIborSwapConvention THB_FIXED_6M_THBFIX_6M_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "THB-FIXED-6M-THBFIX-6M-OFFSHORE",
          FixedRateSwapLegConvention.of(
              THB,
              DayCounts.ACT_365F,
              P6M,
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, THBA_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.THB_THBFIX_OFFSHORE_6M)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, THBA_USNY))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P6M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build(),
          DaysAdjustment.ofBusinessDays(2, THBA));

  public static final FixedIborSwapConvention TRY_FIXED_1Y_TRLIBOR_3M =
      makeConvention(
          "TRY-FIXED-1Y-TRLIBOR-3M",
          IborIndex.of("TRY-TRLIBOR-3M"),
          DayCounts.ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, TRIS));

  public static final FixedIborSwapConvention TWD_FIXED_3M_TAIBOR_3M =
      makeConvention(
          "TWD-FIXED-3M-TAIBOR-3M",
          IborIndex.of("TWD-TAIBOR-3M"),
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, TWTA),
          2);

  public static final FixedIborSwapConvention TWD_FIXED_3M_TAIBOR_3M_OFFSHORE =
      ImmutableFixedIborSwapConvention.of(
          "TWD-FIXED-3M-TAIBOR-3M-OFFSHORE",
          FixedRateSwapLegConvention.of(
              TWD,
              DayCounts.ACT_365F,
              P3M,
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, TWTA_USNY)),
          IborRateSwapLegConvention.builder()
              .index(OffshoreIndices.TWD_TAIBOR_OFFSHORE_3M)
              .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, TWTA_USNY))
              .stubConvention(StubConvention.SMART_INITIAL)
              .paymentFrequency(P3M)
              .compoundingMethod(CompoundingMethod.NONE)
              .build(),
          DaysAdjustment.ofBusinessDays(2, TWTA));

  public static final FixedIborSwapConvention SGD_FIXED_6M_SOR_1M =
      makeConvention(
          "SGD-FIXED-6M-SOR-1M",
          IborIndex.of("SGD-SOR-1M"),
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI),
          3);

  public static final FixedIborSwapConvention SGD_FIXED_1M_SOR_1M =
      makeConvention(
          "SGD-FIXED-1M-SOR-1M",
          IborIndex.of("SGD-SOR-1M"),
          DayCounts.ACT_365F,
          P1M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI),
          3);

  public static final FixedIborSwapConvention SGD_FIXED_3M_SOR_3M =
      makeConvention(
          "SGD-FIXED-3M-SOR-3M",
          IborIndex.of("SGD-SOR-3M"),
          DayCounts.ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI),
          3);

  public static final FixedIborSwapConvention SGD_FIXED_6M_SOR_6M =
      makeConvention(
          "SGD-FIXED-6M-SOR-6M",
          IborIndex.of("SGD-SOR-6M"),
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI),
          3);

  public static final FixedIborSwapConvention SGD_FIXED_6M_SOR_3M =
      makeConvention(
          "SGD-FIXED-6M-SOR-3M",
          IborIndex.of("SGD-SOR-3M"),
          DayCounts.ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI),
          3);

  public static final FixedIborSwapConvention USD_FIXED_6M_LIBOR_3M =
      makeConvention(
          "USD-FIXED-6M-LIBOR-3M",
          IborIndices.USD_LIBOR_3M,
          THIRTY_360_ISDA,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY));

  public static final FixedIborSwapConvention USD_FIXED_1Y_LIBOR_3M =
      makeConvention(
          "USD-FIXED-1Y-LIBOR-3M",
          IborIndices.USD_LIBOR_3M,
          ACT_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_EURIBOR_3M =
      makeConvention(
          "EUR-FIXED-1Y-EURIBOR-3M",
          IborIndices.EUR_EURIBOR_3M,
          THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_EURIBOR_6M =
      makeConvention(
          "EUR-FIXED-1Y-EURIBOR-6M",
          IborIndices.EUR_EURIBOR_6M,
          THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_LIBOR_3M =
      makeConvention(
          "EUR-FIXED-1Y-LIBOR-3M",
          IborIndices.EUR_LIBOR_3M,
          THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention EUR_FIXED_1Y_LIBOR_6M =
      makeConvention(
          "EUR-FIXED-1Y-LIBOR-6M",
          IborIndices.EUR_LIBOR_6M,
          THIRTY_U_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA));

  public static final FixedIborSwapConvention GBP_FIXED_1Y_LIBOR_3M =
      makeConvention(
          "GBP-FIXED-1Y-LIBOR-3M",
          IborIndices.GBP_LIBOR_3M,
          ACT_365F,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO));

  public static final FixedIborSwapConvention GBP_FIXED_6M_LIBOR_6M =
      makeConvention(
          "GBP-FIXED-6M-LIBOR-6M",
          IborIndices.GBP_LIBOR_6M,
          ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO));

  public static final FixedIborSwapConvention GBP_FIXED_3M_LIBOR_3M =
      makeConvention(
          "GBP-FIXED-3M-LIBOR-3M",
          IborIndices.GBP_LIBOR_3M,
          ACT_365F,
          P3M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO));

  public static final FixedIborSwapConvention CHF_FIXED_1Y_LIBOR_3M =
      makeConvention(
          "CHF-FIXED-1Y-LIBOR-3M",
          IborIndices.CHF_LIBOR_3M,
          THIRTY_E_360,
          P12M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU));

  public static final FixedIborSwapConvention JPY_FIXED_6M_TIBORJ_3M =
      makeConvention(
          "JPY-FIXED-6M-TIBOR-JAPAN-3M",
          IborIndices.JPY_TIBOR_JAPAN_3M,
          ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, JPTO));

  public static final FixedIborSwapConvention JPY_FIXED_6M_LIBOR_6M =
      makeConvention(
          "JPY-FIXED-6M-LIBOR-6M",
          IborIndices.JPY_LIBOR_6M,
          ACT_365F,
          P6M,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_JPTO));

  private static FixedIborSwapConvention make6MConvention(
      String name,
      IborIndex index,
      BusinessDayAdjustment accrualAdjustment,
      int spotDateOffsetDays) {
    var fixedLeg =
        FixedRateSwapLegConvention.of(
            index.getCurrency(), DayCounts.ACT_365F, P6M, accrualAdjustment);
    var floatLeg = floatingLeg(index, accrualAdjustment);
    var spotDateOffset =
        DaysAdjustment.ofBusinessDays(spotDateOffsetDays, accrualAdjustment.getCalendar());
    return ImmutableFixedIborSwapConvention.of(name, fixedLeg, floatLeg, spotDateOffset);
  }

  private static FixedIborSwapConvention makeConvention(
      String name,
      IborIndex index,
      DayCount dayCount,
      Frequency fixedFreq,
      BusinessDayAdjustment accrualAdjustment,
      int spotOffset) {
    var floatLeg = floatingLeg(index, accrualAdjustment);
    var fixedLeg =
        FixedRateSwapLegConvention.of(index.getCurrency(), dayCount, fixedFreq, accrualAdjustment);
    var spotAdjustment = DaysAdjustment.ofBusinessDays(spotOffset, accrualAdjustment.getCalendar());
    return ImmutableFixedIborSwapConvention.of(name, fixedLeg, floatLeg, spotAdjustment);
  }

  private static FixedIborSwapConvention makeConvention(
      String name,
      IborIndex index,
      DayCount dayCount,
      Frequency fixedFreq,
      BusinessDayAdjustment accrualAdjustment) {
    var floatLeg = floatingLeg(index, accrualAdjustment);
    var fixedLeg =
        FixedRateSwapLegConvention.of(index.getCurrency(), dayCount, fixedFreq, accrualAdjustment);
    return ImmutableFixedIborSwapConvention.of(name, fixedLeg, floatLeg);
  }

  /**
   * Create a convention based on a 3M floating leg with custom date offsets. This is instead of
   * various defaults being pulled from the IborIndex by IborRateSwapLegConvention
   */
  private static FixedIborSwapConvention make3MConvention(
      String name,
      IborIndex index,
      DayCount dayCount,
      Frequency fixedFreq,
      BusinessDayAdjustment accrualAdjustment,
      int spotDateOffsetDays,
      int fixDateOffset) {
    var floatLeg = floatingLeg3M(index, accrualAdjustment, dayCount, fixDateOffset);
    var fixedLeg =
        FixedRateSwapLegConvention.of(index.getCurrency(), dayCount, fixedFreq, accrualAdjustment);
    var spotDateOffset =
        DaysAdjustment.ofBusinessDays(spotDateOffsetDays, accrualAdjustment.getCalendar());
    return ImmutableFixedIborSwapConvention.of(name, fixedLeg, floatLeg, spotDateOffset);
  }

  private static IborRateSwapLegConvention floatingLeg3M(
      IborIndex index,
      BusinessDayAdjustment accrualAdjustment,
      DayCount dayCount,
      Integer fixDateOffset) {

    return IborRateSwapLegConvention.builder()
        .index(index)
        .stubConvention(StubConvention.SMART_INITIAL)
        .accrualBusinessDayAdjustment(accrualAdjustment)
        .dayCount(dayCount)
        .fixingDateOffset(DaysAdjustment.ofBusinessDays(fixDateOffset, index.getFixingCalendar()))
        .accrualFrequency(P3M)
        .build();
  }

  private static IborRateSwapLegConvention floatingLeg(
      IborIndex index, BusinessDayAdjustment accrualAdjustment) {
    return IborRateSwapLegConvention.builder()
        .index(index)
        .accrualBusinessDayAdjustment(accrualAdjustment)
        .stubConvention(StubConvention.SMART_INITIAL)
        .accrualFrequency(IborIndexAccrualFrequencies.indexAccrualFrequency(index))
        .build();
  }
}
