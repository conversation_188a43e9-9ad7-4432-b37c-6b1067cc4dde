package com.solum.xplain.extensions.constants;

import static java.util.Optional.ofNullable;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import java.util.Map;

public final class IborIndexAccrualFrequencies {
  private static final Map<IborIndex, Frequency> OVERRIDE_FREQUENCIES =
      Map.of(IborIndex.of("KRW-CD-13W"), Frequency.P3M);

  private IborIndexAccrualFrequencies() {}

  public static Frequency indexAccrualFrequency(IborIndex index) {
    return ofNullable(OVERRIDE_FREQUENCIES.get(index))
        .orElseGet(() -> Frequency.of(index.getTenor().getPeriod()));
  }
}
