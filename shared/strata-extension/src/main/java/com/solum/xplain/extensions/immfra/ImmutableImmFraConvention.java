package com.solum.xplain.extensions.immfra;

import static com.opengamma.strata.basics.currency.Currency.AUD;
import static com.opengamma.strata.basics.currency.Currency.NZD;
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;
import static com.opengamma.strata.product.fra.FraDiscountingMethod.AFMA;
import static com.opengamma.strata.product.fra.FraDiscountingMethod.ISDA;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.AdjustableDate;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DateSequence;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.SequenceDate;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.fra.Fra;
import com.opengamma.strata.product.fra.FraDiscountingMethod;
import com.opengamma.strata.product.fra.FraTrade;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;

/**
 * !!!!!!!!!!!!!!!!!!!!!!!! DISCLAIMER !!!!!!!!!!!!!!!!!!!!!
 *
 * <p>!!!! MUST BE REMOVED WHEN OG ADDS SUPPORT FOR THIS !!!!!!
 *
 * <p>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *
 * <p>A market convention for IMM FRA trade. This is based on the existing {@link
 * com.opengamma.strata.product.fra.type.ImmutableFraConvention} All field documentation could be
 * found there
 */
@Data
public final class ImmutableImmFraConvention implements ImmFraConvention {
  private final IborIndex index;
  private final DateSequence dateSequence; // IMM date sequence

  @Override
  public FraTrade createTrade(
      LocalDate tradeDate,
      SequenceDate sequenceDate,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData) {

    var startDate = dateSequence.selectDate(spotDate(tradeDate, refData), sequenceDate);
    var endDate = calculateEndDate(startDate, refData);
    var adjustedPay = getPaymentDateOffset().adjust(startDate, refData);
    return toTrade(tradeDate, startDate, endDate, adjustedPay, buySell, notional, fixedRate);
  }

  @Override
  public FraTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      LocalDate paymentDate,
      BuySell buySell,
      double notional,
      double fixedRate) {
    Optional<LocalDate> tradeDate = tradeInfo.getTradeDate();
    tradeDate.ifPresent(
        localDate -> ArgChecker.inOrderOrEqual(localDate, startDate, "tradeDate", "startDate"));
    // business day adjustment is not passed through as start/end date are fixed at
    // trade creation and should not be adjusted later
    return FraTrade.builder()
        .info(tradeInfo)
        .product(
            Fra.builder()
                .buySell(buySell)
                .currency(getCurrency())
                .notional(notional)
                .startDate(startDate)
                .endDate(endDate)
                .paymentDate(AdjustableDate.of(paymentDate, getPaymentDateOffset().getAdjustment()))
                .fixedRate(fixedRate)
                .index(index)
                .fixingDateOffset(getFixingDateOffset())
                .dayCount(getDayCount())
                .discounting(getDiscounting())
                .build())
        .build();
  }

  private LocalDate spotDate(LocalDate tradeDate, ReferenceData refData) {
    return getSpotDateOffset().adjust(tradeDate, refData);
  }

  public LocalDate calculateEndDate(LocalDate referenceDate, ReferenceData refData) {
    return index.calculateMaturityFromEffective(referenceDate, refData);
  }

  public FraDiscountingMethod getDiscounting() {
    Currency indexCcy = index.getCurrency();
    return indexCcy.equals(AUD) || indexCcy.equals(NZD) ? AFMA : ISDA;
  }

  @Override
  public String getName() {
    return index.getName() + "-IMM-FRA";
  }

  public Currency getCurrency() {
    return index.getCurrency();
  }

  public DayCount getDayCount() {
    return index.getDayCount();
  }

  // Default to modified following
  public BusinessDayAdjustment getBusinessDayAdjustment() {
    return BusinessDayAdjustment.of(
        MODIFIED_FOLLOWING, index.getEffectiveDateOffset().getResultCalendar());
  }

  // Default to index fixing offset
  public DaysAdjustment getFixingDateOffset() {
    return index.getFixingDateOffset();
  }

  // Default to no offset
  public DaysAdjustment getPaymentDateOffset() {
    return DaysAdjustment.NONE;
  }

  // Default to effectiveDateOffset
  public DaysAdjustment getSpotDateOffset() {
    return index.getEffectiveDateOffset();
  }
}
