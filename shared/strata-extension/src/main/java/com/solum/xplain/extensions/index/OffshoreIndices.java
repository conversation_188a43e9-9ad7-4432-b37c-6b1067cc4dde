package com.solum.xplain.extensions.index;

import static com.opengamma.strata.basics.index.OvernightIndices.THB_THOR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.ILS_SHIR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.INR_OMIBOR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.KRW_KOFR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MXN_F_TIIE;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MYR_MYOR;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.OvernightIndices;
import java.util.Map;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OffshoreIndices {
  public static final IborIndex CNY_REPO_OFFSHORE_1W = IborIndex.of("CNY-REPO-OFFSHORE-1W");
  public static final IborIndex THB_THBFIX_OFFSHORE_6M = IborIndex.of("THB-THBFIX-OFFSHORE-6M");
  public static final IborIndex TWD_TAIBOR_OFFSHORE_3M = IborIndex.of("TWD-TAIBOR-OFFSHORE-3M");
  public static final IborIndex KRW_CD_OFFSHORE_13W = IborIndex.of("KRW-CD-OFFSHORE-13W");
  public static final IborIndex MXN_TIIE_OFFSHORE_4W = IborIndex.of("MXN-TIIE-OFFSHORE-4W");
  public static final IborIndex MYR_KLIBOR_OFFSHORE_3M = IborIndex.of("MYR-KLIBOR-OFFSHORE-3M");

  public static final OvernightIndex THB_THOR_OFFSHORE = OvernightIndex.of("THB-THOR-OFFSHORE");
  public static final OvernightIndex INR_OMIBOR_OFFSHORE = OvernightIndex.of("INR-OMIBOR-OFFSHORE");
  public static final OvernightIndex MXN_F_TIIE_OFFSHORE = OvernightIndex.of("MXN-F-TIIE-OFFSHORE");
  public static final OvernightIndex MYR_MYOR_OFFSHORE = OvernightIndex.of("MYR-MYOR-OFFSHORE");
  public static final OvernightIndex CLP_TNA_OFFSHORE = OvernightIndex.of("CLP-TNA-OFFSHORE");
  public static final OvernightIndex COP_OIBR_OFFSHORE = OvernightIndex.of("COP-OIBR-OFFSHORE");
  public static final OvernightIndex BRL_CDI_OFFSHORE = OvernightIndex.of("BRL-CDI-OFFSHORE");
  public static final OvernightIndex ILS_SHIR_OFFSHORE = OvernightIndex.of("ILS-SHIR-OFFSHORE");
  public static final OvernightIndex KRW_KOFR_OFFSHORE = OvernightIndex.of("KRW-KOFR-OFFSHORE");

  private static final Map<IborIndex, IborIndex> IBOR_LOOKUP =
      ImmutableMap.<IborIndex, IborIndex>builder()
          .put(IborIndex.of("CNY-REPO-1W"), CNY_REPO_OFFSHORE_1W)
          .put(IborIndex.of("THB-THBFIX-6M"), THB_THBFIX_OFFSHORE_6M)
          .put(IborIndex.of("TWD-TAIBOR-3M"), TWD_TAIBOR_OFFSHORE_3M)
          .put(IborIndex.of("KRW-CD-13W"), KRW_CD_OFFSHORE_13W)
          .put(IborIndex.of("MXN-TIIE-4W"), MXN_TIIE_OFFSHORE_4W)
          .put(IborIndex.of("MYR-KLIBOR-3M"), MYR_KLIBOR_OFFSHORE_3M)
          .build();

  private static final Map<OvernightIndex, OvernightIndex> OVERNIGHT_LOOKUP =
      ImmutableMap.<OvernightIndex, OvernightIndex>builder()
          .put(THB_THOR, THB_THOR_OFFSHORE)
          .put(INR_OMIBOR, INR_OMIBOR_OFFSHORE)
          .put(OvernightIndex.of("CLP-TNA"), CLP_TNA_OFFSHORE)
          .put(OvernightIndex.of("COP-OIBR"), COP_OIBR_OFFSHORE)
          .put(MXN_F_TIIE, MXN_F_TIIE_OFFSHORE)
          .put(MYR_MYOR, MYR_MYOR_OFFSHORE)
          .put(ILS_SHIR, ILS_SHIR_OFFSHORE)
          .put(KRW_KOFR, KRW_KOFR_OFFSHORE)
          .put(OvernightIndices.BRL_CDI, BRL_CDI_OFFSHORE)
          .build();

  public static Optional<IborIndex> lookupOffshoreIbor(IborIndex index) {
    return Optional.ofNullable(IBOR_LOOKUP.get(index));
  }

  public static Optional<OvernightIndex> lookupOffshoreOvernight(OvernightIndex index) {
    return Optional.ofNullable(OVERNIGHT_LOOKUP.get(index));
  }

  public static boolean isOffshoreIborIndex(IborIndex index) {
    return IBOR_LOOKUP.containsValue(index);
  }

  public static boolean isOffshoreOvernightIndex(OvernightIndex index) {
    return OVERNIGHT_LOOKUP.containsValue(index);
  }

  public static Optional<OvernightIndex> fromOffshoreOvernight(OvernightIndex index) {
    return OVERNIGHT_LOOKUP.entrySet().stream()
        .filter(entry -> entry.getValue().equals(index))
        .map(Map.Entry::getKey)
        .findFirst();
  }

  public static Optional<IborIndex> fromOffshoreIbor(IborIndex index) {
    return IBOR_LOOKUP.entrySet().stream()
        .filter(entry -> entry.getValue().equals(index))
        .map(Map.Entry::getKey)
        .findFirst();
  }

  public static boolean isOffshore(FloatingRateIndex index) {
    if (index instanceof IborIndex ibor) {
      return isOffshoreIborIndex(ibor);
    } else if (index instanceof OvernightIndex overnightIndex) {
      return isOffshoreOvernightIndex(overnightIndex);
    } else {
      return false;
    }
  }

  public static FloatingRateIndex convertFromOnshore(FloatingRateIndex index) {
    if (index instanceof IborIndex ibor) {
      return lookupOffshoreIbor(ibor).orElse(ibor);
    } else if (index instanceof OvernightIndex overnightIndex) {
      return lookupOffshoreOvernight(overnightIndex).orElse(overnightIndex);
    }
    return index;
  }

  public static FloatingRateIndex convertFromOffshore(FloatingRateIndex index) {
    if (index instanceof IborIndex ibor) {
      return fromOffshoreIbor(ibor).orElse(ibor);
    } else if (index instanceof OvernightIndex overnightIndex) {
      return fromOffshoreOvernight(overnightIndex).orElse(overnightIndex);
    }
    return index;
  }

  public static boolean hasOffshoreIndex(FloatingRateIndex index) {
    if (index instanceof IborIndex ibor) {
      return lookupOffshoreIbor(ibor).isPresent();
    } else if (index instanceof OvernightIndex overnightIndex) {
      return lookupOffshoreOvernight(overnightIndex).isPresent();
    }
    return false;
  }
}
