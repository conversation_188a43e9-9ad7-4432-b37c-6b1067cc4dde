package com.solum.xplain.extensions.product;

import static com.opengamma.strata.product.swap.FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.FutureValueNotional;
import com.opengamma.strata.product.swap.InflationRateCalculation;
import com.opengamma.strata.product.swap.NotionalSchedule;
import com.opengamma.strata.product.swap.PaymentSchedule;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.Swap;
import com.opengamma.strata.product.swap.SwapLeg;
import com.opengamma.strata.product.swap.SwapTrade;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention;
import java.time.LocalDate;
import java.time.Period;
import java.util.Optional;
import lombok.NonNull;
import org.joda.beans.JodaBeanUtils;

public class InterpolatedInflationSwapConvention implements FixedInflationSwapConvention {

  @NonNull private final String name;

  @NonNull private final FixedRateSwapLegConvention fixedLeg;

  @NonNull private final InflationRateSwapLegConvention floatingLeg;

  @NonNull private final DaysAdjustment spotDateOffset;

  public static InterpolatedInflationSwapConvention of(
      String name,
      FixedRateSwapLegConvention fixedLeg,
      InflationRateSwapLegConvention floatingLeg,
      DaysAdjustment spotDateOffset) {

    return new InterpolatedInflationSwapConvention(name, fixedLeg, floatingLeg, spotDateOffset);
  }

  private void validate() {
    ArgChecker.isTrue(
        fixedLeg.getCurrency().equals(floatingLeg.getCurrency()),
        Messages.format(
            "Swap leg conventions must have same currency but found {} and {}, conventions {} and {}",
            fixedLeg.getCurrency(),
            floatingLeg.getCurrency(),
            fixedLeg,
            floatingLeg));
  }

  @Override
  public String toString() {
    return getName();
  }

  private InterpolatedInflationSwapConvention(
      String name,
      FixedRateSwapLegConvention fixedLeg,
      InflationRateSwapLegConvention floatingLeg,
      DaysAdjustment spotDateOffset) {
    JodaBeanUtils.notNull(name, "name");
    JodaBeanUtils.notNull(fixedLeg, "fixedLeg");
    JodaBeanUtils.notNull(floatingLeg, "floatingLeg");
    JodaBeanUtils.notNull(spotDateOffset, "spotDateOffset");
    this.name = name;
    this.fixedLeg = fixedLeg;
    this.floatingLeg = floatingLeg;
    this.spotDateOffset = spotDateOffset;
    validate();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public FixedRateSwapLegConvention getFixedLeg() {
    return fixedLeg;
  }

  @Override
  public InflationRateSwapLegConvention getFloatingLeg() {
    return floatingLeg;
  }

  @Override
  public DaysAdjustment getSpotDateOffset() {
    return spotDateOffset;
  }

  @Override
  public SwapTrade createTrade(
      LocalDate tradeDate,
      Tenor tenor,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData) {

    return createTrade(tradeDate, Period.ZERO, tenor, buySell, notional, fixedRate, refData);
  }

  // override OG toLeg method to adjust accrual schedules (BDA = None for accrual dates)
  private RateCalculationSwapLeg getFixedInterpolatedLeg(
      LocalDate startDate,
      LocalDate endDate,
      PayReceive payReceive,
      double notional,
      double fixedRate) {

    return RateCalculationSwapLeg.builder()
        .payReceive(payReceive)
        .accrualSchedule(
            PeriodicSchedule.builder()
                .startDate(startDate)
                .endDate(endDate)
                .frequency(fixedLeg.getAccrualFrequency())
                .businessDayAdjustment(BusinessDayAdjustment.NONE)
                .startDateBusinessDayAdjustment(BusinessDayAdjustment.NONE)
                .endDateBusinessDayAdjustment(BusinessDayAdjustment.NONE)
                .stubConvention(fixedLeg.getStubConvention())
                .rollConvention(fixedLeg.getRollConvention())
                .build())
        .paymentSchedule(
            PaymentSchedule.builder()
                .paymentFrequency(fixedLeg.getPaymentFrequency())
                .businessDayAdjustment(fixedLeg.getAccrualBusinessDayAdjustment())
                .paymentDateOffset(fixedLeg.getPaymentDateOffset())
                .compoundingMethod(fixedLeg.getCompoundingMethod())
                .build())
        .notionalSchedule(NotionalSchedule.of(fixedLeg.getCurrency(), notional))
        .calculation(
            FixedRateCalculation.builder()
                .rate(ValueSchedule.of(fixedRate))
                .dayCount(fixedLeg.getDayCount())
                .futureValueNotional(
                    fixedLeg.getAccrualMethod() == OVERNIGHT_COMPOUNDED_ANNUAL_RATE
                        ? FutureValueNotional.autoCalculate()
                        : null)
                .build())
        .build();
  }

  // override OG toLeg method to adjust accrual schedules (BDA = None for accrual dates)
  private RateCalculationSwapLeg getFloatInterpolatedLeg(
      LocalDate startDate, LocalDate endDate, PayReceive payReceive, double notional) {

    return RateCalculationSwapLeg.builder()
        .payReceive(payReceive)
        .accrualSchedule(
            PeriodicSchedule.builder()
                .startDate(startDate)
                .endDate(endDate)
                .frequency(Frequency.TERM)
                .businessDayAdjustment(BusinessDayAdjustment.NONE)
                .build())
        .paymentSchedule(
            PaymentSchedule.builder()
                // check the BDA will always be the same
                .businessDayAdjustment(fixedLeg.getAccrualBusinessDayAdjustment())
                .paymentFrequency(Frequency.TERM)
                .paymentDateOffset(DaysAdjustment.NONE)
                .build())
        .calculation(
            InflationRateCalculation.builder()
                .index(floatingLeg.getIndex())
                .indexCalculationMethod(floatingLeg.getIndexCalculationMethod())
                .lag(floatingLeg.getLag())
                .build())
        .notionalSchedule(NotionalSchedule.of(floatingLeg.getCurrency(), notional))
        .build();
  }

  @Override
  public SwapTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notional,
      double fixedRate) {

    Optional<LocalDate> tradeDate = tradeInfo.getTradeDate();
    if (tradeDate.isPresent()) {
      ArgChecker.inOrderOrEqual(tradeDate.get(), startDate, "tradeDate", "startDate");
    }
    SwapLeg leg1 =
        getFixedInterpolatedLeg(
            startDate, endDate, PayReceive.ofPay(buySell.isBuy()), notional, fixedRate);
    SwapLeg leg2 =
        getFloatInterpolatedLeg(startDate, endDate, PayReceive.ofPay(buySell.isSell()), notional);
    return SwapTrade.builder().info(tradeInfo).product(Swap.of(leg1, leg2)).build();
  }
}
