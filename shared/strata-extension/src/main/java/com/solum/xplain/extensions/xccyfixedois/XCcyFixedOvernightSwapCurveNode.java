package com.solum.xplain.extensions.xccyfixedois;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.FxRate;
import com.opengamma.strata.basics.currency.FxRateProvider;
import com.opengamma.strata.data.FxRateId;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataId;
import com.opengamma.strata.data.ObservableId;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.param.DatedParameterMetadata;
import com.opengamma.strata.market.param.TenorDateParameterMetadata;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.swap.ResolvedSwapTrade;
import com.opengamma.strata.product.swap.SwapTrade;
import java.time.LocalDate;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
@Getter
@ToString
public final class XCcyFixedOvernightSwapCurveNode implements CurveNode {

  private final XCcyFixedOvernightSwapTemplate template;
  private final FxRateId fxRateId;
  private final ObservableId spreadId;
  private final double additionalSpread;
  private final String label;
  private final ObservableId rateId;

  public static XCcyFixedOvernightSwapCurveNode of(
      XCcyFixedOvernightSwapTemplate template,
      ObservableId rateId,
      double additionalSpread,
      String label) {

    FxRateId fxRateId = FxRateId.of(template.getCurrencyPair());
    return new XCcyFixedOvernightSwapCurveNode(
        template, fxRateId, rateId, additionalSpread, label, rateId);
  }

  @Override
  public Set<? extends MarketDataId<?>> requirements() {
    return Set.of(fxRateId, spreadId);
  }

  @Override
  public String getLabel() {
    return this.label;
  }

  @Override
  public CurveNodeDateOrder getDateOrder() {
    return CurveNodeDateOrder.DEFAULT;
  }

  @Override
  public LocalDate date(LocalDate valuationDate, ReferenceData refData) {
    return CurveNodeDate.END.calculate(
        () -> calculateEnd(valuationDate, refData), () -> calculateEnd(valuationDate, refData));
  }

  private LocalDate calculateEnd(LocalDate valuationDate, ReferenceData refData) {
    SwapTrade trade = this.template.createTrade(valuationDate, BuySell.BUY, 1.0, 1.0, 0, refData);
    return trade.getProduct().getEndDate().adjusted(refData);
  }

  @Override
  public DatedParameterMetadata metadata(LocalDate valuationDate, ReferenceData refData) {
    LocalDate nodeDate = date(valuationDate, refData);
    return TenorDateParameterMetadata.of(nodeDate, template.getTenor(), label);
  }

  @Override
  public SwapTrade trade(double quantity, MarketData marketData, ReferenceData refData) {
    double marketQuote = marketData.getValue(spreadId) + additionalSpread;
    FxRate fxRate = marketData.getValue(fxRateId);
    double rate = fxRate.fxRate(template.getCurrencyPair());
    BuySell buySell = quantity > 0 ? BuySell.SELL : BuySell.BUY;
    double spreadLegNotional = Math.abs(quantity);
    double flatLegNotional = spreadLegNotional * rate;
    return template.createTrade(
        marketData.getValuationDate(),
        buySell,
        spreadLegNotional,
        flatLegNotional,
        marketQuote,
        refData);
  }

  @Override
  public ResolvedSwapTrade resolvedTrade(
      double quantity, MarketData marketData, ReferenceData refData) {
    return this.trade(quantity, marketData, refData).resolve(refData);
  }

  @Override
  public ResolvedSwapTrade sampleResolvedTrade(
      LocalDate valuationDate, FxRateProvider fxProvider, ReferenceData refData) {
    double rate = fxProvider.fxRate(template.getCurrencyPair());
    SwapTrade trade =
        this.template.createTrade(
            valuationDate, BuySell.SELL, 1.0, rate, this.additionalSpread, refData);
    return trade.resolve(refData);
  }

  @Override
  public double initialGuess(MarketData marketData, ValueType valueType) {
    if (ValueType.DISCOUNT_FACTOR.equals(valueType)) {
      return 1.0d;
    }
    return 0.0d;
  }

  public CurveNodeDate getDate() {
    return CurveNodeDate.END;
  }
}
