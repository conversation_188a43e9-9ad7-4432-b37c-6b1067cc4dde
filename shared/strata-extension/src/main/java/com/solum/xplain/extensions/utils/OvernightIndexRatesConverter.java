package com.solum.xplain.extensions.utils;

import static com.solum.xplain.extensions.constants.OvernightIndexConstants.INDICES_WITH_INDEX_BASED_FIXINGS;

import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.pricer.rate.DiscountOvernightIndexRates;
import com.opengamma.strata.pricer.rate.HistoricOvernightIndexRates;
import com.opengamma.strata.pricer.rate.OvernightIndexRates;

public class OvernightIndexRatesConverter {

  /**
   * Converts the rates to use the equivalent overnight rates for CLP-TNA.
   *
   * @param rates the rates to convert
   * @return the converted rates
   */
  public static OvernightIndexRates overnightRates(
      OvernightIndexRates rates, HolidayCalendar holidayCalendar) {
    if (INDICES_WITH_INDEX_BASED_FIXINGS.contains(rates.getIndex())) {
      if (rates instanceof HistoricOvernightIndexRates historicOvernightIndexRates) {
        return HistoricOvernightIndexRates.of(
            historicOvernightIndexRates.getIndex(),
            historicOvernightIndexRates.getValuationDate(),
            IndexBasedOvernightFixingConverter.toOvernightRates(
                historicOvernightIndexRates.getFixings(), holidayCalendar));
      } else if (rates instanceof DiscountOvernightIndexRates discountOvernightIndexRates) {
        return DiscountOvernightIndexRates.of(
            discountOvernightIndexRates.getIndex(),
            discountOvernightIndexRates.getDiscountFactors(),
            IndexBasedOvernightFixingConverter.toOvernightRates(
                discountOvernightIndexRates.getFixings(), holidayCalendar));
      } else {
        throw new IllegalArgumentException(
            "rates must be either HistoricOvernightIndexRates or DiscountOvernightIndexRates");
      }
    } else {
      return rates;
    }
  }
}
