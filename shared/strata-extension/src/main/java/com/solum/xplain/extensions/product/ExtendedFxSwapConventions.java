package com.solum.xplain.extensions.product;

import static com.opengamma.strata.basics.currency.Currency.CAD;
import static com.opengamma.strata.basics.currency.Currency.PHP;
import static com.opengamma.strata.basics.currency.Currency.RUB;
import static com.opengamma.strata.basics.currency.Currency.TRY;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.collect.named.NamedLookup;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.extensions.calendar.XplainHolidayCalendars;
import com.solum.xplain.extensions.constants.PermissibleCurrencies;
import java.util.Map;

public class ExtendedFxSwapConventions implements NamedLookup<FxSwapConvention> {

  public static final ExtendedFxSwapConventions INSTANCE = new ExtendedFxSwapConventions();

  private final Map<String, FxSwapConvention> conventions;

  private ExtendedFxSwapConventions() {
    var builder = ImmutableMap.<String, FxSwapConvention>builder();
    for (var pair : PermissibleCurrencies.FX_SWAP_PAIRS) {
      builder.put(pair.toConventional().toString(), of(pair));
    }
    this.conventions = builder.build();
  }

  private static FxSwapConvention of(CurrencyPair pair) {
    HolidayCalendarId combined = XplainHolidayCalendars.fxCalendarCcy(pair);
    return XplainFxSwapConvention.of(
        pair,
        DaysAdjustment.ofBusinessDays(spot(pair), combined),
        BusinessDayAdjustment.of(FOLLOWING, combined));
  }

  private static int spot(CurrencyPair pair) {
    if (pair.contains(RUB)) {
      return 1;
    }
    if (pair.contains(USD) && (pair.contains(CAD) || pair.contains(TRY) || pair.contains(PHP))) {
      return 1;
    }
    if (pair.contains(PHP) && (pair.contains(CAD) || pair.contains(TRY))) {
      return 1;
    }
    if (pair.contains(CAD) && (pair.contains(TRY))) {
      return 1;
    }
    return 2;
  }

  @Override
  public FxSwapConvention lookup(String name) {
    return conventions.get(name);
  }

  @Override
  public Map<String, FxSwapConvention> lookupAll() {
    return conventions;
  }

  // retrieve spot offset from conventions / exotic currency pairs
  public static DaysAdjustment lookupSpotOffset(CurrencyPair currencyPair) {
    return FxSwapConvention.extendedEnum()
        .find(currencyPair.toString())
        .map(FxSwapConvention::getSpotDateOffset)
        .orElse(of(currencyPair).getSpotDateOffset());
  }
}
