package com.solum.xplain.extensions.product;

import static com.opengamma.strata.basics.date.DateSequences.QUARTERLY_IMM;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_3M;
import static com.solum.xplain.extensions.datesequence.ExtendedDateSequences.QUARTERLY_NZD_IMM;

import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.index.IborIndices;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.index.type.ImmutableIborFutureContractSpec;
import com.solum.xplain.extensions.datesequence.AudFuturesDateSequence;

public final class ExtendedIborContractSpecs {

  public static final String AUD_BBSW_3M_QUARTERLY_FUTURE_NAME = "AUD-BBSW-3M-QUARTERLY-FUTURE";
  public static final String AUD_BBSW_3M_QUARTERLY_FUTURE_SD_NAME =
      "AUD-BBSW-3M-QUARTERLY-FUTURE_SD";
  public static final String CAD_CDOR_3M_IMM_MSE_NAME = "CAD-CDOR-3M-IMM-MSE";
  public static final String JPY_LIBOR_3M_IMM_TFX_NAME = "JPY-LIBOR-3M-IMM-TFX";
  public static final String SEK_STIBOR_3M_IMM_NAME = "SEK-STIBOR-3M-IMM";
  public static final String NZD_BKBM_3M_QUARTERLY_FUTURE_NAME = "NZD-BKBM-3M-QUARTERLY-FUTURE";

  public static final IborFutureContractSpecWithFixingOffsetOverride AUD_BBSW_3M_QUARTERLY_FUTURE =
      new IborFutureContractSpecWithFixingOffsetOverride(
          AUD_BBSW_3M_QUARTERLY_FUTURE_NAME,
          IborIndices.AUD_BBSW_3M,
          new AudFuturesDateSequence(),
          1000000,
          DaysAdjustment.ofBusinessDays(-1, HolidayCalendarIds.AUSY));

  public static final ImmutableIborFutureContractSpec AUD_BBSW_3M_QUARTERLY_FUTURE_SD =
      ImmutableIborFutureContractSpec.builder()
          .name(AUD_BBSW_3M_QUARTERLY_FUTURE_SD_NAME)
          .index(IborIndices.AUD_BBSW_3M)
          .dateSequence(new AudFuturesDateSequence())
          .notional(1000000)
          .build();

  public static final IborFutureContractSpecWithFixingOffsetOverride CAD_CDOR_3M_IMM_MSE =
      new IborFutureContractSpecWithFixingOffsetOverride(
          CAD_CDOR_3M_IMM_MSE_NAME,
          IborIndices.CAD_CDOR_3M,
          QUARTERLY_IMM,
          1000000,
          DaysAdjustment.ofBusinessDays(-2, HolidayCalendarIds.CATO));

  public static final IborFutureContractSpec JPY_LIBOR_3M_IMM_TFX =
      ImmutableIborFutureContractSpec.builder()
          .name(JPY_LIBOR_3M_IMM_TFX_NAME)
          .index(JPY_LIBOR_3M)
          .dateSequence(QUARTERLY_IMM)
          .notional(100000000)
          .build();

  public static final IborFutureContractSpec SEK_STIBOR_3M_IMM =
      ImmutableIborFutureContractSpec.builder()
          .name(SEK_STIBOR_3M_IMM_NAME)
          .index(IborIndices.SEK_STIBOR_3M)
          .dateSequence(QUARTERLY_IMM)
          .notional(1000000)
          .build();

  public static final IborFutureContractSpec NZD_BKBM_3M_QUARTERLY_FUTURE =
      new IborFutureContractSpecWithFixingOffsetOverride(
          NZD_BKBM_3M_QUARTERLY_FUTURE_NAME,
          IborIndices.NZD_BKBM_3M,
          QUARTERLY_NZD_IMM,
          1000000,
          DaysAdjustment.ofBusinessDays(-1, HolidayCalendarIds.NZAU));
}
