package com.solum.xplain.extensions.xccyiborois;

import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.Swap;
import com.opengamma.strata.product.swap.SwapTrade;
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import org.joda.beans.gen.PropertyDefinition;

/**
 * !!!!!!!!!!!!!!!!!!!!!!!! DISCLAIMER !!!!!!!!!!!!!!!!!!!!!
 *
 * <p>!!!! MUST BE REMOVED WHEN OG ADDS SUPPORT FOR THIS !!!!!!
 *
 * <p>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *
 * <p>A market convention for cross-currency Ois-Ois swap trades.
 */
@Builder
@EqualsAndHashCode
public final class ImmutableXCcyIborOvernightSwapConvention
    implements XCcyIborOvernightSwapConvention {

  private final String name;
  private final DaysAdjustment spotDateOffset;

  @PropertyDefinition(validate = "notNull", overrideGet = true)
  private final OvernightRateSwapLegConvention overnightLeg;

  @PropertyDefinition(validate = "notNull", overrideGet = true)
  private final IborRateSwapLegConvention iborLeg;

  private final boolean notionalExchange;

  public static ImmutableXCcyIborOvernightSwapConvention of(
      String name, IborRateSwapLegConvention iborLeg, OvernightRateSwapLegConvention overnightLeg) {
    return new ImmutableXCcyIborOvernightSwapConvention(
        name, iborLeg.getIndex().getEffectiveDateOffset(), overnightLeg, iborLeg, true);
  }

  public static ImmutableXCcyIborOvernightSwapConvention of(
      String name,
      IborRateSwapLegConvention iborLeg,
      OvernightRateSwapLegConvention overnightLeg,
      DaysAdjustment spotDateOffset) {
    return new ImmutableXCcyIborOvernightSwapConvention(
        name, spotDateOffset, overnightLeg, iborLeg, true);
  }

  @Override
  public SwapTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      BuySell buySell,
      double notionalSpreadLeg,
      double notionalFlatLeg,
      double spread) {

    Optional<LocalDate> tradeDate = tradeInfo.getTradeDate();
    tradeDate.ifPresent(d -> ArgChecker.inOrderOrEqual(d, startDate, "tradeDate", "startDate"));
    RateCalculationSwapLeg leg1 =
        iborLeg.toLeg(
            startDate, endDate, PayReceive.ofPay(buySell.isBuy()), notionalSpreadLeg, spread);
    RateCalculationSwapLeg leg2 =
        overnightLeg.toLeg(startDate, endDate, PayReceive.ofPay(buySell.isSell()), notionalFlatLeg);

    if (notionalExchange) {
      leg1 =
          leg1.toBuilder()
              .notionalSchedule(
                  leg1.getNotionalSchedule().toBuilder()
                      .initialExchange(true)
                      .finalExchange(true)
                      .build())
              .build();

      leg2 =
          leg2.toBuilder()
              .notionalSchedule(
                  leg2.getNotionalSchedule().toBuilder()
                      .initialExchange(true)
                      .finalExchange(true)
                      .build())
              .build();
    }

    return SwapTrade.builder().info(tradeInfo).product(Swap.of(leg1, leg2)).build();
  }

  @Override
  public String toString() {
    return getName();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public OvernightRateSwapLegConvention getOvernightLeg() {
    return this.overnightLeg;
  }

  @Override
  public IborRateSwapLegConvention getIborLeg() {
    return this.iborLeg;
  }

  @Override
  public DaysAdjustment getSpotDateOffset() {
    return spotDateOffset;
  }
}
