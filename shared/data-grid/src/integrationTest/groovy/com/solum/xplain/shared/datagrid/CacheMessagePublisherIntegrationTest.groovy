package com.solum.xplain.shared.datagrid

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.impl.cache.CacheMessagePublisher
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.util.ReflectionTestUtils

@RedisCacheTestConfiguration
class CacheMessagePublisherIntegrationTest extends RedisIntegrationSpecification {

  @Autowired
  DataGrid dataGrid
  def publisher
  def invalidationTopic = Mock(PubSubTopic)
  def sourceId = "testSource"

  def setup() {
    publisher = new CacheMessagePublisher(dataGrid)
    ReflectionTestUtils.setField(publisher , "invalidationTopic", invalidationTopic)
  }

  def "should publish invalidation message"() {
    given:
    def cacheName = "testCache"
    def key = "testKey"

    when:
    publisher.publishInvalidation(cacheName, key, sourceId)

    then:
    1 * invalidationTopic.publish({ message ->
      message.cacheName() == cacheName && message.key() == key
    })
  }

  def "should publish invalidation message with null key for clear operations"() {
    given:
    def cacheName = "testCache"

    when:
    publisher.publishInvalidation(cacheName, null, sourceId)

    then:
    1 * invalidationTopic.publish({ message ->
      message.cacheName() == cacheName && message.key() == null
    })
  }
}
