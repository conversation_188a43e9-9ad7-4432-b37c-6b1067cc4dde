package com.solum.xplain.shared.datagrid.event

import com.hazelcast.config.Config
import com.hazelcast.core.Hazelcast
import com.hazelcast.core.HazelcastInstance
import com.solum.xplain.shared.datagrid.ClusterEventPublisher
import com.solum.xplain.shared.datagrid.DataGridAutoConfiguration
import java.util.concurrent.CompletableFuture
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.event.EventListener
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource
import spock.lang.Specification

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = [ClusterEventConfig, TestConfig, DataGridAutoConfiguration, CacheAutoConfiguration])
@TestPropertySource(properties = [
  "debug=true",
  "app.cluster.event-topic=test-topic",
  "app.data-grid.hazelcast.enabled=true",
  "app.data-grid.redis.enabled=false",
  "spring.cache.type=jcache",
  "spring.cache.jcache.provider=com.hazelcast.cache.HazelcastMemberCachingProvider"
])
@EnableConfigurationProperties([ClusterProperties])
@EnableCaching
class HazelcastClusterEventServiceIntegrationTest extends Specification {
  @Autowired
  ClusterEventPublisher clusterEventPublisher
  @Autowired
  TestEventReceiver testEventReceiver

  def "cluster event publisher should result in application event being received"() {
    when:
    clusterEventPublisher.publishEvent(new TestEvent(value: "success"))
    def receivedValue = testEventReceiver.waitForReceipt()

    then:
    receivedValue == "success"
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    TestEventReceiver receiver() {
      return new TestEventReceiver()
    }

    @Bean
    HazelcastInstance hazelcastInstance(Config config) {
      return Hazelcast.newHazelcastInstance(config)
    }
  }

  static class TestEvent implements Serializable {
    String value
  }

  static class TestEventReceiver {
    CompletableFuture<String> receivedValue = new CompletableFuture<>()

    @EventListener
    def onEvent(TestEvent event) {
      receivedValue.complete(event.value)
    }

    String waitForReceipt() {
      return receivedValue.get()
    }
  }
}
