package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.DataGridAutoConfiguration
import com.solum.xplain.shared.datagrid.FencingTokenService
import com.solum.xplain.shared.datagrid.event.ClusterEventConfig
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = [ClusterEventConfig, DataGridAutoConfiguration, RedisAutoConfiguration])
@TestPropertySource(properties = [
  "debug=true",
  "app.cluster.event-topic=test-topic",
  "app.data-grid.hazelcast.enabled=false",
  "app.data-grid.redis.enabled=true",
  "spring.data.redis.client-name=test",
])
class FencingTokenServiceIntegrationTest extends RedisIntegrationSpecification {

  @Autowired
  FencingTokenService fencingTokenService

  def "should generate monotonically increasing tokens for same resource"() {
    given:
    def resourceId = "monotonic-test-${UUID.randomUUID()}"

    when:
    def token1 = fencingTokenService.generateToken(resourceId)
    def token2 = fencingTokenService.generateToken(resourceId)
    def token3 = fencingTokenService.generateToken(resourceId)

    then:
    token2 == token1 + 1
    token3 == token2 + 1
    and:
    fencingTokenService.getCurrentToken(resourceId) == token3
  }

  def "should start token generation from 1 for new resource"() {
    given:
    def resourceId = "new-resource-${UUID.randomUUID()}"

    when:
    def firstToken = fencingTokenService.generateToken(resourceId)

    then:
    firstToken == 1L
  }

  def "should set and retrieve specific token value"() {
    given:
    def resourceId = "set-test-${UUID.randomUUID()}"
    def expectedToken = 42L

    when:
    fencingTokenService.setToken(resourceId, expectedToken)

    then:
    fencingTokenService.getCurrentToken(resourceId) == expectedToken
  }

  def "should return 0 for non-existent resource current token"() {
    given:
    def nonExistentResourceId = "non-existent-${UUID.randomUUID()}"

    when:
    def currentToken = fencingTokenService.getCurrentToken(nonExistentResourceId)

    then:
    currentToken == 0L
  }

  def "should validate tokens correctly against current token"() {
    given:
    def resourceId = "validate-test-${UUID.randomUUID()}"
    def currentTokenValue = 100L
    fencingTokenService.setToken(resourceId, currentTokenValue)

    when:
    def validCurrent = fencingTokenService.validateToken(resourceId, currentTokenValue)
    def validNewer = fencingTokenService.validateToken(resourceId, currentTokenValue + 1)
    def invalidOlder = fencingTokenService.validateToken(resourceId, currentTokenValue - 1)

    then:
    validCurrent
    validNewer
    !invalidOlder
  }

  def "should handle concurrent token generation safely"() {
    given:
    def resourceId = "concurrent-test-${UUID.randomUUID()}"
    def threadCount = 20
    def tokens = Collections.synchronizedList([])
    def latch = new CountDownLatch(threadCount)
    def executor = Executors.newFixedThreadPool(threadCount)

    when:
    //generating tokens concurrently
    (1..threadCount).each {
      executor.submit {
        try {
          tokens << fencingTokenService.generateToken(resourceId)
        } finally {
          latch.countDown()
        }
      }
    }
    latch.await(10, TimeUnit.SECONDS)
    executor.shutdown()

    then: "all tokens should be unique and properly ordered"
    tokens.size() == threadCount
    tokens.unique().size() == threadCount
    tokens.sort() == (1..threadCount).toList()
  }

  def "should identify valid tokens correctly"() {
    given:
    def positiveToken = 1L
    def zeroToken = 0L
    def negativeToken = -1L

    when:
    def positiveValid = fencingTokenService.isValidToken(positiveToken)
    def zeroValid = fencingTokenService.isValidToken(zeroToken)
    def negativeValid = fencingTokenService.isValidToken(negativeToken)

    then:
    //only positive tokens should be valid
    positiveValid
    !zeroValid
    !negativeValid
  }

  def "should correctly identify newer tokens"() {
    given:
    def resourceId = "newer-check-${UUID.randomUUID()}"
    def currentTokenValue = 100L
    fencingTokenService.setToken(resourceId, currentTokenValue)

    when:
    def newerTokenCheck = fencingTokenService.isNewerThanCurrent(resourceId, currentTokenValue + 1)
    def equalTokenCheck = fencingTokenService.isNewerThanCurrent(resourceId, currentTokenValue)
    def olderTokenCheck = fencingTokenService.isNewerThanCurrent(resourceId, currentTokenValue - 1)

    then:
    newerTokenCheck
    !equalTokenCheck
    !olderTokenCheck
  }

  def "should generate correct fencing token format"() {
    given:
    def resourceId = "test-resource"

    when:
    def tokenName = fencingTokenService.getTokenName(resourceId)

    then:
    tokenName == "FENCING_TOKEN__test-resource"
  }

  def "should handle cleanup gracefully"() {
    given:
    def resourceId = "cleanup-test-${UUID.randomUUID()}"

    when:
    fencingTokenService.cleanup(resourceId)

    then:
    noExceptionThrown()
  }

  def "should maintain token isolation between different resources"() {
    given:
    def resourceId1 = "isolation-test-1-${UUID.randomUUID()}"
    def resourceId2 = "isolation-test-2-${UUID.randomUUID()}"

    when:
    def token1Resource1 = fencingTokenService.generateToken(resourceId1)
    def token1Resource2 = fencingTokenService.generateToken(resourceId2)
    def token2Resource1 = fencingTokenService.generateToken(resourceId1)

    then:
    token1Resource1 == 1L
    token1Resource2 == 1L
    token2Resource1 == 2L
    and:
    fencingTokenService.getCurrentToken(resourceId1) == 2L
    fencingTokenService.getCurrentToken(resourceId2) == 1L
  }

  def "should handle edge case token values"() {
    given:
    def resourceId = "edge-case-${UUID.randomUUID()}"

    when:
    fencingTokenService.setToken(resourceId, Long.MAX_VALUE) //Max value
    def maxValueToken = fencingTokenService.getCurrentToken(resourceId)

    fencingTokenService.setToken(resourceId, 1L)
    def minValidToken = fencingTokenService.getCurrentToken(resourceId)

    then:
    maxValueToken == Long.MAX_VALUE
    minValidToken == 1L
    fencingTokenService.isValidToken(maxValueToken)
    fencingTokenService.isValidToken(minValidToken)
  }
}
