package com.solum.xplain.shared.datagrid.event

import com.solum.xplain.shared.datagrid.PubSubTopic
import com.solum.xplain.shared.datagrid.topic.MessageListener
import com.solum.xplain.shared.datagrid.topic.Subscription
import java.util.concurrent.atomic.AtomicReference
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class ClusterEventServiceTest extends Specification {
  def topic = Mock(PubSubTopic)
  def appPublisher = Mock(ApplicationEventPublisher)
  def service = new ClusterEventService(topic, appPublisher)

  def "should publish event to data grid topic"() {
    given:
    def event = "test"

    when:
    service.publishEvent(event)

    then:
    1 * topic.publish(event)
  }

  def "should propagate data grid event to Spring application"() {
    AtomicReference<MessageListener> listener = new AtomicReference<>()
    given:
    def event = "test"
    def sub = Mock(Subscription)
    topic.subscribe(_ as MessageListener) >> { MessageListener ml ->
      listener.set(ml)
      return sub
    }

    when:
    service.subscribeToClusterEventTopic()
    listener.get().onMessage(event)

    then:
    1 * appPublisher.publishEvent(event)
  }
}
