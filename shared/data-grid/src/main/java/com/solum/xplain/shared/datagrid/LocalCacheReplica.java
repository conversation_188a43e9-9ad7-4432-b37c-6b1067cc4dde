package com.solum.xplain.shared.datagrid;

import java.time.Duration;

/**
 * Portable configuration of local/near caching.
 *
 * @param name the name of the cache to enable near caching for
 * @param maxEntries maximum number of entries to hold in the cache before they start being expired
 * @param ttl maximum lifetime of an entry in the cache
 * @param idleTtl maximum lifetime of an untouched (not read) entry in the cache
 * @param autoUpdate true if the cache should listen for changes to the shared cache and update the
 *     cache with local changes. NB Hazelcast only supports updating with local changes for JCache
 *     (JSR-107).
 */
public record LocalCacheReplica(
    String name, int maxEntries, Duration ttl, Duration idleTtl, boolean autoUpdate) {
  public static final int DEFAULT_MAX_ENTRIES = 10_000;
  public static final Duration DEFAULT_TTL = Duration.ZERO;
  public static final Duration DEFAULT_IDLE_TTL = Duration.ZERO;
}
