package com.solum.xplain.shared.datagrid;

import com.solum.xplain.shared.datagrid.topic.MessageListener;
import com.solum.xplain.shared.datagrid.topic.Subscription;

/**
 * Interface for a topic that can be used to publish and subscribe to messages. This is commonly
 * implemented by a message broker such as Kafka but data grids also offer this feature since it can
 * piggyback on the reliable synchronisation they already offer.
 */
public interface PubSubTopic<T> {
  String getName();

  /**
   * Publishes a message to the topic.
   *
   * @param message the message to publish
   */
  void publish(T message);

  /**
   * Subscribes to the topic and listens for messages.
   *
   * @param listener the listener to invoke when a message is received
   */
  Subscription subscribe(MessageListener<T> listener);
}
