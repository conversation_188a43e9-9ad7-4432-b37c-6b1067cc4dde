package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.topic.MessageListener;
import com.solum.xplain.shared.datagrid.topic.Subscription;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.RedisSerializer;

@RequiredArgsConstructor
public class RedisPubSubTopic<T> implements PubSubTopic<T> {
  private final RedisTemplate<?, ?> redisTemplate;
  private final RedisMessageListenerContainer redisMessageListenerContainer;
  @Getter private final String name;

  @Override
  public void publish(T message) {
    redisTemplate.convertAndSend(name, message);
  }

  @Override
  public Subscription subscribe(MessageListener<T> listener) {
    Listener<T> redisListener =
        new Listener<>(
            redisMessageListenerContainer, redisTemplate.getDefaultSerializer(), listener);
    redisListener.subscribe(name);

    return redisListener;
  }

  private static class Listener<T> implements Subscription {
    private final RedisMessageListenerContainer redisMessageListenerContainer;
    private final MessageListenerAdapter adapter;

    private Listener(
        RedisMessageListenerContainer redisMessageListenerContainer,
        RedisSerializer<?> serializer,
        MessageListener<T> listener) {
      this.redisMessageListenerContainer = redisMessageListenerContainer;
      this.adapter = new MessageListenerAdapter(listener);
      adapter.setSerializer(serializer);
      adapter.setDefaultListenerMethod("onMessage");
      adapter.afterPropertiesSet();
    }

    void subscribe(String name) {
      redisMessageListenerContainer.addMessageListener(adapter, ChannelTopic.of(name));
    }

    @Override
    public void unsubscribe() {
      redisMessageListenerContainer.removeMessageListener(adapter);
    }
  }
}
