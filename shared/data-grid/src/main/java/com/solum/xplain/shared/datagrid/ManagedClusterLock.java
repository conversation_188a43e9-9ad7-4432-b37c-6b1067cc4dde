package com.solum.xplain.shared.datagrid;

import com.solum.xplain.shared.datagrid.impl.redis.RedisClusterLock;
import com.solum.xplain.shared.datagrid.impl.redis.RedisLockManager;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;

/**
 * A managed cluster lock that provides automatic heartbeat functionality for Redis-based
 * distributed locks.
 *
 * <p>This implementation wraps a {@link RedisClusterLock} and delegates lock management to a {@link
 * RedisLockManager} to provide automatic lock renewal through heartbeat mechanism.
 *
 * <p>Key behaviors:
 *
 * <ul>
 *   <li>Blocking lock operations ({@code lock()} and {@code lockInterruptibly()}) use Redis's
 *       native blocking mechanisms for efficiency
 *   <li>Non-blocking operations ({@code tryLock()}) use the lock manager for integrated acquisition
 *       and heartbeat startup
 *   <li>All successful lock acquisitions automatically start heartbeat to prevent expiration
 *   <li>Lock releases properly stop heartbeat and clean up resources
 *   <li>Proper error handling prevents orphaned locks
 * </ul>
 */
@Slf4j
@RequiredArgsConstructor
@NullMarked
public class ManagedClusterLock<T extends ClusterLock> implements ClusterLock {
  private final T lock;
  private final LockManager<T> lockManager;

  private void startHeartbeatOnLock() {
    try {
      lockManager.startHeartbeat(lock);
    } catch (Exception e) {
      try {
        lock.unlock();
      } catch (Exception unlockEx) {
        log.warn("Failed to release lock after heartbeat failure: {}", getName(), unlockEx);
      }
      throw new RuntimeException("Failed to start heartbeat for lock: " + getName(), e);
    }
  }

  @Override
  public String getName() {
    return lock.getName();
  }

  @Override
  public boolean isCurrentLockValid() {
    return lock.isCurrentLockValid();
  }

  @Override
  public boolean isLockExpired() {
    return lock.isLockExpired();
  }

  @Override
  public boolean renewLock() {
    return lock.renewLock();
  }

  @Override
  public void lock() {
    lock.lock();
    startHeartbeatOnLock();
  }

  @Override
  public void lockInterruptibly() throws InterruptedException {
    lock.lockInterruptibly();
    startHeartbeatOnLock();
  }

  @Override
  public boolean tryLock() {
    return lockManager.acquireWithHeartbeat(lock);
  }

  @Override
  public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
    return lockManager.acquireWithHeartbeat(lock, time, unit);
  }

  @Override
  public void unlock() {
    lockManager.release(lock);
  }

  @Override
  public Condition newCondition() {
    throw new UnsupportedOperationException("Conditions are not supported for managed locks");
  }
}
