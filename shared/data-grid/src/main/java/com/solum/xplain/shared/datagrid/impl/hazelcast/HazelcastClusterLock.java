package com.solum.xplain.shared.datagrid.impl.hazelcast;

import com.hazelcast.cp.lock.FencedLock;
import com.solum.xplain.shared.datagrid.ClusterLock;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;

/** This adapter class wraps a Hazelcast FencedLock and implements the ClusterLock interface. */
@RequiredArgsConstructor
public class HazelcastClusterLock implements ClusterLock {
  @Delegate private final FencedLock fencedLock;

  @Override
  public boolean isCurrentLockValid() {
    return false;
  }

  @Override
  public boolean isLockExpired() {
    return false;
  }

  @Override
  public boolean renewLock() {
    return false;
  }
}
