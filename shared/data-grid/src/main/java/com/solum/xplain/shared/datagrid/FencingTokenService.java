package com.solum.xplain.shared.datagrid;

import com.solum.xplain.shared.datagrid.impl.redis.RedisAtomicCounter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * Service for managing fencing tokens in a distributed system using Redis.
 *
 * <p>Fencing tokens are monotonically increasing sequence numbers used to prevent split-brain
 * scenarios and ensure data consistency in distributed systems. This service provides thread-safe
 * operations for generating, validating, and managing fencing tokens per resource. The following
 * are the key characteristics:
 *
 * <ul>
 *   <li>Tokens are guaranteed to be monotonically increasing for each resource
 *   <li>Thread-safe operations using Redis atomic operations
 *   <li>Resource isolation - each resource maintains its own token sequence
 *   <li>Tokens start from 1 for new resources
 *   <li>Only positive tokens (> 0) are considered valid
 * </ul>
 *
 * @see <a href="https://martin.kleppmann.com/2016/02/08/how-to-do-distributed-locking.html">Fencing
 *     tokens</a>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@NullMarked
public class FencingTokenService {

  private final StringRedisTemplate stringRedisTemplate;

  /** Redis key prefix for fencing tokens to avoid key collisions. */
  private static final String FENCING_TOKEN_PREFIX = "FENCING_TOKEN__";

  private RedisAtomicCounter getAtomicCounter(String resourceId) {
    return new RedisAtomicCounter(stringRedisTemplate.boundValueOps(resourceId));
  }

  /**
   * Generates a new fencing token
   *
   * <p>This method atomically increments the token counter for the resource and returns the new
   * token value. The first token generated for a new resource will be 1.
   */
  public long generateToken(String resourceId) {
    String tokenName = FENCING_TOKEN_PREFIX + resourceId;
    RedisAtomicCounter tokenCounter = getAtomicCounter(tokenName);
    long token = tokenCounter.incrementAndGet();
    log.debug("Generated fencing token {} for resource: {}", token, resourceId);
    return token;
  }

  /**
   * Retrieves the current fencing token
   *
   * <p>Returns the last generated token value without incrementing it. If no token has been
   * generated for the resource, returns 0.
   */
  public long getCurrentToken(String resourceId) {
    String tokenName = FENCING_TOKEN_PREFIX + resourceId;
    RedisAtomicCounter tokenCounter = getAtomicCounter(tokenName);
    long currentToken = tokenCounter.get();
    log.debug("Current fencing token for resource {}: {}", resourceId, currentToken);
    return currentToken;
  }

  /**
   * Sets the fencing token to a specific value for the specified resource.
   *
   * <p>This method should be used with caution as it can break the monotonic ordering if not used
   * properly. Use for initialization or recovery scenarios.
   */
  public void setToken(String resourceId, long value) {
    String tokenName = FENCING_TOKEN_PREFIX + resourceId;
    RedisAtomicCounter tokenCounter = getAtomicCounter(tokenName);
    tokenCounter.set(value);
    log.info("Set fencing token to {} for resource: {}", value, resourceId);
  }

  /**
   * Validates whether a given token is valid
   *
   * <p>A token is considered valid if it is greater than or equal to the current token for the
   * resource. This allows for both current and future tokens to be valid, which is useful in
   * distributed scenarios where tokens might be generated on different nodes.
   */
  public boolean validateToken(String resourceId, long token) {
    String tokenName = FENCING_TOKEN_PREFIX + resourceId;
    RedisAtomicCounter tokenCounter = getAtomicCounter(tokenName);

    long currentToken = tokenCounter.get();
    boolean isValid = token >= currentToken;

    log.debug(
        "Token validation for resource {}: token={}, current={}, valid={}",
        resourceId,
        token,
        currentToken,
        isValid);

    return isValid;
  }

  /** Checks if a token value is valid. */
  public boolean isValidToken(long token) {
    return token > 0;
  }

  public boolean isNewerThanCurrent(String resourceId, long token) {
    long currentToken = getCurrentToken(resourceId);
    return token > currentToken;
  }

  /**
   * cleanup operation not supported.
   *
   * <p>This method is intentionally a no-op because fencing tokens must maintain their monotonic
   * ordering for safety. Removing or resetting tokens could lead to split-brain scenarios or data
   * corruption in distributed systems.
   *
   * <p>If cleanup is absolutely necessary, consider using {@link #setToken(String, long)} to set a
   * higher token value instead.
   */
  public void cleanup(String resourceId) {
    log.debug("No Operation, fencing tokens must maintain monotonic ordering for safety");
    log.debug("No cleanup for fencing token resource: {} ", resourceId);
  }

  public String getTokenName(String resourceId) {
    return FENCING_TOKEN_PREFIX + resourceId;
  }
}
