package com.solum.xplain.shared.datagrid;

import java.util.concurrent.TimeUnit;
import org.jspecify.annotations.NullMarked;

@NullMarked
public interface LockManager<T extends ClusterLock> {
  boolean acquireWithHeartbeat(T lock);

  boolean acquireWithHeartbeat(T lock, long timeout, TimeUnit unit) throws InterruptedException;

  void release(T lock);

  void shutdown();

  void startHeartbeat(T lock);
}
