package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.ClusterLock;
import com.solum.xplain.shared.datagrid.LockManager;
import jakarta.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * Manages distributed locks with automatic heartbeat functionality
 *
 * <p>Key features are:
 *
 * <ul>
 *   <li>Automatic lock acquisition with heartbeat monitoring
 *   <li>Self-cleaning heartbeat tasks that remove themselves on failure
 *   <li>Graceful shutdown with resource cleanup
 * </ul>
 */
@Getter
@Slf4j
@Component
@NullMarked
public class RedisLockManager<T extends ClusterLock> implements LockManager<T> {

  public static final long RENEWAL_INTERVAL_MS = 7000;

  private final ScheduledExecutorService heartbeatScheduler;
  private final ConcurrentHashMap<String, HeartbeatTask<T>> heartbeatTasksByLockId;

  /**
   * Inner class to manage heartbeat tasks for individual locks.
   *
   * @param <T>
   */
  private static class HeartbeatTask<T extends ClusterLock> {
    private final T lock;
    private final String lockKey;
    private volatile @Nullable ScheduledFuture<?> futureHeartBeat;
    private volatile boolean hasHeartBeatStopped = false;
    private @Nullable Runnable cleanupCallback;

    HeartbeatTask(T lock, String lockKey, @Nullable Runnable cleanupCallback) {
      this.lock = lock;
      this.lockKey = lockKey;
      this.cleanupCallback = cleanupCallback;
    }

    void setFutureHeartBeat(ScheduledFuture<?> futureHeartBeat) {
      this.futureHeartBeat = futureHeartBeat;
    }

    void setCleanupCallback(Runnable cleanupCallback) {
      this.cleanupCallback = cleanupCallback;
    }

    /** Extends the heartbeat and the TTL of the current lock if it's still valid. */
    void renew() {
      if (hasHeartBeatStopped) return;

      try {
        if (!lock.isCurrentLockValid() || lock.isLockExpired()) {
          log.warn("Lock no longer valid or expired, stopping heartbeat: {}", lockKey);
          stopAndCleanup();
          return;
        }
        boolean renewed = lock.renewLock();
        if (!renewed) {
          log.warn("Failed to renew lock, stopping heartbeat: {}", lockKey);
          stopAndCleanup();
        }
      } catch (Exception e) {
        log.error("Error renewing lock {}: {}", lockKey, e.getMessage(), e);
        stopAndCleanup();
      }
    }

    void stop() {
      hasHeartBeatStopped = true;
      ScheduledFuture<?> currentFuture = this.futureHeartBeat;
      if (currentFuture != null && !currentFuture.isCancelled()) {
        currentFuture.cancel(false);
      }
    }

    void stopAndCleanup() {
      stop();
      try {
        if (cleanupCallback != null) {
          cleanupCallback.run();
        }
      } catch (Exception e) {
        log.warn("Error during heartbeat task cleanup for lock {}: {}", lockKey, e.getMessage());
      }
    }
  }

  /**
   * We use daemon threads for the heartbeat scheduler so that the JVM can exit even if there are
   * active locks. We use 2 threads to ensure that even if one task is stuck, the other can still
   * run.
   */
  public RedisLockManager() {
    this.heartbeatScheduler =
        Executors.newScheduledThreadPool(
            2,
            r -> {
              Thread t = new Thread(r, "lock-heartbeat");
              t.setDaemon(true);
              return t;
            });

    this.heartbeatTasksByLockId = new ConcurrentHashMap<>();
  }

  @Override
  public boolean acquireWithHeartbeat(T lock) {
    if (lock.tryLock()) {
      startHeartbeat(lock);
      log.info("Acquired lock: {}", lock.getName());
      return true;
    }
    return false;
  }

  @Override
  public boolean acquireWithHeartbeat(T lock, long timeout, TimeUnit unit)
      throws InterruptedException {
    if (lock.tryLock(timeout, unit)) {
      startHeartbeat(lock);
      log.debug("Acquired lock with timeout: {}", lock.getName());
      return true;
    }
    return false;
  }

  @Override
  public void release(T lock) {
    String lockKey = lock.getName();

    HeartbeatTask<T> task = heartbeatTasksByLockId.remove(lockKey);
    if (task != null) {
      task.stop();
    }

    try {
      lock.unlock();
      log.info("Released lock: {}", lockKey);
    } catch (Exception e) {
      log.warn("Error releasing lock {}: {}", lockKey, e.getMessage(), e);
    }
  }

  @Override
  public void startHeartbeat(T lock) {
    String lockKey = lock.getName();

    // We create HeartbeatTask without a cleanup callback in the start
    // because the task hasn't been registered in the heartbeatTasksByLockId map,yet.
    // We set the cleanup callback only after registration, so it can safely remove
    // itself from the map when the heartbeat stops or expires
    HeartbeatTask<T> task = new HeartbeatTask<>(lock, lockKey, null);

    // cleanup callback removes the task from the map
    // only if it's still the current task for this lockKey
    Runnable cleanupCallback =
        () ->
            heartbeatTasksByLockId.computeIfPresent(
                lockKey,
                (key, currentTask) -> {
                  if (currentTask == task) {
                    return null;
                  }
                  return currentTask;
                });

    // assign the cleanup callback to the task before scheduling it
    task.setCleanupCallback(cleanupCallback);

    // schedule the heartbeat renewal at fixed rate
    ScheduledFuture<?> future =
        heartbeatScheduler.scheduleAtFixedRate(
            task::renew, RENEWAL_INTERVAL_MS, RENEWAL_INTERVAL_MS, TimeUnit.MILLISECONDS);

    task.setFutureHeartBeat(future);

    // register the task in the map. If there was a previous task, stop and clean it up.
    HeartbeatTask<T> previousTask = heartbeatTasksByLockId.put(lockKey, task);
    if (previousTask != null) {
      previousTask.stopAndCleanup();
      log.debug("Replaced existing heartbeat for lock: {}", lockKey);
    }

    log.info("Started heartbeat for lock: {}", lockKey);
  }

  /**
   * Gracefully shuts down the RedisLockManager by:
   *
   * <ul>
   *   <li>Stopping all active heartbeat tasks that periodically renew locks.
   *   <li>Releasing all currently held locks to avoid lock leakage.
   *   <li>Clearing internal state tracking heartbeat tasks.
   *   <li>Shutting down the scheduler that runs heartbeat tasks and waiting briefly for
   *       termination.
   * </ul>
   *
   * <p>This method is annotated with {@link PreDestroy} to ensure it is invoked automatically when
   * the application context is closing, allowing clean resource release.
   *
   * <p>In case the scheduler does not terminate within 2 seconds, it is forcibly shut down.
   * Interruptions during shutdown are handled by re-interrupting the current thread.
   *
   * <p>Any exceptions encountered while releasing individual locks are caught and logged, to ensure
   * that shutdown proceeds for remaining locks.
   */
  @PreDestroy
  @Override
  public void shutdown() {
    log.info("Shutting down RedisLockManager with {} active locks", heartbeatTasksByLockId.size());

    for (String lockKey : new ArrayList<>(heartbeatTasksByLockId.keySet())) {
      HeartbeatTask<T> task = heartbeatTasksByLockId.remove(lockKey);
      if (task != null) {
        task.stop();
        try {
          task.lock.unlock();
          log.info("Released lock during shutdown: {}", lockKey);
        } catch (Exception e) {
          log.warn("Failed to release lock {} during shutdown: {}", lockKey, e.getMessage(), e);
        }
      }
    }

    heartbeatTasksByLockId.clear();
    heartbeatScheduler.shutdown();

    try {
      if (!heartbeatScheduler.awaitTermination(2, TimeUnit.SECONDS)) {
        heartbeatScheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      heartbeatScheduler.shutdownNow();
      Thread.currentThread().interrupt();
    }

    log.info("RedisLockManager shutdown complete");
  }
}
