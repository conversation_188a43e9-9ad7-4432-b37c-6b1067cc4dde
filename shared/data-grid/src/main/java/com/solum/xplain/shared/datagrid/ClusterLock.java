package com.solum.xplain.shared.datagrid;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ClusterLock extends Lock {
  String getName();

  boolean isCurrentLockValid();

  boolean isLockExpired();

  boolean renewLock();

  default boolean tryLock(Duration duration) throws InterruptedException {
    return tryLock(duration.toMillis(), TimeUnit.MILLISECONDS);
  }
}
