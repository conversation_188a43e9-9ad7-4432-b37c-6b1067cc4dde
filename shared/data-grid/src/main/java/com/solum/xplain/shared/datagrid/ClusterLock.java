package com.solum.xplain.shared.datagrid;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import org.jspecify.annotations.NullMarked;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@NullMarked
public interface ClusterLock extends Lock {
  Logger log = LoggerFactory.getLogger(ClusterLock.class);

  String getName();

  default boolean isCurrentLockValid() {
    log.warn("isCurrentLockValid() called on no-op default implementation");
    return false;
  }

  default boolean isLockExpired() {
    log.warn("isLockExpired() called on no-op default implementation");
    return false;
  }

  default boolean renewLock() {
    log.warn("renewLock() called on no-op default implementation");
    return false;
  }

  default boolean tryLock(Duration duration) throws InterruptedException {
    return tryLock(duration.toMillis(), TimeUnit.MILLISECONDS);
  }
}
