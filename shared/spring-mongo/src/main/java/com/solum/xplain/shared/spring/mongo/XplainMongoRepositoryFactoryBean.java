package com.solum.xplain.shared.spring.mongo;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;

public class XplainMongoRepositoryFactoryBean
    extends org.springframework.data.mongodb.repository.support.MongoRepositoryFactoryBean {

  public XplainMongoRepositoryFactoryBean(Class repositoryInterface) {
    super(repositoryInterface);
  }

  @Override
  protected RepositoryFactorySupport getFactoryInstance(MongoOperations operations) {
    return new XplainMongoRepositoryFactory(operations);
  }
}
