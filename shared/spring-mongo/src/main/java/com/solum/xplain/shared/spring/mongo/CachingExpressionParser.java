/*
 * Copyright 2020-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.solum.xplain.shared.spring.mongo;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.ParserContext;

/**
 * This class is a copy of the package scoped class in spring-data-mongodb that we otherwise don't
 * have access to.
 *
 * <p>Caching variant of {@link ExpressionParser}. This implementation does not support {@link
 * #parseExpression(String, ParserContext) parsing with <PERSON>rse<PERSON>ontex<PERSON>}.
 *
 * <AUTHOR>
 * @since 3.1
 */
class CachingExpressionParser implements ExpressionParser {

  private final ExpressionParser delegate;
  private final Map<String, Expression> cache = new ConcurrentHashMap<>();

  CachingExpressionParser(ExpressionParser delegate) {
    this.delegate = delegate;
  }

  @Override
  public Expression parseExpression(String expressionString) throws ParseException {
    return cache.computeIfAbsent(expressionString, delegate::parseExpression);
  }

  @Override
  public Expression parseExpression(String expressionString, ParserContext context)
      throws ParseException {
    throw new UnsupportedOperationException("Parsing using ParserContext is not supported");
  }
}
