package com.solum.xplain.core.utils.async;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.context.request.RequestAttributes;

/**
 * This implementation does not require a current active request as it keeps its own copy of the
 * attributes.
 */
@RequiredArgsConstructor
class DecoupledRequestAttributes implements RequestAttributes {
  private final Map<Integer, Map<String, Object>> allAttributes;

  static DecoupledRequestAttributes copyOf(RequestAttributes source) {
    if (source == null) {
      return null;
    }

    Stream<Integer> scopes =
        Stream.of(RequestAttributes.SCOPE_REQUEST, RequestAttributes.SCOPE_SESSION);
    @SuppressWarnings("DataFlowIssue")
    Map<Integer, Map<String, Object>> allAttributes =
        scopes.collect(
            Collectors.toMap(
                Function.identity(),
                scope ->
                    Arrays.stream(source.getAttributeNames(scope))
                        .collect(
                            Collectors.toMap(
                                Function.identity(),
                                name -> source.getAttribute(name, scope),
                                (a, b) -> a,
                                HashMap::new))));
    return new DecoupledRequestAttributes(allAttributes);
  }

  @Override
  public Object getAttribute(@NonNull String name, int scope) {
    return allAttributes.getOrDefault(scope, Collections.emptyMap()).get(name);
  }

  @Override
  public void setAttribute(@NonNull String name, @NonNull Object value, int scope) {
    if (allAttributes.containsKey(scope)) {
      allAttributes.get(scope).put(name, value);
    }
  }

  @Override
  public void removeAttribute(@NonNull String name, int scope) {
    if (allAttributes.containsKey(scope)) {
      allAttributes.get(scope).remove(name);
    }
  }

  @Override
  public String @NonNull [] getAttributeNames(int scope) {
    return allAttributes
        .getOrDefault(scope, Collections.emptyMap())
        .keySet()
        .toArray(new String[0]);
  }

  @Override
  public void registerDestructionCallback(
      @NonNull String name, @NonNull Runnable callback, int scope) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object resolveReference(@NonNull String key) {
    throw new UnsupportedOperationException();
  }

  @Override
  public @NonNull String getSessionId() {
    throw new UnsupportedOperationException();
  }

  @Override
  public @NonNull Object getSessionMutex() {
    throw new UnsupportedOperationException();
  }
}
