package com.solum.xplain.core.error;

import static org.slf4j.LoggerFactory.getLogger;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.slf4j.Logger;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class ErrorItem implements LogItem {

  private static final Logger LOG = getLogger(ErrorItem.class);

  private Error reason;

  private String description;

  public ErrorItem(Error reason, Exception ex) {
    this.reason = reason;
    this.description = ex.getMessage();
    LOG.debug(ex.getMessage(), ex);
  }

  @Deprecated(since = "All errors must have description", forRemoval = true)
  public ErrorItem(Error error) {
    this.reason = error;
    this.description = null;
  }

  public interface ListOfErrors extends List<ErrorItem> {

    static List<ErrorItem> from(ErrorItem error) {
      return List.of(error);
    }
  }
}
