package com.solum.xplain.shared.utils.cache;

import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jspecify.annotations.NullMarked;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * This aspect intercepts methods annotated with @CacheUnpaged and uses {@link
 * UnpagedCachingService} to cache the results of the method calls.
 *
 * <p>If the cache needs loading, {@code UnpagedCachingService} provides an unpaged {@link
 * ScrollRequest} to the {@link FetchDelegate}. This then calls the original method with the unpaged
 * version of the {@code ScrolLRequest} as a parameter in place of the originally supplied one.
 */
@RequiredArgsConstructor
@Aspect
@Component
@NullMarked
public class CacheUnpagedAspect {
  private final UnpagedCachingService unpagedCachingService;

  @Around("@annotation(cacheUnpaged)")
  public Object cacheUnpaged(ProceedingJoinPoint pjp, CacheUnpaged cacheUnpaged) {

    FetchDelegate<?> fetcher = new FetchDelegate<>(pjp);
    return unpagedCachingService.cacheUnpaged(
        pjp.getSignature().toString(),
        SecurityContextHolder.getContext().getAuthentication().getPrincipal(),
        fetcher,
        cacheUnpaged.invalidateOnEvent(),
        pjp.getArgs());
  }

  static class FetchDelegate<T> implements UnpagedCachingService.FetchWithScrollRequest<T> {
    private final ProceedingJoinPoint pjp;
    private final Object[] params;

    public FetchDelegate(ProceedingJoinPoint pjp) {
      this.pjp = pjp;
      this.params = Arrays.copyOf(pjp.getArgs(), pjp.getArgs().length);
    }

    @SuppressWarnings("unchecked")
    @Override
    @SneakyThrows
    public ScrollableEntry<T> fetch(ScrollRequest scrollRequest) {
      // Replace the ScrollRequest in the parameters with the unpaged one from UnpagedCachingService
      int scrollRequestIndex = findScrollRequestIndex(params);
      params[scrollRequestIndex] = scrollRequest;
      // Proceed with the original method call using the modified parameters
      return (ScrollableEntry<T>) pjp.proceed(params);
    }

    private int findScrollRequestIndex(Object[] params) {
      for (int i = 0; i < params.length; i++) {
        if (params[i] instanceof ScrollRequest) {
          return i;
        }
      }
      throw new IllegalArgumentException("No ScrollRequest found in parameters");
    }
  }
}
