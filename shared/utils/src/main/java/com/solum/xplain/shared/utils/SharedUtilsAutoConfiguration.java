package com.solum.xplain.shared.utils;

import com.hazelcast.core.HazelcastInstance;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.datagrid.ClusterEventPublisher;
import com.solum.xplain.shared.datagrid.impl.hazelcast.HazelcastSerializationConfigurer;
import com.solum.xplain.shared.utils.cache.CacheUnpagedAspect;
import com.solum.xplain.shared.utils.cache.UnpagedCacheEventHandler;
import com.solum.xplain.shared.utils.cache.UnpagedCachingService;
import com.solum.xplain.shared.utils.ratelimit.RateLimitedOperationService;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@AutoConfiguration
@ComponentScan(basePackageClasses = {RateLimitedOperationService.class})
public class SharedUtilsAutoConfiguration {
  @Configuration
  @ConditionalOnClass(HazelcastInstance.class)
  static class HazelcastSerializationConfiguration {
    @Bean
    HazelcastSerializationConfigurer auditUserSerializationConfigurer() {
      return config -> config.getCompactSerializationConfig().addClass(AuditUser.class);
    }
  }

  @Bean
  CacheUnpagedAspect cacheUnpagedAspect(UnpagedCachingService unpagedCachingService) {
    return new CacheUnpagedAspect(unpagedCachingService);
  }

  @Bean
  UnpagedCacheEventHandler unpagedCacheEventHandler(ClusterEventPublisher clusterEventPublisher) {
    return new UnpagedCacheEventHandler(clusterEventPublisher);
  }

  @Bean
  UnpagedCachingService unpagedCachingService(UnpagedCacheEventHandler unpagedCacheEventHandler) {
    return new UnpagedCachingService(unpagedCacheEventHandler);
  }
}
