package com.solum.xplain.shared.utils.filter;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static java.lang.String.format;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.daterange.DateRange;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.regex.Pattern;
import org.springframework.data.mongodb.core.query.Criteria;

public enum FilterOperation {
  EQUAL {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      if (value1 instanceof String) {
        return where(property).regex("^" + Pattern.quote(String.valueOf(value1)) + "$", "i");
      } else if (value1 instanceof Instant value) {
        return new Criteria()
            .andOperator(
                where(property).gte(value), where(property).lt(value.plus(1, ChronoUnit.DAYS)));
      } else {
        return where(property).is(value1);
      }
    }
  },
  EQUAL_STRICT {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      return where(property).in(clauseValues.valuesToMatch());
    }
  },
  LESS_THAN {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return where(property).lt(value1);
    }
  },
  GREATER_THAN {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return where(property).gt(value1);
    }
  },
  BETWEEN {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      var value2 = getSecondValue(clauseValues, this);
      return new Criteria().andOperator(where(property).gte(value1), where(property).lte(value2));
    }
  },
  IN_RANGE {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return new Criteria()
          .andOperator(
              where(propertyName(property, DateRange.Fields.startDate)).lte(value1),
              where(propertyName(property, DateRange.Fields.endDate)).gte(value1));
    }
  },
  CONTAINS {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return where(property).regex(Pattern.quote(String.valueOf(value1)), "i");
    }
  },
  STARTS_WITH {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return where(property).regex("^" + Pattern.quote(String.valueOf(value1)), "i");
    }
  },
  NOT_CONTAINS {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      var value1 = getValue(clauseValues, this);
      return where(property).not().regex(Pattern.quote(String.valueOf(value1)), "i");
    }
  },
  BLANK {
    @Override
    public Criteria criteria(String property, FilterClauseValues clauseValues) {
      return new Criteria()
          .orOperator(where(property).is(null), where(property).is(""), where(property).size(0));
    }
  };

  public static Optional<FilterOperation> parse(String s) {
    if ("equal".equals(s)) {
      return Optional.of(EQUAL);
    } else if ("equalstrict".equals(s)) {
      return Optional.of(EQUAL_STRICT);
    } else if ("lessthan".equals(s)) {
      return Optional.of(LESS_THAN);
    } else if ("greaterthan".equals(s)) {
      return Optional.of(GREATER_THAN);
    } else if ("between".equals(s)) {
      return Optional.of(BETWEEN);
    } else if ("inrange".equals(s)) {
      return Optional.of(IN_RANGE);
    } else if ("contains".equals(s)) {
      return Optional.of(CONTAINS);
    } else if ("startswith".equals(s)) {
      return Optional.of(STARTS_WITH);
    } else if ("notcontains".equals(s)) {
      return Optional.of(NOT_CONTAINS);
    } else if ("blank".equals(s)) {
      return Optional.of(BLANK);
    } else {
      return Optional.empty();
    }
  }

  private static Object getValue(FilterClauseValues clauseValues, FilterOperation operation) {
    return clauseValues
        .getValue()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    format("Operation %s requires a value to match", operation)));
  }

  private static Object getSecondValue(FilterClauseValues clauseValues, FilterOperation operation) {
    return clauseValues
        .getSecondValue()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    format("Operation %s requires a second value to match", operation)));
  }

  public abstract Criteria criteria(String property, FilterClauseValues values);
}
