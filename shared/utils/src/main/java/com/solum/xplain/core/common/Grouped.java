package com.solum.xplain.core.common;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Parameter(
    name = "rowGroupCols",
    in = ParameterIn.QUERY,
    array = @ArraySchema(schema = @Schema(type = "string")),
    description = "Grouping columns")
@Parameter(
    name = "groupKeys",
    in = ParameterIn.QUERY,
    array = @ArraySchema(schema = @Schema(type = "string")),
    description = "Grouping value keys")
public @interface Grouped {}
