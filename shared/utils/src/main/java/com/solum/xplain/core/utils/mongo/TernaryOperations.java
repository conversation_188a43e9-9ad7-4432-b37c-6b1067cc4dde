package com.solum.xplain.core.utils.mongo;

import static com.solum.xplain.core.utils.mongo.MongoVariables.NULL;
import static com.solum.xplain.core.utils.mongo.MongoVariables.REMOVE_DIRECTIVE;
import static org.springframework.data.mongodb.core.aggregation.ComparisonOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Cond.when;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.IfNull.ifNull;

import lombok.RequiredArgsConstructor;
import lombok.experimental.UtilityClass;
import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.ComparisonOperators;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Cond;

@UtilityClass
public class TernaryOperations {
  /**
   * Start creating a new {@link Cond} which checks if a field is null, and, if it is not, returns a
   * value. If the field is null, it returns {@value MongoVariables#REMOVE_DIRECTIVE} (i.e no
   * value). This is intended to be used in a projection operation.
   *
   * <p>Do not confuse this with {@link ConditionalOperators#ifNull(String)} which returns the value
   * of a field or a replacement value if it is null.
   *
   * @param conditionalField the field to check for null
   * @return new instance of {@link OnlyWhenBuilder} to create a {@link Cond}.
   */
  public static OnlyWhenBuilder onlyWhenNotNull(String conditionalField) {
    return new OnlyWhenBuilder(whenNotNull(conditionalField));
  }

  /**
   * Start creating a new {@link Cond} which checks if a field is null, and, if it is not, returns a
   * value. If the field is null, it returns {@value MongoVariables#REMOVE_DIRECTIVE} (i.e no
   * value). This is intended to be used in a projection operation.
   *
   * <p>Do not confuse this with {@link ConditionalOperators#ifNull(String)} which returns the value
   * of a field or a replacement value if it is null.
   *
   * @param expression the expression to evaluate for null
   * @return new instance of {@link OnlyWhenBuilder} to create a {@link Cond}.
   */
  public static OnlyWhenBuilder onlyWhenNotNull(AggregationExpression expression) {
    return new OnlyWhenBuilder(whenNotNull(expression));
  }

  /**
   * Start creating a new {@link Cond} which checks if a field is null, and, if it is, returns a
   * value. If the field is not null, it returns {@value MongoVariables#REMOVE_DIRECTIVE} (i.e no
   * value). This is intended to be used in a projection operation.
   *
   * <p>Do not confuse this with {@link ConditionalOperators#ifNull(String)} which returns the value
   * of a field or a replacement value if it is null.
   *
   * @param conditionalField the field to check for null
   * @return new instance of {@link OnlyWhenBuilder} to create a {@link Cond}.
   */
  public static OnlyWhenBuilder onlyWhenNull(String conditionalField) {
    return new OnlyWhenBuilder(whenNull(conditionalField));
  }

  /**
   * Start creating new {@link Cond} by providing the field reference to check for null in {@code
   * if}.
   *
   * @param conditionalField name of a field to check is null
   * @return never {@literal null}.
   */
  public static Cond.ThenBuilder whenNull(String conditionalField) {
    return when(isNull(conditionalField));
  }

  /**
   * Start creating new {@link Cond} by providing the field reference to check for null in {@code
   * if}.
   *
   * @param expression expression to evaluate if null
   * @return never {@literal null}.
   */
  public static Cond.ThenBuilder whenNull(AggregationExpression expression) {
    return when(valueOf(ifNull(expression).then(NULL)).equalToValue(NULL));
  }

  /**
   * Start creating new {@link Cond} by providing the field reference to check for not-null in
   * {@code if}.
   *
   * @param conditionalField name of a field to check is not null
   * @return never {@literal null}.
   */
  public static Cond.ThenBuilder whenNotNull(String conditionalField) {
    return when(isNotNull(conditionalField));
  }

  /**
   * Boolean aggregation operator to check if a field is null.
   *
   * @param conditionalField name of a field to check is null
   * @return never {@literal null}.
   */
  public static ComparisonOperators.Eq isNull(String conditionalField) {
    return valueOf(ifNull(conditionalField).then(NULL)).equalToValue(NULL);
  }

  /**
   * Boolean aggregation operator to check if a field is not null.
   *
   * @param conditionalField name of a field to check is not null
   * @return never {@literal null}.
   */
  public static ComparisonOperators.Ne isNotNull(String conditionalField) {
    return valueOf(ifNull(conditionalField).then(NULL)).notEqualToValue(NULL);
  }

  /**
   * Start creating new {@link Cond} by providing the field reference to check for not-null in
   * {@code if}.
   *
   * @param expression expression to evaluate is not null
   * @return never {@literal null}.
   */
  public static Cond.ThenBuilder whenNotNull(AggregationExpression expression) {
    return when(valueOf(ifNull(expression).then(NULL)).notEqualToValue(NULL));
  }

  @RequiredArgsConstructor
  public static class OnlyWhenBuilder {
    private final Cond.ThenBuilder thenBuilder;

    /**
     * @param fieldReference must not be {@literal null}.
     * @return the {@link Cond}
     */
    public Cond thenValueOf(String fieldReference) {
      return thenBuilder.thenValueOf(fieldReference).otherwise(REMOVE_DIRECTIVE);
    }

    /**
     * @param value the value to be used if the condition evaluates {@literal true}. Can be a {@link
     *     Document}, a value that is supported by MongoDB or a value that can be converted to a
     *     MongoDB representation but must not be {@literal null}.
     * @return the {@link Cond}
     */
    public Cond then(Object value) {
      return thenBuilder.then(value).otherwise(REMOVE_DIRECTIVE);
    }

    /**
     * @param expression must not be {@literal null}.
     * @return the {@link Cond}
     */
    public Cond thenValueOf(AggregationExpression expression) {
      return thenBuilder.thenValueOf(expression).otherwise(REMOVE_DIRECTIVE);
    }
  }
}
