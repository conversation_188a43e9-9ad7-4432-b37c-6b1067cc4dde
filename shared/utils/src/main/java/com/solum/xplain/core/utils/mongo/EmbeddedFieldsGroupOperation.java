package com.solum.xplain.core.utils.mongo;

import static org.springframework.data.mongodb.core.aggregation.Fields.fields;

import java.util.Arrays;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.aggregation.ExposedFields;
import org.springframework.data.mongodb.core.aggregation.ExposedFields.FieldReference;
import org.springframework.data.mongodb.core.aggregation.Field;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.FieldsExposingAggregationOperation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ScriptOperators.Accumulator;
import org.springframework.lang.NonNull;

@ToString
@EqualsAndHashCode
public class EmbeddedFieldsGroupOperation implements FieldsExposingAggregationOperation {
  private static final String DOT_SEPARATOR = "\\.";

  private final Fields idFields;
  private final GroupOperation groupOperation;

  public static EmbeddedFieldsGroupOperation groupEmbedded(String... fields) {
    return groupEmbedded(fields(fields));
  }

  public static EmbeddedFieldsGroupOperation groupEmbedded(Fields idFields) {
    return new EmbeddedFieldsGroupOperation(idFields, new GroupOperation(idFields));
  }

  private EmbeddedFieldsGroupOperation(Fields fields, GroupOperation groupOperation) {
    this.idFields = fields;
    this.groupOperation = groupOperation;
  }

  public static final class GroupOperationBuilder {
    private final Fields idFields;
    private final GroupOperation.GroupOperationBuilder builder;

    private GroupOperationBuilder(Fields idFields, GroupOperation.GroupOperationBuilder builder) {
      this.builder = builder;
      this.idFields = idFields;
    }

    public EmbeddedFieldsGroupOperation as(String alias) {
      var groupOperation = builder.as(alias);
      return new EmbeddedFieldsGroupOperation(idFields, groupOperation);
    }
  }

  public GroupOperationBuilder count() {
    return newBuilder(groupOperation.count());
  }

  public GroupOperationBuilder sum(String reference) {
    return newBuilder(groupOperation.sum(reference));
  }

  public GroupOperationBuilder sum(AggregationExpression expr) {
    return newBuilder(groupOperation.sum(expr));
  }

  public GroupOperationBuilder addToSet(String reference) {
    return newBuilder(groupOperation.addToSet(reference));
  }

  public GroupOperationBuilder addToSet(Object value) {
    return newBuilder(groupOperation.addToSet(value));
  }

  public GroupOperationBuilder last(String reference) {
    return newBuilder(groupOperation.last(reference));
  }

  public GroupOperationBuilder last(AggregationExpression expr) {
    return newBuilder(groupOperation.last(expr));
  }

  public GroupOperationBuilder first(String reference) {
    return newBuilder(groupOperation.first(reference));
  }

  public GroupOperationBuilder first(AggregationExpression expr) {
    return newBuilder(groupOperation.first(expr));
  }

  public GroupOperationBuilder avg(String reference) {
    return newBuilder(groupOperation.avg(reference));
  }

  public GroupOperationBuilder avg(AggregationExpression expr) {
    return newBuilder(groupOperation.avg(expr));
  }

  public GroupOperationBuilder push(String reference) {
    return newBuilder(groupOperation.push(reference));
  }

  public GroupOperationBuilder push(Object value) {
    return newBuilder(groupOperation.push(value));
  }

  public GroupOperationBuilder min(String reference) {
    return newBuilder(groupOperation.min(reference));
  }

  public GroupOperationBuilder min(AggregationExpression expr) {
    return newBuilder(groupOperation.min(expr));
  }

  public GroupOperationBuilder max(String reference) {
    return newBuilder(groupOperation.max(reference));
  }

  public GroupOperationBuilder max(AggregationExpression expr) {
    return newBuilder(groupOperation.max(expr));
  }

  public GroupOperationBuilder stdDevSamp(String reference) {
    return newBuilder(groupOperation.stdDevSamp(reference));
  }

  public GroupOperationBuilder stdDevSamp(AggregationExpression expr) {
    return newBuilder(groupOperation.stdDevSamp(expr));
  }

  public GroupOperationBuilder stdDevPop(String reference) {
    return newBuilder(groupOperation.stdDevPop(reference));
  }

  public GroupOperationBuilder stdDevPop(AggregationExpression expr) {
    return newBuilder(groupOperation.stdDevPop(expr));
  }

  public GroupOperationBuilder accumulate(Accumulator accumulator) {
    return newBuilder(groupOperation.accumulate(accumulator));
  }

  private GroupOperationBuilder newBuilder(GroupOperation.GroupOperationBuilder builder) {
    return new GroupOperationBuilder(idFields, builder);
  }

  @NonNull
  @Override
  public ExposedFields getFields() {
    return groupOperation.getFields();
  }

  @NonNull
  @Override
  public Document toDocument(@NonNull AggregationOperationContext context) {
    var document = groupOperation.toDocument(context);
    var operation = (Document) document.get(groupOperation.getOperator());
    operation.put(Fields.UNDERSCORE_ID, idDocument(context));
    return new Document(getOperator(), operation);
  }

  private Object idDocument(AggregationOperationContext context) {
    var fields = idFields.asList();
    if (fields.isEmpty()) {
      return null;
    } else if (fields.size() == 1) {
      FieldReference reference = context.getReference(fields.get(0));
      return reference.toString();
    }
    Document inner = new Document();

    for (Field field : fields) {
      var splitFields = Arrays.asList(field.getTarget().split(DOT_SEPARATOR));
      FieldReference reference = context.getReference(field);
      expandIdFields(inner, splitFields, reference.toString());
    }
    return inner;
  }

  private void expandIdFields(@NonNull Document doc, List<String> paths, String ref) {
    var result = doc;
    for (int i = 0; i < paths.size() - 1; i++) {
      var path = paths.get(i);
      result = fillLeaf(result, path);
    }
    result.put(paths.get(paths.size() - 1), ref);
  }

  private Document fillLeaf(Document document, String path) {
    var emptyDoc = new Document();
    var result = (Document) document.putIfAbsent(path, emptyDoc);
    if (result == null) {
      result = emptyDoc;
    }
    return result;
  }

  @NonNull
  @Override
  public String getOperator() {
    return "$group";
  }
}
