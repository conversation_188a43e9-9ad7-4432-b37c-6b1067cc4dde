package com.solum.xplain.core.utils.mongo;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MongoVariables {
  public static final String VALUE_PREFIX = "$$";
  public static final String POSITIONAL_OPERATOR = "$";
  public static final String REMOVE_DIRECTIVE = "$$REMOVE";
  public static final String NULL = "___NULL___";
  public static final String EQUALS_CONDITION = "$eq";
  public static final String TO_OBJECT_ID_CONVERSION = "$toObjectId";
  public static final String EXPRESSION = "$expr";
}
