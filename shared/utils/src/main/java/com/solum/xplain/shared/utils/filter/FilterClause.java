package com.solum.xplain.shared.utils.filter;

import java.util.List;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.mongodb.core.query.Criteria;

public interface FilterClause {
  FilterClause EMPTY_FILTER =
      new FilterClause() {
        @Override
        public boolean isNotEmpty() {
          return false;
        }

        @Override
        public Criteria criteria(
            String propertyPrefix,
            Class<?> type,
            List<NestedCollectionMapping> mappings,
            ConversionService conversionService) {
          return new Criteria();
        }
      };

  static FilterClause emptyFilter() {
    return EMPTY_FILTER;
  }

  default boolean isNotEmpty() {
    return true;
  }

  default Criteria criteria(Class<?> type, ConversionService conversionService) {
    return criteria(null, type, List.of(), conversionService);
  }

  Criteria criteria(
      String propertyPrefix,
      Class<?> type,
      List<NestedCollectionMapping> mappings,
      ConversionService conversionService);
}
