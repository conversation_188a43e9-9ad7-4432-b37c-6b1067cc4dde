package com.solum.xplain.shared.utils.cache;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * This annotation can be applied to any method which takes a {@link
 * com.solum.xplain.core.common.ScrollRequest} parameter and returns a {@link
 * com.solum.xplain.core.common.ScrollableEntry}. Its behaviour is undefined if you apply it to any
 * other kind of method (eventually it may be extended to support other types of paging/return
 * value).
 *
 * <p>It is intended to be used for controller service layer methods where paging cannot be done
 * efficiently in the repository - so that fetching all results is not much more expensive than
 * fetching a page of results. Without passing the whole result set to the client, we can still
 * cache the results for a user and return them in pages on subsequent requests.
 *
 * <p>It changes the behaviour of the annotated method so that:
 *
 * <ul>
 *   <li>the method is always called with a {@link com.solum.xplain.core.common.ScrollRequest} that
 *       has a limit of 0 and an offset of 0, i.e. is not paged. Sorting will be preserved.
 *   <li>the return value will apply the paging in memory to the unpaged results
 *   <li>the result of calling the method is cached in a per-user cache for 30 seconds
 *   <li>further calls to the method by the same user with the same parameters will return a page of
 *       the cached results
 * </ul>
 *
 * <p>Note that the cache key is based on the method signature (including parameter types), the
 * current user, and all the parameters in a composite key. If a parameter value should be amended
 * when used for a cache key, it can implement {@link UnpagedCacheAware} and provide an alternative
 * key value. Currently, this is used for the {@link com.solum.xplain.core.common.ScrollRequest}
 * parameter itself, which returns only its sort criteria for the cache key, and {@link
 * com.solum.xplain.core.common.versions.BitemporalDate}, which does not include the {@code
 * recordDate} in the cache key.
 *
 * <p>Potential future extensions:
 *
 * <ul>
 *   <li>Support for different paging parameters e.g. Pageable
 *   <li>Support for different return types e.g. List, Stream
 *   <li>Annotation parameter to control whether per-user caching is required for this method
 *   <li>Annotation parameter to control when the cache for this method is invalidated (e.g. by
 *       application event type)
 *   <li>Support for setting web response cache headers
 *   <li>Configurable cache expiry
 * </ul>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Inherited
@Documented
public @interface CacheUnpaged {
  /**
   * Defines the events which will invalidate the cache for this method. If any of these events are
   * published by the application, the cache for this method will be invalidated for all users.
   *
   * @return array of events which will invalidate the cache
   */
  Class<?>[] invalidateOnEvent() default {};
}
