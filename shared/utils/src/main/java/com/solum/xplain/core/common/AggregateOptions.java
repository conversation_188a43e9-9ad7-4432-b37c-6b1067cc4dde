package com.solum.xplain.core.common;

import org.springframework.data.mongodb.core.aggregation.AggregationOptions;

public class AggregateOptions {

  public static final AggregationOptions ALLOW_DISK_USE =
      AggregationOptions.builder().allowDiskUse(true).build();

  public static final AggregationOptions ALLOW_DISK_USE_BATCH_1000 =
      AggregationOptions.builder().allowDiskUse(true).cursorBatchSize(1000).build();
}
