package com.solum.xplain.shared.utils.filter

import com.solum.xplain.core.common.daterange.DateRange
import java.time.LocalDate
import java.time.LocalDateTime

class FilteredClass {

  enum FilteredEnum {
    ENUM_VALUE
  }

  class NestedClass {
    String stringProperty
    Integer integerProperty
  }

  String stringProperty
  LocalDate dateProperty
  LocalDateTime dateTimeProperty
  FilteredEnum enumProperty
  BigDecimal bigDecimalProperty
  Integer integerProperty
  NestedClass nestedClassProperty
  List<NestedClass> collectionProperty
  DateRange dateRangeProperty
}
