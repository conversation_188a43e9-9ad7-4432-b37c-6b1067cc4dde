package com.solum.xplain.shared.utils.cache

import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import org.springframework.data.domain.Sort
import spock.lang.Specification

class UnpagedCachingServiceTest extends Specification {
  private final eventHandler = Mock(UnpagedCacheEventHandler)
  static class DataView {
    String id
  }

  static class TestEvent {
    String type
  }

  def "should call provided function with scroll request containing sort only"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we only called the function once to fetch the unpaged data"
    1 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }

    and: "event handler was called to register event mapping"
    2 * eventHandler.registerEventMapping("test1", [] as Class<?>[])
  }

  def "should use separate caches for different cacheIds"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged(2L, "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we called the function twice because the cache IDs were different"
    2 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }
  }

  def "should cache separately for different users"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged("test1", "user2", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we called the function twice because the cache IDs were different"
    2 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }
  }

  def "should register event mappings when invalidateOnEvent is provided"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)
    def eventClasses = [TestEvent] as Class<?>[]

    when:
    service.cacheUnpaged("test1", "user1", function, eventClasses, ScrollRequest.of(0, 1, Sort.by("id")))

    then:
    1 * eventHandler.registerEventMapping("test1", eventClasses)
    1 * function.fetch(_) >> ScrollableEntry.of(data, ScrollRequest.of(0, 0, Sort.by("id")))
  }

  def "should invalidate specific cache when UnpagedCacheInvalidationEvent is received"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when: "cache some data"
    service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))

    and: "invalidate the cache via event"
    service.invalidateCache(new UnpagedCacheInvalidationEvent("test1"))

    and: "fetch again"
    service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))

    then: "function should be called twice since cache was invalidated"
    2 * function.fetch(_) >> ScrollableEntry.of(data, ScrollRequest.of(0, 0, Sort.by("id")))
  }
}
