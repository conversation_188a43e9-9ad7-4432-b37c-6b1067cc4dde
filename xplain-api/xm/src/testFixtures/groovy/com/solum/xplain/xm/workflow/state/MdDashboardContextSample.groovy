package com.solum.xplain.xm.workflow.state

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper.EXC_RESULT_MAPPER

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationBuilder
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType
import com.solum.xplain.xm.excmngmt.process.value.BreakTestCalculationsOverlay
import com.solum.xplain.xm.excmngmt.rules.BreakTest
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rules.value.TestScope
import com.solum.xplain.xm.excmngmt.rules.value.TestType
import java.time.LocalDate

trait MdDashboardContextSample {
  static String MARKET_DATA_GROUP_ID = "mdgId"
  static String MARKET_DATA_GROUP_NAME = "mdgName"
  static String CURVE_GROUP_ID_1 = "cgId1"
  static String CURVE_GROUP_ID_2 = "cgId2"
  static String CURVE_CONFIG_ID_1 = "ccId1"
  static String CURVE_CONFIG_NAME_1 = "CC1"
  static String CURVE_CONFIG_ID_2 = "ccId2"
  static String CURVE_CONFIG_NAME_2 = "CC2"
  static String CURVE_CONFIG_ID_3 = "ccId3"
  static String CURVE_CONFIG_NAME_3 = "CC3"
  static String PROVIDER_1 = "PROVIDER1"
  static String PROVIDER_2 = "PROVIDER2"
  static String BREAK_TEST_1 = "BT1"
  static String BREAK_TEST_2 = "BT2"
  static String BREAK_TEST_3 = "BT3"
  static BitemporalDate STATE_DATE = BitemporalDate.newOfNow()
  static LocalDate PREVIOUS_DATE = LocalDate.now().minusDays(1)

  MdDashboardContext mdDashboardContext() {
    new MdDashboardContext(
      MARKET_DATA_GROUP_ID,
      MARKET_DATA_GROUP_NAME,
      curveConfigurationInstrumentResolvers(),
      STATE_DATE,
      PREVIOUS_DATE,
      [],
      UserBuilder.user()
      )
  }

  MdKeyContext mdKeyContext(List<PreliminaryBreakResults> cleanData = []) {
    new MdKeyContext(
      STATE_DATE,
      PREVIOUS_DATE,
      MARKET_DATA_GROUP_ID,
      MARKET_DATA_GROUP_NAME,
      InstrumentDefinitionBuilder.DUMMY_INSTRUMENT,
      [
        ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID),
        ProviderData.of(PROVIDER_2, 1.0, 0.9, ValueBidAskType.BID)
      ],
      cleanData,
      [
        new PreliminaryBreakResults(ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID),[
          new InstrumentResultBreak(breakTestName: BREAK_TEST_1, operator: Operator.GT, threshold: 1.0, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: true)),
          new InstrumentResultBreak(breakTestName: BREAK_TEST_2, operator: Operator.GT, threshold: null, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: false))
        ] ),
        new PreliminaryBreakResults(ProviderData.of(PROVIDER_2, 1.0, 0.9, ValueBidAskType.BID),[
          new InstrumentResultBreak(breakTestName: BREAK_TEST_1, operator: Operator.GT, threshold: 1.0, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: true)),
          new InstrumentResultBreak(breakTestName: BREAK_TEST_2, operator: Operator.GT, threshold: null, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: false))
        ] )
      ],
      curveConfigurationProviders()
      )
  }

  List<CurveConfigurationProviders> curveConfigurationProviders() {
    [
      new CurveConfigurationProviders(CURVE_CONFIG_ID_1, CURVE_CONFIG_NAME_1, CURVE_GROUP_ID_1, new MarketDataProviders(primary: PROVIDER_1, secondary: PROVIDER_2)),
      new CurveConfigurationProviders(CURVE_CONFIG_ID_2, CURVE_CONFIG_NAME_2, CURVE_GROUP_ID_2, new MarketDataProviders(primary: PROVIDER_1)),
      new CurveConfigurationProviders(CURVE_CONFIG_ID_3, CURVE_CONFIG_NAME_3, CURVE_GROUP_ID_1, new MarketDataProviders(primary: PROVIDER_2, secondary: PROVIDER_1)),
    ]
  }

  MdPreliminaryContext mdPreliminaryContext() {
    new MdPreliminaryContext(
      STATE_DATE,
      STATE_DATE.actualDate.minusDays(2),
      MARKET_DATA_GROUP_ID,
      MARKET_DATA_GROUP_NAME,
      EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT),
      ValueBidAskType.BID,
      PROVIDER_1,
      [ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID)],
      [
        new InstrumentResultBreak(breakTestName: BREAK_TEST_1, operator: Operator.GT, threshold: 1.0, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: true)),
        new InstrumentResultBreak(breakTestName: BREAK_TEST_2, operator: Operator.GT, threshold: null, breakTestType: TestType.VALUE.name(), providerValue: new EntryResultBreakByProvider(triggered: false))
      ],
      false
      )
  }

  MdOverlayContext mdOverlayContext() {
    new MdOverlayContext(
      STATE_DATE,
      MARKET_DATA_GROUP_ID, MARKET_DATA_GROUP_NAME,
      EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT),
      ValueBidAskType.BID,
      CURVE_CONFIG_ID_1,
      CURVE_CONFIG_NAME_1,
      CURVE_GROUP_ID_1,
      new MarketDataProviders(primary: PROVIDER_1, secondary: PROVIDER_2),
      [ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID)],
      [ProviderData.of(PROVIDER_1, 0.9, 0.9, ValueBidAskType.BID)],
      [
        new InstrumentResultBreak(breakTestName: BREAK_TEST_1, providerValue: new EntryResultBreakByProvider(triggered: true))
      ],
      new InstrumentResultResolution(resolution: InstrumentResultResolutionType.PREVIOUS_DAY, provider: PROVIDER_1, value: 0.9, resolutionComment: "gone back"),
      BreakTestCalculationsOverlay.ofBreakTests(
      [(BREAK_TEST_1): new BreakTest(scope: TestScope.PRELIMINARY, name: BREAK_TEST_1, operator: Operator.GT, threshold: [1.0], type: TestType.VALUE),
        (BREAK_TEST_2): new BreakTest(scope: TestScope.PRELIMINARY, name: BREAK_TEST_2, operator: Operator.GT, threshold: [1.0], type: TestType.VALUE)]
      ),
      [:]
      )
  }

  ArrayList<CurveConfigurationInstrumentResolver> curveConfigurationInstrumentResolvers() {
    [
      CurveConfigurationInstrumentResolver.fromConfiguration(
      new CurveConfigurationBuilder()
      .entityId(CURVE_CONFIG_ID_1)
      .name(CURVE_CONFIG_NAME_1)
      .curveGroupId(CURVE_GROUP_ID_1)
      .instruments([(CDS):new MarketDataProviders(primary: PROVIDER_1), (FIXED_IBOR_SWAP):new MarketDataProviders(primary: PROVIDER_2)])
      .build()
      ),
      CurveConfigurationInstrumentResolver.fromConfiguration(
      new CurveConfigurationBuilder()
      .entityId(CURVE_CONFIG_ID_2)
      .name(CURVE_CONFIG_NAME_2)
      .curveGroupId(CURVE_GROUP_ID_2)
      .instruments([(CDS):new MarketDataProviders(primary: PROVIDER_1, secondary: PROVIDER_2), (FIXED_IBOR_SWAP):new MarketDataProviders(primary: PROVIDER_2)])
      .build()
      ),
      CurveConfigurationInstrumentResolver.fromConfiguration(
      new CurveConfigurationBuilder()
      .entityId(CURVE_CONFIG_ID_3)
      .name(CURVE_CONFIG_NAME_3)
      .curveGroupId(CURVE_GROUP_ID_1)
      .instruments([(CDS):new MarketDataProviders(primary: PROVIDER_2), (FIXED_IBOR_SWAP):new MarketDataProviders(primary: PROVIDER_2)])
      .build()
      )
    ]
  }
}
