package com.solum.xplain.xm.tasks.entity

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.tasks.enums.TaskGranularityByContractualTerm
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByTradeType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = IpvTasksDefinition, includeSuperProperties = true)
class IpvTasksDefinitionBuilder {
  IpvTasksDefinitionBuilder() {
    type(IpvExceptionManagementPhase.OVERLAY_1)
    teams([
      new ProductTypeTaskTeams(productType: CoreProductType.FXFWD, resolutionTeams: [TeamBuilder.TEAM_ID], approvalTeams: [TeamBuilder.TEAM_ID]),
    ])
    overrides([])
    granularityByTradeType(TaskGranularityByTradeType.TRADE_ASSET_CLASS)
    granularityByRate(TaskGranularityByRateType.NONE)
    granularityByFxCcyPairType(TaskGranularityByFxCcyPairType.NONE)
    granularityBySector(TaskGranularityBySectorType.NONE)
    granularityByContractualTerm(TaskGranularityByContractualTerm.NONE)
  }
}
