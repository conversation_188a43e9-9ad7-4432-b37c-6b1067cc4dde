package com.solum.xplain.xm.excmngmt.rules


import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = AssetFilter)
class AssetFilterBuilder {

  AssetFilterBuilder() {
    assetClasses([])
    irInstruments([CoreInstrumentType.FRA])
    rateCcys([])
    creditSectors([])
    fxPairs([])
  }

  static assetFilter() {
    new AssetFilterBuilder().build()
  }
}
