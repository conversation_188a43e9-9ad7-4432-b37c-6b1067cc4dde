package com.solum.xplain.xm.dashboards.entity

import static com.solum.xplain.core.portfolio.CoreProductType.CDS
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE
import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.EntityReferenceView
import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.company.value.IpvValuationsProvidersView
import com.solum.xplain.core.company.value.PortfolioSettings
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReference
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.trs.value.NonMtmInstrumentType
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.DashboardType
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.views.DashboardEntryVdView
import com.solum.xplain.xm.dashboards.views.DashboardView
import com.solum.xplain.xm.dashboards.views.DateRangeView
import com.solum.xplain.xm.dashboards.views.VdExceptionManagementPortfolioFilterView
import com.solum.xplain.xm.dashboards.views.VdExceptionManagementSetupView
import com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver
import com.solum.xplain.xm.tasks.value.UniqueExcptMngmntTask
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId

class DashboardBuilder {

  public static final String MD_DASHBOARD_ID = ObjectId.get().toHexString()
  public static final String MD_BATCH_DASHBOARD_ID = ObjectId.get().toHexString()
  public static final String TRS_MARKET_DATA_DASHBOARD_ID = ObjectId.get().toHexString()
  public static final String VD_DASHBOARD_ID = ObjectId.get().toHexString()
  public static final String EXCEPTION_MANAGEMENT_ID = "excManagementId"
  public static final String EXISTING_PORTFOLIO_ID = "000000000000000000000003"
  public static final String COMPANY_ID = "000000000000000000000001"
  public static final String PORTFOLIO_ID = "000000000000000000000002"
  public static final String THIRD_PARTY_PROVIDER_ID = "thirdPartyProviderId"

  public static final LocalDate DASHBOARD_DATE = LocalDate.of(2021, 05, 05) // WEDNESDAY
  public static final LocalDate PREVIOUS_DASHBOARD_DATE = LocalDate.of(2021, 05, 04)
  public static final DateRange DASHBOARD_SINGLE_DATE_RANGE = DateRange.newOf(DASHBOARD_DATE)

  public static final LocalDate DASHBOARD_DATE_ANOTHER = LocalDate.of(2021, 06, 02) // MONDAY

  public static final DateRange DASHBOARD_DATE_RANGE = DateRange.newOf(DASHBOARD_DATE, DASHBOARD_DATE_ANOTHER)

  public static final LocalDate STATE_DATE = LocalDate.of(2021, 05, 01)
  public static final AuditUser DASHBOARD_CREATOR = AuditUser.of(user("creatorId"))

  public static final EntityReference MARKET_DATA_GROUP =
  EntityReference.newOf("mdgId", "MARKET DATA GROUP NAME")
  public static final EntityReference MARKET_DATA_GROUP_ANOTHER =
  EntityReference.newOf("mdgIdAnother", "MARKET DATA GROUP NAME ANOTHER")

  public static final IpvDataGroupReference VALUATION_DATA_GROUP =
  IpvDataGroupReference.newOf("vdgId", "VALUATION DATA GROUP NAME", PricingSlot.OTHER)
  public static final IpvDataGroupReference VALUATION_DATA_GROUP_ANOTHER =
  IpvDataGroupReference.newOf("vdgIdAnother", "VALUATION DATA GROUP NAME ANOTHER", PricingSlot.LDN_1200)

  public static final EntityReference CURVE_CONFIGURATION =
  EntityReference.newOf(ObjectId.get().toHexString(), "CURVE CONFIGURATION NAME")
  public static final EntityReference CURVE_CONFIGURATION_ANOTHER =
  EntityReference.newOf(ObjectId.get().toHexString(), "CURVE CONFIGURATION NAME ANOTHER")

  public static final CurveConfigurationInstrumentResolver CURVE_CONFIGURATION_RESOLVER =
  CurveConfigurationInstrumentResolver.fromConfiguration(
  new CurveConfiguration(
  entityId: CURVE_CONFIGURATION.entityId,
  name: CURVE_CONFIGURATION.name,
  curveGroupId: 'curveGroupId',
  instruments: [:] as Map<InstrumentType, MarketDataProviders>,
  overrides: []
  )
  )

  public static final LegalEntityTrsDataProviderResolver LEGAL_ENTITY_DATA_PROVIDER_SETTINGS =
  new LegalEntityTrsDataProviderResolver(
  companyId: "companyId",
  companyExternalId: "EXT_COMPANY",
  legalEntityId: "legalEntityId",
  legalEntityExternalId: "EXT_ENTITY",
  instruments: [:] as Map<NonMtmInstrumentType, MarketDataProviders>,
  )


  public static final EntityReference COMPANY =
  EntityReference.newOf("companyId", "COMPANY_EXTERNAL_ID")
  public static final EntityReference COMPANY_ANOTHER =
  EntityReference.newOf("anotherCompanyId", "ANOTHER_COMPANY_EXTERNAL_ID")

  public static final EntityReference ENTITY =
  EntityReference.newOf("entityId", "ENTITY_EXTERNAL_ID")
  public static final EntityReference ENTITY_ANOTHER =
  EntityReference.newOf("anotherEntityId", "ANOTHER_ENTITY_EXTERNAL_ID")

  public static final EntityReference PORTFOLIO =
  EntityReference.newOf("portfolioId", "PORTFOLIO_EXTERNAL_ID")
  public static final EntityReference PORTFOLIO_NO_XPLAIN_PROVIDER =
  EntityReference.newOf("noXplainPortfolioId", "NO_XPLAIN_PORTFOLIO_EXTERNAL_ID")
  public static final EntityReference PORTFOLIO_ANOTHER =
  EntityReference.newOf("anotherPortfolioId", "ANOTHER_PORTFOLIO_EXTERNAL_ID")

  public static final UniqueExcptMngmntTask UNIQUE_PRELIMINARY_TASK =
  UniqueExcptMngmntTask.preliminaryTask(MD_DASHBOARD_ID, EXCEPTION_MANAGEMENT_ID)
  public static final UniqueExcptMngmntTask UNIQUE_COMPANY_ENITY_OVERLAY_TASK =
  UniqueExcptMngmntTask.overlayTask(MD_DASHBOARD_ID, EXCEPTION_MANAGEMENT_ID, null, COMPANY.entityId, ENTITY.entityId)
  public static final UniqueExcptMngmntTask UNIQUE_COMPANY_OVERLAY_TASK =
  UniqueExcptMngmntTask.overlayTask(MD_DASHBOARD_ID, EXCEPTION_MANAGEMENT_ID, null, COMPANY.entityId, null)
  public static final UniqueExcptMngmntTask UNIQUE_CURVE_CONFIG_OVERLAY_TASK =
  UniqueExcptMngmntTask.overlayTask(MD_DASHBOARD_ID, EXCEPTION_MANAGEMENT_ID, CURVE_CONFIGURATION.entityId, null, null)
  public static final UniqueExcptMngmntTask UNIQUE_ANOTHER_CURVE_CONFIG_OVERLAY_TASK =
  UniqueExcptMngmntTask.overlayTask(MD_DASHBOARD_ID, EXCEPTION_MANAGEMENT_ID, CURVE_CONFIGURATION_ANOTHER.entityId, null, null)

  public static final PortfolioDashboardSettings PORTFOLIO_IPV_SETTINGS =
  new PortfolioDashboardSettings(
  company: COMPANY,
  entity: ENTITY,
  portfolio: PORTFOLIO,
  marketDataGroup: MARKET_DATA_GROUP,
  slaDeadline: SlaDeadline.LDN_1500,
  ipvDataGroupProducts: [
    new IpvDataGroupProducts(
    ipvDataGroup: VALUATION_DATA_GROUP,
    hasXplainProvider: true,
    products: [(IRS) : new IpvValuationsProvidersView(primary: XPLAIN_PROVIDER_CODE),
      (CDS) : new IpvValuationsProvidersView(primary: XPLAIN_PROVIDER_CODE),
      (XCCY): new IpvValuationsProvidersView(primary: THIRD_PARTY_PROVIDER_ID)]
    )
  ],
  )

  public static final PortfolioDashboardSettings PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS =
  new PortfolioDashboardSettings(
  company: COMPANY,
  entity: ENTITY,
  portfolio: PORTFOLIO_NO_XPLAIN_PROVIDER,
  ipvDataGroupProducts: [
    new IpvDataGroupProducts(
    ipvDataGroup: VALUATION_DATA_GROUP_ANOTHER,
    hasXplainProvider: false,
    products: [:]
    )
  ],
  )

  public static final PortfolioDashboardSettings PORTFOLIO_IPV_SETTINGS_ANOTHER =
  new PortfolioDashboardSettings(
  company: COMPANY_ANOTHER,
  entity: ENTITY_ANOTHER,
  portfolio: PORTFOLIO_ANOTHER,
  marketDataGroup: MARKET_DATA_GROUP_ANOTHER,
  ipvDataGroupProducts: [
    new IpvDataGroupProducts(
    ipvDataGroup: VALUATION_DATA_GROUP_ANOTHER,
    hasXplainProvider: false,
    products: [(IRS): new IpvValuationsProvidersView(primary: THIRD_PARTY_PROVIDER_ID),
      (CDS): new IpvValuationsProvidersView(primary: THIRD_PARTY_PROVIDER_ID)]
    )
  ],
  )

  public static final PortfolioSettings<CompanyLegalEntitySettingsView> PORTFOLIO_SETTINGS = PortfolioSettings.settings(
  new PortfolioView(
  id: PORTFOLIO.entityId,
  externalPortfolioId: PORTFOLIO.name,
  companyId: COMPANY.entityId,
  externalCompanyId: COMPANY.name,
  entityId: ENTITY.entityId,
  externalEntityId: ENTITY.name,
  ),
  CompanyLegalEntitySettingsView.newOf(
  new CompanyLegalEntityValuationSettingsView(
  marketDataGroupId: MARKET_DATA_GROUP.entityId,
  marketDataGroupName: MARKET_DATA_GROUP.name,
  ),
  new CompanyLegalEntityIpvSettingsView(
  slaDeadline: SlaDeadline.LDN_1615,
  products: [
    (IRS) : new IpvValuationsProvidersView(
    ipvDataGroupView: new EntityReferenceView(VALUATION_DATA_GROUP.entityId, VALUATION_DATA_GROUP.name),
    primary: XPLAIN_PROVIDER_CODE),
    (XCCY): new IpvValuationsProvidersView(
    ipvDataGroupView: new EntityReferenceView(VALUATION_DATA_GROUP_ANOTHER.entityId, VALUATION_DATA_GROUP_ANOTHER.name),
    primary: XPLAIN_PROVIDER_CODE)
    // other irrelevant
  ]
  )
  )
  )

  public static final PortfolioSettings<CompanyLegalEntitySettingsView> PORTFOLIO_SETTINGS_MIX_PROVIDERS = PortfolioSettings.settings(
  new PortfolioView(
  id: PORTFOLIO.entityId,
  externalPortfolioId: PORTFOLIO.name,
  companyId: COMPANY.entityId,
  externalCompanyId: COMPANY.name,
  entityId: ENTITY.entityId,
  externalEntityId: ENTITY.name,
  ),
  CompanyLegalEntitySettingsView.newOf(
  new CompanyLegalEntityValuationSettingsView(
  marketDataGroupId: MARKET_DATA_GROUP.entityId,
  marketDataGroupName: MARKET_DATA_GROUP.name,
  ),
  new CompanyLegalEntityIpvSettingsView(
  slaDeadline: SlaDeadline.LDN_1615,
  products: [
    (IRS) : new IpvValuationsProvidersView(
    ipvDataGroupView: new EntityReferenceView(VALUATION_DATA_GROUP.entityId, VALUATION_DATA_GROUP.name),
    primary: THIRD_PARTY_PROVIDER_ID),
    (XCCY): new IpvValuationsProvidersView(
    ipvDataGroupView: new EntityReferenceView(VALUATION_DATA_GROUP_ANOTHER.entityId, VALUATION_DATA_GROUP_ANOTHER.name),
    primary: THIRD_PARTY_PROVIDER_ID,
    secondary: XPLAIN_PROVIDER_CODE,
    tertiary: "3",
    quaternary: "4")
    // other irrelevant
  ]
  )
  )
  )

  public static final PortfolioSettings<CompanyLegalEntitySettingsView> PORTFOLIO_SETTINGS_WITH_NULL_IPV_VIEW = PortfolioSettings.settings(
  new PortfolioView(
  id: PORTFOLIO.entityId,
  externalPortfolioId: PORTFOLIO.name,
  companyId: COMPANY.entityId,
  externalCompanyId: COMPANY.name,
  entityId: ENTITY.entityId,
  externalEntityId: ENTITY.name,
  ),
  CompanyLegalEntitySettingsView.newOf(
  new CompanyLegalEntityValuationSettingsView(
  marketDataGroupId: MARKET_DATA_GROUP.entityId,
  marketDataGroupName: MARKET_DATA_GROUP.name,
  ),
  new CompanyLegalEntityIpvSettingsView(
  slaDeadline: SlaDeadline.LDN_1615,
  products: [
    (IRS) : new IpvValuationsProvidersView(
    ipvDataGroupView: new EntityReferenceView(null, null),
    primary: XPLAIN_PROVIDER_CODE),
    (XCCY): new IpvValuationsProvidersView(
    ipvDataGroupView: null,
    primary: XPLAIN_PROVIDER_CODE),
  ]
  )
  )
  )

  public static final MD_BATCH_DASHBOARD = marketDataBatchDashboard()
  public static final MARKET_DATA_DASHBOARD = marketDataDashboard()
  public static final TRS_MARKET_DATA_DASHBOARD = trsMarketDataDashboard()
  public static final MD_AND_TRS_MARKET_DATA_DASHBOARD = mdAndTrsDashboard()
  public static final VD_DASHBOARD = valuationDataDashboard()
  public static final VD_DASHBOARD_VIEW = valuationDataDashboardView()

  static mdAndTrsDashboard() {
    return new Dashboard(
      id: MD_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.MARKET_DATA,
      stateDate: STATE_DATE,
      dateRange: DASHBOARD_SINGLE_DATE_RANGE,
      mdExceptionManagementSetup: MARKET_DATA_DASHBOARD.mdExceptionManagementSetup,
      trsMdExceptionManagementSetup: TRS_MARKET_DATA_DASHBOARD.trsMdExceptionManagementSetup,
      vdExceptionManagementSetup: null
      )
  }

  static marketDataBatchDashboard(LocalDateTime localDateTime = LocalDateTime.now(), DateRange dateRange = DASHBOARD_DATE_RANGE) {
    return new Dashboard(
      id: MD_BATCH_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.MARKET_DATA_BATCH,
      stateDate: STATE_DATE,
      dateRange: dateRange,
      mdExceptionManagementSetup: MdExceptionManagementSetup.newOf(MARKET_DATA_GROUP, [CURVE_CONFIGURATION_RESOLVER], null, false),
      vdExceptionManagementSetup: null,
      startedAt: localDateTime,
      )
  }

  static trsMarketDataBatchDashboard(LocalDateTime localDateTime = LocalDateTime.now(), DateRange dateRange = DASHBOARD_DATE_RANGE) {

    return new Dashboard(
      id: TRS_MARKET_DATA_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.TRS_MARKET_DATA,
      stateDate: STATE_DATE,
      dateRange: DASHBOARD_SINGLE_DATE_RANGE,
      trsMdExceptionManagementSetup: TrsMdExceptionManagementSetup.newOf(
      MARKET_DATA_GROUP_ANOTHER,
      [LEGAL_ENTITY_DATA_PROVIDER_SETTINGS],
      PREVIOUS_DASHBOARD_DATE,
      ),
      vdExceptionManagementSetup: null,
      )
  }

  static marketDataDashboard(LocalDateTime localDateTime = LocalDateTime.now(), DateRange dateRange = DASHBOARD_SINGLE_DATE_RANGE, boolean relevantOnlyFlag = false) {
    return new Dashboard(
      id: MD_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.MARKET_DATA,
      stateDate: STATE_DATE,
      dateRange: dateRange,
      mdExceptionManagementSetup: MdExceptionManagementSetup.newOf(
      MARKET_DATA_GROUP,
      [CURVE_CONFIGURATION_RESOLVER],
      PREVIOUS_DASHBOARD_DATE,
      relevantOnlyFlag
      ),
      vdExceptionManagementSetup: null,
      startedAt: localDateTime
      )
  }

  static trsMarketDataDashboard() {
    return new Dashboard(
      id: MD_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.TRS_MARKET_DATA,
      stateDate: STATE_DATE,
      dateRange: DASHBOARD_SINGLE_DATE_RANGE,
      trsMdExceptionManagementSetup: TrsMdExceptionManagementSetup.newOf(
      MARKET_DATA_GROUP_ANOTHER,
      [LEGAL_ENTITY_DATA_PROVIDER_SETTINGS],
      PREVIOUS_DASHBOARD_DATE,
      ),
      vdExceptionManagementSetup: null,
      )
  }

  static valuationDataDashboard(PortfolioDashboardSettings secondarySettings = PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS, LocalDateTime localDateTime = LocalDateTime.now(), DateRange dateRange = DASHBOARD_SINGLE_DATE_RANGE) {
    return new Dashboard(
      id: VD_DASHBOARD_ID,
      createdBy: DASHBOARD_CREATOR,
      type: DashboardType.VALUATION_DATA,
      stateDate: STATE_DATE,
      dateRange: dateRange,
      startedAt: localDateTime,
      mdExceptionManagementSetup: null,
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [PORTFOLIO_IPV_SETTINGS, secondarySettings]
      ),
      )
  }

  static valuationDataDashboardView() {
    return new DashboardView(
      VD_DASHBOARD_ID,
      DashboardType.VALUATION_DATA,
      null,
      null,
      new VdExceptionManagementSetupView(
      new VdExceptionManagementPortfolioFilterView([]),
      []
      ),
      new DateRangeView(DASHBOARD_DATE, DASHBOARD_DATE),

      [], [], [
        new DashboardEntryVdView(step: DashboardStep.TRADE_DATA_UPLOAD, status: StepStatus.NOT_STARTED),
        new DashboardEntryVdView(step: DashboardStep.OPV_VALUATIONS, status: StepStatus.NOT_STARTED),
        new DashboardEntryVdView(step: DashboardStep.IPV_OVERLAY_CLEARING, status: StepStatus.NOT_STARTED),
        new DashboardEntryVdView(step: DashboardStep.IPV_OVERLAY_CLEARING_2, status: StepStatus.NOT_STARTED)
      ],
      LocalDateTime.now(), null, STATE_DATE, LocalDateTime.now()
      )
  }
}
