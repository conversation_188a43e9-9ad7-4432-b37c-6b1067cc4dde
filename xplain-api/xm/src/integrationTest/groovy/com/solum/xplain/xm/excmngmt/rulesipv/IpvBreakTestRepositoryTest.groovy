package com.solum.xplain.xm.excmngmt.rulesipv

import static IpvBreakTestBuilder.breakTest
import static IpvBreakTestBuilder.chilTest
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.minorVersionedByIdLatest
import static com.solum.xplain.core.portfolio.CoreProductType.CDS
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.CoreProductType.LOAN_NOTE
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_1
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_2
import static com.solum.xplain.xm.excmngmt.rules.value.Operator.EQ
import static com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestOverrideKey.of
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P1
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P3
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType.NULL_VALUE
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType.PRIMARY_VS_SECONDARY
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType.STALE_VALUE
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType.VALUE
import static java.time.ZoneOffset.ofHours
import static java.time.temporal.ChronoUnit.SECONDS
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.EnableForm
import com.solum.xplain.core.common.value.ResequenceForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.MinorVersionedEntity
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestForm
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideForm
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType
import com.solum.xplain.xm.excmngmt.rulesipv.value.ProductTypeFilterForm
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class IpvBreakTestRepositoryTest extends IntegrationSpecification {

  @Resource
  IpvBreakTestRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  private AuditContext audit() {
    return new AuditContext(AuditUser.of(user), LocalDateTime.now().truncatedTo(SECONDS))
  }

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), IpvBreakTest)
  }

  def "should insert break test"() {
    setup:
    def form = IpvBreakTestForm.builder()
      .scope(OVERLAY_1)
      .name("name")
      .type(VALUE)
      .providersTypes([P1])
      .tradeFilter(new ProductTypeFilterForm(productTypes: [IRS]))
      .measureType(IpvMeasureType.VALUE)
      .operator(EQ)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("comment")
      .build()

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    def entityId = result.getOrNull().id
    def loaded = operations.findOne(minorVersionedByIdLatest(entityId), IpvBreakTest.class)
    !loaded.archived
    loaded.createdBy == audit().user()
    that loaded.createdAt.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    that loaded.recordDate.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    loaded.scope == OVERLAY_1
    loaded.sequence == 1
    loaded.enabled
    loaded.comment == "comment"
    loaded.name == "name"
    loaded.type == VALUE
    loaded.providersTypes == [P1]
    loaded.tradeFilter.productTypes == [IRS]
    loaded.tradeFilter.rateCcys == null
    loaded.tradeFilter.fxPairs == null
    loaded.measureType == IpvMeasureType.VALUE
    loaded.operator == EQ
    loaded.threshold == [BigDecimal.ONE]
  }

  def "should insert provider to provider with parent break test"() {
    setup:
    def parentTest = new IpvBreakTestBuilder().sequence(1).build()
    operations.insert(parentTest)

    def form = IpvBreakTestForm.builder()
      .name("name")
      .scope(IpvExceptionManagementPhase.OVERLAY_2)
      .type(PRIMARY_VS_SECONDARY)
      .parentBreakTestId(parentTest.entityId)
      .tradeFilter(new ProductTypeFilterForm(productTypes: [IRS]))
      .measureType(IpvMeasureType.VALUE)
      .operator(EQ)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("comment")
      .build()

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    def entityId = result.getOrNull().id
    def loaded = operations.findOne(minorVersionedByIdLatest(entityId), IpvBreakTest.class)
    !loaded.archived
    loaded.createdBy == audit().user()
    loaded.createdAt != null
    loaded.recordDate != null

    loaded.enabled
    loaded.sequence == 2
    loaded.scope == IpvExceptionManagementPhase.OVERLAY_2
    loaded.comment == "comment"
    loaded.name == "name"
    loaded.type == PRIMARY_VS_SECONDARY
    loaded.providersTypes == null
    loaded.tradeFilter.productTypes == [IRS]
    loaded.tradeFilter.rateCcys == null
    loaded.tradeFilter.fxPairs == null
    loaded.measureType == IpvMeasureType.VALUE
    loaded.operator == EQ
    loaded.threshold == [BigDecimal.ONE]
    loaded.parentTest.parentId == parentTest.entityId
    loaded.parentTest.name == parentTest.name
  }

  def "should update provider to provider with parent break test"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .name("name")
      .scope(OVERLAY_1)
      .type(PRIMARY_VS_SECONDARY)
      .tradeFilter(new TradeFilter())
      .measureType(IpvMeasureType.VALUE)
      .operator(EQ)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("comment")
      .build()
    def parentTest = new IpvBreakTestBuilder().build()
    operations.insertAll([parentTest, breakTest])

    def form = IpvBreakTestForm.builder()
      .name("name")
      .scope(IpvExceptionManagementPhase.OVERLAY_2)
      .type(PRIMARY_VS_SECONDARY)
      .parentBreakTestId(parentTest.entityId)
      .tradeFilter(new ProductTypeFilterForm(productTypes: []))
      .measureType(IpvMeasureType.VALUE)
      .operator(EQ)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("comment")
      .build()

    when:
    def result = repository.update(breakTest.entityId, form)

    then:
    result.isRight()
    def entityId = result.getOrNull().id
    def loaded = operations.findOne(minorVersionedByIdLatest(entityId), IpvBreakTest.class)
    !loaded.archived
    loaded.createdBy == audit().user()
    loaded.createdAt != null

    loaded.recordDate != null

    loaded.enabled
    loaded.scope == IpvExceptionManagementPhase.OVERLAY_2
    loaded.comment == "comment"
    loaded.name == "name"
    loaded.type == PRIMARY_VS_SECONDARY
    loaded.providersTypes == null
    loaded.tradeFilter.productTypes == []
    loaded.tradeFilter.rateCcys == null
    loaded.tradeFilter.fxPairs == null
    loaded.measureType == IpvMeasureType.VALUE
    loaded.operator == EQ
    loaded.threshold == [BigDecimal.ONE]
    loaded.parentTest.parentId == parentTest.entityId
    loaded.parentTest.name == parentTest.name
  }

  def "should update break test"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().sequence(0).build()
    operations.insert(breakTest)
    def form = IpvBreakTestForm.builder()
      .scope(IpvExceptionManagementPhase.OVERLAY_2)
      .sequence(1)
      .name("newName")
      .type(VALUE)
      .measureType(IpvMeasureType.VALUE)
      .providersTypes([P1, P3])
      .tradeFilter(new ProductTypeFilterForm(productTypes: [XCCY]))
      .operator(EQ)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("updated")
      .build()

    when:
    def result = repository.update(breakTest.entityId, form)

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loaded = all[0]
    loaded.entityId == breakTest.entityId
    that loaded.recordDate.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 2)
    loaded.enabled
    loaded.scope == IpvExceptionManagementPhase.OVERLAY_2
    loaded.sequence == 0 // Not updated this way
    loaded.name == "newName"
    loaded.comment == "updated"
    loaded.type == VALUE
    loaded.providersTypes == [P1, P3]
    loaded.tradeFilter.productTypes == [XCCY]
    loaded.measureType == IpvMeasureType.VALUE
    loaded.operator == EQ
    loaded.threshold == [BigDecimal.ONE]
  }

  def "should update ipv break test trade filter"() {
    setup:
    def breakTest = breakTest()
    operations.insert(breakTest)
    def form = IpvBreakTestForm.builder()
      .name(breakTest.name)
      .type(VALUE)
      .measureType(breakTest.measureType)
      .providersTypes(breakTest.providersTypes)
      .tradeFilter(new ProductTypeFilterForm(
      creditSectors: [CreditSector.BASIC_MATERIALS],
      productTypes: [CDS],
      rateCcys: [],
      fxPairs: []))
      .operator(breakTest.operator)
      .threshold(breakTest.threshold)
      .enabled(true)
      .build()

    when:
    def result = repository.update(breakTest.entityId, form)

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loaded = all[0]
    loaded.tradeFilter.productTypes == [CDS]
    loaded.tradeFilter.creditSectors == [CreditSector.BASIC_MATERIALS]
    loaded.tradeFilter.rateCcys == []
    loaded.tradeFilter.fxPairs == []
  }

  def "should not update ipv break test when equals"() {
    setup:
    def breakTest = breakTest()
    operations.insert(breakTest)
    def form = IpvBreakTestForm.builder()
      .scope(OVERLAY_1)
      .sequence(4325) // Ignored for the purposes of change detection on update
      .name(breakTest.name)
      .type(breakTest.type)
      .measureType(breakTest.measureType)
      .providersTypes(breakTest.providersTypes)
      .tradeFilter(new ProductTypeFilterForm(
      productTypes: [IRS],
      rateCcys: ["EUR"],
      fxPairs: ["EUR/USD"]))
      .operator(breakTest.operator)
      .threshold(breakTest.threshold)
      .enabled(true)
      .build()

    when:
    def result = repository.update(breakTest.entityId, form)

    then:
    result.isRight()
    allSorted(breakTest.entityId).size() == 1
  }

  def "should not update internal test"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .type(NULL_VALUE)
      .build()
    operations.insert(breakTest)
    def form = IpvBreakTestForm.builder()
      .name("newName")
      .type(NULL_VALUE)
      .providersTypes([P1, P3])
      .tradeFilter(new ProductTypeFilterForm(productTypes: [XCCY]))
      .comment("updated")
      .build()

    when:
    def result = repository.update(breakTest.entityId, form)

    then:
    result.isLeft()
    def error = result.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
  }

  def "should get break tests"() {
    setup:
    def breakTest = chilTest()
    breakTest.setOverrides([IpvBreakTestOverrideBuilder.override()])
    operations.insert(breakTest)

    def breakTestArchived = new IpvBreakTestBuilder()
      .archived(true)
      .build()
    operations.insert(breakTestArchived)

    def internalTest = new IpvBreakTest(
      name: "XYZ",
      sequence: 23,
      type: STALE_VALUE,
      threshold: [1]
      )
    operations.insert(internalTest)

    def scrolled = ScrollRequest.of(0, 10, Sort.by(Direction.ASC, "name"))


    when:
    def loaded = repository.getAll(emptyTableFilter(), new BreakTestFilter(null), scrolled)

    then:
    loaded.lastRow == 2
    def result = loaded.content
    result.size() == 2
    result[0].id == breakTest.entityId
    result[0].creatorName == user.name
    result[0].createdAt != null
    result[0].comment == "comment"
    result[0].editable
    !result[0].archived
    result[0].numberOfOverrides == 1

    result[0].enabled
    result[0].name == "Test1"
    result[0].scope == OVERLAY_1
    result[0].type == PRIMARY_VS_SECONDARY
    result[0].providersTypes == [P1]
    result[0].tradeFilter.productTypes == [IRS]
    result[0].tradeFilter.rateCcys == ["EUR"]
    result[0].tradeFilter.fxPairs == ["EUR/USD"]
    result[0].measureType == IpvMeasureType.VALUE
    result[0].operator == EQ
    result[0].threshold == [BigDecimal.ONE]
    result[0].parentBreakTestName == "parentName"
    result[0].parentBreakTestId == "parentId"

    result[1].type == STALE_VALUE
    result[1].name == "XYZ"
    result[1].sequence == 23
    result[1].threshold == [1]
    result[1].numberOfOverrides == 0
  }

  def "should get latest not archived tests"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def breakTest = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    def breakTestV2 = new IpvBreakTestBuilder()
      .entityId(breakTest.entityId)
      .recordDate(stateDate.recordDate.minusMinutes(1))
      .build()
    def archived = new IpvBreakTestBuilder()
      .archived(true)
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    operations.insertAll([breakTest, breakTestV2, archived])
    when:
    def result = repository.latestTests(stateDate)

    then:
    result.size() == 1
    result[0].id == breakTestV2.id
  }

  def "should get latest not archived tests for phase"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def breakTestP1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    def breakTestP1V2 = new IpvBreakTestBuilder()
      .entityId(breakTestP1.entityId)
      .recordDate(stateDate.recordDate.minusMinutes(1))
      .sequence(2)
      .build()
    def archivedP1 = new IpvBreakTestBuilder()
      .archived(true)
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    def breakTest2P1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusSeconds(30))
      .sequence(1)
      .build()

    def breakTestP2 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(1))
      .sequence(2)
      .scope(IpvExceptionManagementPhase.OVERLAY_2)
      .build()
    def breakTest2P2 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusSeconds(30))
      .sequence(null)
      .scope(IpvExceptionManagementPhase.OVERLAY_2)
      .build()
    operations.insertAll([breakTestP1, breakTestP1V2, breakTest2P1, archivedP1, breakTestP2, breakTest2P2])

    when:
    def resultP1 = repository.latestPhaseTests(OVERLAY_1, stateDate)

    then:
    resultP1*.id == [breakTest2P1.id, breakTestP1V2.id]

    when:
    def resultP2 = repository.latestPhaseTests(OVERLAY_2, stateDate)

    then:
    resultP2*.id == [breakTest2P2.id, breakTestP2.id]
  }

  def "should not skip phase 1 with no break tests at all"() {
    when:
    def skip = repository.shouldSkipPhase(OVERLAY_1)

    then:
    !skip
  }

  def "should skip phase with no break tests"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def breakTestP1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    operations.insertAll([breakTestP1])

    when:
    def skip = repository.shouldSkipPhase(OVERLAY_2)

    then:
    skip
  }

  def "should skip phase with only disabled break tests"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def breakTestP1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    def breakTestP2 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .scope(OVERLAY_2)
      .enabled(false)
      .build()
    operations.insertAll([breakTestP1, breakTestP2])

    when:
    def skip = repository.shouldSkipPhase(OVERLAY_2)

    then:
    skip
  }

  def "should not skip phase with enabled break test"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def breakTestP1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .build()
    def breakTestP2_1 = new IpvBreakTestBuilder()
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .scope(OVERLAY_2)
      .build()
    def breakTestP2_2 = new IpvBreakTestBuilder()
      .entityId(ObjectId.get().toString())
      .recordDate(stateDate.recordDate.minusMinutes(2))
      .scope(OVERLAY_2)
      .enabled(false)
      .build()
    operations.insertAll([breakTestP1, breakTestP2_1, breakTestP2_2])

    when:
    def skip = repository.shouldSkipPhase(OVERLAY_2)

    then:
    !skip
  }

  def "should get break test versions"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().build()
    operations.insertAll([breakTest, new IpvBreakTestBuilder().entityId(breakTest.entityId).build()])

    when:
    def result = repository.getVersions(breakTest.entityId)

    then:
    result.size() == 2
  }

  def "should get break test single"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .sequence(2)
      .parentTest(BreakTestParent.parentTest("parentId", "parentName"))
      .build()
    operations.insert(breakTest)

    when:
    def result = repository.getOne(breakTest.entityId)

    then:
    result.isRight()
    def resultTest = result.getOrNull()
    resultTest.sequence == 2
    resultTest.parentBreakTestId == "parentId"
    resultTest.parentBreakTestName == "parentName"
    resultTest.comment == "comment"
    resultTest.enabled
    resultTest.numberOfOverrides == 0
  }

  def "should archive break test"() {
    setup:
    def breakTest = breakTest()
    operations.insert(breakTest)

    when:
    def result = repository.archive(breakTest.entityId)

    then:
    result.isRight()
    def loaded = allSorted(breakTest.entityId)
    loaded.size() == 2
    loaded[0].archived
  }

  def "should disable break test"() {
    setup:
    def breakTest = breakTest()
    operations.insert(breakTest)

    when:
    def result = repository.enableDisable(breakTest.entityId, new EnableForm(false))

    then:
    result.isRight()
    def loaded = allSorted(breakTest.entityId)
    loaded.size() == 2
    !loaded[0].enabled
  }

  def "should set NULL break test sequence to null instead of disabling"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().type(NULL_VALUE).sequence(1).build()
    operations.insert(breakTest)

    when:
    def result = repository.enableDisable(breakTest.entityId, new EnableForm(false))

    then:
    result.isRight()
    def loaded = allSorted(breakTest.entityId)
    loaded.size() == 1 // No new version created
    loaded[0].enabled
    loaded[0].sequence == null
  }

  def "should move break test from #fromSequence to #toBeforeSequence"() {
    setup:
    def breakTest1 = new IpvBreakTestBuilder().sequence(1).build()
    def breakTest2 = new IpvBreakTestBuilder().sequence(2).build()
    def breakTest3 = new IpvBreakTestBuilder().sequence(3).build()
    def breakTest4 = new IpvBreakTestBuilder().sequence(4).build()
    def breakTest5 = new IpvBreakTestBuilder().sequence(5).build()
    def breakTests = [breakTest1, breakTest2, breakTest3, breakTest4, breakTest5]
    operations.insertAll(breakTests)


    when:
    def id = breakTests[fromSequence - 1].entityId
    def result = repository.resequence(id, new ResequenceForm(fromSequence, toBeforeSequence))
    def resultingSequence = operations.find(query(where("entityId").in(breakTests*.entityId)).with(Sort.by(MinorVersionedEntity.Fields.entityId)), IpvBreakTest)

    then:
    result.isRight()
    result.right().get().id == id
    resultingSequence*.sequence == expectedSequence

    where:
    fromSequence | toBeforeSequence | expectedSequence
    2            | 2                | [1, 2, 3, 4, 5]
    1            | 3                | [2, 1, 3, 4, 5]
    2            | 4                | [1, 3, 2, 4, 5]
    1            | 5                | [4, 1, 2, 3, 5]
    4            | 2                | [1, 3, 4, 2, 5]
    5            | 1                | [2, 3, 4, 5, 1]
  }

  def "should return correct results if name exists"() {
    setup:
    def breakTest = breakTest()
    operations.insert(breakTest)

    when:
    def resultWithoutExcluding = repository.existsWithName(breakTest.name, null)
    def resultExcluding = repository.existsWithName(breakTest.name, breakTest.entityId)

    then:
    resultWithoutExcluding
    !resultExcluding
  }

  def "should return correct results if break test is archived"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().archived(archived).build()
    operations.insert(breakTest)

    expect:
    exists == repository.existsWithName(breakTest.name, null)

    where:
    archived | exists
    false    | true
    true     | false
  }

  // Overrides

  def "should get all overrides"() {
    setup:
    def override = IpvBreakTestOverrideBuilder.override()
    def breakTest = new IpvBreakTestBuilder()
      .name("Test1")
      .recordDate(LocalDateTime.now().minusSeconds(5))
      .overrides([override])
      .build()

    operations.insert(breakTest)

    def breakTest2 = new IpvBreakTestBuilder()
      .name("Test2")
      .overrides([override])
      .build()

    operations.insert(breakTest2)

    def breakTest3 = new IpvBreakTestBuilder()
      .name("Test3")
      .overrides([override])
      .archived(true)
      .build()
    operations.insert(breakTest3)

    when:
    def result = repository.getAllOverrides()

    then:
    result.size() == 2
    result[0].breakTestName == "Test1"
    result[0].tradeFilter == override.tradeFilter
    result[0].threshold == override.threshold
    result[0].enabled
    result[1].breakTestName == "Test2"
    result[1].tradeFilter == override.tradeFilter
    result[1].threshold == override.threshold
    result[1].enabled
  }

  def "should get break tests overrides"() {
    setup:
    def override = IpvBreakTestOverrideBuilder.override()
    def breakTest = new IpvBreakTestBuilder()
      .name("Test1")
      .overrides([override])
      .build()
    operations.insert(breakTest)

    def breakTest2 = new IpvBreakTestBuilder()
      .name("Test2")
      .overrides([override])
      .build()

    operations.insert(breakTest2)

    when:
    def result = repository.getOverrides(of(breakTest.entityId, null))

    then:
    result.size() == 1
    result[0].breakTestName == "Test1"
    result[0].tradeFilter == override.tradeFilter
    result[0].threshold == override.threshold
    result[0].enabled
  }

  def "should get break test override single"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .name("Test1")
      .overrides([
        new IpvBreakTestOverrideBuilder().id("1").threshold([BigDecimal.ONE]).build(),
        new IpvBreakTestOverrideBuilder().id("2").threshold([BigDecimal.TEN]).build()
      ])
      .build()
    operations.insert(breakTest)

    when:
    def result = repository.getOverride(of(breakTest.entityId, "1"))

    then:
    result.isRight()
    result.right().get().threshold == [BigDecimal.ONE]
  }


  def "should insert override"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().build()
    operations.insert(breakTest)
    def form = overrideForm()

    when:
    def result = repository.createOverride(of(breakTest.entityId, null), form)

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    !all[0].archived
    all[0].createdBy == audit().user()

    def loadedVersion = all[0]
    loadedVersion.overrides.size() == 1
    loadedVersion.overrides[0].tradeFilter == new TradeFilter(productTypes: [LOAN_NOTE])
    loadedVersion.overrides[0].threshold == [BigDecimal.ONE]
    loadedVersion.overrides[0].enabled
  }

  def "should update override"() {
    setup:
    def override = IpvBreakTestOverrideBuilder.override()
    def breakTest = new IpvBreakTestBuilder()
      .overrides([override])
      .build()
    operations.insert(breakTest)

    def form = overrideForm()

    when:
    def result = repository.updateOverride(of(breakTest.entityId, breakTest.overrides[0].id), form)

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loadedVersion = all[0]
    loadedVersion.overrides.size() == 1
    loadedVersion.overrides[0].tradeFilter == new TradeFilter(productTypes: [LOAN_NOTE])
    loadedVersion.overrides[0].threshold == [BigDecimal.ONE]
    loadedVersion.overrides[0].enabled
  }

  def "should fail to update override if not exist"() {
    setup:
    def breakTest = new IpvBreakTestBuilder().build()
    operations.insert(breakTest)

    def form = overrideForm()

    when:
    def result = repository.updateOverride(of(breakTest.entityId, "invalidId"), form)

    then:
    result.isLeft()
    def error = result.left().getOrNull() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND
    error.description == "Break test override not found"
  }

  def "should delete override"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .overrides([new IpvBreakTestOverrideBuilder().build()])
      .build()
    operations.insert(breakTest)

    when:
    def result = repository.deleteOverride(of(breakTest.entityId, breakTest.overrides[0].id))

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loadedVersion = all[0]
    loadedVersion.overrides.size() == 0
  }

  def "should disable override"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .overrides([new IpvBreakTestOverrideBuilder().build()])
      .build()
    operations.insert(breakTest)

    when:
    def result = repository.enableDisableOverride(of(breakTest.entityId, breakTest.overrides[0].id), new EnableForm(false))

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loadedVersion = all[0]
    loadedVersion.overrides.size() == 1
    !loadedVersion.overrides[0].enabled
  }

  def "should clone override"() {
    setup:
    def breakTest = new IpvBreakTestBuilder()
      .overrides([new IpvBreakTestOverrideBuilder().build()])
      .build()
    operations.insert(breakTest)

    when:
    def result = repository.cloneOverride(of(breakTest.entityId, breakTest.overrides[0].id))

    then:
    result.isRight()
    def all = allSorted(breakTest.entityId)
    all.size() == 2
    def loadedVersion = all[0]
    loadedVersion.overrides.size() == 2
    loadedVersion.overrides[0].id == breakTest.overrides[0].id
    loadedVersion.overrides[0].tradeFilter != null
    loadedVersion.overrides[0].threshold != null
    loadedVersion.overrides[1].id != breakTest.overrides[0].id
    loadedVersion.overrides[1].tradeFilter == loadedVersion.overrides[0].tradeFilter
    loadedVersion.overrides[1].threshold == loadedVersion.overrides[0].threshold
  }

  private List<IpvBreakTest> allSorted(String entityId) {
    operations
      .query(IpvBreakTest).matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "recordDate")))
      .all()
  }

  private IpvBreakTestOverrideForm overrideForm() {
    IpvBreakTestOverrideForm.builder()
      .tradeFilter(new ProductTypeFilterForm(productTypes: [LOAN_NOTE]))
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .build()
  }
}
