package com.solum.xplain.xm.workflow.state

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.workflow.value.HistoricStepInstanceView
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.enums.BreakProviderType
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import java.time.LocalDateTime
import org.springframework.beans.MutablePropertyValues
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class MdKeyStateMapperTest extends IntegrationSpecification implements MdDashboardContextSample {
  @Autowired
  MdKeyStateMapper mapper

  def "should map to InstrumentResultPreliminary"() {
    given:
    StepStateOps<MdKeyState, MdKeyContext> ops = Mock()
    ops.getRootBusinessKey() >> "urn:dashboard:dashboardId"
    def context = new MdKeyContext(
      STATE_DATE,
      STATE_DATE.actualDate.minusDays(2),
      MARKET_DATA_GROUP_ID,
      MARKET_DATA_GROUP_NAME,
      InstrumentDefinitionBuilder.DUMMY_INSTRUMENT,
      [ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID)],
      [
        new PreliminaryBreakResults(
        ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID),
        [
          new InstrumentResultBreak(
          breakTestName: "test",
          measureType: MeasureType.VALUE,
          operator: Operator.GT,
          threshold: 1.0,
          providerType: BreakProviderType.PRIMARY,
          providerValue: new EntryResultBreakByProvider(triggered: false, value: 1)
          )
        ]
        )
      ],
      [],
      []
      )
    def state = new MdKeyState()

    when:
    def mapped = mapper.toInstrumentResultPreliminary(context, context.cleanPreliminaryData()[0], ops)

    then:
    mapped.id == null
    mapped.marketDataGroupId == context.marketDataGroupId()
    mapped.dashboardId == "dashboardId"
    mapped.exceptionManagementResultId == null
    mapped.taskId == null
    mapped.valuationDate == context.stateDate().actualDate
    mapped.instrument.key == context.instrument().key
    mapped.status == EntryResultStatus.VERIFIED
    mapped.modifiedBy == null
    mapped.modifiedAt == null
    mapped.providerData == context.allProviderData()[0]
    mapped.allProvidersData == context.allProviderData()
    mapped.breakTests == context.cleanPreliminaryData()[0].breakTestResults()
    mapped.appliedTestsCount == 1
    !mapped.hasBreaks
    mapped.maxTriggeredThresholdLevel == null
    mapped.resolution == InstrumentResultResolution.newOf()
    mapped.previousStatuses.size() == 0
  }
}
