package com.solum.xplain.xm.excmngmt.rulesonboarding

import static com.solum.xplain.core.common.versions.VersionedDataAggregations.minorVersionedByIdLatest
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static com.solum.xplain.xm.excmngmt.rules.value.Operator.GT
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.RELATIVE_DIFF
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.DC_XPLAIN
import static java.time.ZoneOffset.ofHours
import static java.time.temporal.ChronoUnit.SECONDS
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter
import com.solum.xplain.xm.excmngmt.rulesipv.value.ProductTypeFilterForm
import com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingBreakTestForm
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class OnboardingBreakTestRepositoryTest extends IntegrationSpecification {

  @Resource
  OnboardingBreakTestRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  private AuditContext audit() {
    return new AuditContext(AuditUser.of(user), LocalDateTime.now().truncatedTo(SECONDS))
  }

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), OnboardingBreakTest)
  }

  def "should insert break test"() {
    setup:
    def form = OnboardingBreakTestForm.builder()
      .name("name")
      .type(DC_XPLAIN)
      .tradeFilter(new ProductTypeFilterForm(productTypes: [IRS]))
      .measureType(RELATIVE_DIFF)
      .operator(GT)
      .threshold([BigDecimal.ONE])
      .enabled(true)
      .comment("comment")
      .build()

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    def entityId = result.getOrNull().id
    def loaded = operations.findOne(minorVersionedByIdLatest(entityId), OnboardingBreakTest.class)
    !loaded.archived
    loaded.createdBy == audit().user()
    that loaded.createdAt.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    that loaded.recordDate.toEpochSecond(ofHours(0)),
      closeTo(LocalDateTime.now().toEpochSecond(ofHours(0)), 10)
    loaded.enabled
    loaded.sequence == 1
    loaded.comment == "comment"
    loaded.name == "name"
    loaded.type == DC_XPLAIN
    loaded.tradeFilter.productTypes == [IRS]
    loaded.tradeFilter.rateCcys == null
    loaded.tradeFilter.fxPairs == null
    loaded.measureType == RELATIVE_DIFF
    loaded.operator == GT
    loaded.threshold == [BigDecimal.ONE]
  }

  def "should get break tests"() {
    setup:
    def breakTest = new OnboardingBreakTestBuilder().build()
    operations.insert(breakTest)
    operations.insert(new OnboardingBreakTestBuilder().archived(true).build())

    def scrolled = ScrollRequest.of(0, 10, Sort.by(Direction.ASC, "name"))

    when:
    def loaded = repository.getAll(emptyTableFilter(), new BreakTestFilter(), scrolled)

    then:
    loaded.lastRow == 1
    def result = loaded.content
    result.size() == 1
    result[0].id == breakTest.entityId
    result[0].creatorName == user.name
    result[0].createdAt != null
    result[0].comment == "comment"
    result[0].editable
    !result[0].archived
    result[0].numberOfOverrides == 0

    result[0].enabled
    result[0].name == "Test1"
    result[0].sequence == 0
    result[0].type == DC_XPLAIN
    result[0].tradeFilter.productTypes == [IRS]
    result[0].tradeFilter.rateCcys == ["EUR"]
    result[0].tradeFilter.fxPairs == ["EUR/USD"]
    result[0].measureType == RELATIVE_DIFF
    result[0].operator == GT
    result[0].threshold == [BigDecimal.ONE, BigDecimal.ZERO]
    result[0].parentBreakTestName == null
    result[0].parentBreakTestId == null
  }
}
