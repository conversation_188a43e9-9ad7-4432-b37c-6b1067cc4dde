package com.solum.xplain.xm.onboarding

import static com.solum.xplain.core.common.GroupRequest.emptyGroupRequest
import static com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverter.ROOT_DATE
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.FAILED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.NOT_REQUIRED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.SUCCESS
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportItemSubmissionStatus.SUBMISSION_FAIL
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportItemSubmissionStatus.SUBMISSION_SUCCESS
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.READY_FOR_SUBMISSION
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.SUBMITTED
import static java.math.BigDecimal.valueOf

import com.google.common.collect.Streams
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.OnboardingValuationMetrics
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType
import com.solum.xplain.xm.onboarding.entity.OnboardingReport
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class OnboardingReportRepositoryTest extends IntegrationSpecification {

  private static final String PORTFOLIO_ID = "PID"
  private static final LocalDate TRADE_DATE = LocalDate.of(2023, 1, 1)

  @Resource
  OnboardingReportRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), OnboardingReport)
    operations.remove(new Query(), OnboardingReportItem)
  }

  def "should fetch all reports"() {
    setup:
    def report1 = report()
    operations.save(report1)
    def report2 = report()
    operations.save(report2)

    when:
    def result = repository.reports(emptyTableFilter(), ScrollRequest.of(0, 2))

    then:
    result.startRow == 0
    result.endRow == 2
    result.lastRow == 2
    with(result.content[0]) {
      id == report2.id.toHexString()
      stateDate == report2.stateDate
      status == report2.status
      xplainConformity == report2.xplainConformity
      marketConformity == report2.marketConformity
      vendorConformity == report2.vendorConformity
      tradesCount == report2.tradesCount
      breaksCount == report2.breaksCount
    }
    result.content[1].id == report1.id.toHexString()
  }

  def "should fetch single report by id"() {
    setup:
    def report = report()
    operations.save(report)
    operations.save(new OnboardingReport())

    when:
    def result = repository.reportView(report.getId())

    then:
    result.isRight()
    with(result.getOrNull()) {
      id == report.id.toHexString()
      stateDate == report.stateDate
      status == report.status
      xplainConformity == report.xplainConformity
      marketConformity == report.marketConformity
      vendorConformity == report.vendorConformity
      tradesCount == report.tradesCount
      breaksCount == report.breaksCount
    }
  }

  def "should fetch all report items"() {
    setup:
    def reportId = ObjectId.get()
    def itemWrongReportId = new OnboardingReportItem(
      reportId: ObjectId.get(),
      )
    def item = reportItem(reportId)
    def anotherItem = new OnboardingReportItem(
      reportId: reportId,
      )
    operations.insertAll([itemWrongReportId, item, anotherItem])

    when:
    def result = repository.reportItems(reportId, emptyTableFilter(), ScrollRequest.of(0, 1), emptyGroupRequest())

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == null
    with(result.content[0]) {
      trade.externalTradeId == item.trade.externalTradeId
      trade.notional == item.trade.notional
      trade.vendorOnboardingDate == item.trade.vendorOnboardingDate
      xplainCheckStatus == item.xplainCheckStatus
      xplainCheckMessage == item.xplainCheckMessage
      marketCheckStatus == item.marketCheckStatus
      marketCheckMessage == item.marketCheckMessage
      vendorCheckStatus == item.vendorCheckStatus
      vendorCheckMessage == item.vendorCheckMessage
      submissionStatus == item.submissionStatus
      valuationMetrics == item.valuationMetrics
      vendorPrimaryMetrics == item.vendorPrimaryMetrics
      navOnTradeDate == item.navOnTradeDate
      navOnVendorOnboardingDate == item.navOnVendorOnboardingDate
      breakTests.size() == 1
      breakTests[0] == item.breakTests[0]
    }
  }

  def "should save report"() {
    setup:
    def report = report()

    when:
    repository.saveReport(report)

    then:
    operations.findById(report.id, OnboardingReport.class) == report
  }

  def "should save report items"() {
    setup:
    def reportItem = reportItem(ObjectId.get())

    when:
    repository.saveReportItems([reportItem])

    then:
    operations.findAll(OnboardingReportItem.class) == [reportItem]
  }

  @Unroll
  def "should update report items valuation metrics when #matchesCalcId #matchesVendorCalcId "() {
    setup:
    def calculationId = ObjectId.get()
    def reportItem = reportItem(ObjectId.get())
    if (matchesCalcId) {
      reportItem.calculationId = calculationId
    }
    if (matchesVendorCalcId) {
      reportItem.vendorCalculationId = calculationId
    }
    operations.save(reportItem)

    def metrics = new OnboardingValuationMetrics(productType: FXOPT, presentValue: 1, pvDelta: 2, pvVega: 3, br01: 4)
    def metricsMap = Map.of(reportItem, metrics)

    expect:
    repository.updateValuationMetrics(Map.of(), calculationId)
    repository.updateValuationMetrics(metricsMap, calculationId)

    operations.findById(reportItem.id, OnboardingReportItem.class) == reportItem.tap { {
        if (matchesCalcId) {
          it.setValuationMetrics(metrics)
        }
        if (matchesVendorCalcId) {
          it.setVendorPrimaryMetrics(new OnboardingVendorMetrics(
          provider: XPLAIN_PROVIDER_CODE,
          pv: valueOf(metrics.presentValue),
          delta: valueOf(metrics.delta()),
          vega: valueOf(metrics.pvVega)))
        }
      }
    }

    where:
    matchesCalcId | matchesVendorCalcId
    false         | false
    true          | false
    false         | true
    true          | true
  }

  def "should update report items valuation metrics when XPLAIN provider"() {
    setup:
    def calculationId = ObjectId.get()
    def metrics = new OnboardingValuationMetrics(productType: FXOPT, presentValue: 1, pvDelta: 2, pvVega: 3, br01: 4)

    def reportItemPrimary = reportItem(ObjectId.get())
    reportItemPrimary.vendorCalculationId = calculationId
    def reportItemSecondary = reportItem(ObjectId.get())
    reportItemSecondary.vendorPrimaryMetrics = null
    reportItemSecondary.vendorSecondaryMetrics = new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE)
    reportItemSecondary.vendorCalculationId = calculationId
    def reportItemTertiary = reportItem(ObjectId.get())
    reportItemTertiary.vendorPrimaryMetrics = null
    reportItemTertiary.vendorTertiaryMetrics = new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE)
    reportItemTertiary.vendorCalculationId = calculationId
    def reportItemQuaternary = reportItem(ObjectId.get())
    reportItemQuaternary.vendorPrimaryMetrics = null
    reportItemQuaternary.vendorQuaternaryMetrics = new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE)
    reportItemQuaternary.vendorCalculationId = calculationId
    operations.insertAll([reportItemPrimary, reportItemSecondary, reportItemTertiary, reportItemQuaternary])

    def metricsMap = Map.of(
    reportItemPrimary, metrics,
    reportItemSecondary, metrics,
    reportItemTertiary, metrics,
    reportItemQuaternary, metrics,
    )

    when:
    repository.updateValuationMetrics(metricsMap, calculationId)

    then:
    operations.findById(reportItemPrimary.id, OnboardingReportItem.class) == reportItemPrimary.tap { {
        it.setVendorPrimaryMetrics(new OnboardingVendorMetrics(
        provider: XPLAIN_PROVIDER_CODE,
        pv: valueOf(metrics.presentValue),
        delta: valueOf(metrics.delta()),
        vega: valueOf(metrics.pvVega)))
      }
    }
    operations.findById(reportItemSecondary.id, OnboardingReportItem.class) == reportItemSecondary.tap { {
        it.setVendorSecondaryMetrics(new OnboardingVendorMetrics(
        provider: XPLAIN_PROVIDER_CODE,
        pv: valueOf(metrics.presentValue),
        delta: valueOf(metrics.delta()),
        vega: valueOf(metrics.pvVega)))
      }
    }
    operations.findById(reportItemTertiary.id, OnboardingReportItem.class) == reportItemTertiary.tap { {
        it.setVendorTertiaryMetrics(new OnboardingVendorMetrics(
        provider: XPLAIN_PROVIDER_CODE,
        pv: valueOf(metrics.presentValue),
        delta: valueOf(metrics.delta()),
        vega: valueOf(metrics.pvVega)))
      }
    }
    operations.findById(reportItemQuaternary.id, OnboardingReportItem.class) == reportItemQuaternary.tap { {
        it.setVendorQuaternaryMetrics(new OnboardingVendorMetrics(
        provider: XPLAIN_PROVIDER_CODE,
        pv: valueOf(metrics.presentValue),
        delta: valueOf(metrics.delta()),
        vega: valueOf(metrics.pvVega)))
      }
    }
  }

  def "should update report items xplain conformity results"() {
    setup:
    def reportItem1 = reportItem(ObjectId.get())
    def reportItem2 = reportItem(ObjectId.get())
    reportItem2.xplainCheckStatus = REQUESTED
    def reportItem3 = reportItem(ObjectId.get())
    reportItem3.xplainCheckStatus = REQUESTED
    reportItem3.breakTests = []
    operations.insertAll([reportItem1, reportItem2, reportItem3])

    def breakTests = [new OnboardingTradeResultBreak(breakTestName: "BT", breakTestType: OnboardingTestType.DC_XPLAIN)]
    def results = Map.of(
    reportItem1.id, breakTests,
    reportItem2.id, breakTests,
    reportItem3.id, breakTests,
    )

    when:
    repository.updateBreakResults(Map.of(), OnboardingTestType.DC_XPLAIN)
    repository.updateBreakResults(results, OnboardingTestType.DC_XPLAIN)

    then:
    operations.findById(reportItem1.id, OnboardingReportItem.class) == reportItem1
    operations.findById(reportItem2.id, OnboardingReportItem.class) == reportItem2.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem2.breakTests.stream()).toList()
      it.xplainCheckStatus = FAILED
    }
    operations.findById(reportItem3.id, OnboardingReportItem.class) == reportItem3.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem3.breakTests.stream()).toList()
      it.xplainCheckStatus = SUCCESS
    }
  }

  def "should update report items market conformity results"() {
    setup:
    def reportItem1 = reportItem(ObjectId.get())
    def reportItem2 = reportItem(ObjectId.get())
    reportItem2.marketCheckStatus = REQUESTED
    def reportItem3 = reportItem(ObjectId.get())
    reportItem3.marketCheckStatus = REQUESTED
    reportItem3.breakTests = []
    def reportItem4 = reportItem(ObjectId.get())
    reportItem4.marketCheckStatus = REQUESTED
    reportItem4.breakTests = []
    operations.insertAll([reportItem1, reportItem2, reportItem3, reportItem4])

    def breakTests = [new OnboardingTradeResultBreak(breakTestName: "BT", breakTestType: OnboardingTestType.MARKET_CONF)]
    def breakTestsTriggered = [
      new OnboardingTradeResultBreak(breakTestName: "BT", breakTestType: OnboardingTestType.MARKET_CONF, triggered: true)
    ]
    def results = Map.of(
    reportItem1.id, breakTests,
    reportItem2.id, breakTests,
    reportItem3.id, breakTests,
    reportItem4.id, breakTestsTriggered,
    )

    when:
    repository.updateBreakResults(Map.of(), OnboardingTestType.MARKET_CONF)
    repository.updateBreakResults(results, OnboardingTestType.MARKET_CONF)

    then:
    operations.findById(reportItem1.id, OnboardingReportItem.class) == reportItem1
    operations.findById(reportItem2.id, OnboardingReportItem.class) == reportItem2.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem2.breakTests.stream()).toList()
      it.marketCheckStatus = SUCCESS
    }
    operations.findById(reportItem3.id, OnboardingReportItem.class) == reportItem3.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem3.breakTests.stream()).toList()
      it.marketCheckStatus = SUCCESS
    }
    operations.findById(reportItem4.id, OnboardingReportItem.class) == reportItem4.tap {
      it.breakTests = Streams.concat(breakTestsTriggered.stream(), reportItem4.breakTests.stream()).toList()
      it.marketCheckStatus = FAILED
    }
  }

  def "should update report items vendor conformity results"() {
    setup:
    def reportItem1 = reportItem(ObjectId.get())
    def reportItem2 = reportItem(ObjectId.get())
    reportItem2.vendorCheckStatus = REQUESTED
    def reportItem3 = reportItem(ObjectId.get())
    reportItem3.vendorCheckStatus = REQUESTED
    reportItem3.breakTests = []
    operations.insertAll([reportItem1, reportItem2, reportItem3])

    def breakTests = [new OnboardingTradeResultBreak(breakTestName: "BT", breakTestType: OnboardingTestType.AC_P1)]
    def results = Map.of(
    reportItem1.id, breakTests,
    reportItem2.id, breakTests,
    reportItem3.id, breakTests,
    )

    when:
    repository.updateBreakResults(Map.of(), OnboardingTestType.AC_P1)
    repository.updateBreakResults(results, OnboardingTestType.AC_P1)

    then:
    operations.findById(reportItem1.id, OnboardingReportItem.class) == reportItem1
    operations.findById(reportItem2.id, OnboardingReportItem.class) == reportItem2.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem2.breakTests.stream()).toList()
      it.vendorCheckStatus = SUCCESS
    }
    operations.findById(reportItem3.id, OnboardingReportItem.class) == reportItem3.tap {
      it.breakTests = Streams.concat(breakTests.stream(), reportItem3.breakTests.stream()).toList()
      it.vendorCheckStatus = SUCCESS
    }
  }

  def "should fetch report items by report and portfolio"() {
    setup:
    def reportId = ObjectId.get()
    def itemWrongReportId = new OnboardingReportItem(
    reportId: ObjectId.get(),
    )
    def item = reportItem(reportId)
    def itemAnotherPortfolio = reportItem(reportId)
    itemAnotherPortfolio.trade.portfolioId = "ANOTHER_ID"

    operations.insertAll([itemWrongReportId, item, itemAnotherPortfolio])

    when:
    def result = repository.itemsByReportAndPortfolio(reportId, item.trade.portfolioId)

    then:
    result == [item]
  }

  def "should fetch verified report items"() {
    setup:
    def reportId = ObjectId.get()
    def itemWrongReportId = new OnboardingReportItem(
    reportId: ObjectId.get(),
    )
    def item = reportItem(reportId)
    def itemNotVerified = reportItem(reportId)
    itemNotVerified.marketCheckStatus = FAILED

    operations.insertAll([itemWrongReportId, item, itemNotVerified])

    when:
    def result = repository.verifiedReportItems(reportId)

    then:
    result == [item]
  }

  def "should check if all report items processed"() {
    setup:
    def reportId = ObjectId.get()
    def itemWrongReportId = new OnboardingReportItem(
    reportId: ObjectId.get(),
    xplainCheckStatus: REQUESTED
    )
    def item = reportItem(reportId)

    operations.insertAll([itemWrongReportId, item])

    when:
    def result = repository.allReportItemsProcessed(reportId)

    then:
    result
  }

  def "should update report completed"() {
    setup:
    def report = report()
    operations.insert(report)
    def item = reportItem(report.id)
    operations.insert(item)

    when:
    repository.updateReportCompleted(report.id)

    then:
    def updatedReport = operations.findById(report.id, OnboardingReport.class)
    updatedReport.status == READY_FOR_SUBMISSION
    updatedReport.breaksCount == 1
  }

  @Unroll
  def "should update items calculation ids #hasOnTrade #hasOnOnboardingDate"() {
    setup:
    def reportId = ObjectId.get()
    def item = reportItem(reportId)
    def itemXplainRequested = reportItem(reportId)
    itemXplainRequested.xplainCheckStatus = REQUESTED
    def itemXplainRequestedAnotherPortfolio = reportItem(reportId)
    itemXplainRequestedAnotherPortfolio.xplainCheckStatus = REQUESTED
    itemXplainRequestedAnotherPortfolio.trade.portfolioId = "ANOTHER"
    def itemXplainRequestedAnotherDate = reportItem(reportId)
    itemXplainRequestedAnotherDate.xplainCheckStatus = REQUESTED
    itemXplainRequestedAnotherDate.trade.tradeDetails.info.tradeDate = TRADE_DATE.plusDays(1)
    def itemMarketRequested = reportItem(reportId)
    itemMarketRequested.marketCheckStatus = REQUESTED
    def itemVendorRequestedRootOnboardingDate = reportItem(reportId)
    itemVendorRequestedRootOnboardingDate.trade.vendorOnboardingDate = ROOT_DATE
    itemVendorRequestedRootOnboardingDate.vendorCheckStatus = REQUESTED
    def itemVendorRequestedOnboardingDate = reportItem(reportId)
    itemVendorRequestedOnboardingDate.trade.vendorOnboardingDate = TRADE_DATE
    itemVendorRequestedOnboardingDate.vendorCheckStatus = REQUESTED
    operations.insertAll([
      item,
      itemXplainRequested,
      itemXplainRequestedAnotherPortfolio,
      itemXplainRequestedAnotherDate,
      itemMarketRequested,
      itemVendorRequestedRootOnboardingDate,
      itemVendorRequestedOnboardingDate,
    ])

    def calculationId = ObjectId.get()

    when:
    repository.updateValuationItemsCalculationId(reportId, item.trade.portfolioId, TRADE_DATE, calculationId, hasOnTrade, hasOnOnboardingDate)

    then:
    operations.findById(item.id, OnboardingReportItem.class) == item
    operations.findById(itemXplainRequestedAnotherPortfolio.id, OnboardingReportItem.class) == itemXplainRequestedAnotherPortfolio
    operations.findById(itemXplainRequestedAnotherDate.id, OnboardingReportItem.class) == itemXplainRequestedAnotherDate

    operations.findById(itemXplainRequested.id, OnboardingReportItem.class).calculationId == (hasOnTrade ? calculationId : null)
    operations.findById(itemXplainRequested.id, OnboardingReportItem.class).vendorCalculationId == null
    operations.findById(itemMarketRequested.id, OnboardingReportItem.class).calculationId == (hasOnTrade ? calculationId : null)
    operations.findById(itemMarketRequested.id, OnboardingReportItem.class).vendorCalculationId == null
    operations.findById(itemVendorRequestedRootOnboardingDate.id, OnboardingReportItem.class).calculationId == null
    operations.findById(itemVendorRequestedRootOnboardingDate.id, OnboardingReportItem.class).vendorCalculationId == null
    operations.findById(itemVendorRequestedOnboardingDate.id, OnboardingReportItem.class).calculationId == null
    operations.findById(itemVendorRequestedOnboardingDate.id, OnboardingReportItem.class).vendorCalculationId == (hasOnOnboardingDate ? calculationId : null)

    where:
    hasOnTrade | hasOnOnboardingDate
    false      | false
    true       | false
    false      | true
    true       | true
  }

  def "should update cancelled calculation items"() {
    setup:
    def reportId = ObjectId.get()
    def calculationId = ObjectId.get()
    def item = reportItem(reportId)
    def itemXplainRequested = reportItem(reportId)
    itemXplainRequested.xplainCheckStatus = REQUESTED
    itemXplainRequested.calculationId = calculationId
    def itemMarketRequested = reportItem(reportId)
    itemMarketRequested.marketCheckStatus = REQUESTED
    itemMarketRequested.calculationId = calculationId
    operations.insertAll([item, itemXplainRequested, itemMarketRequested])

    when:
    repository.updateCancelledCalculationItems(reportId, calculationId)

    then:
    operations.findById(item.id, OnboardingReportItem.class) == item
    operations.findById(itemXplainRequested.id, OnboardingReportItem.class).xplainCheckStatus == FAILED
    operations.findById(itemXplainRequested.id, OnboardingReportItem.class).marketCheckStatus == SUCCESS
    operations.findById(itemMarketRequested.id, OnboardingReportItem.class).xplainCheckStatus == NOT_REQUIRED
    operations.findById(itemMarketRequested.id, OnboardingReportItem.class).marketCheckStatus == FAILED
  }

  @Unroll
  def "should update failed valuation items #hasOnTrade #hasOnOnboardingDate"() {
    setup:
    def reportId = ObjectId.get()
    def item = reportItem(reportId)
    def itemXplainRequested = reportItem(reportId)
    itemXplainRequested.xplainCheckStatus = REQUESTED
    def itemXplainRequestedAnotherPortfolio = reportItem(reportId)
    itemXplainRequestedAnotherPortfolio.xplainCheckStatus = REQUESTED
    itemXplainRequestedAnotherPortfolio.trade.portfolioId = "ANOTHER"
    def itemXplainRequestedAnotherDate = reportItem(reportId)
    itemXplainRequestedAnotherDate.xplainCheckStatus = REQUESTED
    itemXplainRequestedAnotherDate.trade.tradeDetails.info.tradeDate = TRADE_DATE.plusDays(1)
    def itemMarketRequested = reportItem(reportId)
    itemMarketRequested.marketCheckStatus = REQUESTED
    def itemVendorRequestedRootOnboardingDate = reportItem(reportId)
    itemVendorRequestedRootOnboardingDate.trade.vendorOnboardingDate = ROOT_DATE
    itemVendorRequestedRootOnboardingDate.vendorCheckStatus = REQUESTED
    def itemVendorRequestedOnboardingDate = reportItem(reportId)
    itemVendorRequestedOnboardingDate.trade.vendorOnboardingDate = TRADE_DATE
    itemVendorRequestedOnboardingDate.vendorCheckStatus = REQUESTED
    operations.insertAll([
      item,
      itemXplainRequested,
      itemXplainRequestedAnotherPortfolio,
      itemXplainRequestedAnotherDate,
      itemMarketRequested,
      itemVendorRequestedRootOnboardingDate,
      itemVendorRequestedOnboardingDate,
    ])

    when:
    repository.updateFailedValuationItems(reportId, PORTFOLIO_ID, TRADE_DATE, "Some failure", hasOnTrade, hasOnOnboardingDate)

    then:
    operations.findById(item.id, OnboardingReportItem.class) == item
    operations.findById(itemXplainRequestedAnotherPortfolio.id, OnboardingReportItem.class) == itemXplainRequestedAnotherPortfolio
    operations.findById(itemXplainRequestedAnotherDate.id, OnboardingReportItem.class) == itemXplainRequestedAnotherDate

    operations.findById(itemXplainRequested.id, OnboardingReportItem.class) == (hasOnTrade ? itemXplainRequested.tap {
      it.xplainCheckStatus = FAILED
      it.xplainCheckMessage = "Some failure"
    } : itemXplainRequested)
    operations.findById(itemMarketRequested.id, OnboardingReportItem.class) == (hasOnTrade ? itemMarketRequested.tap {
      it.marketCheckStatus = FAILED
      it.marketCheckMessage = "Some failure"
    } : itemMarketRequested)
    operations.findById(itemVendorRequestedRootOnboardingDate.id, OnboardingReportItem.class) == itemVendorRequestedRootOnboardingDate
    operations.findById(itemVendorRequestedOnboardingDate.id, OnboardingReportItem.class) == (hasOnOnboardingDate ? itemVendorRequestedOnboardingDate.tap {
      it.vendorCheckStatus = FAILED
      it.vendorCheckMessage = "Some failure"
    } : itemVendorRequestedOnboardingDate)

    where:
    hasOnTrade | hasOnOnboardingDate
    false      | false
    true       | false
    false      | true
    true       | true
  }

  def "should update submission status"() {
    setup:
    def report = report()
    operations.insert(report)
    def itemSuccess = reportItem(report.id)
    def itemFailed = reportItem(report.id)
    operations.insertAll([itemSuccess, itemFailed])

    when:
    repository.updateSubmissionStatus(report.id, [itemSuccess.id], [itemFailed.id])

    then:
    def updatedReport = operations.findById(report.id, OnboardingReport.class)
    updatedReport.status == SUBMITTED
    operations.findById(itemSuccess.id, OnboardingReportItem.class) == itemSuccess.tap { it.submissionStatus = SUBMISSION_SUCCESS }
    operations.findById(itemFailed.id, OnboardingReportItem.class) == itemFailed.tap { it.submissionStatus = SUBMISSION_FAIL }
  }

  def "should delete report"() {
    setup:
    def report1 = report()
    report1.status = OnboardingReportStatus.IN_PROGRESS
    def report2 = report()
    operations.insertAll([report1, report2])
    def item1 = reportItem(report1.id)
    def item2 = reportItem(report2.id)
    operations.insertAll([item1, item2])

    when:
    repository.deleteReport(report1.id)

    then:
    operations.findAll(OnboardingReport.class) == [report2]
    operations.findAll(OnboardingReportItem.class) == [item2]
  }

  def "should fetch all xplain calculation ids"() {
    setup:
    def report1 = report()
    report1.status = OnboardingReportStatus.IN_PROGRESS
    def report2 = report()
    operations.insertAll([report1, report2])
    def item1 = reportItem(report1.id)
    item1.calculationId = ObjectId.get()
    def item2 = reportItem(report1.id)
    item2.calculationId = ObjectId.get()
    item2.vendorCalculationId = item1.calculationId
    def item3 = reportItem(report1.id)
    item3.calculationId = item2.calculationId
    def item4 = reportItem(report2.id)
    item4.calculationId = ObjectId.get()
    def item5 = reportItem(report1.id)
    def item6 = reportItem(report1.id)
    item6.vendorCalculationId = ObjectId.get()
    operations.insertAll([item1, item2, item3, item4, item5, item6])

    when:
    def result = repository.reportCalculationIds(report1.id).sort(false)

    then:
    result == [item1.calculationId, item2.calculationId, item6.vendorCalculationId]
  }

  OnboardingReport report() {
    new OnboardingReport(
    stateDate: LocalDate.of(2023, 1, 1),
    status: SUBMITTED,
    xplainConformity: true,
    marketConformity: false,
    vendorConformity: true,
    tradesCount: 10,
    breaksCount: 11,
    )
  }

  OnboardingReportItem reportItem(ObjectId reportId) {
    new OnboardingReportItem(
    reportId: reportId,
    trade: new Trade(
    productType: IRS,
    externalTradeId: "TRADE_ID",
    portfolioId: PORTFOLIO_ID,
    notional: 10.0,
    vendorOnboardingDate: LocalDate.of(2024, 2, 1),
    tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: TRADE_DATE)),
    ),
    xplainCheckStatus: NOT_REQUIRED,
    xplainCheckMessage: null,
    marketCheckStatus: SUCCESS,
    marketCheckMessage: "GOOD",
    vendorCheckStatus: FAILED,
    vendorCheckMessage: "FAIL",
    submissionStatus: SUBMISSION_SUCCESS,
    valuationMetrics: new OnboardingValuationMetrics(presentValue: 1),
    vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE),
    navOnTradeDate: 0.0,
    navOnVendorOnboardingDate: 1.0,
    breakTests: [
      new OnboardingTradeResultBreak(
      breakTestName: "BT",
      breakTestType: OnboardingTestType.DC_XPLAIN,
      triggered: true,
      )
    ],
    )
  }
}
