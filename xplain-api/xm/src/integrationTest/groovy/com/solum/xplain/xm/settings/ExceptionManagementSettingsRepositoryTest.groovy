package com.solum.xplain.xm.settings

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsForm
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ExceptionManagementSettingsRepositoryTest extends IntegrationSpecification {

  @Resource
  MongoOperations operations

  @Resource
  ExceptionManagementSettingsRepository repository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), ExceptionManagementSettings)
  }

  def "should return default settings"() {
    when:
    def result = repository.exceptionManagementSettings(BitemporalDate.newOf(NewVersionFormV2.ROOT_DATE))

    then:
    result.currencyType == IpvValueCurrencyType.TRADE_CCY
    result.validFrom == NewVersionFormV2.ROOT_DATE
  }

  def "should return settings version"() {
    setup:
    def settings = new ExceptionManagementSettings(
      currencyType: IpvValueCurrencyType.REPORTING_CCY,
      validFrom: NewVersionFormV2.ROOT_DATE,
      state: State.ACTIVE,
      )
    operations.save(settings)

    when:
    def result = repository.exceptionManagementSettings(BitemporalDate.newOf(NewVersionFormV2.ROOT_DATE))

    then:
    result.id == settings.id
    result.currencyType == IpvValueCurrencyType.REPORTING_CCY
    result.validFrom == NewVersionFormV2.ROOT_DATE
  }


  def "should return error while updating when root version doesn't exist"() {
    setup:
    def settings = new ExceptionManagementSettingsForm(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL, new NewVersionFormV2("comment", LocalDate.now(), NewVersionFormV2.ROOT_DATE, FutureVersionsAction.KEEP))

    when:
    def result = repository.save(NewVersionFormV2.ROOT_DATE, settings)

    then:
    result.isLeft()

    def error = (ErrorItem) result.left().get()
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Root version must be created before modifying other versions"
  }

  def "should update root version"() {
    setup:
    def settings = new ExceptionManagementSettingsForm(IpvValueCurrencyType.REPORTING_CCY, IpvValueNavLevel.TRADE_LEVEL, new NewVersionFormV2("comment", NewVersionFormV2.ROOT_DATE, NewVersionFormV2.ROOT_DATE, FutureVersionsAction.KEEP))

    when:
    def result = repository.save(NewVersionFormV2.ROOT_DATE, settings)

    then:
    result.isRight()

    def loaded = operations.findById(result.getOrNull().id, ExceptionManagementSettings)
    loaded != null
    loaded.modifiedBy.userId == creator.id
    loaded.validFrom == NewVersionFormV2.ROOT_DATE
    loaded.comment == "comment"
    loaded.currencyType == IpvValueCurrencyType.REPORTING_CCY
  }

  def "should create new version"() {
    setup:
    def rootVersion = operations.save(ExceptionManagementSettings.empty())
    def settings = new ExceptionManagementSettingsForm(IpvValueCurrencyType.REPORTING_CCY, IpvValueNavLevel.TRADE_LEVEL, new NewVersionFormV2("comment", LocalDate.now(), NewVersionFormV2.ROOT_DATE, FutureVersionsAction.KEEP))

    when:
    def result = repository.save(NewVersionFormV2.ROOT_DATE, settings)

    then:
    result.isRight()
    result.getOrNull().id != rootVersion.id

    and:
    def loaded = operations.findById(result.getOrNull().id, ExceptionManagementSettings)
    loaded.validFrom == LocalDate.now()
  }

  def "should delete settings version"() {
    setup:
    def settings = new ExceptionManagementSettings(
      currencyType: IpvValueCurrencyType.REPORTING_CCY,
      validFrom: LocalDate.now(),
      state: State.ACTIVE,
      )
    operations.save(settings)
    when:
    def result = repository.deleteSettingsVersion(LocalDate.now())

    then:
    result.isRight()
    result.getOrNull().id != settings.id

    and:
    def loaded = operations.findById(result.getOrNull().id, ExceptionManagementSettings)
    loaded.validFrom == LocalDate.now()
    loaded.state == State.DELETED
  }

  def "should fail deleting ROOT version"() {
    setup:
    def settings = new ExceptionManagementSettings(
      currencyType: IpvValueCurrencyType.REPORTING_CCY,
      validFrom: NewVersionFormV2.ROOT_DATE,
      state: State.ACTIVE,
      )
    operations.save(settings)
    when:
    def result = repository.deleteSettingsVersion(NewVersionFormV2.ROOT_DATE)

    then:
    result.isLeft()

    def error = (ErrorItem) result.left().get()
    error.description == "Can not delete first version!"
  }

  def "should return correct currency type"() {
    setup:
    def settings = new ExceptionManagementSettings(
      currencyType: IpvValueCurrencyType.REPORTING_CCY,
      validFrom: LocalDate.now(),
      state: State.ACTIVE
      )
    operations.save(settings)

    when:
    def result = repository.getCurrencyType(BitemporalDate.newOf(LocalDate.now()))

    then:
    result == IpvValueCurrencyType.REPORTING_CCY.toString()
  }

  def "should return correct nav level"() {
    setup:
    def settings = new ExceptionManagementSettings(
      navLevel: IpvValueNavLevel.TRADE_LEVEL,
      validFrom: LocalDate.now(),
      state: State.ACTIVE
      )
    operations.save(settings)

    when:
    def result = repository.getNavLevel(BitemporalDate.newOf(LocalDate.now()))

    then:
    result == IpvValueNavLevel.TRADE_LEVEL.toString()
  }
}
