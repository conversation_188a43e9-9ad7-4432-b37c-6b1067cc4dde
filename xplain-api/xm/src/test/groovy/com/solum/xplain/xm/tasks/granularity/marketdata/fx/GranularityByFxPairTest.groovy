package com.solum.xplain.xm.tasks.granularity.marketdata.fx

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_RATES
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOL_SKEW

import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.extensions.constants.PermissibleCurrencies
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.tasks.TaskMapper
import com.solum.xplain.xm.tasks.entity.TaskExecution
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class GranularityByFxPairTest extends Specification {
  def taskMapper = Mappers.getMapper(TaskMapper.class)
  static List<InstrumentType> INSTRUMENTS = Arrays.asList(CoreInstrumentType.values())

  def "should split all asset classes"() {
    setup:
    def rule = new GranularityByFxPair(taskMapper, INSTRUMENTS)

    when:
    def executions = rule.split(new TaskExecution(assetFilter: new AssetFilter()))

    then:
    executions.size() == PermissibleCurrencies.FX_SWAP_PAIRS.size() + 1
    for (int i = 0; i < PermissibleCurrencies.FX_SWAP_PAIRS.size(); i++) {
      verifyAll {
        executions[i].assetFilter.fxPairs == [PermissibleCurrencies.FX_SWAP_PAIRS[i].toString()]
        executions[i].assetFilter.assetClasses == [FX_RATES, FX_SWAP, FX_VOLS, FX_VOL_SKEW]
      }
    }
  }

  def "should split only by filter asset classes"() {
    setup:
    def rule = new GranularityByFxPair(taskMapper, INSTRUMENTS)

    when:
    def executions = rule.split(new TaskExecution(assetFilter: new AssetFilter(assetClasses: [FX_RATES])))

    then:
    executions.size() == PermissibleCurrencies.FX_SWAP_PAIRS.size()
    executions.stream().allMatch { e -> e.assetFilter.assetClasses == [FX_RATES] }
  }
}
