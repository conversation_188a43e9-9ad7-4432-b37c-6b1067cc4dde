package com.solum.xplain.xm.dashboardsteps.opvvaluations

import com.solum.xplain.calculation.events.TradesCalculationFinishedEvent
import com.solum.xplain.core.portfolio.CalculationStatus
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.onboarding.events.OnboardingValuationCompletedEvent
import com.solum.xplain.xm.workflow.XmWorkflowService
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class CoreCalculationEventsListenerTest extends Specification {
  static CALCULATION_ID = ObjectId.get()
  static PORTFOLIO_ID = ObjectId.get()
  static DASHBOARD_ID = "dashboardId"
  static ONBOARDING_ID = ObjectId.get()

  ApplicationEventPublisher eventPublisher = Mock()
  XmWorkflowService xmWorkflowService = Mock()
  def listener = new CoreCalculationEventsListener(eventPublisher, xmWorkflowService)

  def "should correctly process finished workflow dashboard calculation"() {
    setup:
    def dashboard = new Dashboard(id: DASHBOARD_ID)
    def event = TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, DASHBOARD_ID, null, null, CalculationStatus.FINISHED)

    when:
    listener.onEvent(event)

    then:

    1 * xmWorkflowService.onCalculationFinished(event) >> true
  }

  def "should correctly process canceled onboarding calculation"() {
    setup:
    def event = TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, null, ONBOARDING_ID, CalculationStatus.NOT_PERFORMED)

    when:
    listener.onEvent(event)

    then:

    1 * eventPublisher.publishEvent(_) >> { OnboardingValuationCompletedEvent onboardingEvent ->
      onboardingEvent.portfolioId == PORTFOLIO_ID
      onboardingEvent.reportId == ONBOARDING_ID
      onboardingEvent.calculationId == CALCULATION_ID
      onboardingEvent.cancelled
    }
  }

  def "should correctly process finished onboarding calculation"() {
    setup:
    def event = TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, null, ONBOARDING_ID, CalculationStatus.FINISHED)

    when:
    listener.onEvent(event)

    then:

    1 * eventPublisher.publishEvent(_) >> { OnboardingValuationCompletedEvent onboardingEvent ->
      onboardingEvent.portfolioId == PORTFOLIO_ID
      onboardingEvent.reportId == ONBOARDING_ID
      onboardingEvent.calculationId == CALCULATION_ID
      !onboardingEvent.cancelled
    }
  }

  def "should correctly process manual calculation"() {
    setup:
    def event = TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, null, null, status)

    when:
    listener.onEvent(event)

    then:
    0 * eventPublisher.publishEvent(_)

    where:
    status << [CalculationStatus.FINISHED, CalculationStatus.NOT_PERFORMED]
  }
}
