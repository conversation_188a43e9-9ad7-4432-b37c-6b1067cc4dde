package com.solum.xplain.xm.excmngmt.processipv.converter

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD

import com.opengamma.strata.basics.currency.FxRateProvider
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData
import spock.lang.Specification

class ReportingIpvValueCurrencyConverterTest extends Specification {

  def fxProvider = Mock(FxRateProvider)
  def converter = new ReportingIpvValueCurrencyConverter(
  EUR,
  fxProvider
  )

  def "should convert notional only when FX rate present"() {
    given:
    def fxRate = 0.8
    fxProvider.fxRate(USD, EUR) >> fxRate

    when:
    def result1 = converter.convertScalingData(new TradeBreakScalingData(999.0, 100.0), USD)

    then:
    result1.nav() == 999.0
    result1.notional() == 80.0

    when:
    def result2 = converter.convertScalingData(new TradeBreakScalingData(null, 100.0), USD)

    then:
    result2.nav() == null
    result2.notional() == 80.0
  }

  def "should return null when FX rate not found"() {
    given:
    def scalingData = new TradeBreakScalingData(999.0, 100.0)
    fxProvider.fxRate(USD, EUR) >> { throw new IllegalArgumentException("ERR") }

    when:
    def result = converter.convertScalingData(scalingData, USD)

    then:
    result.nav() == 999.0
    result.notional() == null
  }

  def "should return same when FX rate not found but reportCcy = tradeCcy"() {
    given:
    def scalingData = new TradeBreakScalingData(null, 100.0)

    when:
    def result = converter.convertScalingData(scalingData, EUR)

    then:
    result.nav() == null
    result.notional() == 100.0
  }

  def "should not throw error when scaling data is null"() {
    when:
    def result = converter.convertScalingData(null, USD)

    then:
    result == null
  }

  def "should return XM currency"() {
    when:
    def result = converter.xmCurrency(USD)

    then:
    result == Optional.of(EUR)
  }

  def "should return null when fxRates/reportingCcy are null"() {
    setup:
    def scalingData = new TradeBreakScalingData(999.0, 100.0)

    when:
    def resultWhenNoReportingCcy = new ReportingIpvValueCurrencyConverter(null, fxProvider).convertScalingData(scalingData, USD)

    then:
    resultWhenNoReportingCcy.nav() == 999.0
    resultWhenNoReportingCcy.notional() == null

    when:
    def resultWhenNoFxRates = new ReportingIpvValueCurrencyConverter(EUR, null).convertScalingData(scalingData, USD)

    then:
    resultWhenNoFxRates.nav() == 999.0
    resultWhenNoFxRates.notional() == null
  }
}
