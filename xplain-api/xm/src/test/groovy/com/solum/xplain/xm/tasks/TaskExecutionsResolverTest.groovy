package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CAPFLOOR_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CREDIT_INDEX
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CREDIT_INDEX_TRANCHE
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_RATES
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOL_SKEW
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.INFLATION_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.SWAPTION_SKEW
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.SWAPTION_VOLS
import static com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType.PRELIMINARY

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.core.teams.value.TeamNameView
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.tasks.entity.AssetClassTaskTeams
import com.solum.xplain.xm.tasks.entity.TaskDefinitionOverride
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.entity.TasksDefinition
import com.solum.xplain.xm.tasks.entity.TasksTeamsOverride
import com.solum.xplain.xm.tasks.enums.TaskGranularityByAssetClassType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByInstrumentType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType
import com.solum.xplain.xm.tasks.value.TaskExecutionsResolver
import org.bson.types.ObjectId
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class TaskExecutionsResolverTest extends Specification {

  def taskMapper = Mappers.getMapper(TaskMapper.class)
  static String teamId = ObjectId.get().toHexString()
  static List<InstrumentType> INSTRUMENTS = Arrays.asList(CoreInstrumentType.values())

  def "should split task by granularity NONE"() {
    setup:
    def resolver = TaskExecutionsResolver.newOf(
      taskMapper,
      definition({}),
      new TaskExecution(),
      [new TeamNameView(id: teamId, name: "team")],
      INSTRUMENTS
      )

    when:
    def result = resolver.resolveTasks()

    then:
    result.size() == 1

    def filter = result.get(0).assetFilter
    filter.assetClasses == []
    filter.rateCcys == []
    filter.creditSectors == []
    filter.fxPairs == []
  }

  def "should ignore overrides with empty resolution and approval teams"() {
    setup:
    def resolver = TaskExecutionsResolver.newOf(
      taskMapper,
      definition({ c ->
        c.overrides = [
          new TaskDefinitionOverride(
          overrideTeams: [
            new TasksTeamsOverride(
            filter: new AssetFilter(assetClasses: [SWAPTION_VOLS]),
            resolutionTeams: resolutionTeams,
            approvalTeams: approvalTeams
            )
          ]
          )
        ]
      }),
      new TaskExecution(),
      [new TeamNameView(id: teamId, name: "team")],
      INSTRUMENTS
      )

    when:
    def result = resolver.resolveTasks()

    then:
    result.size() == size

    where:
    resolutionTeams | approvalTeams | size
    [teamId]        | [teamId]      | 2
    []              | [teamId]      | 1
    [teamId]        | []            | 1
    [teamId]        | []            | 1
  }

  def "should split task by asset class ASSET"() {
    setup:
    def resolver = TaskExecutionsResolver.newOf(
      taskMapper,
      definition({ c ->
        c.granularityByAssetClassType = TaskGranularityByAssetClassType.ASSET
      }),
      new TaskExecution(),
      [new TeamNameView(id: teamId, name: "team")],
      INSTRUMENTS
      )

    when:
    def result = resolver.resolveTasks()

    then:
    result.size() == 3
    result.stream()
      .filter({ c ->
        c.assetFilter.assetClasses == [IR_RATE, INFLATION_RATE, SWAPTION_VOLS, SWAPTION_SKEW, CAPFLOOR_VOLS]
      }).count() == 1
    result.stream()
      .filter({ c -> c.assetFilter.assetClasses == [CDS, CREDIT_INDEX, CREDIT_INDEX_TRANCHE] })
      .count() == 1
    result.stream()
      .filter({ c -> c.assetFilter.assetClasses == [FX_RATES, FX_SWAP, FX_VOLS, FX_VOL_SKEW] })
      .count() == 1
  }

  def definition(Closure c) {
    new TasksDefinition(
      type: PRELIMINARY,
      teams: teams(),
      overrides: [],
      granularityByAssetClassType: TaskGranularityByAssetClassType.NONE,
      granularityByRate: TaskGranularityByRateType.NONE,
      granularityBySector: TaskGranularityBySectorType.NONE,
      granularityByInstrument: TaskGranularityByInstrumentType.NONE,
      granularityByFxCcyPair: TaskGranularityByFxCcyPairType.NONE

      ).with(true, c)
  }

  static teams() {
    CoreAssetClass.values()
      .collect({ c ->
        new AssetClassTaskTeams(
          assetClass: c,
          resolutionTeams: [teamId],
          approvalTeams: [teamId]
          )
      })
  }
}
