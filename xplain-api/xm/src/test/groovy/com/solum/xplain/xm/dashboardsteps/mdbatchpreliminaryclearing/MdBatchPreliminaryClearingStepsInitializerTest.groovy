package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdBatchPreliminaryClearingStepsInitializerTest extends Specification {

  private static final String RESULT_ID = "batchXMResultId"

  DashboardStepProcessor processor = Mock()
  MdTaskExecutionService taskExecutionService = Mock()
  ExceptionManagementCalculationRepository exceptionManagementRepository = Mock()

  MdBatchPreliminaryClearingStepsInitializer initializer = new MdBatchPreliminaryClearingStepsInitializer(
  processor,
  taskExecutionService,
  exceptionManagementRepository
  )

  def setup() {
    processor.getStateDate() >> BitemporalDate.newOfNow()
  }

  def "should create steps"() {
    setup:
    1 * exceptionManagementRepository.entitiesByDashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(
      [
        new ExceptionManagementResult(
        dashboardId: MD_BATCH_DASHBOARD_ID,
        status: CalculationTestStatus.BATCH_PROCESSING,
        id: RESULT_ID
        )
      ]
      )

    TaskExecution taskExecution = Mock(TaskExecution)
    taskExecution.dashboardId >> MD_BATCH_DASHBOARD_ID
    taskExecution.status >> TaskExecutionStatus.NOT_STARTED
    taskExecution.breaksCount >> 1L
    1 * taskExecutionService.createTaskExecutions(_, _, _) >> [taskExecution]

    1 * processor.createMdBatchSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMdBatch>
      assert steps.size() == 1
      assert steps[0].status == StepStatus.IN_PROGRESS
      assert steps[0].breaksCount == 1L
      return Either.right(steps)
    }

    when:
    def result = initializer.execute(MD_BATCH_DASHBOARD)

    then:
    result.isRight()
  }

  def "should create steps when tasks approved"() {
    setup:
    1 * exceptionManagementRepository.entitiesByDashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(
      [
        new ExceptionManagementResult(
        dashboardId: MD_BATCH_DASHBOARD_ID,
        status: CalculationTestStatus.PRELIMINARY_BATCH_APPROVED,
        id: RESULT_ID
        )
      ]
      )

    TaskExecution taskExecution = Mock(TaskExecution)
    taskExecution.dashboardId >> MD_BATCH_DASHBOARD_ID
    taskExecution.status >> TaskExecutionStatus.APPROVED
    taskExecution.breaksCount >> null
    1 * taskExecutionService.createTaskExecutions(_, _, _) >> [taskExecution]

    1 * processor.createMdBatchSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMdBatch>
      assert steps.size() == 1
      assert steps[0].status == StepStatus.COMPLETED
      assert steps[0].breaksCount == 0L
      return Either.right(steps)
    }

    when:
    def result = initializer.execute(MD_BATCH_DASHBOARD)

    then:
    result.isRight()
  }
}
