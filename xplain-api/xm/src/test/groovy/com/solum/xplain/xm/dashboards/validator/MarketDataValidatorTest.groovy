package com.solum.xplain.xm.dashboards.validator

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.mdvalue.MarketDataValueService
import io.atlassian.fugue.Either
import spock.lang.Specification

class MarketDataValidatorTest extends Specification {

  static def BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  static def ERROR = Optional.of(Error.OBJECT_NOT_FOUND.entity())
  static def NO_ERROR = Optional.empty()

  MarketDataValueService marketDataValueService = Mock()

  MarketDataValidator validator = new MarketDataValidator(marketDataValueService)

  def "should validate market data group"() {
    setup:
    marketDataValueService.getResolvedValuesByKey(
      MARKET_DATA_GROUP.entityId,
      BITEMPORAL_STATE_DATE,
      DASHBOARD_DATE,
      []
      ) >> dataEither

    expect:
    validator.validate(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP, []) == expected

    where:
    dataEither                                   | expected
    Either.right(Map.of())                       | NO_ERROR
    Either.left(Error.OBJECT_NOT_FOUND.entity()) | ERROR
  }
}
