package com.solum.xplain.xm.excmngmt.process

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.math.BigDecimal.TEN

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.mdvalue.MarketDataValueRepository
import com.solum.xplain.core.mdvalue.MarketDataValueService
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView
import com.solum.xplain.xm.excmngmt.market.CleanMarketDataRepository
import com.solum.xplain.xm.excmngmt.market.value.ResolvedInstrument
import com.solum.xplain.xm.excmngmt.market.value.ResolvedOverlayInstrument
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import java.time.LocalDate
import spock.lang.Specification

class MarketDataExtractionServiceTest extends Specification {

  private static final BitemporalDate STATE_DATE = BitemporalDate.newOf(LocalDate.of(2018, 10, 10))
  private static final LocalDate CURVE_DATE = LocalDate.of(2018, 10, 11)

  def cleanMdRepository = Mock(CleanMarketDataRepository)
  def marketDataValueRepository = Mock(MarketDataValueRepository)
  def marketDataValueService = Mock(MarketDataValueService)

  def service = new MarketDataExtractionService(cleanMdRepository, marketDataValueRepository, marketDataValueService)

  def "should correctly resolve preliminary data"() {
    setup:
    1 * cleanMdRepository.cleanPreliminaryData("md", CURVE_DATE) >> [
      new ResolvedInstrument(instrumentKey: "I1", providerData: ProviderData.of("A", 1.0, null, BID)),
      new ResolvedInstrument(instrumentKey: "I1", providerData: ProviderData.of("B", 2.0, null, BID)),
      new ResolvedInstrument(instrumentKey: "I1", providerData: ProviderData.of("A", 3.0, null, ASK)),
      new ResolvedInstrument(instrumentKey: "I2", providerData: ProviderData.of("A", 20.0, null, BID)),
      new ResolvedInstrument(instrumentKey: "I2", providerData: ProviderData.of("A", 21.0, null, ASK)),
      new ResolvedInstrument(instrumentKey: "I4", providerData: ProviderData.of("A", 1.0, null, BID)),
      new ResolvedInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", 1.0, null, BID)),
      new ResolvedInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", 2.0, null, ASK))
    ]

    1 * marketDataValueService.getResolvedValuesByKey("md", STATE_DATE, CURVE_DATE, []) >> right(["I1": new ResolvedMarketDataValueView(key: "I1",
      values: [
        new MarketDataValueFlatView(provider: "A", value: 0, bidAsk: BID),
        new MarketDataValueFlatView(provider: "C", value: 10, bidAsk: BID),
        new MarketDataValueFlatView(provider: "C", value: 11, bidAsk: ASK),
      ]),
      "I3": new ResolvedMarketDataValueView(key: "I3",
      values: [new MarketDataValueFlatView(provider: "D", value: 10, bidAsk: BID)])])

    when:
    def result = service.preliminaryData("md", CURVE_DATE, STATE_DATE, [])

    then:
    result.size() == 4

    result["I1"].size() == 3
    result["I1"]["A"][BID] == 1
    result["I1"]["A"][ASK] == 3
    result["I1"]["B"][BID] == 2
    result["I1"]["C"][BID] == 10
    result["I1"]["C"][ASK] == 11

    result["I2"].size() == 1
    result["I2"]["A"][BID] == 20
    result["I2"]["A"][ASK] == 21

    result["I3"].size() == 1
    result["I3"]["D"][BID] == 10

    result["I4"].size() == 2
    result["I4"]["A"][BID] == 1
    result["I4"]["B"][BID] == 1
    result["I4"]["B"][ASK] == 2
  }

  def "should correctly resolve overlay data"() {
    setup:
    1 * cleanMdRepository.cleanOverlayData("md", CURVE_DATE) >> [
      new ResolvedOverlayInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", TEN, null, BID), curveConfigurationId: "C1"),
      new ResolvedOverlayInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", TEN, null, BID), curveConfigurationId: "C2"),
    ]

    when:
    def result = service.overlayData("md", CURVE_DATE)

    then:
    result.size() == 1

    result["I4"].size() == 2
    result["I4"].contains(new ResolvedOverlayInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", TEN, null, BID), curveConfigurationId: "C1"))
    result["I4"].contains(new ResolvedOverlayInstrument(instrumentKey: "I4", providerData: ProviderData.of("B", TEN, null, BID), curveConfigurationId: "C2"))
  }

  def "should correctly resolve preliminary data history"() {
    setup:
    1 * marketDataValueRepository.getMarketDataDates("md", STATE_DATE) >> [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2020, 1, 2),
      LocalDate.of(2020, 1, 3),
      LocalDate.of(2020, 1, 4)
    ]

    1 * cleanMdRepository.cleanPreliminaryData("md", LocalDate.of(2020, 1, 2)) >> [
      new ResolvedInstrument(instrumentKey: "I", providerData: ProviderData.of("A", TEN, null, BID)),
    ]

    1 * cleanMdRepository.cleanPreliminaryData("md", LocalDate.of(2020, 1, 3)) >> [
      new ResolvedInstrument(instrumentKey: "I", providerData: ProviderData.of("A", TEN, null, BID)),
    ]

    1 * marketDataValueService.getResolvedValuesByKey("md",
      STATE_DATE,
      LocalDate.of(2020, 1, 2),
      []) >> left(Error.OBJECT_NOT_FOUND.entity())
    1 * marketDataValueService.getResolvedValuesByKey("md",
      STATE_DATE,
      LocalDate.of(2020, 1, 3),
      []) >> left(Error.OBJECT_NOT_FOUND.entity())

    when:
    def result = service.historicalPreliminaryData("md", LocalDate.of(2020, 1, 4), STATE_DATE, 2, [])

    then:
    result.availableDates == [LocalDate.of(2020, 1, 3), LocalDate.of(2020, 1, 2)]
    def instrumentData = result.instrumentData("I")
    instrumentData.data.size() == 2
    instrumentData.data[LocalDate.of(2020, 1, 3)]["A"][BID] == TEN
    instrumentData.data[LocalDate.of(2020, 1, 2)]["A"][BID] == TEN
  }
}
