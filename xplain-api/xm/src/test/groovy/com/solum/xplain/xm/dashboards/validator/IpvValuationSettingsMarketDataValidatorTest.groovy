package com.solum.xplain.xm.dashboards.validator

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.COMPANY
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.ENTITY
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_IPV_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_SETTINGS
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.error.Error
import io.atlassian.fugue.Either
import spock.lang.Specification

class IpvValuationSettingsMarketDataValidatorTest extends Specification {

  static def BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  static def ERROR = Optional.of(Error.OBJECT_NOT_FOUND.entity())
  static def NO_ERROR = Optional.empty()

  CompanyPortfolioSettingsResolver settingsResolver = Mock()
  MarketDataValidator marketDataValidator = Mock()

  IpvValuationSettingsMarketDataValidator validator = new IpvValuationSettingsMarketDataValidator(
  settingsResolver,
  marketDataValidator
  )

  def "should validate portfolios market data"() {
    setup:
    1 * settingsResolver.portfolioSettings(
      COMPANY.entityId,
      ENTITY.entityId,
      PORTFOLIO.entityId,
      BITEMPORAL_STATE_DATE
      ) >> Either.right(PORTFOLIO_SETTINGS)

    1 * marketDataValidator.validate(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, _, Arrays.asList(CoreAssetGroup.values())) >> mdValidationResult

    expect:
    validator.validate(DASHBOARD_DATE, BITEMPORAL_STATE_DATE, [PORTFOLIO_IPV_SETTINGS]) == expected

    where:
    mdValidationResult                           | expected
    Optional.empty()                             | NO_ERROR
    Optional.of(Error.OBJECT_NOT_FOUND.entity()) | ERROR
  }

  def "should not validate portfolios market data when no XPLAIN provider"() {
    setup:
    0 * settingsResolver.portfolioSettings(_, _, _, _)
    0 * marketDataValidator.validate(_, _, _)

    expect:
    validator.validate(DASHBOARD_DATE, BITEMPORAL_STATE_DATE, [PORTFOLIO_NO_XPLAIN_PROVIDER_IPV_SETTINGS]) == NO_ERROR
  }
}
