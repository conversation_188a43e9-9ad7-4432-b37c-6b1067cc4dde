package com.solum.xplain.xm.classifiers

import com.solum.xplain.core.classifiers.Classifier
import com.solum.xplain.xm.excmngmt.processipv.resolution.classifiers.IpvExceptionManagementResolutionTypesClassifierProvider
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType
import spock.lang.Specification

class XmClassifiersProviderTest extends Specification {

  def ipvResolutionTypesProvider = Mock(IpvExceptionManagementResolutionTypesClassifierProvider)
  def provider = new XmClassifiersProvider(ipvResolutionTypesProvider)

  def setup() {
    ipvResolutionTypesProvider.ipvResolutions() >> ipvResolutionTypesClassifier()
    ipvResolutionTypesProvider.entryResultStatusDetail() >> entryResultStatusDetailClassifier()
  }

  def "should return correct classifier list"() {
    expect:
    provider.classifiers().size() == 27
  }

  def "should return correct breakTestOperators"() {
    when:
    def op = provider.classifiers().stream().filter({ c -> c.id == "breakTestOperators" })
    .findAny().get()

    then:
    op.values.size() == 2
    op.values[0].id == "GT"
    op.values[0].name == ">"
    op.values[1].id == "GTE"
    op.values[1].name == ">="
  }

  def "should return correct breakTestTenorBucketOperators"() {
    when:
    def op = provider.classifiers().stream().filter({ c -> c.id == "breakTestTenorBucketOperators" })
    .findAny().get()

    then:
    op.values.size() == 5
    op.values.get(0).id == "EQ"
    op.values.get(0).name == "="
  }

  def "should return correct breakTestScopes"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "breakTestScopes" })
    .findAny().get()

    then:
    types.values.size() == 3
    types.values[0].id == "PRELIMINARY"
    types.values[0].name == "Preliminary"
    types.values[0].values.size() == 4
    types.values[0].values[0].id == "ZERO_VALUE"
    types.values[0].values[0].values == []
    types.values[0].values[1].id == "STALE_VALUE"
    types.values[0].values[1].values == []
    types.values[0].values[2].id == "VALUE"
    types.values[0].values[2].values[0].id == "VALUE"
    types.values[0].values[3].id == "DAY_TO_DAY_SIGN"
    types.values[0].values[3].values == []

    types.values[1].id == "PRELIMINARY_BATCH"
    types.values[1].name == "Preliminary (Batch)"
    types.values[1].values.size() == 2
    types.values[1].values[0].id == "ZERO_VALUE"
    types.values[1].values[0].values == []
    types.values[1].values[1].id == "VALUE"
    types.values[1].values[1].values[0].id == "VALUE"

    types.values[2].id == "OVERLAY"
    types.values[2].name == "Overlay"
    types.values[2].values.size() == 3
    types.values[2].values[0].id == "DAY_TO_DAY"
    types.values[2].values[0].values[0].id == "ABSOLUTE_DIFF"
    types.values[2].values[0].values[1].id == "RELATIVE_DIFF"
    types.values[2].values[1].id == "PRIMARY_VS_SECONDARY"
    types.values[2].values[1].values[0].id == "ABSOLUTE_DIFF"
    types.values[2].values[1].values[1].id == "RELATIVE_DIFF"
    types.values[2].values[2].id == "VALUE"
    types.values[2].values[2].values.size() == 1
    types.values[2].values[2].values[0].id == "Z_SCORE"
  }

  def "should return correct breakTestMeasureType"() {
    when:
    def types = provider.classifiers().stream()
      .filter({ c -> c.id == XmClassifiersProvider.BREAK_TEST_MEASURE_TYPE })
      .findAny().get()

    then:
    types.values.size() == MeasureType.values().length
    types.values[0].id == "RELATIVE_DIFF"
    types.values[0].name == "Relative Difference"
  }

  def "should return correct ipvBreakTestMeasures"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "ipvBreakTestMeasures" })
    .findAny().get()

    then:
    types.values.size() == 8

    types.values[0].id == "DAY_TO_DAY"
    types.values[0].values[0].id == "ABSOLUTE_DIFF"
    types.values[0].values[1].id == "RELATIVE_DIFF"
    types.values[0].values[2].id == "GREEKS_DV01"
    types.values[0].values[3].id == "GREEKS_DV01_VEGA"
    types.values[0].values[4].id == "GREEKS_VEGA"
    types.values[0].values[5].id == "GREEKS"
    types.values[0].values[6].id == "NAV"
    types.values[0].values[7].id == "NOTIONAL"

    types.values[1].id == "DAY_TO_DAY_SIGN"
    types.values[1].values == []

    types.values[2].id == "PRIMARY_VS_SECONDARY"
    types.values[2].values[0].id == "ABSOLUTE_DIFF"
    types.values[2].values[1].id == "RELATIVE_DIFF"

    types.values[3].id == "PRIMARY_VS_TERTIARY"
    types.values[3].values[0].id == "ABSOLUTE_DIFF"
    types.values[3].values[1].id == "RELATIVE_DIFF"

    types.values[4].id == "PRIMARY_VS_QUATERNARY"
    types.values[4].values[0].id == "ABSOLUTE_DIFF"
    types.values[4].values[1].id == "RELATIVE_DIFF"

    types.values[5].id == "STALE_VALUE"
    types.values[5].values == []

    types.values[6].id == "ZERO_VALUE"
    types.values[6].values == []

    types.values[7].id == "VALUE"
    types.values[7].values[0].id == "VALUE"
  }

  def "should return correct ipvBreakTestMeasureType"() {
    when:
    def types = provider.classifiers().stream()
      .filter({ c -> c.id == XmClassifiersProvider.IPV_BREAK_TEST_MEASURE_TYPE })
      .findAny().get()

    then:
    types.values.size() == IpvMeasureType.values().length
    types.values[0].id == "RELATIVE_DIFF"
    types.values[0].name == "Relative"
  }

  def "should return correct ipvBreakTestProvidersType"() {
    when:
    def types = provider.classifiers().stream()
      .filter({ c -> c.id == "ipvBreakTestProvidersType" })
      .findAny().get()

    then:
    types.values*.id == ["P1", "P2", "P3", "P4", "ACCOUNTING_COST", "DEAL_COST", "MANUAL", "MULTIPLE", "DO_NOT_PRICE"]
  }

  def "should return correct onboardingBreakTestMeasures"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "onboardingBreakTestMeasures" })
    .findAny().get()

    then:
    types.values.size() == 3

    types.values[0].id == "DC_XPLAIN"
    types.values[0].name == "Xplain Conformity (vs. Deal Cost)"
    types.values[0].values[0].id == "ABSOLUTE_DIFF"
    types.values[0].values[1].id == "RELATIVE_DIFF"
    types.values[0].values[2].id == "GREEKS_DV01"
    types.values[0].values[3].id == "GREEKS_DV01_VEGA"

    types.values[1].id == "MARKET_CONF"
    types.values[1].name == "Market Conformity (vs. Deal Cost)"
    types.values[1].values[0].id == "ABSOLUTE_DIFF"
    types.values[1].values[1].id == "RELATIVE_DIFF"
    types.values[1].values[2].id == "GREEKS_DV01"
    types.values[1].values[3].id == "GREEKS_DV01_VEGA"

    types.values[2].id == "AC_P1"
    types.values[2].name == "Vendor Conformity (vs. Accounting Cost)"
    types.values[2].values[0].id == "ABSOLUTE_DIFF"
    types.values[2].values[1].id == "RELATIVE_DIFF"
    types.values[2].values[2].id == "GREEKS_DV01"
    types.values[2].values[3].id == "GREEKS_DV01_VEGA"
  }

  def "should return correct onboardingConformityCheckStatuses"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "onboardingConformityCheckStatuses" })
    .findAny().get()

    then:
    types.values.size() == 4

    types.values[0].id == "REQUESTED"
    types.values[0].name == "Requested"

    types.values[1].id == "SUCCESS"
    types.values[1].name == "Success"

    types.values[2].id == "FAILED"
    types.values[2].name == "Failed"

    types.values[3].id == "NOT_REQUIRED"
    types.values[3].name == "Not required"
  }

  def "should return correct exceptionManagementCalculationStatuses"() {
    when:
    def types = provider.classifiers()
      .stream()
      .filter({ c -> c.id == "exceptionManagementCalculationStatuses" })
      .findAny()
      .get()

    then:
    types.values.size() == 6
    types.values.get(0).id == "IN_PRELIMINARY"
    types.values.get(0).values*.id == ["KEEP", "PREVIOUS_DAY", "OVERRIDE_USER", "IGNORE_NULL"]

    types.values.get(1).id == "IN_OVERLAY"
    types.values.get(1).values*.id == [
      "KEEP",
      "PREVIOUS_DAY",
      "SWITCH_TO_SECONDARY",
      "OVERRIDE_USER",
      "DELTA_SECONDARY",
      "SWITCH_TO_BID",
      "SWITCH_TO_ASK"
    ]
  }

  def "should return correct exceptionManagementResolutionTypes"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "exceptionManagementResolutionTypes" })
    .findAny().get()

    then:
    types.values*.id == [
      "KEEP",
      "PREVIOUS_DAY",
      "SWITCH_TO_SECONDARY",
      "DELTA_SECONDARY",
      "OVERRIDE_USER",
      "IGNORE_NULL",
      "SWITCH_TO_BID",
      "SWITCH_TO_ASK",
    ]
  }

  def "should return correct ipvExceptionManagementResolutionTypes"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "ipvExceptionManagementResolutionTypes" })
    .findAny().get()

    then:
    types.values*.id == [
      "KEEP",
      "PREVIOUS_DAY",
      "SWITCH_TO_SECONDARY",
      "SWITCH_TO_TERTIARY",
      "SWITCH_TO_QUATERNARY",
      "USE_ACCOUNTING_COST",
      "USE_DEAL_COST",
      "DELTA_SECONDARY",
      "DELTA_TERTIARY",
      "DELTA_QUATERNARY",
      "DO_NOT_PRICE",
      "HOLD",
      "OVERRIDE_USER",
    ]
  }

  def "should return ipvExceptionManagementTradeValueTypes"() {
    when:
    def vals = provider.classifiers().stream().filter({ c -> c.id == "ipvExceptionManagementTradeValueTypes" })
    .findAny().get()

    then:
    vals.values.size() == 3
    vals.values[0].id == "PV"
    vals.values[1].id == "DELTA"
    vals.values[2].id == "VEGA"
  }

  def "should return correct task granularity by trade type"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "taskGranularityByTradeTypes" })
    .findAny().get()

    then:
    types.values.size() == 3
    types.values.get(0).id == "NONE"
    types.values.get(0).name == "None"
    types.values.get(1).id == "TRADE_ASSET_CLASS"
    types.values.get(1).name == "Per Asset Class"
    types.values.get(2).id == "TRADE_TYPE"
    types.values.get(2).name == "Per Trade Type"
  }

  def "should return correct dashboard types"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "dashboardType" })
    .findAny().get()

    then:
    types.values.size() == 5
    types.values.get(0).id == "MARKET_DATA_BATCH"
    types.values.get(0).name == "MARKET DATA BATCH"
    types.values.get(1).id == "TRS_MARKET_DATA_BATCH"
    types.values.get(1).name == "TRS MARKET DATA BATCH"
    types.values.get(2).id == "MARKET_DATA"
    types.values.get(2).name == "MARKET DATA"
    types.values.get(3).id == "TRS_MARKET_DATA"
    types.values.get(3).name == "TRS MARKET DATA"
    types.values.get(4).id == "VALUATION_DATA"
    types.values.get(4).name == "VALUATION DATA"
  }

  def "should return correct dashboard statuses"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "dashboardStatus" })
    .findAny().get()

    then:
    types.values.size() == 3
    types.values.get(0).id == "NOT_STARTED"
    types.values.get(0).name == "Not Started"
    types.values.get(1).id == "IN_PROGRESS"
    types.values.get(1).name == "In Progress"
    types.values.get(2).id == "COMPLETED"
    types.values.get(2).name == "Completed"
  }

  def "should return correct entry result statuses"() {
    when:
    def types = provider.classifiers().stream().filter({c -> c.id == "entryResultStatus" })
    .findAny().get()

    then:
    types.values*.id == [
      "VERIFIED",
      "WAITING_SUBMISSION",
      "WAITING_RESOLUTION",
      "REJECTED",
      "WAITING_APPROVAL",
      "HOLD",
      "RAW"
    ]
  }

  def "should return correct task granularities by contractual term"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "taskGranularityByContractualTerm" })
    .findAny().get()

    then:
    types.values.size() == 2
    types.values.get(0).id == "NONE"
    types.values.get(0).name == "None"
    types.values.get(1).id == "SLA_DEADLINE"
    types.values.get(1).name == "Per SLA Deadline"
  }

  def "should return correct IPV exception management types"() {
    when:
    def types = provider.classifiers().stream().filter({ c -> c.id == "ipvExceptionManagementPhase" })
    .findAny().get()

    then:
    types.values.size() == 2
    types.values.get(0).id == "OVERLAY_1"
    types.values.get(0).name == "Overlay I"
    types.values.get(1).id == "OVERLAY_2"
    types.values.get(1).name == "Overlay II"
  }

  def ipvResolutionTypesClassifier() {
    return new Classifier(
      "ipvExceptionManagementCalculationStatuses",
      null,
      List.of(
      new Classifier("KEEP", List.of(new Classifier("RE_RUN_UPDATED"))),
      new Classifier("SWITCH_TO_SECONDARY", List.of(new Classifier("SWITCH_TO_SECONDARY")))
      ))
  }

  def entryResultStatusDetailClassifier() {
    return new Classifier(
      "entryResultStatusDetail",
      null,
      List.of(
      new Classifier("QUERY_VENDOR")
      ))
  }
}
