package com.solum.xplain.xm.excmngmt.rulesipv

import static com.solum.xplain.core.portfolio.CoreProductType.CAP_FLOOR
import static com.solum.xplain.core.portfolio.CoreProductType.CDS
import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.portfolio.CoreProductType.INFLATION
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import spock.lang.Specification
import spock.lang.Unroll

class TradeFilterTest extends Specification {

  def static final CURRENCY = "EUR"
  def static final ANOTHER_CURRENCY = "USD"
  def static final FX_PAIR = "EUR/USD"
  def static final ANOTHER_FX_PAIR = "EUR/GBP"
  def static final CREDIT_SECTOR = CreditSector.FINANCIALS
  def static final ANOTHER_CREDIT_SECTOR = CreditSector.UTILITIES

  @Unroll
  def "should correctly match trade #productType and filter #companyEntityPortfolios with #expectedResult"() {
    setup:
    def trade = new Trade(externalCompanyId: "CID", externalEntityId: "EID", portfolioExternalId: "PID", productType: productType)
    def filter = new TradeFilter(companyEntityPortfolios: companyEntityPortfolios, productTypes: [IRS])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    companyEntityPortfolios                         | productType | expectedResult
    null                                            | IRS         | true
    [:]                                             | IRS         | true
    ["CID": [:]]                                    | IRS         | true
    ["CID": ["EID": []]]                            | IRS         | true
    ["CID": ["EID": ["PID"]]]                       | IRS         | true
    ["CID": ["EID": ["PID"]]]                       | CDS         | false
    ["X": [:]]                                      | null        | false
    ["X": ["X": []]]                                | null        | false
    ["X": ["X": ["X"]]]                             | null        | false
    ["CID": ["EID": ["X", "PID"]]]                  | IRS         | true
    ["CID": ["EID": ["X"], "EID2": ["PID"]]]        | IRS         | false
    ["CID": ["EID2": []], "CID2": ["EID": ["PID"]]] | IRS         | false
  }

  @Unroll
  def "should correctly match product types filter for #trade.productType"() {
    setup:
    def filter = new TradeFilter(productTypes: [IRS, FXFWD, CDS])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    trade                      | expectedResult
    // RATES
    dummyRatesTrade(IRS)       | true
    dummyRatesTrade(XCCY)      | false
    dummyRatesTrade(INFLATION) | false
    dummyRatesTrade(SWAPTION)  | false
    dummyRatesTrade(CAP_FLOOR) | false
    //FX
    dummyFxTrade(FXFWD)        | true
    dummyFxTrade(FXOPT)        | false
    //CREDIT
    dummyCreditTrade(CDS)      | true
  }

  @Unroll
  def "should correctly match rates currencies filter for #trade.productType"() {
    setup:
    def filter = new TradeFilter(rateCcys: [CURRENCY])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    trade                                        | expectedResult
    // RATES
    dummyRatesTrade(IRS)                         | true
    dummyRatesTrade(IRS, ANOTHER_CURRENCY)       | false
    dummyRatesTrade(XCCY)                        | true
    dummyRatesTrade(XCCY, ANOTHER_CURRENCY)      | false
    dummyRatesTrade(INFLATION)                   | true
    dummyRatesTrade(INFLATION, ANOTHER_CURRENCY) | false
    dummyRatesTrade(SWAPTION)                    | true
    dummyRatesTrade(SWAPTION, ANOTHER_CURRENCY)  | false
    dummyRatesTrade(CAP_FLOOR)                   | true
    dummyRatesTrade(CAP_FLOOR, ANOTHER_CURRENCY) | false
    //FX
    dummyFxTrade(FXFWD)                          | true
    dummyFxTrade(FXOPT)                          | true
    //CREDIT
    dummyCreditTrade(CDS)                        | true
  }

  @Unroll
  def "should correctly match credit sectors filter for #trade.productType"() {
    setup:
    def filter = new TradeFilter(creditSectors: [CREDIT_SECTOR])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    trade                                        | expectedResult
    // RATES
    dummyRatesTrade(IRS)                         | true
    dummyRatesTrade(XCCY)                        | true
    dummyRatesTrade(INFLATION)                   | true
    dummyRatesTrade(SWAPTION)                    | true
    dummyRatesTrade(CAP_FLOOR)                   | true
    //FX
    dummyFxTrade(FXFWD)                          | true
    dummyFxTrade(FXOPT)                          | true
    //CREDIT
    dummyCreditTrade(CDS)                        | true
    dummyCreditTrade(CDS, ANOTHER_CREDIT_SECTOR) | false
  }

  @Unroll
  def "should correctly match fx pairs filter for #trade.productType"() {
    setup:
    def filter = new TradeFilter(fxPairs: [FX_PAIR])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    trade                                | expectedResult
    // RATES
    dummyRatesTrade(IRS)                 | true
    dummyRatesTrade(XCCY)                | true
    dummyRatesTrade(INFLATION)           | true
    dummyRatesTrade(SWAPTION)            | true
    dummyRatesTrade(CAP_FLOOR)           | true
    //FX
    dummyFxTrade(FXFWD)                  | true
    dummyFxTrade(FXFWD, ANOTHER_FX_PAIR) | false
    dummyFxTrade(FXOPT)                  | true
    dummyFxTrade(FXOPT, ANOTHER_FX_PAIR) | false
    //CREDIT
    dummyCreditTrade(CDS)                | true
  }

  @Unroll
  def "should correctly match combined filter for #trade.productType"() {
    setup:
    def filter = new TradeFilter(
      rateCcys: [CURRENCY, ANOTHER_CURRENCY],
      creditSectors: [CREDIT_SECTOR, ANOTHER_CREDIT_SECTOR],
      fxPairs: [FX_PAIR, ANOTHER_FX_PAIR])

    when:
    def result = filter.matches(trade)

    then:
    result == expectedResult

    where:
    trade                                        | expectedResult
    // RATES
    dummyRatesTrade(IRS)                         | true
    dummyRatesTrade(IRS, ANOTHER_CURRENCY)       | true
    dummyRatesTrade(XCCY)                        | true
    dummyRatesTrade(XCCY, ANOTHER_CURRENCY)      | true
    dummyRatesTrade(INFLATION)                   | true
    dummyRatesTrade(INFLATION, ANOTHER_CURRENCY) | true
    dummyRatesTrade(SWAPTION)                    | true
    dummyRatesTrade(SWAPTION, ANOTHER_CURRENCY)  | true
    dummyRatesTrade(CAP_FLOOR)                   | true
    dummyRatesTrade(CAP_FLOOR, ANOTHER_CURRENCY) | true
    //FX
    dummyFxTrade(FXFWD)                          | true
    dummyFxTrade(FXFWD, ANOTHER_FX_PAIR)         | true
    dummyFxTrade(FXOPT)                          | true
    dummyFxTrade(FXOPT, ANOTHER_FX_PAIR)         | true
    //CREDIT
    dummyCreditTrade(CDS)                        | true
    dummyCreditTrade(CDS, ANOTHER_CREDIT_SECTOR) | true
  }

  def dummyRatesTrade(CoreProductType productType, String ccy = CURRENCY) {
    def trade = Mock(Trade)
    trade.productType >> productType
    trade.currency >> ccy
    trade
  }

  def dummyFxTrade(CoreProductType productType, String currencyPair = FX_PAIR) {
    def trade = Mock(Trade)
    trade.productType >> productType
    trade.currencyPair >> currencyPair
    trade
  }

  def dummyCreditTrade(CoreProductType productType, CreditSector creditSector = CREDIT_SECTOR) {
    def trade = Mock(Trade)
    trade.productType >> productType
    trade.creditSector >> creditSector
    trade
  }
}
