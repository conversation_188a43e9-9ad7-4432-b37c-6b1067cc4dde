package com.solum.xplain.xm.settings


import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsForm
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsView
import java.time.LocalDate
import spock.lang.Specification

class ExceptionManagementSettingsControllerServiceTest extends Specification {
  def repository = Mock(ExceptionManagementSettingsRepository)
  def service = new ExceptionManagementSettingsControllerService(repository)

  def "should get settings latest versions"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()


    when:
    def result = service.getSettings(stateDate)

    then:
    1 * repository.entityView(stateDate) >> new ExceptionManagementSettingsView(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL)

    and:
    result == new ExceptionManagementSettingsView(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL)
  }

  def "should get all settings versions"() {
    when:
    service.getSettingsVersions()

    then:
    1 * repository.entityVersions()
  }

  def "should get future versions"() {
    when:
    service.futureVersions(LocalDate.now())

    then:
    1 * repository.futureVersions(LocalDate.now())
  }

  def "should update settings version"() {
    setup:
    def stateDate = LocalDate.now()
    def form = new ExceptionManagementSettingsForm(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL, NewVersionFormV2.newDefault())

    when:
    service.updateSettings(stateDate, form)

    then:
    1 * repository.save(stateDate, form)
  }

  def "should delete settings version"() {
    setup:
    def stateDate = LocalDate.now()

    when:
    service.deleteSettingsVersion(stateDate)

    then:
    1 * repository.deleteSettingsVersion(stateDate)
  }
}
