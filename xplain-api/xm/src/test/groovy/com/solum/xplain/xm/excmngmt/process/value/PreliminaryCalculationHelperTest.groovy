package com.solum.xplain.xm.excmngmt.process.value

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO

import spock.lang.Specification

class PreliminaryCalculationHelperTest extends Specification {
  def "should correctly resolve MD by instrument"() {
    setup:
    def data = [
      "A": [:],
      "B": ["1": [(BID): ZERO]],
      "C": ["1": [(BID): ONE],
        "2": [(BID): TEN]],
    ]
    def prevData = [
      "C": ["2": [(MID): ONE],
        "3": [(BID): ZERO]],
      "D": ["1": [(BID): TEN]],
    ]

    when:
    def result = PreliminaryCalculationHelper.marketDataProvidersByInstrument(data, prevData)

    then:
    result.size() == 4

    result["A"].isEmpty()

    result["B"].size() == 3
    result["B"][0].provider == "1"
    result["B"][0].bidAskType == ASK
    result["B"][0].value == null
    result["B"][0].previousValue == null
    result["B"][1].provider == "1"
    result["B"][1].bidAskType == MID
    result["B"][1].value == null
    result["B"][1].previousValue == null
    result["B"][2].provider == "1"
    result["B"][2].bidAskType == BID
    result["B"][2].value == ZERO
    result["B"][2].previousValue == null

    result["C"].size() == 9
    result["C"][0].provider == "1"
    result["C"][0].bidAskType == ASK
    result["C"][0].value == null
    result["C"][0].previousValue == null
    result["C"][1].provider == "1"
    result["C"][1].bidAskType == MID
    result["C"][1].value == null
    result["C"][1].previousValue == null
    result["C"][2].provider == "1"
    result["C"][2].bidAskType == BID
    result["C"][2].value == ONE
    result["C"][2].previousValue == null

    result["C"][3].provider == "2"
    result["C"][3].bidAskType == ASK
    result["C"][3].value == null
    result["C"][3].previousValue == null
    result["C"][4].provider == "2"
    result["C"][4].bidAskType == MID
    result["C"][4].value == null
    result["C"][4].previousValue == ONE
    result["C"][5].provider == "2"
    result["C"][5].bidAskType == BID
    result["C"][5].value == TEN
    result["C"][5].previousValue == null

    result["C"][6].provider == "3"
    result["C"][6].bidAskType == ASK
    result["C"][6].value == null
    result["C"][6].previousValue == null
    result["C"][7].provider == "3"
    result["C"][7].bidAskType == MID
    result["C"][7].value == null
    result["C"][7].previousValue == null
    result["C"][8].provider == "3"
    result["C"][8].bidAskType == BID
    result["C"][8].value == null
    result["C"][8].previousValue == ZERO

    result["D"].size() == 3
    result["D"][0].provider == "1"
    result["D"][0].bidAskType == ASK
    result["D"][0].value == null
    result["D"][0].previousValue == null
    result["D"][1].provider == "1"
    result["D"][1].bidAskType == MID
    result["D"][1].value == null
    result["D"][1].previousValue == null
    result["D"][2].provider == "1"
    result["D"][2].bidAskType == BID
    result["D"][2].value == null
    result["D"][2].previousValue == TEN
  }
}
