package com.solum.xplain.xm.workflow.steps.md

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext
import com.solum.xplain.xm.workflow.state.MdPreliminaryState
import org.springframework.beans.MutablePropertyValues
import spock.lang.Specification

class SetInitialBaseValueStepTest extends Specification {
  StepStateOps stepStateOps = Mock(StepStateOps)
  static ProviderData nullMv = ProviderData.of("NULL", null, null, ValueBidAskType.MID)
  static ProviderData validMv = ProviderData.of("VALID", 1.0, 0.9, ValueBidAskType.MID)

  def "should set outcome (#availableMv.provider, #requestedProvider)"() {
    given:
    def currentState = new MdPreliminaryState()
    def currentContext = new MdPreliminaryContext(BitemporalDate.newOfNow(), null, "mdgId", null, null, ValueBidAskType.MID, expectedProviderName,
      [availableMv], null, false)
    stepStateOps.getInitialState() >> currentState
    stepStateOps.getContext() >> currentContext

    when:
    new SetInitialBaseValueStep().runStep(stepStateOps)

    then:
    1 * stepStateOps.setOutcome(_) >> { MutablePropertyValues outcome ->
      assert outcome.get("baseValue") == expectedBaseValue
      assert outcome.get("providerName") == expectedProviderName
    }


    where:
    availableMv | requestedProvider || expectedBaseValue | expectedProviderName
    nullMv      | "NULL"            || null              | "NULL"
    validMv     | "VALID"           || 1.0               | "VALID"
    validMv     | "MISSING"         || null              | null
  }
}
