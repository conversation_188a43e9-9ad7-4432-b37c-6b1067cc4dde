package com.solum.xplain.xm.excmngmt.process.value


import com.solum.xplain.xm.excmngmt.rules.BreakTest
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent
import spock.lang.Specification

class BreakTestCalculationsPreliminaryTest extends Specification {

  def "should correctly create set of tests"() {
    setup:
    def breakTest = Mock(BreakTest)
    def child = Mock(BreakTest)
    child.getParentTest() >> new BreakTestParent(parentId: "parentId")

    def breakTests = ["parentId": breakTest, "id": child]

    when:
    def result = BreakTestCalculationsPreliminary.ofBreakTests(breakTests)

    then:
    result.calculations.size() == 1
    result.calculations[0].breakTest == breakTest
  }

  def "should correctly invoke calculations"() {
    setup:
    def c = Mock(BreakTestCalculationPreliminary)
    def instrCalc = Mock(InstrumentMarketDataBreakCalculatorPreliminary)
    def calculations = new BreakTestCalculationsPreliminary([c])

    1 * c.resolveBreak(instrCalc) >> null

    when:
    def result = calculations.processCalc(instrCalc)

    then:
    result.isEmpty()
  }
}
