package com.solum.xplain.xm.excmngmt.process

import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.ValueSet
import com.solum.xplain.workflow.repository.WorkflowDataCacheService
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import java.time.LocalDate
import java.time.Month
import org.bson.types.ObjectId
import org.springframework.cache.Cache
import org.springframework.cache.Cache.ValueWrapper
import spock.lang.Specification

class CachingPreliminaryDataServiceTest extends Specification {
  def instrumentResultPreliminaryQueryRepository = Mock(InstrumentResultPreliminaryQueryRepository)
  def dataGrid = Mock(DataGrid)
  def instrumentResultPreliminaryCache = Mock(Cache)
  def workflowDataCacheService = Mock(WorkflowDataCacheService)
  def cachingPreliminaryDataService = new CachingPreliminaryDataService(
  instrumentResultPreliminaryQueryRepository, dataGrid, workflowDataCacheService)

  def setup() {
    workflowDataCacheService.getCache(InstrumentResultPreliminary.class) >> instrumentResultPreliminaryCache
  }

  def "should add result ID to set"() {
    given:
    def id = ObjectId.get()
    def record = new InstrumentResultPreliminary(
      id: id.toHexString(),
      marketDataGroupId: "mdg",
      valuationDate: LocalDate.of(2024, Month.AUGUST, 13),
      instrument: new Instrument(key: "INSTRUMENT"),
      providerData: new ProviderData(provider: "BOB", bidAskType: ValueBidAskType.BID)
      )

    when:
    cachingPreliminaryDataService.addToMdkSet(record)

    then:
    1 * dataGrid.addValueToSet("mdg-2024-08-13-INSTRUMENT", id)
  }

  def "should find and remove price point set using cache"() {
    given:
    def id1 = ObjectId.get()
    def id2 = ObjectId.get()
    def marketDataGroupId = "mdg"
    def stateDate = LocalDate.of(2024, Month.AUGUST, 13)
    def instrumentKey = "INSTRUMENT"

    def cached1 = Mock(ValueWrapper) {
      get() >> new InstrumentResultPreliminary(providerData: new ProviderData(provider: "BOB", bidAskType: ValueBidAskType.BID))
    }
    def cached2 = null  // simulate cache miss
    def repoResult2 = new InstrumentResultPreliminary(providerData: new ProviderData(provider: "ALICE", bidAskType: ValueBidAskType.BID))

    def valueSet = Mock(ValueSet) {
      values() >> [id1, id2]
    }

    dataGrid.getValueSet("mdg-2024-08-13-INSTRUMENT") >> valueSet
    instrumentResultPreliminaryCache.get(id1, InstrumentResultPreliminary) >> cached1.get()
    instrumentResultPreliminaryCache.get(id2, InstrumentResultPreliminary) >> null
    instrumentResultPreliminaryQueryRepository.findById(id2.toHexString()) >> repoResult2

    when:
    def result = cachingPreliminaryDataService.findAndRemoveMdkSet(marketDataGroupId, stateDate, instrumentKey)

    then:
    result.size() == 2
    result[0].providerData.provider == "BOB"
    result[1].providerData.provider == "ALICE"

    1 * instrumentResultPreliminaryCache.evict(id1)
    0 * instrumentResultPreliminaryCache.evict(id2)

    1 * valueSet.close()
  }


  def "should find and remove mdk set using cache"() {
    given:
    def id1 = ObjectId.get()
    def id2 = ObjectId.get()
    def marketDataGroupId = "mdg"
    def stateDate = LocalDate.of(2024, Month.AUGUST, 13)
    def instrumentKey = "INSTRUMENT"
    def instrumentResultPreliminary1 = new InstrumentResultPreliminary(providerData: new ProviderData(provider: "BOB", bidAskType: ValueBidAskType.BID))
    def instrumentResultPreliminary2 = new InstrumentResultPreliminary(providerData: new ProviderData(provider: "ALICE", bidAskType: ValueBidAskType.BID))
    def valueSet = Mock(ValueSet, {
      values() >> [id1, id2]
    })
    dataGrid.getValueSet("mdg-2024-08-13-INSTRUMENT") >> valueSet

    when:
    def result = cachingPreliminaryDataService.findAndRemoveMdkSet(marketDataGroupId, stateDate, instrumentKey)

    then:
    result == [instrumentResultPreliminary1, instrumentResultPreliminary2]

    and: "instrument 1 came from cache"
    1 * instrumentResultPreliminaryCache.get(id1, InstrumentResultPreliminary.class) >> instrumentResultPreliminary1
    1 * instrumentResultPreliminaryCache.evict(id1)
    0 * instrumentResultPreliminaryQueryRepository.findById(id1.toHexString()) >> instrumentResultPreliminary1

    and: "instrument 2 came from repository"
    1 * instrumentResultPreliminaryCache.get(id2, InstrumentResultPreliminary.class) >> null
    0 * instrumentResultPreliminaryCache.evict(id2)
    1 * instrumentResultPreliminaryQueryRepository.findById(id2.toHexString()) >> instrumentResultPreliminary2

    and: "value set was removed"
    1 * valueSet.close()
  }
}
