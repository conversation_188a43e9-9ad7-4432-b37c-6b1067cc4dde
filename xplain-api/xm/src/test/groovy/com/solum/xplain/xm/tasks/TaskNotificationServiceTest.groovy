package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.sockets.WrappedEmitter.defaultEmitter
import static com.solum.xplain.xm.dashboards.sockets.type.DashboardSocketEvent.dashboardReference

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams
import com.solum.xplain.core.sockets.SocketReference
import com.solum.xplain.core.sockets.constants.CoreSocketTypes
import com.solum.xplain.core.sockets.events.SocketEvent
import com.solum.xplain.core.teams.value.TeamNameView
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.service.TaskNotificationService
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class TaskNotificationServiceTest extends Specification {
  AuthenticationContext authenticationContext = Mock()
  ApplicationEventPublisher publisher = Mock()

  TaskNotificationService service = new TaskNotificationService(authenticationContext, publisher)

  def "should correctly notify users on tasks/dashboards changes"() {
    setup:
    def eventType = "TYPE"
    def executions = [
      new TaskExecution(dashboardId: "D1", resolutionTeams: [new TeamNameView(id: "T1")]),
      new TaskExecution(dashboardId: "D2", approvalTeams: [new TeamNameView(id: "T2")]),
    ]
    def defaultTeams = new MdTaskDefaultTeams(
    defaultResolutionTeam: new TeamNameView(id: "RT"),
    defaultApprovalTeam: new TeamNameView(id: "AT")
    )

    def userIds = ["userId"]
    def expectedTeams = ["T1", "T2", "RT", "AT"]
    1 * authenticationContext.activeUserIdsInTeams({
      verifyAll(it, List<String>) {
        containsAll(expectedTeams)
        size() == expectedTeams.size()
      }
    }) >> userIds

    when:
    service.notifyUsers(eventType, executions, defaultTeams)

    then:
    1 * publisher.publishEvent({
      verifyAll(it, SocketEvent) {
        audienceType == CoreSocketTypes.USER
        audienceIds == userIds
        eventType == eventType
        data == "Tasks changed"
      }
    })

    1 * publisher.publishEvent({
      verifyAll(it, SocketEvent) {
        audienceType == "DASHBOARD"
        audienceIds == ["D1"]
        eventType == eventType
        data == null
      }
    })

    1 * publisher.publishEvent({
      verifyAll(it, SocketEvent) {
        audienceType == "DASHBOARD"
        audienceIds == ["D2"]
        eventType == eventType
        data == null
      }
    })
  }
}
