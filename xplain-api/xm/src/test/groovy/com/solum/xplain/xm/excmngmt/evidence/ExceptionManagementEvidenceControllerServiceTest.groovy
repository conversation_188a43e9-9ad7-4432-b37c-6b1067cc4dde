package com.solum.xplain.xm.excmngmt.evidence

import spock.lang.Specification

class ExceptionManagementEvidenceControllerServiceTest extends Specification {
  ExceptionManagementEvidenceRepository exceptionManagementEvidenceRepository = Mock()

  def service = new ExceptionManagementEvidenceControllerService(exceptionManagementEvidenceRepository)


  def "should invoke repository when download requested"() {
    when:
    service.exportEvidence("ID")

    then:
    1 * exceptionManagementEvidenceRepository.exportFile("ID")
  }
}
