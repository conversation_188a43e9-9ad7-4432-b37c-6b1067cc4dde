package com.solum.xplain.xm.excmngmt.process.value

import com.opengamma.strata.basics.date.Tenor
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.rules.BreakTest
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import org.bson.types.ObjectId
import spock.lang.Specification

class BreakTestCalculationsOverlayTest extends Specification {

  def "should correctly create set of tests"() {
    setup:
    def breakTest = Mock(BreakTest)
    def child1 = Mock(BreakTest)
    child1.getEnabled() >> true
    child1.getParentTest() >> new BreakTestParent(parentId: "parentId")
    def child2 = Mock(BreakTest)
    child2.getEnabled() >> false
    child2.getParentTest() >> new BreakTestParent(parentId: "parentId")

    def breakTests = ["parentId": breakTest, "id1": child1, "id2": child2]

    when:
    def result = BreakTestCalculationsOverlay.ofBreakTests(breakTests)

    then:
    result.calculations.size() == 1
    result.calculations[0].entityId == "parentId"
    result.calculations[0].breakTest == breakTest
    result.calculations[0].dependentTests == [child1]
  }

  def "should correctly invoke calculations"() {
    setup:
    def c = Mock(BreakTestCalculationOverlay)
    def instrCalc = Mock(InstrumentMarketDataBreakCalculatorOverlay)
    def calculations = new BreakTestCalculationsOverlay([c])

    def breaks = Mock(InstrumentResultBreak)
    1 * c.resolveBreak(instrCalc) >> [breaks]

    when:
    def result = calculations.processCalc(instrCalc)

    then:
    result.size() == 1
    result[0] == breaks
  }

  def "should correctly identify Z-Score observation periods"() {
    setup:
    BreakTest zScoreTest1 = new BreakTest(id: ObjectId.get().toString(), entityId: ObjectId.get().toString(), name: "ZScore Test 1", enabled: true, measureType: MeasureType.Z_SCORE, operator: Operator.GTE, threshold: List.of(1), zScoreObservationPeriod: "1Y")
    BreakTest zScoreTest2 = new BreakTest(id: ObjectId.get().toString(), entityId: ObjectId.get().toString(), name: "ZScore Test 2", enabled: true, measureType: MeasureType.Z_SCORE, operator: Operator.GTE, threshold: List.of(1), zScoreObservationPeriod: "2Y")
    BreakTest pvsTest = new BreakTest(id: ObjectId.get().toString(), entityId: ObjectId.get().toString(), name: "PVS Test", enabled: true, measureType: MeasureType.RELATIVE_DIFF, operator: Operator.GTE, threshold: List.of(0.5), parentTest: new BreakTestParent(parentId: zScoreTest1.entityId, name: zScoreTest1.name))

    BreakTestCalculationsOverlay calculations = BreakTestCalculationsOverlay.ofBreakTests([(zScoreTest1.entityId): zScoreTest1, (zScoreTest2.entityId): zScoreTest2, (pvsTest.entityId): pvsTest])

    when:
    def tenors = calculations.getObservationPeriods(MeasureType.Z_SCORE).sort(false) { it.period.toTotalMonths() + it.period.getDays() }

    then:
    tenors == [Tenor.TENOR_12M, Tenor.TENOR_2Y]
  }
}
