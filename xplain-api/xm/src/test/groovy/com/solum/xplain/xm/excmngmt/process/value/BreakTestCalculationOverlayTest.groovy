package com.solum.xplain.xm.excmngmt.process.value

import static com.solum.xplain.xm.excmngmt.rules.value.MeasureType.ABSOLUTE_DIFF
import static com.solum.xplain.xm.excmngmt.rules.value.MeasureType.RELATIVE_DIFF
import static com.solum.xplain.xm.excmngmt.rules.value.MeasureType.Z_SCORE
import static com.solum.xplain.xm.excmngmt.rules.value.Operator.EQ
import static com.solum.xplain.xm.excmngmt.rules.value.TestType.DAY_TO_DAY
import static com.solum.xplain.xm.excmngmt.rules.value.TestType.PRIMARY_VS_SECONDARY
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN

import com.opengamma.strata.basics.date.Tenor
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.process.enums.BreakProviderType
import com.solum.xplain.xm.excmngmt.rules.BreakTest
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent
import com.solum.xplain.xm.excmngmt.rules.value.TestType
import spock.lang.Specification

class BreakTestCalculationOverlayTest extends Specification {

  private static final def TEST_THRESHOLD = [ONE, TEN]
  private static final def DEPENDANT_TEST_THRESHOLD = [TEN]

  private BigDecimal P1_MEASURE_VALUE = BigDecimal.valueOf(11)
  private EntryResultBreakByProvider P1_RESULT = resultBreak(P1_MEASURE_VALUE, false, TEST_THRESHOLD[0])
  private EntryResultBreakByProvider P1_RESULT_CALCULATION_ONLY = resultBreak(P1_MEASURE_VALUE, true, TEST_THRESHOLD[0])

  private BigDecimal P2_MEASURE_VALUE = BigDecimal.valueOf(22)
  private EntryResultBreakByProvider P2_RESULT = resultBreak(P2_MEASURE_VALUE, false, TEST_THRESHOLD[0])
  private EntryResultBreakByProvider P2_RESULT_CALCULATION_ONLY = resultBreak(P2_MEASURE_VALUE, true, TEST_THRESHOLD[0])

  private BigDecimal P1_VS_P2_MEASURE_VALUE = BigDecimal.valueOf(33)
  private EntryResultBreakByProvider P1_VS_P2_RESULT = resultBreak(P1_VS_P2_MEASURE_VALUE, false, DEPENDANT_TEST_THRESHOLD[0])

  def instrument = Mock(InstrumentDefinition)
  def instrumentCalculator = instrumentCalculator(instrument)
  def dependantInstrumentCalculator = instrumentCalculator(instrument)

  def "should correctly accept dependant tests"() {
    setup:
    def breakTest = new BreakTest(entityId: "id", enabled: true)

    def dependantBreakTest1 = new BreakTest(
      parentTest: new BreakTestParent(parentId: breakTest.id),
      enabled: true
      )
    def dependantBreakTest2 = new BreakTest(
      parentTest: new BreakTestParent(parentId: "anotherId"),
      enabled: true
      )
    def dependantBreakTest3 = new BreakTest(
      parentTest: new BreakTestParent(parentId: breakTest.id),
      enabled: false
      )

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    calculation.acceptDependant(dependantBreakTest1)
    calculation.acceptDependant(dependantBreakTest2)
    calculation.acceptDependant(dependantBreakTest3)

    then:
    calculation.dependentTests.size() == 1
    calculation.dependentTests[0] == dependantBreakTest1
  }

  def "should resolve no breaks when instrument does not match"() {
    setup:
    def breakTest = Mock(BreakTest)
    breakTest.matches(instrument) >> false

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    def breaks = calculation.resolveBreak(instrumentCalculator)

    then:
    breaks.size() == 0
  }

  def "should not resolve breaks when test disabled"() {
    setup:
    def breakTest = Mock(BreakTest)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> false
    breakTest.matches(instrument) >> true

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    def breaks = calculation.resolveBreak(instrumentCalculator)

    then:
    breaks.size() == 0
  }

  def "should correctly resolve DAY_TO_DAY"() {
    setup:
    def breakTest = Mock(BreakTest)
    def resultResolver = EQ.resolver(TEST_THRESHOLD)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> true
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> DAY_TO_DAY
    breakTest.getName() >> "TEST"
    breakTest.getMeasureType() >> ABSOLUTE_DIFF
    breakTest.getOperator() >> EQ
    breakTest.resultResolver(instrument) >> resultResolver
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    def breaks = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.PRIMARY,
      ABSOLUTE_DIFF,
      resultResolver
      ) >> P1_RESULT
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.SECONDARY,
      ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> P2_RESULT_CALCULATION_ONLY

    breaks.size() == 2
    breaks[0].breakTestName == "TEST"
    breaks[0].breakTestType == DAY_TO_DAY.getName()
    breaks[0].measureType == ABSOLUTE_DIFF
    breaks[0].operator == EQ
    breaks[0].threshold == TEST_THRESHOLD[0]
    breaks[0].providerType == BreakProviderType.PRIMARY
    breaks[0].providerValue == P1_RESULT
    breaks[0].parentBreakTestName == null

    breaks[1].breakTestName == "TEST"
    breaks[1].breakTestType == DAY_TO_DAY.getName()
    breaks[1].measureType == ABSOLUTE_DIFF
    breaks[1].operator == EQ
    breaks[1].threshold == TEST_THRESHOLD[0]
    breaks[1].providerType == BreakProviderType.SECONDARY
    breaks[1].providerValue == P2_RESULT_CALCULATION_ONLY
    breaks[1].parentBreakTestName == null
  }

  def "should correctly resolve DAY_TO_DAY breaks with dependant"() {
    setup:
    def breakTest = Mock(BreakTest)
    def resultResolver = EQ.resolver(TEST_THRESHOLD)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> true
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> DAY_TO_DAY
    breakTest.getName() >> "TEST"
    breakTest.getMeasureType() >> ABSOLUTE_DIFF
    breakTest.getOperator() >> EQ
    breakTest.resultResolver(instrument) >> resultResolver
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]

    def dependantBreakTest = Mock(BreakTest)
    def dependantResultResolver = EQ.resolver(DEPENDANT_TEST_THRESHOLD)
    dependantBreakTest.getId() >> "dId"
    dependantBreakTest.getEnabled() >> true
    dependantBreakTest.matches(instrument) >> true
    dependantBreakTest.getParentTest() >> new BreakTestParent(parentId: breakTest.id, name: breakTest.name)
    dependantBreakTest.getType() >> PRIMARY_VS_SECONDARY
    dependantBreakTest.getName() >> "P1 VS P2 TEST"
    dependantBreakTest.getMeasureType() >> RELATIVE_DIFF
    dependantBreakTest.getOperator() >> EQ
    dependantBreakTest.resultResolver(instrument) >> dependantResultResolver
    dependantBreakTest.resolveFirstThreshold(instrument) >> DEPENDANT_TEST_THRESHOLD[0]

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    calculation.acceptDependant(dependantBreakTest)
    def breaks = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.PRIMARY,
      ABSOLUTE_DIFF,
      resultResolver
      ) >> P1_RESULT
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.SECONDARY,
      ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> P2_RESULT_CALCULATION_ONLY
    1 * instrumentCalculator.toMeasureProcessed(
      P1_MEASURE_VALUE,
      P2_MEASURE_VALUE
      ) >> dependantInstrumentCalculator
    1 * dependantInstrumentCalculator.providerDiff(
      RELATIVE_DIFF,
      dependantResultResolver
      ) >> P1_VS_P2_RESULT

    breaks.size() == 3

    breaks[0].breakTestName == "TEST"
    breaks[0].breakTestType == DAY_TO_DAY.getName()
    breaks[0].measureType == ABSOLUTE_DIFF
    breaks[0].operator == EQ
    breaks[0].threshold == TEST_THRESHOLD[0]
    breaks[0].providerType == BreakProviderType.PRIMARY
    breaks[0].providerValue == P1_RESULT
    breaks[0].parentBreakTestName == null

    breaks[1].breakTestName == "TEST"
    breaks[1].breakTestType == DAY_TO_DAY.getName()
    breaks[1].measureType == ABSOLUTE_DIFF
    breaks[1].operator == EQ
    breaks[1].threshold == TEST_THRESHOLD[0]
    breaks[1].providerType == BreakProviderType.SECONDARY
    breaks[1].providerValue == P2_RESULT_CALCULATION_ONLY
    breaks[1].parentBreakTestName == null

    breaks[2].breakTestName == "P1 VS P2 TEST"
    breaks[2].breakTestType == PRIMARY_VS_SECONDARY.getName()
    breaks[2].measureType == RELATIVE_DIFF
    breaks[2].operator == EQ
    breaks[2].threshold == DEPENDANT_TEST_THRESHOLD[0]
    breaks[2].providerType == null
    breaks[2].providerValue == P1_VS_P2_RESULT
    breaks[2].parentBreakTestName == "TEST"
  }

  def "should correctly resolve DAY_TO_DAY breaks with dependant when test disabled"() {
    setup:
    def breakTest = Mock(BreakTest)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> false
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> DAY_TO_DAY
    breakTest.getName() >> "TEST"
    breakTest.getMeasureType() >> ABSOLUTE_DIFF
    breakTest.getOperator() >> EQ
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]

    def dependantBreakTest = Mock(BreakTest)
    def dependantResultResolver = EQ.resolver(DEPENDANT_TEST_THRESHOLD)
    dependantBreakTest.getId() >> "dId"
    dependantBreakTest.getEnabled() >> true
    dependantBreakTest.matches(instrument) >> true
    dependantBreakTest.getParentTest() >> new BreakTestParent(parentId: breakTest.id, name: breakTest.name)
    dependantBreakTest.getType() >> PRIMARY_VS_SECONDARY
    dependantBreakTest.getName() >> "P1 VS P2 TEST"
    dependantBreakTest.getMeasureType() >> RELATIVE_DIFF
    dependantBreakTest.getOperator() >> EQ
    dependantBreakTest.resultResolver(instrument) >> dependantResultResolver
    dependantBreakTest.resolveFirstThreshold(instrument) >> DEPENDANT_TEST_THRESHOLD[0]

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    calculation.acceptDependant(dependantBreakTest)
    def results = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.PRIMARY,
      ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> P1_RESULT_CALCULATION_ONLY
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.SECONDARY,
      ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> P2_RESULT_CALCULATION_ONLY
    1 * instrumentCalculator.toMeasureProcessed(
      P1_MEASURE_VALUE,
      P2_MEASURE_VALUE
      ) >> dependantInstrumentCalculator
    1 * dependantInstrumentCalculator.providerDiff(
      RELATIVE_DIFF,
      dependantResultResolver
      ) >> P1_VS_P2_RESULT

    results.size() == 3

    results[0].breakTestName == "TEST"
    results[0].breakTestType == DAY_TO_DAY.getName()
    results[0].measureType == ABSOLUTE_DIFF
    results[0].operator == EQ
    results[0].threshold == TEST_THRESHOLD[0]
    results[0].providerType == BreakProviderType.PRIMARY
    results[0].providerValue == P1_RESULT_CALCULATION_ONLY
    results[0].parentBreakTestName == null

    results[1].breakTestName == "TEST"
    results[1].breakTestType == DAY_TO_DAY.getName()
    results[1].measureType == ABSOLUTE_DIFF
    results[1].operator == EQ
    results[1].threshold == TEST_THRESHOLD[0]
    results[1].providerType == BreakProviderType.SECONDARY
    results[1].providerValue == P2_RESULT_CALCULATION_ONLY
    results[1].parentBreakTestName == null

    results[2].breakTestName == "P1 VS P2 TEST"
    results[2].breakTestType == PRIMARY_VS_SECONDARY.getName()
    results[2].measureType == RELATIVE_DIFF
    results[2].operator == EQ
    results[2].threshold == DEPENDANT_TEST_THRESHOLD[0]
    results[2].providerType == null
    results[2].providerValue == P1_VS_P2_RESULT
    results[2].parentBreakTestName == "TEST"
  }

  def "should correctly resolve DAY_TO_DAY breaks with dependant when dependent disabled"() {
    setup:
    def breakTest = Mock(BreakTest)
    def resultResolver = EQ.resolver(TEST_THRESHOLD)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> true
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> DAY_TO_DAY
    breakTest.getName() >> "TEST"
    breakTest.getMeasureType() >> ABSOLUTE_DIFF
    breakTest.getOperator() >> EQ
    breakTest.resultResolver(instrument) >> resultResolver
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]

    def dependantBreakTest = Mock(BreakTest)
    dependantBreakTest.getId() >> "dId"
    dependantBreakTest.getEnabled() >> false
    dependantBreakTest.matches(instrument) >> true
    dependantBreakTest.getParentTest() >> new BreakTestParent(parentId: breakTest.id, name: breakTest.name)
    dependantBreakTest.getType() >> PRIMARY_VS_SECONDARY
    dependantBreakTest.getName() >> "P1 VS P2 TEST"
    dependantBreakTest.getMeasureType() >> RELATIVE_DIFF
    dependantBreakTest.getOperator() >> EQ
    dependantBreakTest.resolveFirstThreshold(instrument) >> DEPENDANT_TEST_THRESHOLD[0]

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    calculation.acceptDependant(dependantBreakTest)
    def results = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.PRIMARY,
      ABSOLUTE_DIFF,
      resultResolver
      ) >> P1_RESULT
    1 * instrumentCalculator.dayOnDay(
      BreakProviderType.SECONDARY,
      ABSOLUTE_DIFF,
      EntryResultResolver.calculationOnly()
      ) >> P2_RESULT

    results.size() == 2

    results[0].breakTestName == "TEST"
    results[0].breakTestType == DAY_TO_DAY.getName()
    results[0].measureType == ABSOLUTE_DIFF
    results[0].operator == EQ
    results[0].threshold == TEST_THRESHOLD[0]
    results[0].providerType == BreakProviderType.PRIMARY
    results[0].providerValue == P1_RESULT
    results[0].parentBreakTestName == null

    results[1].breakTestName == "TEST"
    results[1].breakTestType == DAY_TO_DAY.getName()
    results[1].measureType == ABSOLUTE_DIFF
    results[1].operator == EQ
    results[1].threshold == TEST_THRESHOLD[0]
    results[1].providerType == BreakProviderType.SECONDARY
    results[1].providerValue == P2_RESULT
    results[1].parentBreakTestName == null
  }

  def "should correctly resolve VALUE"() {
    setup:
    def breakTest = Mock(BreakTest)
    def resultResolver = EQ.resolver(TEST_THRESHOLD)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> true
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> TestType.VALUE
    breakTest.getName() >> "VALUE TEST"
    breakTest.getMeasureType() >> Z_SCORE
    breakTest.getOperator() >> EQ
    breakTest.resultResolver(instrument) >> resultResolver
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]
    breakTest.zScoreObservationPeriodNormalized() >> Tenor.ofDays(1)

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    def results = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.value(
      BreakProviderType.PRIMARY,
      Z_SCORE,
      Tenor.ofDays(1),
      resultResolver
      ) >> P1_RESULT
    1 * instrumentCalculator.value(
      BreakProviderType.SECONDARY,
      Z_SCORE,
      Tenor.ofDays(1),
      EntryResultResolver.calculationOnly()
      ) >> P2_RESULT_CALCULATION_ONLY

    results.size() == 2

    results[0].breakTestName == "VALUE TEST"
    results[0].breakTestType == TestType.VALUE.getName()
    results[0].measureType == Z_SCORE
    results[0].operator == EQ
    results[0].threshold == TEST_THRESHOLD[0]
    results[0].providerType == BreakProviderType.PRIMARY
    results[0].providerValue == P1_RESULT
    results[0].parentBreakTestName == null

    results[1].breakTestName == "VALUE TEST"
    results[1].breakTestType == TestType.VALUE.getName()
    results[1].measureType == Z_SCORE
    results[1].operator == EQ
    results[1].threshold == TEST_THRESHOLD[0]
    results[1].providerType == BreakProviderType.SECONDARY
    results[1].providerValue == P2_RESULT_CALCULATION_ONLY
    results[1].parentBreakTestName == null
  }

  def "should correctly resolve PRIMARY_VS_SECONDARY"() {
    setup:
    def breakTest = Mock(BreakTest)
    def resultResolver = EQ.resolver(TEST_THRESHOLD)
    breakTest.getId() >> "id"
    breakTest.getEnabled() >> true
    breakTest.matches(instrument) >> true
    breakTest.getOverrides() >> []
    breakTest.getType() >> PRIMARY_VS_SECONDARY
    breakTest.getName() >> "P1 VS P2 TEST"
    breakTest.getMeasureType() >> ABSOLUTE_DIFF
    breakTest.getOperator() >> EQ
    breakTest.resultResolver(instrument) >> resultResolver
    breakTest.resolveFirstThreshold(instrument) >> TEST_THRESHOLD[0]

    when:
    def calculation = BreakTestCalculationOverlay.ofBreakTest(breakTest.id, breakTest)
    def results = calculation.resolveBreak(instrumentCalculator)

    then:
    1 * instrumentCalculator.providerDiff(
      ABSOLUTE_DIFF,
      resultResolver
      ) >> P1_VS_P2_RESULT

    results.size() == 1

    results[0].breakTestName == "P1 VS P2 TEST"
    results[0].breakTestType == PRIMARY_VS_SECONDARY.getName()
    results[0].measureType == ABSOLUTE_DIFF
    results[0].operator == EQ
    results[0].threshold == TEST_THRESHOLD[0]
    results[0].providerType == null
    results[0].providerValue == P1_VS_P2_RESULT
    results[0].parentBreakTestName == null
  }

  def resultBreak(BigDecimal measureValue, boolean calculationOnly, BigDecimal threshold) {
    def result = Mock(EntryResultBreakByProvider)
    result.getValue() >> measureValue
    result.isCalculationOnly() >> calculationOnly
    result.getTriggeredThreshold() >> threshold
    result
  }

  def instrumentCalculator(InstrumentDefinition instrument) {
    def instrumentCalculator = Mock(InstrumentMarketDataBreakCalculatorOverlay)
    instrumentCalculator.getInstrument() >> instrument
    instrumentCalculator
  }
}
