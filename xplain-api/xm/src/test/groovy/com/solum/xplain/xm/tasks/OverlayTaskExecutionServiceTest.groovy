package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver.fromConfiguration
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.REJECTED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_SUBMISSION
import static com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType.OVERLAY

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.core.instrument.InstrumentTypeResolverHelper
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams
import com.solum.xplain.core.settings.repository.TaskDefaultTeamsSettingsRepository
import com.solum.xplain.core.sockets.events.EventType
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamNameView
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboardsteps.mdoverlayclearing.MdOverlayClearingEvent
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.excmngmt.value.RevertEntriesRequest
import com.solum.xplain.xm.tasks.entity.AssetClassTaskTeams
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.entity.TasksDefinition
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.tasks.enums.TaskGranularityByAssetClassType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByInstrumentType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType
import com.solum.xplain.xm.tasks.repository.TaskDefinitionRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import com.solum.xplain.xm.tasks.service.OverlayTaskExecutionService
import com.solum.xplain.xm.tasks.service.TaskNotificationService
import com.solum.xplain.xm.tasks.value.TaskExecutionsResolver
import com.solum.xplain.xm.tasks.value.UniqueExcptMngmntTask
import com.solum.xplain.xm.workflow.XmWorkflowService
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import org.mapstruct.factory.Mappers
import org.springframework.context.ApplicationEventPublisher
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.Authentication
import spock.lang.Specification

class OverlayTaskExecutionServiceTest extends Specification {
  ExceptionManagementCalculationRepository calculationRepository = Mock()
  TaskExecutionRepository executionRepository = Mock()
  AuthenticationContext userRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  AuditEntryService auditEntryService = Mock()
  TaskNotificationService taskNotificationService = Mock()
  TaskDefaultTeamsSettingsRepository taskDefaultsRepository = Mock()
  TeamRepository teamRepository = Mock()
  TaskDefinitionRepository taskDefinitionRepository = Mock()
  XmStepInstanceQueryRepository xmStepInstanceQueryRepository = Mock()
  XmWorkflowService xmWorkflowService = Mock()
  TaskMapper mapper = Mappers.getMapper(TaskMapper)

  OverlayTaskExecutionService service = new OverlayTaskExecutionService(
  executionRepository,
  userRepository,
  taskNotificationService,
  taskDefaultsRepository,
  auditEntryService,
  calculationRepository,
  publisher,
  taskDefinitionRepository,
  xmStepInstanceQueryRepository,
  xmWorkflowService
  )

  def setup() {
    teamRepository.teamNamesList() >> []
    taskDefaultsRepository.getTaskDefaultTeams() >> new MdTaskDefaultTeams()
  }

  def "should create overlay task executions"() {
    def config1 = new CurveConfiguration(entityId: "id1", name: "name1", instruments: [:] as Map<InstrumentType, MarketDataProviders>)
    def config2 = new CurveConfiguration(entityId: "id2", name: "name2", instruments: [:] as Map<InstrumentType, MarketDataProviders>,)
    setup:
    def taskResolver = TaskExecutionsResolver.newOf(
      mapper,
      definition(),
      new TaskExecution(status: TaskExecutionStatus.NOT_STARTED),
      teamRepository.teamNamesList(),
      [fromConfiguration(config1), fromConfiguration(config2)],
      [new LegalEntityTrsDataProviderResolver("companyId", "extCompanyId", null, null, null)],
      InstrumentTypeResolverHelper.coreResolver().values()
      )
    3 * calculationRepository.setOverlayTaskIds(_) >> Optional.empty()
    auditEntryService.newEntry(AuditEntry.of(TasksDefinition.TASKS_DEFINITION_COLLECTION, "Overlay MD tasks created")) >> Either.right(new AuditEntry(id: "auditEntryId"))
    3 * calculationRepository.overlayPendingCount(_) >> 1l
    when:
    service.createMdTaskExecutions(taskResolver)

    then:
    1 * executionRepository.insertAll(_ as List) >> { List<?> args ->
      def list = args[0] as List<TaskExecution>
      assert list.size() == 3
      assert list.stream()
      .anyMatch({ c -> c.curveConfigurationId == "id1" && c.curveConfigurationName == "name1" })
      assert list.stream()
      .anyMatch({ c -> c.curveConfigurationId == "id2" && c.curveConfigurationName == "name2" })
      assert list.stream()
      .anyMatch({ c -> c.companyId == "companyId" && c.companyExternalId == "extCompanyId" })
      assert list.stream()
      .allMatch({ c -> c.status == TaskExecutionStatus.NOT_STARTED })
      return list
    }
  }

  def "should verify tasks"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    1 * executionRepository.allTasksCompleted(new UniqueExcptMngmntTask(
      OVERLAY,
      "id",
      null,
      null,
      "id",
      "dashboardId")
      ) >> true
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = TaskExecutionStatus.IN_APPROVAL
      c.approvedBy = AuditUser.of(user)
      c.taskExceptionManagementType = OVERLAY
      c.exceptionManagementId = "id"
    })
    1 * executionRepository.executions(["id"]) >> [execution]
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], WAITING_APPROVAL) >> true
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], REJECTED) >> true
    1 * executionRepository.save(execution) >> Either.right(execution)
    1 * calculationRepository.approveOverlay("id") >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.submitTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
  }

  def "should verify tasks from different exception managements"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    1 * executionRepository.allTasksCompleted(new UniqueExcptMngmntTask(
      OVERLAY,
      "id",
      null,
      null,
      "id",
      "dashboardId")) >> true
    1 * executionRepository.allTasksCompleted(new UniqueExcptMngmntTask(
      OVERLAY,
      "id",
      null,
      null,
      "id1",
      "dashboardId")) >> true
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * executionRepository.executions(["id"]) >> [
      taskExecution({ c ->
        c.id = "id"
        c.status = TaskExecutionStatus.IN_APPROVAL
        c.approvedBy = AuditUser.of(user)
        c.taskExceptionManagementType = OVERLAY
        c.exceptionManagementId = "id"
      }),
      taskExecution({ c ->
        c.id = "id1"
        c.status = TaskExecutionStatus.IN_APPROVAL
        c.approvedBy = AuditUser.of(user)
        c.taskExceptionManagementType = OVERLAY
        c.exceptionManagementId = "id1"
      })
    ]
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id", "id1"], WAITING_APPROVAL) >> true
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], REJECTED) >> true

    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id1"], REJECTED) >> true

    2 * executionRepository.save(_) >> { TaskExecution task -> return Either.right(task) }
    1 * calculationRepository.approveOverlay("id") >> Either.right(EntityId.entityId("id"))
    1 * calculationRepository.approveOverlay("id1") >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.submitTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
  }

  def "should verify company/entity tasks tasks"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    1 * executionRepository.allTasksCompleted(new UniqueExcptMngmntTask(
      OVERLAY,
      null,
      "companyId",
      'legalId',
      "id",
      "dashboardId")
      ) >> true
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = TaskExecutionStatus.IN_APPROVAL
      c.approvedBy = AuditUser.of(user)
      c.taskExceptionManagementType = OVERLAY
      c.curveConfigurationId = null
      c.companyId = "companyId"
      c.legalEntityId = "legalId"
      c.exceptionManagementId = "id"
    })
    1 * executionRepository.executions(["id"]) >> [execution]
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], WAITING_APPROVAL) >> true
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], REJECTED) >> true
    1 * executionRepository.save(execution) >> Either.right(execution)
    1 * calculationRepository.approveOverlay("id") >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.submitTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
  }

  def "should start progress on task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.NOT_STARTED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())]
      )
    def execution2 = new TaskExecution(
      status: TaskExecutionStatus.REJECTED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(user)
      )
    def execution3 = new TaskExecution(
      status: TaskExecutionStatus.PAUSED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(user)
      )
    1 * executionRepository.executions(_) >> [execution, execution2, execution3]
    1 * executionRepository.save(execution) >> Either.right(execution)
    1 * executionRepository.save(execution2) >> Either.right(execution2)
    1 * executionRepository.save(execution3) >> Either.right(execution3)

    1 * taskNotificationService.notifyUsers(EventType.MD_OVERLAY_UPDATED, [execution, execution2, execution3], new MdTaskDefaultTeams())

    when:
    def result = service.startTasksProgress(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
    execution.status == TaskExecutionStatus.IN_RESOLUTION
    execution.performedBy == AuditUser.of(user)
    execution.previousStatuses.size() == 1
    execution.previousStatuses[0].status == TaskExecutionStatus.NOT_STARTED

    execution2.status == TaskExecutionStatus.IN_RESOLUTION

    execution3.status == TaskExecutionStatus.IN_RESOLUTION
    execution3.performedBy == AuditUser.of(user)
    execution3.previousStatuses.size() == 1
    execution3.previousStatuses[0].status == TaskExecutionStatus.PAUSED
  }

  def "should start progress on workflow task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD],
    user, _) >> [
      new StepInstance(businessKey: "key1", assignee: Mock(AuditUser)),
      new StepInstance(businessKey: "key2")
    ]

    when:
    def result = service.startTasksProgress(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
    1 * xmWorkflowService.claimMdOverlayResolutionTasks(["key2"], user)
  }

  def "should fail starting progress on task and revert - invalid status"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.NOT_STARTED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())]
      )
    def failed = new TaskExecution(
      status: TaskExecutionStatus.IN_RESOLUTION,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())]
      )
    1 * executionRepository.executions(_) >> [execution, failed]
    2 * executionRepository.save(execution) >> Either.right(execution)
    0 * executionRepository.save(failed)
    when:
    def result = service.startTasksProgress(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "Task has been already taken"
    execution.previousStatuses.size() == 0
    execution.status == TaskExecutionStatus.NOT_STARTED
    execution.performedBy == null
  }

  def "should fail starting progress on task and revert - not in workers team"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.NOT_STARTED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())]
      )
    def failed = new TaskExecution(
      status: TaskExecutionStatus.NOT_STARTED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: ObjectId.get().toHexString())]
      )
    1 * executionRepository.executions(_) >> [execution, failed]
    2 * executionRepository.save(execution) >> Either.right(execution)
    0 * executionRepository.save(failed)
    when:
    def result = service.startTasksProgress(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "User does not belong to resolvers team!"
    execution.previousStatuses.size() == 0
    execution.status == TaskExecutionStatus.NOT_STARTED
    execution.performedBy == null
  }

  def "should fail starting progress on task and revert - not user who paused task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    def otherUser = UserBuilder.userWithTeams("otherId", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(otherUser)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def failed = new TaskExecution(
      status: TaskExecutionStatus.PAUSED,
      taskExceptionManagementType: OVERLAY,
      resolutionTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(user)
      )
    1 * executionRepository.executions(_) >> [failed]
    0 * executionRepository.save(_)
    when:
    def result = service.startTasksProgress(new TestingAuthenticationToken(otherUser, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "Task has been already taken"
  }

  def "should start verification on task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))
      )
    1 * executionRepository.executions(_) >> [execution]
    1 * executionRepository.save(execution) >> Either.right(execution)

    1 * taskNotificationService.notifyUsers(EventType.MD_OVERLAY_UPDATED, [execution], new MdTaskDefaultTeams())

    when:
    def result = service.startTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
    execution.status == TaskExecutionStatus.IN_APPROVAL
    execution.approvedBy == AuditUser.of(user)
    execution.previousStatuses.size() == 1
    execution.previousStatuses[0].status == TaskExecutionStatus.PENDING_APPROVAL
  }

  def "should start verification on workflow task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE],
    user, _) >> [
      new StepInstance(businessKey: "key1", assignee: Mock(AuditUser)),
      new StepInstance(businessKey: "key2")
    ]

    when:
    def result = service.startTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
    1 * xmWorkflowService.claimMdOverlayApprovalTasks(["key2"], user)
  }

  def "should fail starting verification on task and revert - invalid status"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))

      )
    def failed = new TaskExecution(
      status: TaskExecutionStatus.IN_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))

      )
    1 * executionRepository.executions(_) >> [execution, failed]
    2 * executionRepository.save(execution) >> Either.right(execution)
    0 * executionRepository.save(failed)
    when:
    def result = service.startTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "Task has been already taken"
    execution.previousStatuses.size() == 0
    execution.status == TaskExecutionStatus.PENDING_APPROVAL
    execution.approvedBy == null
  }

  def "should fail starting verification on task and revert - not in approvers team"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))

      )
    def failed = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: ObjectId.get().toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))
      )
    1 * executionRepository.executions(_) >> [execution, failed]
    2 * executionRepository.save(execution) >> Either.right(execution)
    0 * executionRepository.save(failed)
    when:
    def result = service.startTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "User does not belong to approvers team!"
    execution.previousStatuses.size() == 0
    execution.status == TaskExecutionStatus.PENDING_APPROVAL
    execution.approvedBy == null
  }

  def "should fail starting verification on task and revert - same user"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: team.id.toHexString())],
      performedBy: AuditUser.of(UserBuilder.user(ObjectId.get().toHexString()))

      )
    def failed = new TaskExecution(
      status: TaskExecutionStatus.PENDING_APPROVAL,
      taskExceptionManagementType: OVERLAY,
      approvalTeams: [new TeamNameView(id: ObjectId.get().toHexString())],
      performedBy: AuditUser.of(user)
      )
    1 * executionRepository.executions(_) >> [execution, failed]
    2 * executionRepository.save(execution) >> Either.right(execution)
    0 * executionRepository.save(failed)
    when:
    def result = service.startTasksApproval(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "Same user can not resolve and verify same task"
    execution.previousStatuses.size() == 0
    execution.status == TaskExecutionStatus.PENDING_APPROVAL
    execution.approvedBy == null
  }

  def "should submit tasks"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, _, _) >> []
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = TaskExecutionStatus.IN_RESOLUTION
      c.performedBy = AuditUser.of(user)
      c.exceptionManagementId = "exceptionManagementId"
    })
    1 * executionRepository.executions(["id"]) >> [execution]
    1 * calculationRepository.noOverlayItemsWithStatus(_, ["id"], REJECTED, WAITING_RESOLUTION) >> true
    1 * executionRepository.save(execution) >> Either.right(execution)
    when:
    def result = service.submitTasksResolution(new TestingAuthenticationToken(user, null), ["id"])
    then:
    result.isRight()
    execution.status == TaskExecutionStatus.PENDING_APPROVAL
    execution.previousStatuses[0].status == TaskExecutionStatus.IN_RESOLUTION
  }

  def "should submit workflow tasks"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.FINALIZING],
    user, _) >> [new StepInstance(businessKey: "key1"), new StepInstance(businessKey: "key2")]

    when:
    def result = service.submitTasksResolution(new TestingAuthenticationToken(user, null), ["id"])

    then:
    result.isRight()
    1 * xmWorkflowService.completeMdOverlayResolutionTasks(["key1", "key2"])
  }

  def "should submit verification tasks"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.FINALIZING],
    user, _) >> [new StepInstance(businessKey: "key1"), new StepInstance(businessKey: "key2")]

    when:
    def result = service.submitTasksApproval(new TestingAuthenticationToken(user, null), ["id"])

    then:
    result.isRight()
    1 * xmWorkflowService.completeMdOverlayApprovalTasks(["key1", "key2"])
  }

  def "should cancel task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD], null, _) >> []
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = currentStatus
    })
    1 * taskNotificationService.notifyUsers(EventType.MD_OVERLAY_UPDATED, [execution], new MdTaskDefaultTeams())
    1 * executionRepository.execution("id") >> Either.right(execution)
    1 * executionRepository.save(execution) >> Either.right(execution)
    1 * calculationRepository.revertOverlayItems({ it.statusesToRevert == statusesToRevert } as RevertEntriesRequest) >> Either.right([])

    when:
    def result = service.cancelTask(new TestingAuthenticationToken(user, null), "id")
    then:
    result.isRight()
    execution.status == expectedStatus
    execution.performedBy == null
    execution.previousStatuses.size() == 1
    execution.previousStatuses[0].status == currentStatus

    where:
    currentStatus                     | expectedStatus                       | statusesToRevert
    TaskExecutionStatus.IN_RESOLUTION | TaskExecutionStatus.NOT_STARTED      | [WAITING_APPROVAL]
    TaskExecutionStatus.IN_APPROVAL   | TaskExecutionStatus.PENDING_APPROVAL | [WAITING_SUBMISSION, REJECTED]
  }

  def "should cancel workflow task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD], null, _) >> [new StepInstance(businessKey: "key1")]
    1 * xmWorkflowService.revertMdOverlayTask(["key1"])

    when:
    def result = service.cancelTask(new TestingAuthenticationToken(user, null), "id")
    then:
    result.isRight()
    with (result.right().get()) { EntityId it ->
      assert it.id == "id"
    }
  }

  def "should fail cancel task when wrong status"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD], null, _) >> []
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = TaskExecutionStatus.APPROVED
    })
    1 * executionRepository.execution("id") >> Either.right(execution)

    when:
    def result = service.cancelTask(new TestingAuthenticationToken(user, null), "id")
    then:
    result.isLeft()
    result.left().get().reason == Error.OPERATION_NOT_ALLOWED
    result.left().get().description == "Unable to cancel task with status: APPROVED"
  }

  def "should fail cancel task for user when task doesnt belong"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user, _) >> []
    def execution = taskExecution({ c ->
      c.id = "id"
      c.status = TaskExecutionStatus.IN_RESOLUTION
      c.performedBy = AuditUser.of(UserBuilder.user("test"))
    })
    1 * executionRepository.execution("id") >> Either.right(execution)

    when:
    def result = service.cancelOwnTask(new TestingAuthenticationToken(user, null), "id")
    then:
    result.isLeft()
    result.left().get().reason == Error.OPERATION_NOT_ALLOWED
  }

  def "should cancel own workflow task"() {
    setup:
    def team = TeamBuilder.teamWithId()
    def user = UserBuilder.userWithTeams("id", [team.getId()])
    1 * userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(OVERLAY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user, _) >> [new StepInstance(businessKey: "key1")]
    1 * xmWorkflowService.revertMdOverlayTask(["key1"])

    when:
    def result = service.cancelOwnTask(new TestingAuthenticationToken(user, null), "id")

    then:
    result.isRight()
    with (result.right().get()) { EntityId it ->
      assert it.id == "id"
    }
  }

  static TasksDefinition definition() {
    new TasksDefinition(
      type: OVERLAY,
      teams: [
        new AssetClassTaskTeams(
        assetClass: IR_RATE,
        resolutionTeams: [ObjectId.get().toHexString()],
        approvalTeams: [ObjectId.get().toHexString()]),
      ],
      overrides: [],
      granularityByAssetClassType: TaskGranularityByAssetClassType.NONE,
      granularityByRate: TaskGranularityByRateType.NONE,
      granularityBySector: TaskGranularityBySectorType.NONE,
      granularityByInstrument: TaskGranularityByInstrumentType.NONE,
      granularityByFxCcyPair: TaskGranularityByFxCcyPairType.NONE

      )
  }

  static TaskExecution taskExecution(Closure c) {
    new TaskExecution(
      taskExceptionManagementType: OVERLAY,
      assetFilter: new AssetFilter(
      assetClasses: [IR_RATE],
      irInstruments: [CoreInstrumentType.IBOR_FIXING_DEPOSIT, CoreInstrumentType.FIXED_IBOR_SWAP],
      rateCcys: ["EUR", "USD"],
      creditSectors: [],
      fxPairs: []),
      status: TaskExecutionStatus.NOT_STARTED,
      curveConfigurationId: "id",
      curveConfigurationName: "name",
      marketDataGroupId: "mdId",
      marketDataGroupName: "groupName",
      valuationDate: LocalDate.now(),
      dashboardId: "dashboardId"
      ).with(true, c)
  }
}
