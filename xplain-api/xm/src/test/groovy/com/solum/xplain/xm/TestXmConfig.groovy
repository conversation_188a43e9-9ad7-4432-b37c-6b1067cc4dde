package com.solum.xplain.xm

import com.solum.xplain.calculation.integration.CalculationRequestProducer
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import org.springframework.boot.SpringBootConfiguration
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Bean
import spock.mock.DetachedMockFactory

@EnableAutoConfiguration
@SpringBootConfiguration
@ImportAutoConfiguration
class TestXmConfig extends XmConfig {
  private DetachedMockFactory factory = new DetachedMockFactory()

  @Bean
  CalibrationCacheService calibrationCacheService() {
    factory.Mock(CalibrationCacheService)
  }

  @Bean
  CalculationRequestProducer calculationRequestProducer() {
    factory.Mock(CalculationRequestProducer)
  }
}
