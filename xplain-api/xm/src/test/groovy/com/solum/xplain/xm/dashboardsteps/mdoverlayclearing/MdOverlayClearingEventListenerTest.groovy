package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdOverlayClearing
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.dashboardfinalize.FinalizeDashboardEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MdOverlayClearingEventListenerTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MdOverlayClearingStepsInitializer clearingStepsInitializer = Mock()
  MdOverlayClearingStepsFinalizer clearingStepsFinalizer = Mock()
  MdOverlayClearingCleanup stepCleanup = Mock()

  MdOverlayClearingEventListener listener = new MdOverlayClearingEventListener(
  dashboardRepository,
  publisher,
  clearingStepsInitializer,
  clearingStepsFinalizer,
  stepCleanup,
  )

  def setup() {
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.right(MARKET_DATA_DASHBOARD)
  }

  def "should correctly proxy MD_OVERLAY_CLEARING step event when REQUESTED"() {
    when: "all steps completed"
    listener.onEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdOverlayClearing()])
    1 * publisher.publishEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build()
      )

    when: "not all steps completed"
    listener.onEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdOverlayClearing().tap {
        status = StepStatus.IN_PROGRESS
      }])
    0 * publisher.publishEvent(_)
  }

  def "should correctly proxy MD_OVERLAY_CLEARING step event when UPDATED"() {
    setup:
    def event = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .curveConfigurationId(CURVE_CONFIGURATION.entityId)
      .build()
    def event1 = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .curveConfigurationId(CURVE_CONFIGURATION.entityId)
      .build()

    when: "all steps completed"
    listener.onEvent(event)

    then:
    1 * clearingStepsFinalizer.execute(MARKET_DATA_DASHBOARD, event) >> Either.right([dashboardStepMdOverlayClearing()])
    1 * publisher.publishEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build()
      )

    when: "not all steps completed"

    listener.onEvent(event1)

    then:
    1 * clearingStepsFinalizer.execute(MARKET_DATA_DASHBOARD, event1) >> Either.right([dashboardStepMdOverlayClearing().tap {
        status = StepStatus.IN_PROGRESS
      }])
    0 * publisher.publishEvent(_)
  }

  def "should correctly proxy MD_OVERLAY_CLEARING step event when COMPLETED - MD Only"() {
    when:
    listener.onEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(new FinalizeDashboardEvent(MD_DASHBOARD_ID))
  }

  def "should correctly process MD_OVERLAY_CLEARING step event when dashboard deleted"() {
    setup:
    def dashboardId = "someId"
    def dashboard = new Dashboard(id: "DELETED")
    1 * dashboardRepository.dashboard(dashboardId) >> Either.right(dashboard)
    1 * dashboardRepository.dashboard("DELETED") >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    1 * clearingStepsInitializer.execute(dashboard) >> Either.right([dashboardStepMdOverlayClearing()])

    when:
    listener.onEvent(MdOverlayClearingEvent
      .builder()
      .dashboardId(dashboardId)
      .type(REQUESTED)
      .build())


    then:
    1 * stepCleanup.execute("DELETED")
  }
}
