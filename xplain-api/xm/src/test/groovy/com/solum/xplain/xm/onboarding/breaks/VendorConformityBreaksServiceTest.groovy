package com.solum.xplain.xm.onboarding.breaks


import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.PRIMARY
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.QUATERNARY
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.TERTIARY
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.AC_P1
import static com.solum.xplain.xm.onboarding.breaks.OnboardingBreakTestData.ofVendor
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.FAILED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.ipv.data.IpvDataRepository
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettings
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettingsResolver
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class VendorConformityBreaksServiceTest extends Specification {

  private static def STATE_DATE = BitemporalDate.newOfNow()
  private static def ONBOARDING_DATE = LocalDate.of(2024, 2, 1)
  private static def NAV_ON_TRADEDATE = BigDecimal.ZERO
  private static def NAV_ON_ONBOARDING = BigDecimal.ONE
  private static def TRADE_KEY = "VDK"
  private static def VDGROUP = "VD"
  private static def PRIMARY_PROVIDER = "PRIMARY"
  private static def TERTIARY_PROVIDER = "TERTIARY"
  private static def QUATERNARY_PROVIDER = "QUATERNARY"

  def onboardingBreakTestCalculationsProvider = Mock(OnboardingBreakTestCalculationsProvider)
  def ipvDataRepository = Mock(IpvDataRepository)
  def settingsResolver = Mock(TradeCompanySettingsResolver)
  def onboardingBreakTestCalculations = Mock(OnboardingBreakTestCalculations)

  def service = new VendorConformityBreaksService(
  onboardingBreakTestCalculationsProvider,
  ipvDataRepository,
  )

  def "should correctly process single item when 3rd party provider"() {
    setup:
    def trade = new Trade(
      key: TRADE_KEY,
      vendorOnboardingDate: ONBOARDING_DATE,
      )
    def reportItem = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      trade: trade,
      navOnTradeDate: NAV_ON_TRADEDATE,
      navOnVendorOnboardingDate: NAV_ON_ONBOARDING,
      )
    def reportItemXplain = new OnboardingReportItem(xplainCheckMessage: REQUESTED)
    def onboardingMetrics = new OnboardingVendorMetrics(provider: PRIMARY_PROVIDER, pv: 1, delta: 2, vega: 3)

    1 * onboardingBreakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculations
    1 * settingsResolver.tradeSettings(trade) >> Either.right(new TradeCompanySettings(
      VDGROUP,
      PRIMARY_PROVIDER,
      null,
      null,
      null,
      ))
    1 * ipvDataRepository.getValueAtDate(VDGROUP, TRADE_KEY, PRIMARY_PROVIDER, ONBOARDING_DATE) >> Either.right(
      new IpvDataProviderValueView(provider: onboardingMetrics.provider, value: onboardingMetrics.pv, delta: onboardingMetrics.delta, vega: onboardingMetrics.vega)
      )

    def breakResult = Mock(OnboardingTradeResultBreak)
    1 * breakResult.isTriggered() >> true
    1 * onboardingBreakTestCalculations.process(trade, ofVendor(onboardingMetrics, NAV_ON_ONBOARDING), PRIMARY) >> [breakResult]

    when:
    service.processBreaks([reportItem, reportItemXplain], settingsResolver, STATE_DATE)

    then:
    reportItem.vendorPrimaryMetrics == onboardingMetrics
    reportItem.breakTests == [breakResult]
    reportItem.vendorCheckStatus == FAILED
    reportItem.vendorCheckMessage == null
    reportItemXplain == new OnboardingReportItem(xplainCheckMessage: REQUESTED)
  }

  def "should correctly process single item when 3rd party + XPLAIN providers"() {
    setup:
    def trade = new Trade(
      vendorOnboardingDate: ONBOARDING_DATE,
      key: TRADE_KEY,
      )
    def reportItem = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      trade: trade,
      navOnTradeDate: NAV_ON_TRADEDATE,
      navOnVendorOnboardingDate: NAV_ON_ONBOARDING,
      )
    def reportItemXplain = new OnboardingReportItem(xplainCheckMessage: REQUESTED)
    def primaryOnboardingMetrics = new OnboardingVendorMetrics(provider: PRIMARY_PROVIDER, pv: 1, delta: 2, vega: 3)
    def tertiaryOnboardingMetrics = new OnboardingVendorMetrics(provider: TERTIARY_PROVIDER, pv: 2, delta: 3, vega: 4)
    def quaternaryOnboardingMetrics = new OnboardingVendorMetrics(provider: QUATERNARY_PROVIDER, pv: 3, delta: 4, vega: 5)

    1 * onboardingBreakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculations
    1 * settingsResolver.tradeSettings(trade) >> Either.right(new TradeCompanySettings(
      VDGROUP,
      PRIMARY_PROVIDER,
      XPLAIN_PROVIDER_CODE,
      TERTIARY_PROVIDER,
      QUATERNARY_PROVIDER,
      ))
    1 * ipvDataRepository.getValueAtDate(VDGROUP, TRADE_KEY, PRIMARY_PROVIDER, ONBOARDING_DATE) >> Either.right(
      new IpvDataProviderValueView(provider: primaryOnboardingMetrics.provider, value: primaryOnboardingMetrics.pv, delta: primaryOnboardingMetrics.delta, vega: primaryOnboardingMetrics.vega)
      )
    1 * ipvDataRepository.getValueAtDate(VDGROUP, TRADE_KEY, TERTIARY_PROVIDER, ONBOARDING_DATE) >> Either.right(
      new IpvDataProviderValueView(provider: tertiaryOnboardingMetrics.provider, value: tertiaryOnboardingMetrics.pv, delta: tertiaryOnboardingMetrics.delta, vega: tertiaryOnboardingMetrics.vega)
      )
    1 * ipvDataRepository.getValueAtDate(VDGROUP, TRADE_KEY, QUATERNARY_PROVIDER, ONBOARDING_DATE) >> Either.right(
      new IpvDataProviderValueView(provider: quaternaryOnboardingMetrics.provider, value: quaternaryOnboardingMetrics.pv, delta: quaternaryOnboardingMetrics.delta, vega: quaternaryOnboardingMetrics.vega)
      )

    def breakResultP = Mock(OnboardingTradeResultBreak)
    def breakResultT = Mock(OnboardingTradeResultBreak)
    def breakResultQ = Mock(OnboardingTradeResultBreak)
    1 * onboardingBreakTestCalculations.process(trade, ofVendor(primaryOnboardingMetrics, NAV_ON_ONBOARDING), PRIMARY) >> [breakResultP]
    1 * onboardingBreakTestCalculations.process(trade, ofVendor(tertiaryOnboardingMetrics, NAV_ON_ONBOARDING), TERTIARY) >> [breakResultT]
    1 * onboardingBreakTestCalculations.process(trade, ofVendor(quaternaryOnboardingMetrics, NAV_ON_ONBOARDING), QUATERNARY) >> [breakResultQ]

    when:
    service.processBreaks([reportItem, reportItemXplain], settingsResolver, STATE_DATE)

    then:
    reportItem.vendorPrimaryMetrics == primaryOnboardingMetrics
    reportItem.vendorSecondaryMetrics == new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE)
    reportItem.vendorTertiaryMetrics == tertiaryOnboardingMetrics
    reportItem.vendorQuaternaryMetrics == quaternaryOnboardingMetrics
    reportItem.breakTests == [breakResultP, breakResultT, breakResultQ]
    reportItem.vendorCheckStatus == REQUESTED
    reportItem.vendorCheckMessage == null
    reportItemXplain == new OnboardingReportItem(xplainCheckMessage: REQUESTED)
  }

  def "should correctly process single item when Xplain provider"() {
    setup:
    def trade = new Trade(
      key: TRADE_KEY,
      vendorOnboardingDate: STATE_DATE.actualDate,
      )
    def reportItem = new OnboardingReportItem(vendorCheckStatus: REQUESTED, trade: trade)

    1 * onboardingBreakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculations
    1 * settingsResolver.tradeSettings(trade) >> Either.right(new TradeCompanySettings(
      VDGROUP,
      XPLAIN_PROVIDER_CODE,
      null,
      null,
      null,
      ))
    0 * ipvDataRepository.getValueAtDate(_, _, _)
    0 * onboardingBreakTestCalculations.process(_, _)

    when:
    service.processBreaks([reportItem], settingsResolver, STATE_DATE)

    then:
    reportItem.vendorPrimaryMetrics == new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE)
    reportItem.breakTests == []
    reportItem.vendorCheckStatus == REQUESTED
    reportItem.vendorCheckMessage == null
  }

  def "should fail process single item when Xplain provider if no onboarding date"() {
    setup:
    def trade = new Trade(key: TRADE_KEY)
    def reportItem = new OnboardingReportItem(vendorCheckStatus: REQUESTED, trade: trade)

    1 * onboardingBreakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculations
    1 * settingsResolver.tradeSettings(trade) >> Either.right(new TradeCompanySettings(
      VDGROUP,
      XPLAIN_PROVIDER_CODE,
      null,
      null,
      null,
      ))
    0 * ipvDataRepository.getValueAtDate(_, _, _)
    0 * onboardingBreakTestCalculations.process(_, _)

    when:
    service.processBreaks([reportItem], settingsResolver, STATE_DATE)

    then:
    reportItem.vendorPrimaryMetrics == null
    reportItem.breakTests == []
    reportItem.vendorCheckStatus == FAILED
    reportItem.vendorCheckMessage == "NAV Effective date is required"
  }

  def "should fail when VD group settings not found"() {
    setup:
    def trade = new Trade(vendorOnboardingDate: STATE_DATE.actualDate)
    def reportItem = new OnboardingReportItem(vendorCheckStatus: REQUESTED, trade: trade)

    1 * onboardingBreakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculations
    1 * settingsResolver.tradeSettings(trade) >> Either.right(new TradeCompanySettings(
      null,
      PRIMARY_PROVIDER,
      null,
      null,
      null,
      ))
    0 * ipvDataRepository.getValueAtDate(_, _, _)

    0 * onboardingBreakTestCalculations.process(_, _)

    when:
    service.processBreaks([reportItem], settingsResolver, STATE_DATE)

    then:
    reportItem.vendorPrimaryMetrics == null
    reportItem.breakTests == []
    reportItem.vendorCheckStatus == FAILED
    reportItem.vendorCheckMessage == "Unable to resolve VD group"
  }
}
