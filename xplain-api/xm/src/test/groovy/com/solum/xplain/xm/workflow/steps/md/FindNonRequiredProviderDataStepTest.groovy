package com.solum.xplain.xm.workflow.steps.md

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.xm.excmngmt.process.CachingMarketDataService
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper
import com.solum.xplain.xm.excmngmt.process.InstrumentDefinitionsCacheService
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.workflow.state.InstrumentKeyCurveConfigurations
import com.solum.xplain.xm.workflow.state.MdDashboardContextSample
import com.solum.xplain.xm.workflow.state.MdDashboardState
import spock.lang.Specification


class FindNonRequiredProviderDataStepTest extends Specification implements MdDashboardContextSample {
  CachingMarketDataService cachingMarketDataService = Mock()
  ExceptionManagementResultMapper exceptionManagementResultMapper = ExceptionManagementResultMapper.EXC_RESULT_MAPPER
  InstrumentDefinitionsCacheService instrumentDefinitionsCacheService = Mock()
  FindNonRequiredProviderDataStep step = new FindNonRequiredProviderDataStep(cachingMarketDataService, exceptionManagementResultMapper, instrumentDefinitionsCacheService)
  StepStateOps stepStateOps = Mock()

  def "should find required provider data"() {
    given:
    def parentContext = mdDashboardContext()
    def parentState = new MdDashboardState(
    nonRequiredInstrumentDefinitions: [
      new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y.key, [CURVE_CONFIG_ID_1].toSet()),
      new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M.key, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2].toSet())
    ]
    )
    def results = []

    stepStateOps.getContext() >> parentContext
    stepStateOps.getInitialState() >> parentState
    stepStateOps.getRootBusinessKey() >> "urn:dashboard:dashboardId"

    and: "data is present for CDS_1Y and missing for FIXED_IBOR_SWAP_EUR_6M"
    def cds1yData = [
      ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID),
      ProviderData.of(PROVIDER_1, 1.1, 1.0, ValueBidAskType.ASK),
      ProviderData.of(PROVIDER_2, 1.0, 0.9, ValueBidAskType.BID),
      ProviderData.of("UNMAPPED", 1.5, 1.3, ValueBidAskType.BID)
    ]
    cachingMarketDataService.providerData(parentContext.marketDataGroupId(), parentContext.previousDate(), InstrumentDefinitionBuilder.CDS_1Y, parentContext.stateDate()) >>
    cds1yData
    cachingMarketDataService.providerData(parentContext.marketDataGroupId(), parentContext.previousDate(), InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M, parentContext.stateDate()) >>
    []

    instrumentDefinitionsCacheService.getInstrumentDefinition(InstrumentDefinitionBuilder.CDS_1Y.key) >> InstrumentDefinitionBuilder.CDS_1Y
    instrumentDefinitionsCacheService.getInstrumentDefinition(InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M.key) >> InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M

    when:
    step.runStep(stepStateOps)

    then:
    2 * stepStateOps.submitBulkDataModification(_ as NonRequiredProviderDataCreate) >> {
      args ->
      results << args[0]
    }
    and:
    assert results.size() == 2
    def cds1y = results.find(it -> it.instrumentDefinition == InstrumentDefinitionBuilder.CDS_1Y)
    def fixedIborSwap = results.find(it -> it.instrumentDefinition == InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M)

    assert cds1y.providerData().size() == 2
    //based on nonRequiredInstrumentDefinitions, CDS_1Y is mapped to CURVE_CONFIG_ID_1 which only contains PROVIDER_1 values
    assert cds1y.providerData().contains(ProviderData.of(PROVIDER_1, 1.0, 0.9, ValueBidAskType.BID))
    assert cds1y.providerData().contains(ProviderData.of(PROVIDER_1, 1.1, 1.0, ValueBidAskType.ASK))

    //no provider data for FIXED_IBOR_SWAP
    assert fixedIborSwap.providerData().size() == 0
  }
}
