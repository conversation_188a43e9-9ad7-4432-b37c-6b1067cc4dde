package com.solum.xplain.xm.excmngmt.processipv.converter

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD

import com.opengamma.strata.basics.currency.FxRateProvider
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData
import spock.lang.Specification

class TradeCurrencyIpvValueConverterTest extends Specification {

  def fxProvider = Mock(FxRateProvider)
  def converter = new TradeCurrencyIpvValueConverter(
  EUR,
  fxProvider
  )


  def "should convert value when FX rate present"() {
    given:
    def fxRate = 1.5
    fxProvider.fxRate(EUR, USD) >> fxRate

    when:
    def result1 = converter.convertScalingData(new TradeBreakScalingData(100.0, 50.0), USD)

    then:
    result1.nav() == 150.0
    result1.notional() == 50.0

    when:
    def result2 = converter.convertScalingData(new TradeBreakScalingData(100.0, null), USD)

    then:
    result2.nav() == 150.0
    result2.notional() == null
  }

  def "should return null when FX rate not found"() {
    given:
    def scalingData = new TradeBreakScalingData(100.0, null)
    fxProvider.fxRate(EUR, USD) >> { throw new IllegalArgumentException("ERR") }

    when:
    def result = converter.convertScalingData(scalingData, USD)

    then:
    result.nav() == null
    result.notional() == null
  }

  def "should return same when FX rate not found but reportCcy = tradeCcy"() {
    given:
    def scalingData = new TradeBreakScalingData(100.0, null)

    when:
    def result = converter.convertScalingData(scalingData, EUR)

    then:
    result.nav() == 100.0
    result.notional() == null
  }

  def "should not throw error when scaling data is null"() {
    when:
    def result = converter.convertScalingData(null, USD)

    then:
    result == null
  }

  def "should return XM currency"() {
    when:
    def result = converter.xmCurrency(USD)

    then:
    result == Optional.of(USD)
  }

  def "should return null when fxRates/reportingCcy are null"() {
    setup:
    def scalingData = new TradeBreakScalingData(999.0, 100.0)

    when:
    def resultWhenNoReportingCcy = new TradeCurrencyIpvValueConverter(null, fxProvider).convertScalingData(scalingData, USD)

    then:
    resultWhenNoReportingCcy.nav() == null
    resultWhenNoReportingCcy.notional() == 100.0

    when:
    def resultWhenNoFxRates = new TradeCurrencyIpvValueConverter(EUR, null).convertScalingData(scalingData, USD)

    then:
    resultWhenNoFxRates.nav() == null
    resultWhenNoFxRates.notional() == 100.0
  }
}
