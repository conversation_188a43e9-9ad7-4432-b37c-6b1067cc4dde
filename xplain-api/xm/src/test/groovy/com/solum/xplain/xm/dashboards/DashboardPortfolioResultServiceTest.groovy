package com.solum.xplain.xm.dashboards

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD_VIEW
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_2

import com.google.common.io.ByteStreams
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.EntityReferenceView
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReference
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReferenceView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.PortfolioDashboardSettings
import com.solum.xplain.xm.dashboards.entity.VdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboards.views.DashboardPortfolio
import com.solum.xplain.xm.dashboards.views.DashboardPortfolioListView
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm.IndividualOverrideForm
import com.solum.xplain.xm.excmngmt.form.OverrideForm
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType
import com.solum.xplain.xm.excmngmt.processipv.value.IpvPortfolioItemResultFilter
import com.solum.xplain.xm.excmngmt.processipv.view.IpvPortfolioItemResultView
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Stream
import org.springframework.web.multipart.MultipartFile
import spock.lang.Specification

class DashboardPortfolioResultServiceTest extends Specification {

  def authenticationContext = Mock(AuthenticationContext)
  def ipvExceptionManagementRepository = Mock(IpvExceptionManagementCalculationRepository)
  def evidenceRepository = Mock(ExceptionManagementEvidenceRepository)
  def dashboardRepository = Mock(DashboardRepository)

  DashboardPortfolioResultService service = new DashboardPortfolioResultService(authenticationContext, ipvExceptionManagementRepository, evidenceRepository, dashboardRepository)

  def "should get ipv portfolio valuation results csv"() {
    setup:
    def date = LocalDate.parse("2020-09-17")
    def data = Mock(PortfolioDashboardSettings)
    data.portfolio >> EntityReference.newOf("portfolioId", "extPortfolioId")
    data.company >> EntityReference.newOf("companyId", "extCompanyId")
    data.entity >> EntityReference.newOf("entityId", "extEntityId")

    def dashboard = new Dashboard(
      vdExceptionManagementSetup: new VdExceptionManagementSetup(
      VdExceptionManagementPortfolioFilter.empty(),
      [data]
      ),
      dateRange: DateRange.newOf(LocalDate.now())
      )

    def view = new IpvPortfolioItemResultView()
    view.setResolvedValue(1.0)
    view.setResolutionProviderType(IpvProvidersType.P1)
    view.setResolutionProviderName("provider")
    view.setResolution(TradeResultResolutionType.KEEP)
    view.setKey("key")
    view.setTradeInfoStrike(2.0)
    view.setTradeInfoExpiryDate(date)
    view.setTradeInfoPremiumDate(date)
    view.setTradeInfoSettlementDate(date)
    view.setTradeInfoPremiumValueAmount(3.0)
    view.setTradeInfoPremiumValueCurrency("EUR")
    view.setTradeInfoTradeType(CoreProductType.CAP_FLOOR)
    view.setReceiveLegExtIdentifier("RLID")
    def view2 = new IpvPortfolioItemResultView()
    view2.setResolvedValue(4.0)
    view2.setResolutionProviderType(IpvProvidersType.P1)
    view2.setResolutionProviderName("provider2")
    view2.setResolution(TradeResultResolutionType.KEEP)
    view2.setKey("key2")
    view2.setTradeInfoStrike(2.0)
    view2.setTradeInfoExpiryDate(date)
    view2.setTradeInfoPremiumDate(date)
    view2.setTradeInfoSettlementDate(date)
    view2.setTradeInfoPremiumValueAmount(3.0)
    view2.setTradeInfoPremiumValueCurrency("EUR")
    view2.setTradeInfoTradeType(CoreProductType.FXOPT)
    view2.setPayLegExtIdentifier("PLID")
    view2.setReceiveLegExtIdentifier("RLID")

    1 * dashboardRepository.dashboard(_) >> Either.right(dashboard)
    1 * dashboardRepository.dashboardWithSteps(dashboard, _) >> VD_DASHBOARD_VIEW
    1 * ipvExceptionManagementRepository.portfolioResultItemsStream(
      _,
      "portfolioId", OVERLAY_2,
      _ as TableFilter
      ) >> Stream.of(view, view2)

    when:
    def result = service.portfolioResultItemsCsv("mngtId", "portfolioId", LocalDate.now(), TableFilter.emptyTableFilter())

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == readResult("IpvPortfolioValuationResults.csv")
  }

  def "should get all ipv portfolios valuation results csv"() {
    setup:
    def date = LocalDate.parse("2020-09-17")
    def portfolio1 = new DashboardPortfolio(
      EntityReference.newOf("companyId", "extComapnyId"),
      EntityReference.newOf("entityId", "extEntityId"),
      EntityReference.newOf("portfolioId", "extPortfolioId"),
      [IpvDataGroupReference.newOf("ipvDataGroupId", "extIpvDataGroupId", PricingSlot.LDN_1500)],
      false
      )
    def portfolio2 = new DashboardPortfolio(
      EntityReference.newOf("companyId2", "extComapnyId2"),
      EntityReference.newOf("entityId2", "extEntityId2"),
      EntityReference.newOf("portfolioId2", "extPortfolioId2"),
      [IpvDataGroupReference.newOf("ipvDataGroupId", "extIpvDataGroupId", PricingSlot.LDN_1500)],
      true
      )

    def view = new IpvPortfolioItemResultView()
    view.setPortfolioId("portfolioId")
    view.setPricingSlot(PricingSlot.LDN_1500)
    view.setSlaDeadline(SlaDeadline.OTHER)
    view.setResolvedValue(1.0)
    view.setResolutionProviderType(IpvProvidersType.P1)
    view.setResolutionProviderName("provider")
    view.setResolution(TradeResultResolutionType.KEEP)
    view.setKey("key")
    view.setTradeInfoStrike(2.0)
    view.setTradeInfoExpiryDate(date)
    view.setTradeInfoPremiumDate(date)
    view.setTradeInfoSettlementDate(date)
    view.setTradeInfoPremiumValueAmount(3.0)
    view.setTradeInfoPremiumValueCurrency("EUR")
    view.setTradeInfoTradeType(CoreProductType.CAP_FLOOR)
    def view2 = new IpvPortfolioItemResultView()
    view2.setPortfolioId("portfolioId")
    view2.setResolvedValue(4.0)
    view2.setResolutionProviderType(IpvProvidersType.P1)
    view2.setResolutionProviderName("provider2")
    view2.setResolution(TradeResultResolutionType.KEEP)
    view2.setKey("key2")
    view2.setTradeInfoStrike(2.0)
    view2.setTradeInfoExpiryDate(date)
    view2.setTradeInfoPremiumDate(date)
    view2.setTradeInfoSettlementDate(date)
    view2.setTradeInfoPremiumValueAmount(3.0)
    view2.setTradeInfoPremiumValueCurrency("EUR")
    view2.setTradeInfoTradeType(CoreProductType.FXOPT)
    def view3 = new IpvPortfolioItemResultView()
    view3.setPortfolioId("portfolioId2")
    view3.setResolvedValue(4.0)
    view3.setResolutionProviderType(IpvProvidersType.P1)
    view3.setResolutionProviderName("provider2")
    view3.setResolution(TradeResultResolutionType.KEEP)
    view3.setKey("key2")
    view3.setTradeInfoStrike(2.0)
    view3.setTradeInfoExpiryDate(date)
    view3.setTradeInfoPremiumDate(date)
    view3.setTradeInfoSettlementDate(date)
    view3.setTradeInfoPremiumValueAmount(3.0)
    view3.setTradeInfoPremiumValueCurrency("EUR")
    view3.setTradeInfoTradeType(CoreProductType.FXOPT)

    when:
    def result = service.allPortfoliosResultItemsCsv(VD_DASHBOARD.id, LocalDate.now(), TableFilter.emptyTableFilter())

    then:
    1 * dashboardRepository.dashboard(VD_DASHBOARD_ID) >> Either.right(VD_DASHBOARD)
    1 * dashboardRepository.getDashboardPortfolios(VD_DASHBOARD_ID, _ as TableFilter) >> [portfolio1, portfolio2]
    1 * dashboardRepository.dashboardWithSteps(VD_DASHBOARD, _) >> VD_DASHBOARD_VIEW
    1 * ipvExceptionManagementRepository.allPortfoliosResultItemsStream(VD_DASHBOARD_ID, [portfolio1, portfolio2], OVERLAY_2) >> Stream.of(view, view2, view3)

    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == readResult("AllIpvPortfolioValuationResults.csv")
  }

  def "should return portfolio result items"() {
    setup:
    def date = LocalDate.parse("2020-09-17")
    def filter = new IpvPortfolioItemResultFilter(true, null)

    def view = new IpvPortfolioItemResultView()
    view.setPortfolioId("portfolioId")
    view.setPricingSlot(PricingSlot.LDN_1500)
    view.setSlaDeadline(SlaDeadline.OTHER)
    view.setResolvedValue(1.0)
    view.setResolutionProviderType(IpvProvidersType.P1)
    view.setResolutionProviderName("provider")
    view.setResolution(TradeResultResolutionType.KEEP)
    view.setKey("key")
    view.setTradeInfoStrike(2.0)
    view.setTradeInfoExpiryDate(date)
    view.setTradeInfoPremiumDate(date)
    view.setTradeInfoSettlementDate(date)
    view.setTradeInfoPremiumValueAmount(3.0)
    view.setTradeInfoPremiumValueCurrency("EUR")
    view.setTradeInfoTradeType(CoreProductType.CAP_FLOOR)
    def view2 = new IpvPortfolioItemResultView()
    view2.setPortfolioId("portfolioId")
    view2.setResolvedValue(4.0)
    view2.setResolutionProviderType(IpvProvidersType.P1)
    view2.setResolutionProviderName("provider2")
    view2.setResolution(TradeResultResolutionType.KEEP)
    view2.setKey("key2")
    view2.setTradeInfoStrike(2.0)
    view2.setTradeInfoExpiryDate(date)
    view2.setTradeInfoPremiumDate(date)
    view2.setTradeInfoSettlementDate(date)
    view2.setTradeInfoPremiumValueAmount(3.0)
    view2.setTradeInfoPremiumValueCurrency("EUR")
    view2.setTradeInfoTradeType(CoreProductType.FXOPT)

    1 * dashboardRepository.dashboard(VD_DASHBOARD_ID) >> Either.right(VD_DASHBOARD)
    1 * dashboardRepository.dashboardWithSteps(VD_DASHBOARD, _) >> VD_DASHBOARD_VIEW
    1 * ipvExceptionManagementRepository.portfolioResultItems(VD_DASHBOARD_ID, "portfolioId", OVERLAY_2, null, filter, _, _) >> ScrollableEntry.of([view, view2], ScrollRequest.unconstrained())

    when:
    def result = service.portfolioResultItems(VD_DASHBOARD_ID, "portfolioId", null, filter, TableFilter.emptyTableFilter(), ScrollRequest.unconstrained())

    then:
    result.content.size() == 2
    with(result.content) {
      it[0].portfolioId == "portfolioId"
      it[0].key == "key"
      it[1].portfolioId == "portfolioId"
      it[1].key == "key2"
    }
  }

  def "should return dashboard portfolios with counts"() {
    setup:
    def company = new EntityReferenceView(entityId: "companyId", name: "COMPANY")
    def legalEntity = new EntityReferenceView(entityId: "entityId", name: "ENTITY")
    def vdGroup = new IpvDataGroupReferenceView(entityId: "ipvID", name: "VD1", pricingSlot: PricingSlot.OTHER)
    def portfolio1 = new EntityReferenceView(entityId: "portfolioId", name: "PORTFOLIO")

    var portfolios = [
      new DashboardPortfolioListView(
      company,
      legalEntity,
      portfolio1,
      [vdGroup],
      "resultId",
      1,
      2
      )
    ]

    1 * dashboardRepository.getDashboard(VD_DASHBOARD_ID, _) >> Either.right(VD_DASHBOARD_VIEW)
    1 * ipvExceptionManagementRepository.portfolioResultStatusCounts(VD_DASHBOARD_ID, OVERLAY_2) >> []
    1 * dashboardRepository.getDashboardPortfolios(VD_DASHBOARD_ID, TableFilter.emptyTableFilter(), ScrollRequest.unconstrained(), []) >> Either.right(ScrollableEntry.of(portfolios, ScrollRequest.unconstrained()))

    when:
    def result = service.getDashboardPortfolioResults(VD_DASHBOARD_ID, TableFilter.emptyTableFilter(), ScrollRequest.unconstrained())

    then:
    result.isRight()
    with(result.getOrNull().content) {
      it[0].portfolio == portfolio1
      it[0].company == company
      it[0].entity == legalEntity
      it[0].resultId == "resultId"
      it[0].verifiedTradesCount == 1
      it[0].totalTradesCount == 2
    }
  }

  def "should override VD entries"() {
    setup:
    def user = UserBuilder.user()
    def form = new ApplyOverrideForm([
      new IndividualOverrideForm("id1", new OverrideForm(BigDecimal.ONE, "comment")),
      new IndividualOverrideForm("id2", new OverrideForm(BigDecimal.ONE, "comment2")),
    ])
    def evidence = Mock(MultipartFile)

    when:
    def result = service.overrideResolutions(VD_DASHBOARD_ID, form, evidence)

    then:
    1 * authenticationContext.currentUser() >> UserBuilder.user()
    1 * dashboardRepository.getDashboard(VD_DASHBOARD_ID, _) >> Either.right(VD_DASHBOARD_VIEW)
    1 * evidenceRepository.saveFile([VD_DASHBOARD_ID], evidence) >> Either.right(new ExceptionManagementEvidence())
    1 * ipvExceptionManagementRepository.overrideResults(VD_DASHBOARD_ID, OVERLAY_2, form, new ExceptionManagementEvidence(), user) >> Either.right([EntityId.entityId("id1")])

    and:
    result.isRight()
    result.getOrNull() == [EntityId.entityId("id1")]
  }

  String readResult(String filename) {
    def resourcePath = "/dashboards/csv/" + filename
    return new String(ByteStreams.toByteArray(getClass().getResourceAsStream(resourcePath)))
  }
}
