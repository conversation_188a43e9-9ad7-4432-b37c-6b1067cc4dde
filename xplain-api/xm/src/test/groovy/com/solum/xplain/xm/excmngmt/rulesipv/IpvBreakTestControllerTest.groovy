package com.solum.xplain.xm.excmngmt.rulesipv

import static com.solum.xplain.core.authentication.Authorities.CONFIGURE_VD_BREAK_TEST
import static com.solum.xplain.core.authentication.Authorities.MODIFY_VD_BREAK_TEST
import static com.solum.xplain.core.authentication.Authorities.VIEW_VD_BREAK_TEST
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.classifiers.Classifier
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.EnableForm
import com.solum.xplain.core.common.value.ResequenceForm
import com.solum.xplain.core.test.TestSecurityConfig
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.classifiers.XmClassifiersProvider
import com.solum.xplain.xm.excmngmt.processipv.validation.ValidXmClassifierStringSetValidator
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestOverrideKey
import com.solum.xplain.xm.excmngmt.rulesipv.validation.ValidIpvBreakTestFormValidator
import com.solum.xplain.xm.excmngmt.rulesipv.validation.ValidIpvOverrideThresholdsValidator
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestForm
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideForm
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestView
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [IpvBreakTestController])
@MockMvcConfiguration
@Import([TestSecurityConfig])
class IpvBreakTestControllerTest extends Specification {

  @SpringBean
  IpvBreakTestControllerService service = Mock()
  @SpringBean
  AuthenticationContext userRepository = Mock()
  @SpringBean
  IpvBreakTestRepository repository = Mock()
  @SpringBean
  RequestPathVariablesSupport variablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  ValidIpvBreakTestFormValidator validator = new ValidIpvBreakTestFormValidator(repository, variablesSupport)
  @SpringBean
  ValidIpvOverrideThresholdsValidator overrideValidator = new ValidIpvOverrideThresholdsValidator(repository, variablesSupport)
  @SpringBean
  XmClassifiersProvider xmClassifiersProvider = Mock()
  @SpringBean
  ValidXmClassifierStringSetValidator validXmClassifierStringSetValidator = new ValidXmClassifierStringSetValidator(xmClassifiersProvider)

  @Autowired
  ObjectMapper objectMapper
  @Autowired
  MockMvc mockMvc

  @WithMockUser
  @Unroll
  "should perform form (#form) role #role  validation with response #code #responseBody inserting"() {
    setup:
    service.create(_ as IpvBreakTestForm) >> right(EntityId.entityId("1"))
    variablesSupport.getPathVariable("id") >> null
    repository.existsWithName("duplicate", null) >> true
    xmClassifiersProvider.classifiers() >> [new Classifier("breakTestOperators", [new Classifier("GT"), new Classifier("GTE")])]
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(post("/exception-management/ipv-break-tests")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                 | form                                                                         | responseBody                                               | code
    MODIFY_VD_BREAK_TEST | formStale({ m -> m })                                                        | "id"                                                       | 200
    VIEW_VD_BREAK_TEST   | formStale({ m -> m })                                                        | "OPERATION_NOT_ALLOWED"                                    | 403
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.remove("name") })                                         | "NotEmpty.ipvBreakTestForm.name"                           | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.remove("scope") })                                        | "NotNull.ipvBreakTestForm.scope"                           | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("scope", "random") })                             | "InvalidFormat"                                            | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("name", "") })                                    | "NotEmpty.ipvBreakTestForm.name"                           | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("name", "duplicate") })                           | "ValidIpvBreakTestForm.ipvBreakTestForm.name"              | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.remove("type") })                                         | "NotNull.ipvBreakTestForm.type"                            | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("type", "ERROR") })                               | "InvalidFormat"                                            | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("providersTypes", ["P1"]) })                      | "Null.ipvBreakTestForm.providersTypes"                     | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("providersTypes", ["ERROR"]) })                   | "InvalidFormat"                                            | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.remove("enabled") })                                      | "NotNull.ipvBreakTestForm.enabled"                         | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("enabled", "ERROR") })                            | "InvalidFormat"                                            | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.put("measureType", "RELATIVE_DIFF") })                    | "Null.ipvBreakTestForm.measureType"                        | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.put("operator", "GT") })                                  | "Null.ipvBreakTestForm.operator"                           | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.put("parentBreakTestId", "000000000000000000000001") })   | "Null.ipvBreakTestForm.parentBreakTestId"                  | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("threshold", [1]) })                              | "Observation period must be single and be greater than 1!" | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("threshold", [2, 3]) })                           | "Observation period must be single and be greater than 1!" | 412

    MODIFY_VD_BREAK_TEST | formP2P({ m -> m })                                                          | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formP2P({ m -> m.put("type", "PRIMARY_VS_QUATERNARY") })                     | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formP2P({ m -> m.put("parentBreakTestId", "000000000000000000000001") })     | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formP2P({ m -> m.put("parentBreakTestId", "test") })                         | "ValidObjectId.ipvBreakTestForm.parentBreakTestId"         | 412
    MODIFY_VD_BREAK_TEST | formP2P({ m -> m.put("providersTypes", ["P1"]) })                            | "Null.ipvBreakTestForm.providersType"                      | 412

    MODIFY_VD_BREAK_TEST | formValue({ m -> m })                                                        | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.remove("providersTypes") })                               | "NotNull.ipvBreakTestForm.providersType"                   | 412
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.remove("operator") })                                     | "NotNull.ipvBreakTestForm.operator"                        | 412
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.remove("measureType") })                                  | "NotNull.ipvBreakTestForm.measureType"                     | 412
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.put("parentBreakTestId", "000000000000000000000001") })   | "Null.ipvBreakTestForm.parentBreakTestId"                  | 412
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.replace("threshold", [1, 2, 3]) })                        | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.replace("threshold", [3, 2, 1]) })                        | "Multiple threshold must be sorted!"                       | 412
    MODIFY_VD_BREAK_TEST | formValue({ m -> m.replace("threshold", [null]) })                           | "Threshold must not be null!"                              | 412

    MODIFY_VD_BREAK_TEST | formZero({ m -> m })                                                         | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formZero({ m -> m.put("measureType", "VALUE") })                             | "Null.ipvBreakTestForm.measureType"                        | 412
    MODIFY_VD_BREAK_TEST | formZero({ m -> m.put("threshold", [10]) })                                  | "Null.ipvBreakTestForm.threshold"                          | 412
    MODIFY_VD_BREAK_TEST | formZero({ m -> m.put("operator", "GT") })                                   | "Null.ipvBreakTestForm.operator"                           | 412
    MODIFY_VD_BREAK_TEST | formZero({ m -> m.remove("providersTypes") })                                | "NotNull.ipvBreakTestForm.providersTypes"                  | 412
    MODIFY_VD_BREAK_TEST | formZero({ m -> m.put("parentBreakTestId", "000000000000000000000001") })    | "Null.ipvBreakTestForm.parentBreakTestId"                  | 412

    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m })                                                      | "id"                                                       | 200
    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m.put("measureType", "VALUE") })                          | "Null.ipvBreakTestForm.measureType"                        | 412
    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m.put("threshold", [10]) })                               | "Null.ipvBreakTestForm.threshold"                          | 412
    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m.put("operator", "GT") })                                | "Null.ipvBreakTestForm.operator"                           | 412
    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m.remove("providersTypes") })                             | "NotNull.ipvBreakTestForm.providersTypes"                  | 412
    MODIFY_VD_BREAK_TEST | formD2DSign({ m -> m.put("parentBreakTestId", "000000000000000000000001") }) | "Null.ipvBreakTestForm.parentBreakTestId"                  | 412
  }

  @WithMockUser
  @Unroll
  "should perform form (#form) role #role #id validation with response #code #responseBody updating"() {
    setup:
    service.update(_ as String, _ as IpvBreakTestForm) >> right(EntityId.entityId("1"))
    repository.existsWithName("Name", "id") >> false
    repository.existsWithName("Name", "idDuplicate") >> true
    repository.getOne("id") >> right(new IpvBreakTestView(type: IpvTestType.STALE_VALUE))
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(put("/exception-management/ipv-break-tests/" + id)
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                 | form                                                | id            | responseBody                                        | code
    MODIFY_VD_BREAK_TEST | formStale({ m -> m })                               | "id"          | "id"                                                | 200
    VIEW_VD_BREAK_TEST   | formStale({ m -> m })                               | "id"          | "OPERATION_NOT_ALLOWED"                             | 403
    MODIFY_VD_BREAK_TEST | formStale({ m -> m.replace("type", "DAY_TO_DAY") }) | "id"          | "Type can not be changed!"                          | 412
    MODIFY_VD_BREAK_TEST | formStale({ m -> m })                               | "idDuplicate" | "IPV break test already exists with the same name!" | 412
  }

  @WithMockUser
  @Unroll
  def "should perform form (#form) role #role validation with response #code #responseBody resequencing"() {
    setup:
    service.resequence("id", _ as ResequenceForm) >> right(EntityId.entityId("id"))
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(put("/exception-management/ipv-break-tests/" + id + "/sequence")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                    | form                        | id     | responseBody                               | code
    CONFIGURE_VD_BREAK_TEST | new ResequenceForm(1, 3)    | "id"   | "id"                                       | 200
    VIEW_VD_BREAK_TEST      | new ResequenceForm(1, 3)    | "id"   | "OPERATION_NOT_ALLOWED"                    | 403
    CONFIGURE_VD_BREAK_TEST | new ResequenceForm(null, 3) | "id"   | "NotNull.resequenceForm.fromSequence"      | 412
    CONFIGURE_VD_BREAK_TEST | new ResequenceForm(1, null) | "id"   | "NotNull.resequenceForm.toBeforeSequence"  | 412
  }

  @WithMockUser
  @Unroll
  "should perform #form role #role validation with response #code #responseBody inserting override"() {
    setup:
    service.createOverride(_ as BreakTestOverrideKey, _ as IpvBreakTestOverrideForm) >> right(EntityId.entityId("1"))
    repository.getOne("id") >> right(new IpvBreakTestView(operator: Operator.GT))
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(post("/exception-management/ipv-break-tests/id/overrides")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                 | form                                                       | responseBody                                                | code
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m })                                       | "id"                                                        | 200
    VIEW_VD_BREAK_TEST   | formOvrd({ m -> m })                                       | "OPERATION_NOT_ALLOWED"                                     | 403
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.remove("tradeFilter") })                 | "NotNull.ipvBreakTestOverrideForm.tradeFilter"              | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m["tradeFilter"].remove("productTypes") }) | "NotNull.ipvBreakTestOverrideForm.tradeFilter.productTypes" | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.remove("threshold") })                   | "NotEmpty.ipvBreakTestOverrideForm.threshold"               | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.replace("threshold", [-1]) })            | "Threshold can not be less than zero!"                      | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.replace("threshold", [1, 2, 3]) })       | "id"                                                        | 200
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.replace("threshold", [3, 2, 1]) })       | "Multiple threshold must be sorted!"                        | 412
  }

  @WithMockUser
  @Unroll
  "should perform #form role #role validation with response #code #responseBody updating override"() {
    setup:
    service.updateOverride(BreakTestOverrideKey.of("id", "oid"), _ as IpvBreakTestOverrideForm) >> right(EntityId.entityId("1"))
    repository.getOne("id") >> right(new IpvBreakTestView(operator: Operator.GT))
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(put("/exception-management/ipv-break-tests/id/overrides/oid")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                 | form                                                       | responseBody                                                | code
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m })                                       | "id"                                                        | 200
    VIEW_VD_BREAK_TEST   | formOvrd({ m -> m })                                       | "OPERATION_NOT_ALLOWED"                                     | 403
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.remove("tradeFilter") })                 | "NotNull.ipvBreakTestOverrideForm.tradeFilter"              | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m["tradeFilter"].remove("productTypes") }) | "NotNull.ipvBreakTestOverrideForm.tradeFilter.productTypes" | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.remove("threshold") })                   | "NotEmpty.ipvBreakTestOverrideForm.threshold"               | 412
    MODIFY_VD_BREAK_TEST | formOvrd({ m -> m.replace("threshold", [-1]) })            | "Threshold can not be less than zero!"                      | 412
  }

  @WithMockUser
  @Unroll
  "should perform #form role #role validation with response #code #responseBody enable/disable override"() {
    setup:
    service.enableDisableOverride(BreakTestOverrideKey.of("id", "oid"), _ as EnableForm) >> right(EntityId.entityId("1"))
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())

    def results = mockMvc.perform(put("/exception-management/ipv-break-tests/id/overrides/oid/enable")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content(toJson(form)))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    role                    | form                                     | responseBody                | code
    CONFIGURE_VD_BREAK_TEST | formEnable({ m -> m })                   | "id"                        | 200
    MODIFY_VD_BREAK_TEST    | formEnable({ m -> m })                   | "OPERATION_NOT_ALLOWED"     | 403
    VIEW_VD_BREAK_TEST      | formEnable({ m -> m })                   | "OPERATION_NOT_ALLOWED"     | 403
    CONFIGURE_VD_BREAK_TEST | formEnable({ m -> m.remove("enabled") }) | "NotNull.enableForm.enable" | 412
  }

  def formStale(Closure c) {
    [
      scope         : "OVERLAY_1",
      name          : "Name",
      type          : "STALE_VALUE",
      providersTypes: null,
      tradeFilter   : [
        "productTypes": ["IRS"],
        "rateCcys"    : [],
        "fxPairs"     : []
      ],
      enabled       : "true",
      threshold     : [2]
    ].with(true, c)
  }

  def formValue(Closure c) {
    formStale({ i ->
      i.putAll([
        type          : "VALUE",
        providersTypes: ["P1", "P3"],
        operator      : "GT",
        threshold     : [1],
        measureType   : "VALUE"
      ])
    }).with(true, c)
  }

  def formP2P(Closure c) {
    formStale({ i ->
      i.putAll([
        type       : "PRIMARY_VS_SECONDARY",
        measureType: "RELATIVE_DIFF",
        operator   : "GTE",
        threshold  : [1]
      ])
    }).with(true, c)
  }

  def formZero(Closure c) {
    [
      name          : "Name",
      scope         : "OVERLAY_1",
      type          : "ZERO_VALUE",
      providersTypes: ["P1", "P3"],
      tradeFilter   : [
        "productTypes"     : [],
        "rateCcys"         : [],
        "fxPairs"          : [],
        "creditSectors"    : [],
        "underlyingIndices": [],
        "portfolioIds"     : []
      ],
      enabled       : "true",
    ].with(true, c)
  }

  def formD2DSign(Closure c) {
    [
      name          : "Name",
      scope         : "OVERLAY_1",
      type          : "DAY_TO_DAY_SIGN",
      providersTypes: ["P1", "P3"],
      tradeFilter   : [
        "productTypes"     : [],
        "rateCcys"         : [],
        "fxPairs"          : [],
        "creditSectors"    : [],
        "underlyingIndices": [],
        "portfolioIds"     : []
      ],
      enabled       : "true",
    ].with(true, c)
  }

  def formOvrd(Closure c) {
    [
      tradeFilter: [
        "productTypes": ["IRS"],
        "rateCcys"    : ["EUR"]
      ],
      enabled    : "true",
      threshold  : [1]
    ].with(true, c)
  }

  def formEnable(Closure c) {
    [
      enabled: "true"
    ].with(true, c)
  }
}
