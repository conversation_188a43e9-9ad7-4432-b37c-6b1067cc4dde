package com.solum.xplain.xm.excmngmt.process.value

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider.ofMultiLevelBreak
import static com.solum.xplain.xm.excmngmt.process.value.InstrumentMarketDataBreakCalculatorPreliminary.forInstrument
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO
import static org.apache.commons.lang3.ObjectUtils.anyNull

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType
import spock.lang.Specification
import spock.lang.Unroll

class HistoricalInstrumentMarketDataBreakCalculatorPreliminaryTest extends Specification {
  def EMPTY_HISTORY = new HistoricalInstrumentMarketData([], [:])

  def "should correctly perform null with triggered result"() {
    setup:
    def instrument = Mock(InstrumentDefinition)
    def data = Mock(ProviderData)
    def calculator = forInstrument(instrument, EMPTY_HISTORY, data)

    when:
    def result = calculator.nullValue()

    then:
    result == new EntryResultBreakByProvider(triggered: true)
  }

  def "should correctly perform zero with #triggered result"() {
    setup:
    def instrument = Mock(InstrumentDefinition)
    def data = Mock(ProviderData)
    def calculator = forInstrument(instrument, EMPTY_HISTORY, data)
    data.getValue() >> value

    when:
    def result = calculator.zeroValue()

    then:
    result == new EntryResultBreakByProvider(triggered: triggered, value: value)

    where:
    value | triggered
    null  | false
    0.0   | true
    0.1   | false
  }

  def "should correctly perform stale with triggered result"() {
    setup:
    def instrument = Mock(InstrumentDefinition)
    def data = Mock(ProviderData)
    data.getValue() >> ONE
    data.getBidAskType() >> BID
    data.getProvider() >> "A"

    def historicalData = Mock(HistoricalInstrumentMarketData)
    historicalData.staleValueDays(ONE, "A", BID) >> 3

    def calculator = forInstrument(instrument, historicalData, data)

    when:
    def result = calculator.staleValue(threshold)

    then:
    result == new EntryResultBreakByProvider(triggered: true, value: 3)

    where:
    threshold << [ONE, BigDecimal.valueOf(2l), BigDecimal.valueOf(3l)]
  }

  def "should correctly perform stale with no trigger"() {
    setup:
    def instrument = Mock(InstrumentDefinition)
    def data = Mock(ProviderData)
    data.getValue() >> ONE
    data.getBidAskType() >> BID
    data.getProvider() >> "A"

    def historicalData = Mock(HistoricalInstrumentMarketData)
    historicalData.staleValueDays(ONE, "A", BID) >> 2

    def calculator = forInstrument(instrument, historicalData, data)

    when:
    def result = calculator.staleValue(TEN)

    then:
    result == new EntryResultBreakByProvider(triggered: false, value: 2)
  }


  def "should correctly perform value with triggered result"() {
    setup:
    def measure = MeasureType.VALUE
    def testFunc = { v -> ofMultiLevelBreak(1, ONE, v) }

    def instrument = Mock(InstrumentDefinition)
    def data = Mock(ProviderData)
    data.getValue() >> ZERO
    def calculator = forInstrument(instrument, EMPTY_HISTORY, data)

    when:
    def result = calculator.value(measure, testFunc)

    then:
    result == new EntryResultBreakByProvider(triggered: true, triggeredThreshold: ONE, triggeredThresholdLevel: 1, value: ZERO)
  }

  def "should correctly perform value with empty result no data"() {
    setup:
    def measure = MeasureType.ABSOLUTE_DIFF
    def testFunc = { v -> Optional.of(1) }

    def instrument = Mock(InstrumentDefinition)
    def calculator = forInstrument(instrument, EMPTY_HISTORY, null)

    when:
    def result = calculator.value(measure, testFunc)

    then:
    result == new EntryResultBreakByProvider(triggered: false)
  }

  def "should correctly perform day to day sign with no provider data"() {
    setup:
    def calculator = forInstrument(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, EMPTY_HISTORY, null)

    when:
    def result = calculator.dayToDaySign()

    then:
    result == new EntryResultBreakByProvider(triggered: false)
  }

  @Unroll
  def "should correctly perform day to day sign for t=#value, t-1=#previousValue"() {
    setup:
    def data = ProviderData.of("P", value, previousValue, BID)
    def calculator = forInstrument(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, EMPTY_HISTORY, data)

    when:
    def result = calculator.dayToDaySign()

    then:
    result == new EntryResultBreakByProvider(triggered: isTriggered, value: anyNull(value, previousValue) ? null : value)

    where:
    value        | previousValue | isTriggered
    null         | null          | false
    ONE          | null          | false
    null         | ONE           | false

    ZERO         | ONE           | false
    ZERO         | ONE.negate()  | false
    ONE.negate() | ZERO          | false
    ZERO         | ZERO.negate() | false

    ONE          | ONE           | false
    ONE          | ONE.negate()  | true
    ONE.negate() | ONE           | true
    ONE          | TEN.negate()  | true
    TEN.negate() | ONE           | true
  }
}
