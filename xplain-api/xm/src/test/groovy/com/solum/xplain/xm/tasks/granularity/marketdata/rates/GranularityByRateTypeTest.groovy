package com.solum.xplain.xm.tasks.granularity.marketdata.rates

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.tasks.TaskMapper
import com.solum.xplain.xm.tasks.entity.TaskExecution
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class GranularityByRateTypeTest extends Specification {
  def taskMapper = Mappers.getMapper(TaskMapper.class)
  static List<InstrumentType> INSTRUMENTS = Arrays.asList(CoreInstrumentType.values())

  def "should split all asset classes"() {
    setup:
    def rule = new GranularityByRateType(taskMapper, INSTRUMENTS)

    when:
    def executions = rule.split(new TaskExecution(assetFilter: new AssetFilter()))

    then:
    executions.size() == 3
    executions.stream().anyMatch { e -> e.assetFilter.assetClasses == [CoreAssetClass.IR_RATE] && !e.assetFilter.irInstruments.contains(CoreInstrumentType.BOND_YIELD) }
    executions.stream().anyMatch { e -> e.assetFilter.assetClasses == [CoreAssetClass.IR_RATE] && e.assetFilter.irInstruments.contains(CoreInstrumentType.BOND_YIELD) }
    executions.stream().anyMatch { e -> !e.assetFilter.assetClasses.contains([CoreAssetClass.IR_RATE]) }
  }
}
