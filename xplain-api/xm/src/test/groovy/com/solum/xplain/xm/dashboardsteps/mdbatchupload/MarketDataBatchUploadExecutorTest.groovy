package com.solum.xplain.xm.dashboardsteps.mdbatchupload

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataBatchDashboard

import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import io.atlassian.fugue.Either
import spock.lang.Specification

class MarketDataBatchUploadExecutorTest extends Specification {

  DashboardStepProcessor processor = Mock()

  MarketDataBatchUploadExecutor executor = new MarketDataBatchUploadExecutor(processor)

  def "should execute"() {
    setup:
    def dashboard = marketDataBatchDashboard()

    1 * processor.createMdBatchSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMdBatch>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MARKET_DATA_BATCH_UPLOAD
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = executor.execute(dashboard)

    then:
    result.isRight()
    result.getOrNull() == dashboard.entityId()
  }
}
