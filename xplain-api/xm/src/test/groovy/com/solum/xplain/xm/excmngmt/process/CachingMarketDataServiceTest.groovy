package com.solum.xplain.xm.excmngmt.process

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.mdvalue.MarketDataValueRepository
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import java.time.LocalDate
import spock.lang.Specification

class CachingMarketDataServiceTest extends Specification {
  def marketDataExtractionService = Mock(MarketDataExtractionService)
  def marketDataValueRepository = Mock(MarketDataValueRepository)
  def stateDate = BitemporalDate.newOf(LocalDate.now())
  def service = new CachingMarketDataService(marketDataExtractionService, marketDataValueRepository)

  def "should fetch market data for key"() {
    given:
    def marketDataGroupId = "mdgId"
    def instrument = InstrumentDefinitionBuilder.DUMMY_INSTRUMENT

    when:
    def data = service.providerData(marketDataGroupId, previousDate, instrument, stateDate)
    .sort(false, (a, b) -> {
      def result = a.provider.compareTo(b.provider)
      if (result == 0) {
        result = a.bidAskType.compareTo(b.bidAskType)
      }
      result
    })

    then: "should fetch market data for date and previous date"
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >>
    [
      "KEY":
      [
        "PROVIDER1": [(ValueBidAskType.BID): 1.0, (ValueBidAskType.ASK): 1.1],
        "PROVIDER2": [(ValueBidAskType.ASK): 1.2]
      ]
    ]
    (previousDate == null ? 0 : 1) * marketDataExtractionService.preliminaryData(marketDataGroupId, previousDate, stateDate, _) >>
    [
      "KEY":
      [
        "PROVIDER1": [(ValueBidAskType.BID): 0.9, (ValueBidAskType.ASK): 1.0],
        "PROVIDER2": [(ValueBidAskType.ASK): 1.1]
      ]
    ]

    and: "results should be returned for instrument"
    data.size() == 6
    data*.provider == ["PROVIDER1", "PROVIDER1", "PROVIDER1", "PROVIDER2", "PROVIDER2", "PROVIDER2"]
    data*.bidAskType == [ValueBidAskType.BID, ValueBidAskType.MID, ValueBidAskType.ASK, ValueBidAskType.BID, ValueBidAskType.MID, ValueBidAskType.ASK]
    data*.value == [1.0, null, 1.1, null, null, 1.2]
    data*.previousValue == (previousDate == null ? [null, null, null, null, null, null] : [0.9, null, 1.0, null, null, 1.1])

    where:
    previousDate                 | _
    null                         | "should fetch market data for key"
    LocalDate.now().minusDays(1) | "should fetch market data for key"
  }

  def "should fetch historical market data for key"() {
    given:
    def marketDataGroupId = "mdgId"
    def instrument = InstrumentDefinitionBuilder.DUMMY_INSTRUMENT

    when:
    def data = service.historicalData(marketDataGroupId, instrument, 2, stateDate)

    then: "should fetch market data for the previous 2 dates"
    1 * marketDataValueRepository.getMarketDataDates(marketDataGroupId, stateDate) >> [
      LocalDate.now().plusDays(1),
      LocalDate.now(),
      LocalDate.now().minusDays(1),
      LocalDate.now().minusDays(4),
      LocalDate.now().minusDays(5)
    ]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(1), stateDate, _) >>
    [
      "KEY":
      [
        "PROVIDER1": [(ValueBidAskType.BID): 1.0, (ValueBidAskType.ASK): 1.1],
        "PROVIDER2": [(ValueBidAskType.ASK): 1.2]
      ]
    ]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(4), stateDate, _) >>
    [
      "KEY":
      [
        "PROVIDER1": [(ValueBidAskType.BID): 4.0, (ValueBidAskType.ASK): 1.1],
        "PROVIDER2": [(ValueBidAskType.ASK): 4.2]
      ]
    ]

    and: "results should be returned for instrument"
    data.staleValueDays(currentValue, provider, bidAskType) == expectedStale

    where:
    currentValue | provider    | bidAskType          || expectedStale
    1.0          | "PROVIDER1" | ValueBidAskType.BID || 1
    0.0          | "PROVIDER1" | ValueBidAskType.BID || 0
    1.1          | "PROVIDER1" | ValueBidAskType.ASK || 2
    1.2          | "PROVIDER2" | ValueBidAskType.ASK || 1
    4.2          | "PROVIDER2" | ValueBidAskType.ASK || 0
  }

  def "should not fetch more than once for the same dates"() {
    given:
    def marketDataGroupId = "mdgId"
    def instrument = InstrumentDefinitionBuilder.DUMMY_INSTRUMENT
    marketDataValueRepository.getMarketDataDates(marketDataGroupId, stateDate) >> [
      LocalDate.now().plusDays(1),
      LocalDate.now(),
      LocalDate.now().minusDays(1),
      LocalDate.now().minusDays(2),
      LocalDate.now().minusDays(3),
      LocalDate.now().minusDays(4)
    ]

    when:
    service.providerData(marketDataGroupId, LocalDate.now().minusDays(1), instrument, stateDate)

    then: "should fetch market data for date and previous date"
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >> [:]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(1), stateDate, _) >> [:]

    when: "fetch for same current date but different previous date"
    service.providerData(marketDataGroupId, LocalDate.now().minusDays(2), instrument, stateDate)

    then: "should fetch market data for previous date only"
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >> [:]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(2), stateDate, _) >> [:]

    when: "fetch for same dates"
    service.providerData(marketDataGroupId, LocalDate.now().minusDays(1), instrument, stateDate)

    then: "should not fetch again"
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(1), stateDate, _) >> [:]

    when: "fetch historic data for 3 days"
    service.historicalData(marketDataGroupId, instrument, 3, stateDate)

    then: "should fetch market data for T-3 only"
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(1), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(2), stateDate, _) >> [:]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(3), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(4), stateDate, _) >> [:]

    when: "fetch historic data for 20 days"
    service.historicalData(marketDataGroupId, instrument, 20, stateDate)

    then: "should fetch market data for T-4 only"
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now(), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(1), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(2), stateDate, _) >> [:]
    0 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(3), stateDate, _) >> [:]
    1 * marketDataExtractionService.preliminaryData(marketDataGroupId, LocalDate.now().minusDays(4), stateDate, _) >> [:]
  }
}
