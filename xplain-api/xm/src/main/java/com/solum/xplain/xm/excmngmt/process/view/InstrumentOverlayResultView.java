package com.solum.xplain.xm.excmngmt.process.view;

import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.BREAK_TEST_THRESHOLD_LEVEL;

import com.solum.xplain.core.classifiers.xm.CoreThresholdLevel;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableView;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewMapping;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.HasEditableResult;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistoryView;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ConfigurableView
public class InstrumentOverlayResultView implements HasEditableResult {

  @ConfigurableViewIgnore(comment = "Internal ID not exposed to user")
  @NotNull
  private String id;

  @NotNull private InstrumentView instrument;

  @ConfigurableViewIgnore(comment = "Internal ID not exposed to user")
  @Nullable
  private String curveConfigurationId;

  @ConfigurableViewQuery(sortable = true)
  @Nullable
  private String curveConfigurationName;

  @ConfigurableViewIgnore(comment = "Internal information not exposed to user")
  private LocalDate valuationDate;

  @ConfigurableViewIgnore(comment = "Internal ID not exposed to user")
  @Nullable
  private String companyId;

  @ConfigurableViewIgnore(
      comment = "Exposed through legalEntityExternalId as a combined field instead.")
  @Nullable
  private String companyExternalId;

  @ConfigurableViewIgnore(comment = "Internal ID not exposed to user")
  @Nullable
  private String legalEntityId;

  @ConfigurableViewField(InstrumentResultViewFieldName.COMPANY_AND_LEGAL_ENTITY)
  @ConfigurableViewQuery(sortable = true)
  @Nullable
  private String legalEntityExternalId;

  @ConfigurableViewField(InstrumentResultViewFieldName.STATUS)
  @ConfigurableViewField(InstrumentResultViewFieldName.VERIFICATION_RESOLUTION)
  @NotNull
  private EntryResultStatus status;

  @ConfigurableViewIgnore private WorkflowStatus stepStatus;

  @ConfigurableViewMapping(from = "provider", ignore = true)
  @Nullable
  private InstrumentResultResolutionView resolution;

  @ConfigurableViewQuery(sortable = true)
  @Nullable
  private ValueBidAskType bidAskType;

  @ConfigurableViewField(prefix = InstrumentResultViewFieldName.PRIMARY_PREFIX)
  @Nullable
  private ProviderDataView primaryProviderData;

  @ConfigurableViewField(prefix = InstrumentResultViewFieldName.SECONDARY_PREFIX)
  @Nullable
  private ProviderDataView secondaryProviderData;

  @ConfigurableViewIgnore(comment = "Internal information not exposed to user")
  @Nullable
  private BigDecimal previousOverlayValue;

  @ConfigurableViewField(unwrapPropertyPath = true)
  @Nullable
  private List<InstrumentResultBreakView> breakTests;

  @ConfigurableViewIgnore(comment = "Internal information not exposed to user")
  private AuditUser modifiedBy;

  @ConfigurableViewIgnore(comment = "Internal information not exposed to user")
  @NotNull
  private LocalDateTime modifiedAt;

  @ConfigurableViewIgnore @Nullable private List<EntryResultStatusHistoryView> previousStatuses;

  @ConfigurableViewField(enumClassifier = BREAK_TEST_THRESHOLD_LEVEL)
  @ConfigurableViewQuery(sortable = true)
  @Nullable
  private CoreThresholdLevel maxTriggeredThresholdLevel;

  @Override
  public boolean isResolved() {
    return resolution != null;
  }
}
