package com.solum.xplain.xm.tasks.granularity.marketdata.rates;

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE;

import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter;
import com.solum.xplain.xm.tasks.TaskMapper;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.granularity.marketdata.BaseMdTaskGranularityRule;
import java.util.List;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class GranularityByIrInstrument extends BaseMdTaskGranularityRule {

  public GranularityByIrInstrument(TaskMapper mapper, List<InstrumentType> supportedInstruments) {
    super(mapper, supportedInstruments);
  }

  @Override
  public List<TaskExecution> split(TaskExecution taskExecution) {
    var assetFilter = taskExecution.getAssetFilter();
    var assetClasses = assetFilter.permissibleAssetClasses(allAssetClasses());
    return assetFilter
        .separateAssetClassesAndSplitOn(v -> v == IR_RATE, assetClasses, this::splitByIrInstruments)
        .stream()
        .map(v -> mapper.copy(taskExecution, v))
        .toList();
  }

  private List<AssetFilter> splitByIrInstruments(AssetFilter assetFilter) {
    return assetFilter.permissibleInstruments(supportedInstruments).stream()
        .filter(v -> v.getInstrumentGroup() == CoreInstrumentGroup.IR)
        .map(irInstrument -> AssetFilter.copyWithInstrument(assetFilter, irInstrument))
        .toList();
  }
}
