package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.lang.NonNull;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MdOverlayClearingEvent extends DashboardEvent {

  private final String curveConfigurationId;
  private final String companyId;
  private final String legalEntityId;

  @Builder
  public MdOverlayClearingEvent(
      @lombok.NonNull String dashboardId,
      @lombok.NonNull Type type,
      String curveConfigurationId,
      String companyId,
      String legalEntityId) {
    super(dashboardId, type);
    this.curveConfigurationId = curveConfigurationId;
    this.companyId = companyId;
    this.legalEntityId = legalEntityId;
  }

  @NonNull
  @Override
  public DashboardStep getStep() {
    return DashboardStep.MD_OVERLAY_CLEARING;
  }
}
