package com.solum.xplain.xm.excmngmt.processipv.value;

import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Streams;
import com.solum.xplain.core.portfolio.value.PortfolioItemCalculatedExcMngmntView;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks;
import com.solum.xplain.xm.settings.IpvValueCurrencyType;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class IpvPreliminaryCleanOwnDataCombined {

  public static final IpvPreliminaryCleanOwnDataCombined EMPTY =
      new IpvPreliminaryCleanOwnDataCombined(emptyMap());

  private final Map<String, ProviderDataWithGreeks> data;

  /**
   * Build map of trade -> ProviderDataWithGreeks for distinct keys found in calcsByTrade +
   * previousCalcsByTrade. For each trade provider data metric, current and previous will both be
   * set if the trade is present in both calcsByTrade and previousCalcsByTrade.
   */
  public static IpvPreliminaryCleanOwnDataCombined of(
      IpvValueCurrencyType currencyType,
      Map<String, PortfolioItemCalculatedExcMngmntView> calcsByTrade,
      Map<String, PortfolioItemCalculatedExcMngmntView> previousCalcsByTrade) {
    var allTrades =
        Streams.concat(calcsByTrade.keySet().stream(), previousCalcsByTrade.keySet().stream())
            .collect(toImmutableSet());
    var pvFunction = pvMapper(currencyType);
    var deltaFunction = deltaMapper(currencyType);
    var vegaFunction = vegaMapper(currencyType);

    var data = ImmutableMap.<String, ProviderDataWithGreeks>builder();

    for (var t : allTrades) {
      var current = calcsByTrade.get(t);
      var previous = previousCalcsByTrade.get(t);

      var own = new ProviderDataWithGreeks();
      own.setProvider(XPLAIN_PROVIDER_CODE);
      own.setPv(extract(current, previous, pvFunction));
      own.setDelta(extract(current, previous, deltaFunction));
      own.setVega(extract(current, previous, vegaFunction));
      own.setParRate(
          extract(
              current, previous, PortfolioItemCalculatedExcMngmntView::getMetricsBreakevenParRate));
      own.setImpliedVol(
          extract(
              current,
              previous,
              PortfolioItemCalculatedExcMngmntView::getMetricsBreakevenImpliedVol));

      data.put(t, own);
    }
    return new IpvPreliminaryCleanOwnDataCombined(data.build());
  }

  private static ProviderDataValue extract(
      PortfolioItemCalculatedExcMngmntView currentValue,
      PortfolioItemCalculatedExcMngmntView previousValue,
      Function<PortfolioItemCalculatedExcMngmntView, Double> mapper) {
    return new ProviderDataValue(
        ofNullable(currentValue).map(mapper).map(BigDecimal::new).orElse(null),
        ofNullable(previousValue).map(mapper).map(BigDecimal::new).orElse(null));
  }

  private static Function<PortfolioItemCalculatedExcMngmntView, Double> pvMapper(
      IpvValueCurrencyType currencyType) {
    return switch (currencyType) {
      case TRADE_CCY -> PortfolioItemCalculatedExcMngmntView::getMetricsPresentValuePayLegCurrency;
      case REPORTING_CCY -> PortfolioItemCalculatedExcMngmntView::getMetricsPresentValue;
    };
  }

  private static Function<PortfolioItemCalculatedExcMngmntView, Double> deltaMapper(
      IpvValueCurrencyType currencyType) {
    boolean inLocalCcy = currencyType == IpvValueCurrencyType.TRADE_CCY;
    return view -> view.delta(inLocalCcy);
  }

  private static Function<PortfolioItemCalculatedExcMngmntView, Double> vegaMapper(
      IpvValueCurrencyType currencyType) {
    return switch (currencyType) {
      case TRADE_CCY -> PortfolioItemCalculatedExcMngmntView::getMetricsPvVegaLocalCcy;
      case REPORTING_CCY -> PortfolioItemCalculatedExcMngmntView::getMetricsPvVega;
    };
  }

  public Optional<ProviderDataWithGreeks> tradeData(String tradeKey) {
    return ofNullable(data.get(tradeKey));
  }
}
