package com.solum.xplain.xm.tasks.view.summary;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class TaskSummaryUtils {
  public static final String FILTER_JOIN_ON = " + ";
  public static final String NO_FILTERS = "N/A";
  public static final String ALL_FILTERS = "ALL";

  public static <T> String filterSummary(List<T> filters, Function<T, String> toStringFn) {
    return filterSummary(filters, toStringFn, null);
  }

  public static <T> String filterSummary(
      List<T> filters,
      Function<T, String> toStringFn,
      @Nullable Function<List<T>, String> groupSupplier) {
    if (filters == null || filters.isEmpty()) {
      return ALL_FILTERS;
    } else if (filters.size() == 1) {
      return toStringFn.apply(filters.get(0));
    }
    return Optional.ofNullable(groupSupplier)
        .map(s -> s.apply(filters))
        .orElseGet(() -> TaskSummaryUtils.fallbackSupplier(filters, toStringFn));
  }

  public static String fxPairSummary(List<String> fxPairs) {
    return filterSummary(
        fxPairs,
        Function.identity(),
        fx ->
            fx.stream()
                .map(CurrencyPair::parse)
                .map(CurrencyPair::getBase)
                .map(base -> CurrencyPair.of(base, Currency.XXX))
                .map(CurrencyPair::toString)
                .findFirst()
                .orElse(null));
  }

  private static <T> String fallbackSupplier(List<T> objects, Function<T, String> toStringFn) {
    return objects.stream().map(toStringFn).collect(Collectors.joining(FILTER_JOIN_ON));
  }
}
