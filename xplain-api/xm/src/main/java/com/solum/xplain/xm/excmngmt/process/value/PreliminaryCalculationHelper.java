package com.solum.xplain.xm.excmngmt.process.value;

import static com.solum.xplain.core.common.CollectionUtils.mergedSet;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;
import org.springframework.lang.NonNull;

@UtilityClass
public class PreliminaryCalculationHelper {

  private static final ValueBidAskType[] valueTypes = new ValueBidAskType[] {ASK, MID, BID};

  /**
   * Builds a map of providers by instrument. The map's keys are the instruments and the values are
   * lists of ProviderData objects. The ProviderData objects contain the provider name, the current
   * value for the provider on the instrument, and the previous value for the provider on the
   * instrument.
   *
   * <p>The current and previous values are used to calculate the break tests.
   *
   * <p>The current and previous values are stored in the ProviderData objects in the following way:
   * - the current value is stored in the ProviderData's value field - the previous value is stored
   * in the ProviderData's previousValue field
   *
   * <p>The ProviderData objects are stored in the map in the following way: - the instrument is the
   * key - the value is a list of ProviderData objects
   *
   * @param currDataByInstrument the map of current market data by instrument
   * @param prevDataByInstrument the map of previous market data by instrument
   * @return a map of providers by instrument
   */
  public static Map<String, List<ProviderData>> marketDataProvidersByInstrument(
      @NonNull Map<String, Map<String, Map<ValueBidAskType, BigDecimal>>> currDataByInstrument,
      @NonNull Map<String, Map<String, Map<ValueBidAskType, BigDecimal>>> prevDataByInstrument) {

    var allInstruments = mergedSet(currDataByInstrument.keySet(), prevDataByInstrument.keySet());

    var results = ImmutableMap.<String, List<ProviderData>>builder();
    for (var instrument : allInstruments) {
      var currValuesByProvider = currDataByInstrument.getOrDefault(instrument, ImmutableMap.of());
      var prevValuesByProvider = prevDataByInstrument.getOrDefault(instrument, ImmutableMap.of());

      var providerValues = ImmutableList.<ProviderData>builder();
      for (var provider : mergedSet(currValuesByProvider.keySet(), prevValuesByProvider.keySet())) {
        for (var valueType : valueTypes) {
          providerValues.add(
              ProviderData.of(
                  provider,
                  currValuesByProvider.getOrDefault(provider, ImmutableMap.of()).get(valueType),
                  prevValuesByProvider.getOrDefault(provider, ImmutableMap.of()).get(valueType),
                  valueType));
        }
      }

      results.put(instrument, providerValues.build());
    }
    return results.build();
  }
}
