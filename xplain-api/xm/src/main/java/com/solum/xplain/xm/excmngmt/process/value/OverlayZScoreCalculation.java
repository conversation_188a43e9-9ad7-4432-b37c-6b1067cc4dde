package com.solum.xplain.xm.excmngmt.process.value;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.trs.value.TrsAssetClassGroup;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.excmngmt.process.instrument.InstrumentRequirements;
import com.solum.xplain.xm.excmngmt.stat.data.StatisticalZScoreData;
import com.solum.xplain.xm.excmngmt.stat.value.CalculationStatisticalZScoreData;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@EqualsAndHashCode
@ToString
public class OverlayZScoreCalculation {

  private final Map<Tenor, List<StatisticalZScoreData>> statDataByTenor;
  private final Map<InstrumentDefinition, List<InstrumentResultPreliminary>>
      preliminaryMarketDataByInstrument;

  public OverlayZScoreCalculation(
      Map<Tenor, List<StatisticalZScoreData>> statDataByTenor,
      Map<InstrumentDefinition, List<InstrumentResultPreliminary>>
          preliminaryMarketDataByInstrument) {
    this.statDataByTenor = statDataByTenor;
    this.preliminaryMarketDataByInstrument = preliminaryMarketDataByInstrument;
  }

  public Map<Tenor, CalculationStatisticalZScoreData> calculate(
      @NonNull InstrumentDefinition instrument,
      @NonNull ValueBidAskType bidAskType,
      @NonNull InstrumentRequirements requirements) {
    return statDataByTenor.entrySet().stream()
        .collect(
            toMap(
                Entry::getKey, v -> calculate(instrument, bidAskType, requirements, v.getValue())));
  }

  private CalculationStatisticalZScoreData calculate(
      @NonNull InstrumentDefinition instrument,
      @NonNull ValueBidAskType bidAskType,
      @NonNull InstrumentRequirements requirements,
      @NonNull List<StatisticalZScoreData> allData) {
    var stats =
        allData.stream()
            .filter(
                i ->
                    StringUtils.equals(i.getCurveConfigurationId(), requirements.configurationId())
                        && StringUtils.equals(i.getLegalEntityId(), requirements.legalEntityId())
                        && StringUtils.equals(i.getCompanyId(), requirements.companyId())
                        && StringUtils.equals(i.getCurve(), instrument.getAssetName())
                        && StringUtils.equals(i.getInstrumentKey(), instrument.getKey())
                        && Objects.equals(i.getBidAskType(), bidAskType))
            .toList();

    var res = new CalculationStatisticalZScoreData();
    for (var z : stats) {
      if (z.isConditional() && instrument.getAssetClass().getGroup() != TrsAssetClassGroup.TRS) {
        var v =
            relatedInst(instrument).stream()
                .sorted(comparing(InstrumentDefinition::parsedTenor))
                .map(i -> resolvedPrelimProvidersData(i, bidAskType, requirements))
                .map(r -> z.isPrimary() ? r.getFirst() : r.getSecond())
                .filter(Objects::nonNull)
                .map(ProviderData::dayDiff)
                .filter(Objects::nonNull)
                .map(BigDecimal::doubleValue)
                .toList();
        res.withCondZ(z, v);
      } else {
        res.withZ(z);
      }
    }

    return res;
  }

  private Pair<ProviderData, ProviderData> resolvedPrelimProvidersData(
      @NonNull InstrumentDefinition instrumentDefinition,
      @NonNull ValueBidAskType bidAskType,
      @NonNull InstrumentRequirements requirements) {
    var curveConfigurationProvider = Optional.ofNullable(requirements.providers());
    var providers =
        preliminaryMarketDataByInstrument.get(instrumentDefinition).stream()
            .map(InstrumentResultPreliminary::resolvedValue)
            .toList();

    ProviderData primary =
        curveConfigurationProvider
            .flatMap(provider -> findProviderData(providers, provider.getPrimary(), bidAskType))
            .orElse(null);

    ProviderData secondary =
        curveConfigurationProvider
            .flatMap(
                provider ->
                    findProviderData(
                        providers,
                        isEmpty(provider.getSecondary())
                            ? provider.getPrimary()
                            : provider.getSecondary(),
                        bidAskType))
            .orElse(null);
    return Pair.create(primary, secondary);
  }

  private Optional<ProviderData> findProviderData(
      @NonNull List<ProviderData> providers,
      @Nullable String providerName,
      @NonNull ValueBidAskType bidAskType) {
    return providers.stream()
        .filter(v -> Objects.equals(providerName, v.getProvider()))
        .filter(v -> Objects.equals(bidAskType, v.getBidAskType()))
        .findAny();
  }

  private List<InstrumentDefinition> relatedInst(InstrumentDefinition instrumentDefinition) {
    return preliminaryMarketDataByInstrument.keySet().stream()
        .filter(
            i ->
                i.getAssetClass() == instrumentDefinition.getAssetClass()
                    && StringUtils.equals(i.getAssetName(), instrumentDefinition.getAssetName()))
        .filter(i -> !i.equals(instrumentDefinition))
        .toList();
  }
}
