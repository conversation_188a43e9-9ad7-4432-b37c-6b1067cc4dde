package com.solum.xplain.xm.excmngmt.process.data;

import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION;

import com.solum.xplain.core.common.AuditContext;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.excmngmt.HasEntryResult;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.process.form.ResolutionForm;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

public interface HasInstrumentResult extends HasEntryResult<InstrumentResultResolution> {

  // Mimics lombok generated field name constant fields
  @SuppressWarnings("java:S115")
  @NoArgsConstructor(access = AccessLevel.PRIVATE)
  final class Fields {
    public static final String hasBreaks = "hasBreaks";
  }

  void setModifiedBy(AuditUser modifiedBy);

  void setModifiedAt(LocalDateTime modifiedAt);

  List<InstrumentResultBreak> getBreakTests();

  void setBreakTests(List<InstrumentResultBreak> breakTests);

  Instrument getInstrument();

  Optional<ErrorItem> applyResolutionValues(ResolutionForm resolution);

  void setMaxTriggeredThresholdLevel(Integer maxTriggeredThresholdLevel);

  Integer getMaxTriggeredThresholdLevel();

  default Optional<ErrorItem> applyResolution(
      ResolutionForm resolutionForm,
      AuditContext audit,
      EntryResultStatus status,
      ExceptionManagementEvidence evidence) {
    this.saveStatus();
    this.setStatus(status);
    this.setResolution(InstrumentResultResolution.newOf(resolutionForm, evidence));
    this.setModifiedBy(audit.user());
    this.setModifiedAt(audit.now());
    return applyResolutionValues(resolutionForm);
  }

  default HasEntryResult<InstrumentResultResolution> undoResolution() {
    this.saveStatus();
    this.setStatus(WAITING_RESOLUTION);
    this.setResolution(null);
    return this;
  }

  default void withBreakTests(List<InstrumentResultBreak> breakTests) {
    var breaksTriggered = HasEntryResult.anyHasBreak(breakTests);
    this.setHasBreaks(breaksTriggered);
    this.setBreakTests(breakTests);
    this.setStatus(breaksTriggered ? WAITING_RESOLUTION : VERIFIED);
    this.setAppliedTestsCount(HasEntryResult.countAppliedTests(breakTests));
    this.setMaxTriggeredThresholdLevel(HasEntryResult.maxTriggeredThresholdLevel(breakTests));
  }
}
