package com.solum.xplain.xm.excmngmt.rules;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_CONFIGURE_MD_BREAK_TEST;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_MD_BREAK_TEST;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MD_BREAK_TEST;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestOverrideKey.of;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.value.EnableForm;
import com.solum.xplain.core.common.value.ResequenceForm;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestForm;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestView;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestView;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/exception-management/break-tests")
public class BreakTestController {

  private final BreakTestControllerService service;

  public BreakTestController(BreakTestControllerService service) {
    this.service = service;
  }

  @Operation(summary = "Get all break tests")
  @GetMapping
  @CommonErrors
  @ScrolledFiltered
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public ScrollableEntry<BreakTestView> getAll(
      TableFilter tableFilter,
      BreakTestFilter filter,
      @SortDefault(sort = {BaseBreakTestView.Fields.sequence}) ScrollRequest scrollRequest) {
    return service.getAll(scrollRequest, tableFilter, filter);
  }

  @Operation(summary = "Get one break test")
  @GetMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public ResponseEntity<BreakTestView> getOne(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.getOne(id));
  }

  @Operation(summary = "Create new break test")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> create(@Valid @RequestBody BreakTestForm form) {
    return eitherErrorItemResponse(service.create(form));
  }

  @Operation(summary = "Update break test")
  @PutMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> update(
      @PathVariable("id") String id, @Valid @RequestBody BreakTestForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Set enable flag")
  @PutMapping("/{id}/enable")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_MD_BREAK_TEST)
  public ResponseEntity<EntityId> updateEnabled(
      @PathVariable("id") String id, @Valid @RequestBody EnableForm form) {
    return eitherErrorItemResponse(service.enableDisable(id, form));
  }

  @Operation(summary = "Reorder sequence of break test to be before another")
  @PutMapping("/{id}/sequence")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_MD_BREAK_TEST)
  public ResponseEntity<EntityId> updateSequence(
      @PathVariable("id") String id, @Valid @RequestBody ResequenceForm form) {
    return eitherErrorItemResponse(service.resequence(id, form));
  }

  @Operation(summary = "Archive break test")
  @PutMapping("/{id}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> archive(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.archive(id));
  }

  @Operation(summary = "Get break test versions")
  @GetMapping("/{id}/versions")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public ResponseEntity<List<BreakTestView>> getVersions(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.getVersions(id));
  }

  // Overrides

  @Operation(summary = "Get all overrides")
  @GetMapping("/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public List<BreakTestOverrideView> getAll() {
    return service.getAllOverrides();
  }

  @Operation(summary = "Get break test overrides")
  @GetMapping("/{id}/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public List<BreakTestOverrideView> getAll(@PathVariable("id") String id) {
    return service.getOverrides(of(id, null));
  }

  @Operation(summary = "Get break test override by id")
  @GetMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MD_BREAK_TEST)
  public ResponseEntity<BreakTestOverrideView> getOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.getOverride(of(id, overrideId)));
  }

  @Operation(summary = "Create new break test override")
  @PostMapping("/{id}/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> createOverride(
      @PathVariable("id") String id, @Valid @RequestBody BreakTestOverrideForm form) {
    return eitherErrorItemResponse(service.createOverride(of(id, null), form));
  }

  @Operation(summary = "Update break test override")
  @PutMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> updateOverride(
      @PathVariable("id") String id,
      @PathVariable("overrideId") String overrideId,
      @Valid @RequestBody BreakTestOverrideForm form) {
    return eitherErrorItemResponse(service.updateOverride(of(id, overrideId), form));
  }

  @Operation(summary = "Delete break test override")
  @DeleteMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> deleteOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.deleteOverride(of(id, overrideId)));
  }

  @Operation(summary = "Enable/disable break test override")
  @PutMapping("/{id}/overrides/{overrideId}/enable")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_MD_BREAK_TEST)
  public ResponseEntity<EntityId> toggleOverride(
      @PathVariable("id") String id,
      @PathVariable("overrideId") String overrideId,
      @Valid @RequestBody EnableForm f) {
    return eitherErrorItemResponse(service.enableDisableOverride(of(id, overrideId), f));
  }

  @Operation(summary = "Clone break test override")
  @PutMapping("/{id}/overrides/{overrideId}/clone")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_MD_BREAK_TEST)
  public ResponseEntity<EntityId> cloneOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.cloneOverride(of(id, overrideId)));
  }
}
