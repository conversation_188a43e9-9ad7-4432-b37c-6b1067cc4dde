package com.solum.xplain.xm.excmngmt.processipv.history;

import com.solum.xplain.core.portfolio.trade.TradeValuationHistoryProvider;
import com.solum.xplain.core.portfolio.trade.TradeValuationHistoryView;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class IpvExceptionManagementValuationHistoryProvider
    implements TradeValuationHistoryProvider {

  private final IpvExceptionManagementValuationHistoryRepository repository;

  @Override
  public List<TradeValuationHistoryView> getTradeValuationHistory(
      PortfolioView portfolioView, String tradeEntityId) {
    return repository.getTradeValuationHistory(tradeEntityId, portfolioView.getId());
  }
}
