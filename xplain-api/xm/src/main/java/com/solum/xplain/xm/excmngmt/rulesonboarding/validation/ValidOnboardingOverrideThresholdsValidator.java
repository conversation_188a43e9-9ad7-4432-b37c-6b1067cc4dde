package com.solum.xplain.xm.excmngmt.rulesonboarding.validation;

import static com.solum.xplain.xm.excmngmt.rulesbase.validation.ValidThresholdsValidatorUtils.validateThresholdsForOperator;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestView;
import com.solum.xplain.xm.excmngmt.rulesonboarding.OnboardingBreakTestRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ValidOnboardingOverrideThresholdsValidator
    implements ConstraintValidator<ValidOnboardingOverrideThresholds, BaseBreakTestOverrideForm> {

  private OnboardingBreakTestRepository repository;
  private RequestPathVariablesSupport requestPathVariablesSupport;

  @Override
  public boolean isValid(BaseBreakTestOverrideForm form, ConstraintValidatorContext ctx) {
    if (form == null || isEmpty(form.getThreshold())) {
      return true;
    }
    return repository
        .getOne(requestPathVariablesSupport.getPathVariable("id"))
        .toOptional()
        .map(BaseBreakTestView::getOperator)
        .map(o -> validateThresholdsForOperator(o, form.getThreshold(), ctx))
        .orElse(true);
  }
}
