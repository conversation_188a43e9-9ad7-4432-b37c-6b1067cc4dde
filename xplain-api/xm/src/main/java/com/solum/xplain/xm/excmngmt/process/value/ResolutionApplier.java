package com.solum.xplain.xm.excmngmt.process.value;

import static com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType.SWITCH_TO_SECONDARY;

import com.solum.xplain.core.common.AuditContext;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.process.data.HasInstrumentResult;
import com.solum.xplain.xm.excmngmt.process.form.ApplyResolutionForm;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.lang.NonNull;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ResolutionApplier<T extends HasInstrumentResult> {

  private final List<T> instrumentToApply;
  private final EntryResultStatus statusToApply;
  private final ExceptionManagementEvidence evidence;

  public static <T extends HasInstrumentResult> ResolutionApplier<T> applyFor(
      @NonNull List<T> instrumentToApply,
      @NonNull EntryResultStatus statusToAply,
      ExceptionManagementEvidence evidence) {
    return new ResolutionApplier<>(instrumentToApply, statusToAply, evidence);
  }

  public List<ErrorItem> applyResolution(ApplyResolutionForm form, AuditContext audit) {
    return applyOverall(form, audit);
  }

  private List<ErrorItem> applyOverall(ApplyResolutionForm form, AuditContext audit) {
    var resolution = form.getOverallResolution();
    return apply(
        r -> r.applyResolution(resolution, audit, statusToApply, evidence),
        e ->
            e.isHasBreaks()
                && (e.canApplyResolution() || resolution.resolutionType() == SWITCH_TO_SECONDARY));
  }

  private List<ErrorItem> apply(Function<T, Optional<ErrorItem>> mapper, Predicate<T> filter) {
    return instrumentToApply.stream().filter(filter).map(mapper).flatMap(Optional::stream).toList();
  }
}
