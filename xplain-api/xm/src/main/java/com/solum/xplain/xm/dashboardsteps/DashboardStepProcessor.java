package com.solum.xplain.xm.dashboardsteps;

import static java.lang.String.format;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EitherUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntry;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class DashboardStepProcessor {

  private static final String STEP_STATUS_MESSAGE_TEMPLATE = "%s is %s";

  private final DashboardEntryRepository dashboardEntryRepository;
  private final AuditEntryService auditEntryService;

  public DashboardStepProcessor(
      DashboardEntryRepository dashboardEntryRepository, AuditEntryService auditEntryService) {
    this.dashboardEntryRepository = dashboardEntryRepository;
    this.auditEntryService = auditEntryService;
  }

  public BitemporalDate getStateDate() {
    return BitemporalDate.newOfNow();
  }

  public Either<List<ErrorItem>, List<DashboardEntryMdBatch>> createMdBatchSteps(
      List<DashboardEntryMdBatch> steps) {
    return processSteps(
        steps, step -> dashboardEntryRepository.createEntry(step).map(this::loggedStep));
  }

  public List<DashboardEntryMdBatch> getMdBatchSteps(Dashboard dashboard) {
    return dashboardEntryRepository.getMdBatchEntries(dashboard.getId());
  }

  public Either<List<ErrorItem>, List<DashboardEntryMdBatch>> updateMdBatchSteps(
      List<DashboardEntryMdBatch> steps) {
    return processSteps(
        steps, step -> dashboardEntryRepository.updateEntry(step).map(this::loggedStep));
  }

  public Either<List<ErrorItem>, EntityId> performMdBatchStep(
      BitemporalDate stateDate,
      DashboardEntryMdBatch step,
      Supplier<Either<ErrorItem, EntityId>> resultProvider) {
    return performStep(
        stateDate,
        step,
        dashboardEntryRepository::createEntry,
        resultProvider,
        this::successfulStep);
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> createMdSteps(
      List<DashboardEntryMd> steps) {
    return processSteps(
        steps, step -> dashboardEntryRepository.createEntry(step).map(this::loggedStep));
  }

  public List<DashboardEntryMd> getMdSteps(Dashboard dashboard) {
    return dashboardEntryRepository.getMdEntries(dashboard.getId());
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> updateMdSteps(
      List<DashboardEntryMd> steps) {
    return processSteps(
        steps, step -> dashboardEntryRepository.updateEntry(step).map(this::loggedStep));
  }

  public Either<List<ErrorItem>, EntityId> performMdStep(
      BitemporalDate stateDate,
      DashboardEntryMd step,
      Supplier<Either<ErrorItem, EntityId>> resultProvider) {
    return performStep(
        stateDate,
        step,
        dashboardEntryRepository::createEntry,
        resultProvider,
        this::successfulStep);
  }

  private <T extends DashboardEntry> Either<List<ErrorItem>, EntityId> performStep(
      BitemporalDate stateDate,
      T step,
      Function<T, Either<ErrorItem, T>> stepCreationFn,
      Supplier<Either<ErrorItem, EntityId>> resultProvider,
      BiFunction<T, EntityId, EntityId> successFn) {
    return stepCreationFn
        .apply(step.started(stateDate))
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            s ->
                resultProvider
                    .get()
                    .bimap(
                        error -> failedStep(step, error),
                        entityId -> successFn.apply(step, entityId)));
  }

  private <T extends DashboardEntry> EntityId successfulStep(T step, EntityId result) {
    var completedStep = step.completedWithResult(result.getId());
    loggedStep(completedStep, null);
    return dashboardEntryRepository.updateEntry(completedStep).fold(e -> result, s -> result);
  }

  private <T extends DashboardEntry> List<ErrorItem> failedStep(T step, ErrorItem error) {
    var completedStep = step.failed(error);
    loggedStep(completedStep, error);
    return dashboardEntryRepository
        .updateEntry(completedStep)
        .fold(e -> List.of(error, e), s -> List.of(error));
  }

  private <T extends DashboardEntry> T loggedStep(T dashboardEntry) {
    return loggedStep(dashboardEntry, dashboardEntry.getError());
  }

  private <T extends DashboardEntry> T loggedStep(T dashboardEntry, ErrorItem error) {
    var auditEntry =
        AuditEntry.of(
            Dashboard.COLLECTION_NAME,
            format(
                STEP_STATUS_MESSAGE_TEMPLATE,
                dashboardEntry.getStep().getLabel(),
                dashboardEntry.getStatus()),
            dashboardEntry.getDashboardId());
    if (error != null) {
      auditEntryService.newEntryWithLogs(auditEntry, List.of(error));
    } else {
      auditEntryService.newEntry(auditEntry);
    }
    return dashboardEntry;
  }

  private <T extends DashboardEntry> Either<List<ErrorItem>, List<T>> processSteps(
      List<T> steps, Function<T, Either<ErrorItem, T>> processFn) {
    return steps.stream()
        .map(processFn)
        .collect(Collectors.collectingAndThen(toList(), EitherUtils::allRightOrEmpty));
  }
}
