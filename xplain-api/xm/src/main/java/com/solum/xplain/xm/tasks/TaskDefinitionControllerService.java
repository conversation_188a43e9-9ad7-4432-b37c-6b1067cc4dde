package com.solum.xplain.xm.tasks;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.form.TasksDefinitionForm;
import com.solum.xplain.xm.tasks.repository.TaskDefinitionRepository;
import com.solum.xplain.xm.tasks.view.TasksDefinitionView;
import org.springframework.stereotype.Service;

@Service
public class TaskDefinitionControllerService {
  private final TaskDefinitionRepository taskDefinitionRepository;

  public TaskDefinitionControllerService(TaskDefinitionRepository taskDefinitionRepository) {
    this.taskDefinitionRepository = taskDefinitionRepository;
  }

  public TasksDefinitionView view(TaskExceptionManagementType type) {
    return taskDefinitionRepository.view(type);
  }

  public EntityId update(TasksDefinitionForm form) {
    return taskDefinitionRepository.save(form);
  }
}
