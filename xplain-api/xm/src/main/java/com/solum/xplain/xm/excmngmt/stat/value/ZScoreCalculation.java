package com.solum.xplain.xm.excmngmt.stat.value;

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;
import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.xm.excmngmt.rules.value.ProvidersType;
import com.solum.xplain.xm.excmngmt.stat.data.StatisticalZScoreData;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Pair;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.linear.MatrixUtils;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.stat.correlation.Covariance;
import org.slf4j.Logger;

@AllArgsConstructor
public class ZScoreCalculation {

  private static final Logger LOG = getLogger(ZScoreCalculation.class);

  private final DiffCalculator primaryDiffProvider;
  private final DiffCalculator secondaryDiffProvider;
  private final List<InstrumentDefinition> instruments;
  private final String marketDataGroupId;
  private final String legalEntityId;
  private final String companyId;
  private final String curveConfigId;
  private final String curve;
  private final ProvidersType providersType;
  private final ValueBidAskType bidAskType;

  private static Pair<Double, RealMatrix> condZAttributes(RealMatrix covarianceMatrix, int idx) {
    var sum11 = covarianceMatrix.getColumn(idx)[idx];
    var sum1N = calcSum1N(covarianceMatrix, idx);
    var sumN1 = calcSumN1(covarianceMatrix, idx);
    var sumNN = calcSumTNN(covarianceMatrix, idx);

    var res = sum1N.multiply(MatrixUtils.inverse(sumNN)).multiply(sumN1);
    var variance = sum11 - res.getColumn(0)[0];
    var conditionalStd = Math.sqrt(variance);

    return Pair.pair(conditionalStd, sum1N.multiply(MatrixUtils.inverse(sumNN)));
  }

  public ZScoreCalculationResult zScores() {
    return zScoresStandard().combined(zScoresCond());
  }

  ZScoreCalculationResult zScoresStandard() {
    var results = new ZScoreCalculationResult();

    BiConsumer<Map.Entry<String, Either<List<ErrorItem>, Map<LocalDate, Double>>>, Boolean>
        zScoreApplier =
            (e, primary) -> {
              if (e.getValue().isLeft()) {
                results.withError(e.getValue().left().get());
                return;
              }

              var res = new StatisticalZScoreData();
              res.setMarketDataGroupId(marketDataGroupId);
              res.setCurveConfigurationId(curveConfigId);
              res.setCompanyId(companyId);
              res.setLegalEntityId(legalEntityId);
              res.setCurve(curve);
              res.setConditional(false);
              res.setInstrumentKey(e.getKey());
              res.setBidAskType(bidAskType);
              res.setPrimary(primary);
              var data = e.getValue().getOrNull();
              if (data.size() <= 1) {
                results.withError(
                    CALCULATION_ERROR.entity(
                        "Unable to calculate SD, requires at "
                            + "least 2 data "
                            + "points. Key "
                            + e.getKey()
                            + ", primary = "
                            + primary));
                return;
              }

              var mean =
                  data.values().stream().mapToDouble(Double::doubleValue).average().orElse(0d);
              var sum = 0d;
              for (Double num : data.values()) {
                sum += Math.pow(num - mean, 2);
              }

              res.setStdDev(Math.sqrt(sum / (data.size() - 1)));
              res.setMean(mean);
              results.withResult(res);
            };

    for (var e : primaryDiffProvider.validatedData().entrySet()) {
      zScoreApplier.accept(e, true);
    }

    if (providersType != ProvidersType.PRIMARY) {
      for (var e : secondaryDiffProvider.validatedData().entrySet()) {
        zScoreApplier.accept(e, false);
      }
    }

    return results;
  }

  private static Array2DRowRealMatrix calcSum1N(RealMatrix covarianceMatrix, int idx) {
    var dimension = covarianceMatrix.getColumnDimension();
    var t1N = new double[1][dimension - 1];
    var idxRow = covarianceMatrix.getRow(idx);
    for (var n = 0; n < idxRow.length; n++) {
      if (n == idx) {
        continue;
      }
      var index = normalizeIndex(n, idx);
      t1N[0][index] = idxRow[n];
    }
    return new Array2DRowRealMatrix(t1N);
  }

  private static Array2DRowRealMatrix calcSumN1(RealMatrix covarianceMatrix, int idx) {
    var dimension = covarianceMatrix.getColumnDimension();
    var tN1 = new double[dimension - 1][1];
    var idxCol = covarianceMatrix.getColumn(idx);
    for (var n = 0; n < idxCol.length; n++) {
      if (n == idx) {
        continue;
      }
      var index = normalizeIndex(n, idx);
      tN1[index][0] = idxCol[n];
    }
    return new Array2DRowRealMatrix(tN1);
  }

  private static Array2DRowRealMatrix calcSumTNN(RealMatrix covarianceMatrix, int idx) {
    var dimension = covarianceMatrix.getColumnDimension();
    var tNN = new double[dimension - 1][dimension - 1];
    var idxCol = covarianceMatrix.getColumn(idx);
    var idxRow = covarianceMatrix.getRow(idx);
    for (var i = 0; i < idxRow.length; i++) {
      if (i == idx) {
        continue;
      }
      for (var j = 0; j < idxCol.length; j++) {
        if (j == idx) {
          continue;
        }
        var rowIndex = normalizeIndex(i, idx);
        var columnIndex = normalizeIndex(j, idx);
        tNN[rowIndex][columnIndex] = covarianceMatrix.getRow(i)[j];
      }
    }
    return new Array2DRowRealMatrix(tNN);
  }

  private static Integer normalizeIndex(int index, int maxIndex) {
    if (index > maxIndex) {
      return index - 1;
    }
    return index;
  }

  ZScoreCalculationResult zScoresCond() {
    if (curve == null) {
      return new ZScoreCalculationResult();
    }

    Function<Map<String, Either<List<ErrorItem>, Map<LocalDate, Double>>>, List<ErrorItem>>
        extractErrorsF =
            m ->
                m.values().stream()
                    .filter(Either::isLeft)
                    .map(listMapEither -> listMapEither.left().get())
                    .flatMap(List::stream)
                    .toList();

    var sortedKeys =
        instruments.stream()
            .sorted(Comparator.comparing(InstrumentDefinition::parsedTenor))
            .map(InstrumentDefinition::getKey)
            .toList();

    var results = new ZScoreCalculationResult();
    if (sortedKeys.size() < 2) {
      LOG.warn("Unable to calculate cond Z {}. Less than 2 nodes", curve);
      results.withError(
          List.of(
              CALCULATION_ERROR.entity(
                  "Unable to calculate conditional for "
                      + curve
                      + ". At"
                      + " least 2 nodes "
                      + "required")));
      return results;
    }

    BiConsumer<Map<String, Either<List<ErrorItem>, Map<LocalDate, Double>>>, Boolean> process =
        (diffs, primary) -> {
          var allData =
              diffs.entrySet().stream()
                  .collect(toMap(Map.Entry::getKey, e -> e.getValue().getOrNull()));

          var means =
              sortedKeys.stream()
                  .map(allData::get)
                  .map(
                      v ->
                          ofNullable(v).map(Map::values).stream()
                              .flatMap(Collection::stream)
                              .mapToDouble(Double::doubleValue)
                              .average())
                  .map(v -> v.orElse(0d))
                  .toList();

          for (var e : allData.keySet()) {
            var res = new StatisticalZScoreData();
            res.setMarketDataGroupId(marketDataGroupId);
            res.setCurveConfigurationId(curveConfigId);
            res.setCurve(curve);
            res.setConditional(true);
            res.setInstrumentKey(e);
            res.setBidAskType(bidAskType);
            res.setPrimary(primary);

            var idx = sortedKeys.indexOf(e);
            var meanExcludeSelf =
                IntStream.range(0, means.size())
                    .filter(n -> n != idx)
                    .mapToObj(means::get)
                    .mapToDouble(v -> v)
                    .toArray();
            res.setMeanVector(new Array2DRowRealMatrix(meanExcludeSelf));
            res.setMean(means.get(idx));

            try {
              var covarianceMatrix = covarianceMatrix(sortedKeys, allData);
              var condZAttributes = condZAttributes(covarianceMatrix, idx);
              res.setCovariance(condZAttributes.right());
              res.setCondVariation(condZAttributes.left());
              results.withResult(res);
            } catch (Exception err) {
              results.withError(
                  List.of(
                      CALCULATION_ERROR.entity(
                          "Unable to calculate conditional Z matrices: "
                              + err.getMessage()
                              + ". Key "
                              + e
                              + ", primary = "
                              + primary)));
            }
          }
        };

    var primaryDiffs = primaryDiffProvider.validatedData();
    var primaryErrors = extractErrorsF.apply(primaryDiffs);
    if (!primaryErrors.isEmpty()) {
      results.withError(primaryErrors);
      LOG.warn("Unable to calculate cond Z for {} primary. Errors: {}", curve, results.getErrors());
    } else {
      process.accept(primaryDiffs, true);
    }

    if (providersType != ProvidersType.PRIMARY) {
      var secondaryDiffs = secondaryDiffProvider.validatedData(primaryDiffProvider);
      var secondaryErrors = extractErrorsF.apply(secondaryDiffs);
      if (!secondaryErrors.isEmpty()) {
        results.withError(secondaryErrors);
        LOG.warn(
            "Unable to calculate cond Z for {} secondary. Errors: {}", curve, results.getErrors());
        return results;
      }
      process.accept(secondaryDiffs, false);
    }

    return results;
  }

  private RealMatrix covarianceMatrix(
      List<String> sortedKeys, Map<String, Map<LocalDate, Double>> data) {
    var dataCleaned = new double[primaryDiffProvider.getAvailableDates().size()][sortedKeys.size()];
    var i = 0;
    for (var k : sortedKeys) {
      var v = data.get(k);

      var j = 0;
      for (var d : primaryDiffProvider.getAvailableDates()) {
        dataCleaned[j][i] = ofNullable(v.get(d)).orElse(Double.NaN);
        j++;
      }
      i++;
    }

    return new Covariance(new Array2DRowRealMatrix(dataCleaned), false).getCovarianceMatrix();
  }
}
