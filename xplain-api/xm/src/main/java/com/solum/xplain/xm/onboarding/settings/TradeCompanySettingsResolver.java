package com.solum.xplain.xm.onboarding.settings;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.IpvSettingsRuleService;
import com.solum.xplain.core.company.mapper.IpvRuleFacts;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.rules.RulesService;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;

@EqualsAndHashCode
@ToString
public class TradeCompanySettingsResolver {

  private final Rules ipvDataGroupRules;
  private final Rules ipvProvidersRules;
  private final RulesService<Rule, Rules> rulesService;

  public TradeCompanySettingsResolver(
      BitemporalDate stateDate,
      IpvSettingsRuleService ipvSettingsRuleService,
      RulesService<Rule, Rules> rulesService) {
    this.rulesService = rulesService;
    this.ipvDataGroupRules = ipvSettingsRuleService.getAllIpvDataGroupRules(stateDate);
    this.ipvProvidersRules = ipvSettingsRuleService.getAllIpvProvidersRules(stateDate);
  }

  public Either<ErrorItem, TradeCompanySettings> tradeSettings(Trade trade) {
    return Steps.begin(ipvDataGroupForTrade(trade, ipvDataGroupRules))
        .then(__ -> providersForTrade(trade, ipvProvidersRules))
        .yield(
            (ipvDataGroupVo, providersVo) ->
                Either.<ErrorItem, TradeCompanySettings>right(
                    new TradeCompanySettings(
                        ipvDataGroupVo.entityId(),
                        providersVo.primary(),
                        providersVo.secondary(),
                        providersVo.tertiary(),
                        providersVo.quaternary())))
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Unable to resolve valuation settings")));
  }

  /**
   * Use the rules to find the valuation data group for a trade.
   *
   * @param trade the trade to find the VDG for
   * @param rules the rules to execute to determine it
   * @return the reference to the valuation data group
   */
  private Optional<IpvDataGroupVo> ipvDataGroupForTrade(Trade trade, Rules rules) {
    return rulesService.execute(rules, new IpvRuleFacts(trade), IpvDataGroupVo.class);
  }

  /**
   * Use the rules to find the providers for a trade.
   *
   * @param trade the trade to find the providers for
   * @param rules the rules to execute to determine it
   * @return the valuation data providers for the trade
   */
  private Optional<ProvidersVo> providersForTrade(Trade trade, Rules rules) {
    return rulesService.execute(rules, new IpvRuleFacts(trade), ProvidersVo.class);
  }
}
