package com.solum.xplain.xm.workflow.steps.md;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VerifyPreliminaryResolutionStep
    implements ServiceStepExecutor<MdPreliminaryState, MdPreliminaryContext> {
  @Override
  public void runStep(StepStateOps<MdPreliminaryState, MdPreliminaryContext> ops) {
    AttributedValue pendingValue = ops.getInitialState().getValuePendingApproval();
    ops.setOutcome(
        new MutablePropertyValues()
            .add(MdPreliminaryState.Fields.valuePendingApproval, null)
            .add(MdPreliminaryState.Fields.baseValue, pendingValue.value())
            .add(MdPreliminaryState.Fields.providerName, pendingValue.providerName()));
    // Don't need to set VERIFIED as that is done in SavePreliminaryStep anyway
  }
}
