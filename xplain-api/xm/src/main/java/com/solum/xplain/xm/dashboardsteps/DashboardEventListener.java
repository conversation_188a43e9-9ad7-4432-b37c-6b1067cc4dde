package com.solum.xplain.xm.dashboardsteps;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntry;
import com.solum.xplain.xm.dashboards.enums.DashboardType;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public abstract class DashboardEventListener {

  protected final DashboardRepository dashboardRepository;
  protected final DashboardEntryRepository entryRepository;

  protected boolean dashboardCompleted(Dashboard dashboard) {
    boolean completed = allMdStepsFinished(dashboard) && alBatchEntriesFinished(dashboard);
    log.trace("Checking if dashboard {} is completed: {}", dashboard.getId(), completed);
    return completed;
  }

  protected boolean allMdStepsFinished(Dashboard dashboard) {
    if (dashboard.getType() != DashboardType.MARKET_DATA) {
      return true;
    }
    var entries = entryRepository.getMdEntries(dashboard.getId());
    return allStepsCompleted(entries);
  }

  protected boolean alBatchEntriesFinished(Dashboard dashboard) {
    if (dashboard.getType() != DashboardType.MARKET_DATA_BATCH) {
      return true;
    }
    var entries = entryRepository.getMdBatchEntries(dashboard.getId());
    return allStepsCompleted(entries);
  }

  protected <T extends DashboardEntry> boolean allStepsCompleted(List<T> steps) {
    return steps.stream()
        .allMatch(
            e -> e.getStatus() == StepStatus.COMPLETED || e.getStatus() == StepStatus.NOT_REQUIRED);
  }
}
