package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing;

import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository;
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult;
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Component;

@Component
public class MdBatchPreliminaryClearingStepsFinalizer {

  private final DashboardStepProcessor processor;
  private final ExceptionManagementCalculationRepository exceptionManagementRepository;

  public MdBatchPreliminaryClearingStepsFinalizer(
      DashboardStepProcessor processor,
      ExceptionManagementCalculationRepository exceptionManagementRepository) {
    this.processor = processor;
    this.exceptionManagementRepository = exceptionManagementRepository;
  }

  public Either<List<ErrorItem>, DashboardEntryMdBatch> execute(Dashboard dashboard) {
    return exceptionManagementRepository
        .entitiesByDashboard(dashboard.getId())
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(this::isXmCompleted)
        .flatMap(isCompleted -> processStep(dashboard, isCompleted));
  }

  private boolean isXmCompleted(List<ExceptionManagementResult> results) {
    return results.stream()
        .allMatch(
            r -> Objects.equals(r.getStatus(), CalculationTestStatus.PRELIMINARY_BATCH_APPROVED));
  }

  private Either<List<ErrorItem>, DashboardEntryMdBatch> processStep(
      Dashboard dashboard, boolean isCompleted) {
    var steps = clearingSteps(dashboard);
    if (isCompleted) {
      return completeStep(steps);
    } else {
      return steps.collect(
          Collectors.collectingAndThen(toList(), ss -> Either.right(IterableUtils.first(ss))));
    }
  }

  private Stream<DashboardEntryMdBatch> clearingSteps(Dashboard dashboard) {
    return processor.getMdBatchSteps(dashboard).stream()
        .filter(e -> e.getStep() == DashboardStep.MD_BATCH_PRELIMINARY_CLEARING);
  }

  private Either<List<ErrorItem>, DashboardEntryMdBatch> completeStep(
      Stream<DashboardEntryMdBatch> steps) {
    var clearingSteps =
        steps
            .filter(e -> e.getStatus() == StepStatus.IN_PROGRESS)
            .findFirst()
            .map(DashboardEntryMdBatch::completed)
            .map(DashboardEntryMdBatch.class::cast)
            .map(List::of)
            .orElse(List.of());
    return processor.updateMdBatchSteps(clearingSteps).map(IterableUtils::first);
  }
}
