package com.solum.xplain.xm.dashboards.repository;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.xm.workflow.XmWorkflowService.businessKeyFromDashboardId;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.replaceRoot;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.mongodb.client.result.UpdateResult;
import com.solum.xplain.core.calculationapi.CalculationResultProvider;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.daterange.DateRange;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReference;
import com.solum.xplain.core.sockets.constants.CoreSocketEvents;
import com.solum.xplain.core.sockets.events.EventType;
import com.solum.xplain.core.sockets.events.SocketEvent;
import com.solum.xplain.core.utils.PathUtils;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.dashboards.DashboardEntryService;
import com.solum.xplain.xm.dashboards.DashboardMapper;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntry;
import com.solum.xplain.xm.dashboards.entity.IpvDataGroupProducts;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.PortfolioDashboardSettings;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.VdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.DashboardType;
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent;
import com.solum.xplain.xm.dashboards.forms.PricingSlotPortfolioForm;
import com.solum.xplain.xm.dashboards.sockets.type.DashboardSocketEvent;
import com.solum.xplain.xm.dashboards.views.DashboardDateView;
import com.solum.xplain.xm.dashboards.views.DashboardEntryMdBatchView;
import com.solum.xplain.xm.dashboards.views.DashboardEntryMdView;
import com.solum.xplain.xm.dashboards.views.DashboardEntryVdView;
import com.solum.xplain.xm.dashboards.views.DashboardEntryView;
import com.solum.xplain.xm.dashboards.views.DashboardListView;
import com.solum.xplain.xm.dashboards.views.DashboardPortfolio;
import com.solum.xplain.xm.dashboards.views.DashboardPortfolioListView;
import com.solum.xplain.xm.dashboards.views.DashboardView;
import com.solum.xplain.xm.dashboards.views.PortfolioResultCountView;
import com.solum.xplain.xm.excmngmt.process.InstrumentReferenceResolver;
import com.solum.xplain.xm.tasks.view.TaskCountsGroupView;
import com.solum.xplain.xm.tasks.view.TaskCountsView;
import com.solum.xplain.xm.tasks.view.UniqueTaskCountKey;
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
@Slf4j
public class DashboardRepository {

  private static final String DASHBOARD_NOT_FOUND =
      "Dashboard no longer exists. Please refresh your view and try again.";

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final DashboardMapper mapper;
  private final DashboardEntryRepository entryRepository;
  private final DashboardEntryService entryService;
  private final XmStepInstanceQueryRepository xmStepInstanceQueryRepository;
  private final CalculationResultProvider calculationResultProvider;
  private final ApplicationEventPublisher publisher;

  private static Criteria dateCriteria(LocalDate startDate, LocalDate endDate) {
    return Criteria.where(joinPaths(Dashboard.Fields.dateRange, DateRange.Fields.startDate))
        .lte(startDate)
        .and(joinPaths(Dashboard.Fields.dateRange, DateRange.Fields.endDate))
        .gte(endDate);
  }

  private static Criteria typeCriteria(DashboardType... types) {
    return where(Dashboard.Fields.type).in((Object[]) types);
  }

  public Either<ErrorItem, EntityId> createDashboard(Dashboard dashboard) {
    var d = mongoOperations.save(dashboard);
    publisher.publishEvent(globalEvent(d, EventType.DASHBOARD_CREATED));
    return Either.right(d.entityId());
  }

  public Either<ErrorItem, Dashboard> dashboard(String dashboardId) {
    var result = mongoOperations.findById(dashboardId, Dashboard.class);
    if (result == null) {
      // WARNING: If you're here in a test, you may need to save a Dashboard entity - otherwise this
      // deletes your workflow data
      log.warn(
          "Attempting to retrieve missing dashboard {} - firing event to clean up data",
          dashboardId);
      publisher.publishEvent(DashboardDeletedEvent.newOf(dashboardId));
      return Either.left(OBJECT_NOT_FOUND.entity(DASHBOARD_NOT_FOUND));
    }
    return Either.right(result);
  }

  public ScrollableEntry<DashboardListView> getDashboards(
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      @Nullable LocalDateTime fromDate,
      LocalDate stateDate) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(addMarketDataGroupField())
            .add(match(tableFilter.criteria(Dashboard.class, conversionService)));

    if (fromDate != null) {
      operations.add(
          match(
              Criteria.where(joinPaths(Dashboard.Fields.dateRange, DateRange.Fields.startDate))
                  .gte(fromDate)
                  .lte(stateDate) // Add upper boundary to limit to state date
              ));
    }

    operations.addAll(
        new ScrollSortOperations(scrollRequest, DashboardListView.Fields.id)
            .withDefaultSort(Sort.by(Direction.DESC, Dashboard.Fields.startedAt))
            .build());

    var items =
        mongoOperations
            .aggregate(newAggregation(Dashboard.class, operations.build()), Dashboard.class)
            .getMappedResults();
    return ScrollableEntry.of(items, scrollRequest).map(mapper::toDeletableListView);
  }

  public Optional<Dashboard> getSingleDayMDDashboard(String marketDataGroupId, LocalDate date) {
    return mongoOperations
        .query(Dashboard.class)
        .matching(
            dashboardByMarketDataCriteria(marketDataGroupId).andOperator(dateCriteria(date, date)))
        .first();
  }

  public Optional<DashboardDateView> previousPortfolioDashboardId(
      String portfolioId, LocalDate valuationDate) {
    var settingsPath = ipvValuationSettingsPath();

    var portfolioCriteria =
        where(
                joinPaths(
                    settingsPath,
                    PortfolioDashboardSettings.Fields.portfolio,
                    EntityReference.Fields.entityId))
            .is(portfolioId);

    var operations =
        List.of(
            match(
                where(PathUtils.joinPaths(Dashboard.Fields.dateRange, DateRange.Fields.startDate))
                    .lt(valuationDate)),
            match(where(Dashboard.Fields.finishedAt).ne(null)),
            unwind(settingsPath),
            unwind(settingsPath, PortfolioDashboardSettings.Fields.ipvDataGroupProducts),
            match(portfolioCriteria),
            sort(Direction.DESC, Dashboard.Fields.id),
            limit(1));

    var result =
        mongoOperations
            .aggregateAndReturn(DashboardDateView.class)
            .by(newAggregation(Dashboard.class, operations))
            .all()
            .getUniqueMappedResult();

    return Optional.ofNullable(result);
  }

  public Either<ErrorItem, DashboardView> getDashboard(
      String dashboardId, Supplier<List<TaskCountsGroupView>> taskCountGroupsSupplier) {
    return dashboard(dashboardId).map(d -> dashboardWithSteps(d, taskCountGroupsSupplier.get()));
  }

  public Map<String, InstrumentReferenceResolver> dashboardInstrumentReferenceResolvers(
      Set<String> dashboarIds) {
    return mongoOperations
        .query(Dashboard.class)
        .matching(where(Dashboard.Fields.id).in(dashboarIds))
        .stream()
        .map(InstrumentReferenceResolver::newOf)
        .collect(
            Collectors.toMap(InstrumentReferenceResolver::getDashboardId, Function.identity()));
  }

  public DashboardView dashboardWithSteps(
      Dashboard dashboard, List<TaskCountsGroupView> taskCountGroups) {
    final var taskCounts = taskCounts(taskCountGroups);
    return mapper.toView(
        dashboard,
        mdBatchEntryViews(dashboard, taskCounts),
        mdEntryViews(dashboard, taskCounts),
        vdEntryViews(dashboard, taskCounts));
  }

  private Map<UniqueTaskCountKey, TaskCountsView> taskCounts(
      List<TaskCountsGroupView> taskCountGroups) {
    return taskCountGroups.stream()
        .collect(toMap(TaskCountsGroupView::getId, TaskCountsGroupView::getCounts));
  }

  @NonNull
  private List<DashboardEntryMdBatchView> mdBatchEntryViews(
      Dashboard dashboard, Map<UniqueTaskCountKey, TaskCountsView> taskCounts) {
    var type = dashboard.getType();
    if (DashboardType.MARKET_DATA_BATCH != type && DashboardType.TRS_MARKET_DATA_BATCH != type) {
      return List.of();
    }
    var stepViews =
        entryRepository.getMdBatchEntries(dashboard.getId()).stream()
            .map(e -> mapper.toView(e, taskCountView(e, taskCounts)))
            .collect(Collectors.groupingBy(DashboardEntryMdBatchView::getStep));
    return entryViews(
        stepViews,
        DashboardEntryMdBatchView.PLACEHOLDER_VIEWS,
        DashboardEntryMdBatchView.defaultComparator());
  }

  @NonNull
  private List<DashboardEntryMdView> mdEntryViews(
      Dashboard dashboard, Map<UniqueTaskCountKey, TaskCountsView> taskCounts) {
    var type = dashboard.getType();
    if (DashboardType.MARKET_DATA != type && DashboardType.TRS_MARKET_DATA != type) {
      return List.of();
    }
    List<DashboardEntryMdView> steps =
        xmStepInstanceQueryRepository.getMdDashboardTimelineViews(
            businessKeyFromDashboardId(dashboard.getId()).toString());
    Map<DashboardStep, List<DashboardEntryMdView>> stepViews;
    List<DashboardEntryMdView> placeholderViews = Collections.emptyList();
    if (steps.isEmpty()) {
      // TODO: SXSD-9015: legacy market data dashboard - replace with similar to vdEntryViews.
      stepViews =
          entryRepository.getMdEntries(dashboard.getId()).stream()
              .map(e -> mapper.toView(e, taskCountView(e, taskCounts)))
              .collect(Collectors.groupingBy(DashboardEntryMdView::getStep));
      if (dashboard.getFinishedAt() == null) {
        placeholderViews = DashboardEntryMdView.PLACEHOLDER_VIEWS;
      }
    } else {
      stepViews = steps.stream().collect(Collectors.groupingBy(DashboardEntryMdView::getStep));
      // Only use placeholder entry views if the dashboard isn't finished.
      if (dashboard.getFinishedAt() == null) {
        placeholderViews = entryService.getWorkflowMdPlaceholderViews();
      }
    }
    return entryViews(
        stepViews,
        placeholderViews,
        DashboardEntryMdView.defaultComparator(),
        DashboardEntryMdView.defaultPostProcessor());
  }

  @NonNull
  private List<DashboardEntryVdView> vdEntryViews(
      Dashboard dashboard, Map<UniqueTaskCountKey, TaskCountsView> taskCounts) {
    var type = dashboard.getType();
    if (DashboardType.VALUATION_DATA != type) {
      return List.of();
    }
    List<DashboardEntryVdView> steps =
        xmStepInstanceQueryRepository.getVdDashboardTimelineViews(
            businessKeyFromDashboardId(dashboard.getId()).toString());
    Map<DashboardStep, List<DashboardEntryVdView>> stepViews;
    List<DashboardEntryVdView> placeholderViews = Collections.emptyList();
    if (steps.isEmpty()) {
      if (dashboard.getStartedAt() == null) {
        // No steps because it's not started yet, return the workflow placeholders.
        placeholderViews = entryService.getWorkflowVdPlaceholderViews();
      } // else no steps because it's a legacy dashboard, return nothing.
      stepViews = Collections.emptyMap();
    } else {
      stepViews = steps.stream().collect(Collectors.groupingBy(DashboardEntryVdView::getStep));
      // Only use placeholder entry views if the dashboard isn't finished.
      if (dashboard.getFinishedAt() == null) {
        placeholderViews = entryService.getWorkflowVdPlaceholderViews();
      }
    }

    return entryViews(
        stepViews,
        placeholderViews,
        DashboardEntryVdView.defaultComparator(),
        DashboardEntryVdView.defaultPostProcessor());
  }

  private <T extends DashboardEntry> TaskCountsView taskCountView(
      T entry, Map<UniqueTaskCountKey, TaskCountsView> taskCounts) {
    return entry.taskCountKey().map(taskCounts::get).orElse(null);
  }

  private <T extends DashboardEntryView> List<T> entryViews(
      Map<DashboardStep, List<T>> viewsByStep,
      List<T> placeholderViews,
      Comparator<T> comparator,
      @NonNull BiFunction<List<T>, T, T> postProcessor) {
    List<T> result = entryViews(viewsByStep, placeholderViews, comparator);
    return result.stream().map(v -> postProcessor.apply(result, v)).collect(Collectors.toList());
  }

  private <T extends DashboardEntryView> List<T> entryViews(
      Map<DashboardStep, List<T>> viewsByStep, List<T> placeholderViews, Comparator<T> comparator) {
    return Stream.concat(
            viewsByStep.values().stream().flatMap(Collection::stream),
            placeholderViews.stream().filter(v -> !viewsByStep.containsKey(v.getStep())))
        .sorted(comparator)
        .toList();
  }

  public Either<ErrorItem, ScrollableEntry<DashboardPortfolioListView>> getDashboardPortfolios(
      String dashboardId,
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      List<PortfolioResultCountView> entriesCounters) {
    return dashboard(dashboardId)
        .map(d -> dashboardPortfolios(d.getId(), tableFilter, scrollRequest, entriesCounters));
  }

  private ScrollableEntry<DashboardPortfolioListView> dashboardPortfolios(
      String dashboardId,
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      List<PortfolioResultCountView> entriesCounters) {
    var operations = dashboardPortfoliosOperation(dashboardId, tableFilter);
    operations.addAll(
        new ScrollSortOperations(
                scrollRequest,
                joinPaths(DashboardPortfolio.Fields.portfolio, EntityReference.Fields.entityId))
            .build());

    var items = dashboardPortfoliosList(operations.build());
    var resultIdByPortfolio =
        calculationResultProvider.latestDashboardCalculationsByPortfolioId(dashboardId);
    var countersMap =
        entriesCounters.stream()
            .collect(Collectors.toMap(PortfolioResultCountView::portfolioId, Function.identity()));
    return ScrollableEntry.of(items, scrollRequest)
        .map(
            e -> {
              var portfolioId = e.portfolio().getEntityId();
              var resultId = resultIdByPortfolio.get(portfolioId);
              return mapper.toPortfolioListView(
                  e,
                  resultId == null ? null : resultId.toHexString(),
                  countersMap.get(portfolioId));
            });
  }

  public List<String> getCalculationResultIds(String dashboardId, TableFilter tableFilter) {
    var idsByPortfolioId =
        calculationResultProvider.latestDashboardCalculationsByPortfolioId(dashboardId);
    var operations = dashboardPortfoliosOperation(dashboardId, tableFilter).build();

    return dashboardPortfoliosList(operations).stream()
        .map(p -> p.portfolio().getEntityId())
        .filter(idsByPortfolioId::containsKey)
        .map(idsByPortfolioId::get)
        .map(ObjectId::toHexString)
        .toList();
  }

  public List<DashboardPortfolio> getDashboardPortfolios(
      String dashboardId, TableFilter tableFilter) {
    var operations = dashboardPortfoliosOperation(dashboardId, tableFilter).build();
    return dashboardPortfoliosList(operations);
  }

  private List<DashboardPortfolio> dashboardPortfoliosList(
      ImmutableList<AggregationOperation> operations) {
    return mongoOperations
        .aggregate(newAggregation(Dashboard.class, operations), DashboardPortfolio.class)
        .getMappedResults();
  }

  private ImmutableList.Builder<AggregationOperation> dashboardPortfoliosOperation(
      String dashboardId, TableFilter tableFilter) {
    var settingsPath = ipvValuationSettingsPath();
    return ImmutableList.<AggregationOperation>builder()
        .add(match(where(Dashboard.Fields.id).is(dashboardId)))
        .add(unwind(settingsPath))
        .add(replaceRoot(settingsPath))
        .add(
            project(
                    PortfolioDashboardSettings.Fields.company,
                    PortfolioDashboardSettings.Fields.entity,
                    PortfolioDashboardSettings.Fields.portfolio)
                .and(
                    joinPaths(
                        PortfolioDashboardSettings.Fields.ipvDataGroupProducts,
                        IpvDataGroupProducts.Fields.ipvDataGroup))
                .as(DashboardPortfolioListView.Fields.ipvDataGroups))
        .add(match(tableFilter.criteria(DashboardPortfolio.class, conversionService)));
  }

  private String ipvValuationSettingsPath() {
    return joinPaths(
        Dashboard.Fields.vdExceptionManagementSetup,
        VdExceptionManagementSetup.Fields.ipvValuationSettings);
  }

  public void markFinished(String dashboardId) {
    updateStatus(dashboardId, Dashboard.Fields.finishedAt, false);
  }

  public void markInProgress(String dashboardId) {
    updateStatus(dashboardId, Dashboard.Fields.startedAt, true);
  }

  private void updateStatus(String dashboardId, String timeStampField, boolean inProgress) {
    Update update = new Update().set(timeStampField, LocalDateTime.now());

    if (inProgress) {
      update.set(Dashboard.Fields.finishedAt, null);
    }

    UpdateResult result =
        mongoOperations
            .update(Dashboard.class)
            .matching(where(Dashboard.Fields.id).is(dashboardId))
            .apply(update)
            .first();
    log.trace(
        "Updating dashboard {} {} to now: {} records updated",
        dashboardId,
        timeStampField,
        result.getModifiedCount());
    publisher.publishEvent(
        DashboardSocketEvent.dashboardEvent(dashboardId, EventType.DASHBOARD_UPDATED));
  }

  public boolean existsByMarketDataGroup(
      @NonNull LocalDate dateRangeStart,
      @NonNull LocalDate dateRangeEnd,
      String marketDataGroupId,
      String trsMarketDataGroupId) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(dateCriteria(dateRangeStart, dateRangeEnd)))
            .add(match(uniqueDashboardCriteria(marketDataGroupId, trsMarketDataGroupId)))
            .build();
    return dashboardExists(operations);
  }

  private Criteria uniqueDashboardCriteria(String marketDataGroupId, String trsMarketDataGroupId) {
    var marketDataIdPath =
        joinPaths(
            Dashboard.Fields.mdExceptionManagementSetup,
            MdExceptionManagementSetup.Fields.marketDataGroup,
            EntityReference.Fields.entityId);
    var trsMarketDataIdPath =
        joinPaths(
            Dashboard.Fields.trsMdExceptionManagementSetup,
            TrsMdExceptionManagementSetup.Fields.marketDataGroup,
            EntityReference.Fields.entityId);

    var criteriaBuilder = ImmutableList.<Criteria>builder();
    if (marketDataGroupId != null) {
      criteriaBuilder.add(where(marketDataIdPath).is(marketDataGroupId));
    }
    if (trsMarketDataGroupId != null) {
      criteriaBuilder.add(where(trsMarketDataIdPath).is(trsMarketDataGroupId));
    }

    return typeCriteria(
            DashboardType.MARKET_DATA,
            DashboardType.TRS_MARKET_DATA,
            DashboardType.MARKET_DATA_BATCH,
            DashboardType.TRS_MARKET_DATA_BATCH)
        .orOperator(criteriaBuilder.build().toArray(new Criteria[0]));
  }

  private Criteria dashboardByMarketDataCriteria(String marketDataGroupId) {
    var marketDataIdPath =
        joinPaths(
            Dashboard.Fields.mdExceptionManagementSetup,
            MdExceptionManagementSetup.Fields.marketDataGroup,
            EntityReference.Fields.entityId);
    return typeCriteria(DashboardType.MARKET_DATA, DashboardType.MARKET_DATA_BATCH)
        .and(marketDataIdPath)
        .is(marketDataGroupId);
  }

  public boolean existsByPortfolios(
      @NonNull LocalDate dateRangeStart,
      @NonNull LocalDate dateRangeEnd,
      @NonNull List<PricingSlotPortfolioForm> pricingSlotPortfolio) {
    if (pricingSlotPortfolio.isEmpty()) {
      return false;
    }
    var portfoliosCriteria =
        pricingSlotPortfolio.stream()
            .map(
                p ->
                    where(
                            joinPaths(
                                PortfolioDashboardSettings.Fields.portfolio,
                                EntityReference.Fields.entityId))
                        .is(p.portfolioId())
                        .and(
                            joinPaths(
                                PortfolioDashboardSettings.Fields.ipvDataGroupProducts,
                                IpvDataGroupProducts.Fields.ipvDataGroup,
                                IpvDataGroupReference.Fields.pricingSlot))
                        .is(p.pricingSlot()))
            .toList();

    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    dateCriteria(dateRangeStart, dateRangeEnd)
                        .andOperator(typeCriteria(DashboardType.VALUATION_DATA))),
                replaceRoot(Dashboard.Fields.vdExceptionManagementSetup),
                unwind(VdExceptionManagementSetup.Fields.ipvValuationSettings),
                replaceRoot(VdExceptionManagementSetup.Fields.ipvValuationSettings),
                unwind(PortfolioDashboardSettings.Fields.ipvDataGroupProducts),
                match(new Criteria().orOperator(portfoliosCriteria)))
            .build();
    return dashboardExists(operations);
  }

  public Either<ErrorItem, EntityId> deleteDashboard(String dashboardId) {
    return dashboard(dashboardId)
        .map(
            d -> {
              publisher.publishEvent(DashboardDeletedEvent.newOf(d.getId()));
              mongoOperations.remove(d);
              publisher.publishEvent(globalEvent(d, EventType.DASHBOARD_DELETED));
              return d.entityId();
            });
  }

  private boolean dashboardExists(List<AggregationOperation> operations) {
    return !mongoOperations
        .aggregate(newAggregation(Dashboard.class, operations), Document.class)
        .getMappedResults()
        .isEmpty();
  }

  private SocketEvent globalEvent(Dashboard dashboard, String eventType) {
    return CoreSocketEvents.global(eventType, dashboard.getId());
  }

  private static AggregationOperation addMarketDataGroupField() {
    return AddFieldsOperation.builder()
        .addField(Dashboard.Fields.marketDataGroup)
        .withValue(
            ConditionalOperators.ifNull(
                    joinPaths(
                        Dashboard.Fields.mdExceptionManagementSetup,
                        MdExceptionManagementSetup.Fields.marketDataGroup,
                        EntityReference.Fields.name))
                .thenValueOf(
                    joinPaths(
                        Dashboard.Fields.trsMdExceptionManagementSetup,
                        TrsMdExceptionManagementSetup.Fields.marketDataGroup,
                        EntityReference.Fields.name)))
        .build();
  }
}
