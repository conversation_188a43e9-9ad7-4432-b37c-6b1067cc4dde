package com.solum.xplain.xm.settings;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsForm;
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ExceptionManagementSettingsControllerService {

  private final ExceptionManagementSettingsRepository repository;

  public ExceptionManagementSettingsView getSettings(BitemporalDate stateDate) {
    return repository.entityView(stateDate);
  }

  public DateList futureVersions(LocalDate stateDate) {
    return repository.futureVersions(stateDate);
  }

  public List<ExceptionManagementSettingsView> getSettingsVersions() {
    return repository.entityVersions();
  }

  public Either<ErrorItem, EntityId> updateSettings(
      LocalDate versionDate, ExceptionManagementSettingsForm form) {
    return repository.save(versionDate, form);
  }

  public Either<ErrorItem, EntityId> deleteSettingsVersion(LocalDate versionDate) {
    return repository.deleteSettingsVersion(versionDate);
  }
}
