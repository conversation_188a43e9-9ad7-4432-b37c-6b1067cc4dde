package com.solum.xplain.xm.excmngmt.rulesipv;

import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTestControllerService;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestView;
import org.springframework.stereotype.Service;

@Service
public class IpvBreakTestControllerService
    extends BaseBreakTestControllerService<
        Trade,
        IpvBreakTestOverride,
        IpvBreakTest,
        IpvBreakTestForm,
        IpvBreakTestOverrideForm,
        IpvBreakTestView,
        IpvBreakTestOverrideView> {

  public IpvBreakTestControllerService(IpvBreakTestRepository repository) {
    super(repository);
  }
}
