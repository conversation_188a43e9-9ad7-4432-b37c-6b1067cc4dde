package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvBreakTestCalculations;
import com.solum.xplain.xm.workflow.VdXmWorkflowProvider;
import jakarta.annotation.Nullable;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.experimental.FieldNameConstants;

/**
 * Attached immutable context object for a dashboard single-trade workflow for a single phase
 * ({@value VdXmWorkflowProvider#VD_XM_PHASE_PROCESS_ID}). It is initially populated with the trade,
 * phase and break test details.
 */
@FieldNameConstants
public record VdPhaseContext(
    BitemporalDate stateDate,
    IpvDataGroupVo vdg,
    // Only populated if providers.hasXplainProvider()
    String marketDataGroupId,
    PricingSlot pricingSlot,
    SlaDeadline slaDeadline,
    Trade trade,
    @Nullable String calculationCurrency,
    ProvidersVo providers,
    IpvExceptionManagementPhase phase,
    IpvBreakTestCalculations breakTestCalculations)
    implements Serializable {

  public VdPhaseContext withTrade(Trade trade) {
    return new VdPhaseContext(
        this.stateDate,
        this.vdg,
        this.marketDataGroupId,
        this.pricingSlot,
        this.slaDeadline,
        trade,
        this.calculationCurrency,
        this.providers,
        this.phase,
        this.breakTestCalculations);
  }

  public LocalDateTime tradeRecordDate() {
    return trade.getRecordFrom();
  }
}
