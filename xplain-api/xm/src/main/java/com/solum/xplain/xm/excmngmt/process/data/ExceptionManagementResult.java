package com.solum.xplain.xm.excmngmt.process.data;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.APPROVED;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.BATCH_PROCESSING;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_OVERLAY;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_PRELIMINARY;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_APPROVED;
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_BATCH_APPROVED;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.Nullable;

@Data
@Document
@FieldNameConstants
public class ExceptionManagementResult {

  @Id private String id;
  private String dashboardId;

  private CalculationTestStatus status;
  private CalculationTestStatus preliminaryStatus;

  private LocalDate curveDate;

  private String marketDataGroupId;
  private String marketDataGroupName;

  private String trsMarketDataGroupId;
  private String trsMarketDataGroupName;

  public static ExceptionManagementResult preliminary(
      String dashboardId,
      LocalDate curveDate,
      @Nullable MdExceptionManagementSetup setup,
      @Nullable TrsMdExceptionManagementSetup trsSetup) {
    var result = result(dashboardId, curveDate, setup, trsSetup);
    result.setStatus(IN_PRELIMINARY);
    result.setPreliminaryStatus(IN_PRELIMINARY);
    return result;
  }

  public static ExceptionManagementResult batchPreliminary(
      String dashboardId,
      LocalDate curveDate,
      @Nullable MdExceptionManagementSetup setup,
      @Nullable TrsMdExceptionManagementSetup trsSetup) {
    var result = result(dashboardId, curveDate, setup, trsSetup);
    result.setStatus(BATCH_PROCESSING);
    result.setPreliminaryStatus(BATCH_PROCESSING);
    return result;
  }

  private static ExceptionManagementResult result(
      String dashboardId,
      LocalDate curveDate,
      @Nullable MdExceptionManagementSetup setup,
      @Nullable TrsMdExceptionManagementSetup trsSetup) {
    var result = new ExceptionManagementResult();
    result.setDashboardId(dashboardId);
    result.setCurveDate(curveDate);
    Optional.ofNullable(setup)
        .ifPresent(
            md -> {
              result.setMarketDataGroupId(md.getMarketDataGroup().getEntityId());
              result.setMarketDataGroupName(md.getMarketDataGroup().getName());
            });
    Optional.ofNullable(trsSetup)
        .ifPresent(
            md -> {
              result.setTrsMarketDataGroupId(md.getMarketDataGroup().getEntityId());
              result.setTrsMarketDataGroupName(md.getMarketDataGroup().getName());
            });
    return result;
  }

  public ExceptionManagementResult withApproved() {
    if (status == IN_PRELIMINARY) {
      this.setStatus(PRELIMINARY_APPROVED);
      this.setPreliminaryStatus(PRELIMINARY_APPROVED);
    } else if (status == IN_OVERLAY) {
      this.setStatus(APPROVED);
    } else if (status == BATCH_PROCESSING) {
      this.setStatus(PRELIMINARY_BATCH_APPROVED);
      this.setPreliminaryStatus(PRELIMINARY_BATCH_APPROVED);
    }
    return this;
  }

  public ExceptionManagementResult inOverlay() {
    this.setStatus(IN_OVERLAY);
    return this;
  }

  public Either<ErrorItem, ExceptionManagementResult> ensureApprovePreliminaryAllowed() {
    return status == IN_PRELIMINARY || status == BATCH_PROCESSING
        ? right(this)
        : left(OPERATION_NOT_ALLOWED.entity("MD results are not in preliminary state"));
  }

  public Either<ErrorItem, ExceptionManagementResult> ensureApproveOverlayAllowed() {
    return status == IN_OVERLAY
        ? right(this)
        : left(OPERATION_NOT_ALLOWED.entity("MD results are not in overlay state"));
  }
}
