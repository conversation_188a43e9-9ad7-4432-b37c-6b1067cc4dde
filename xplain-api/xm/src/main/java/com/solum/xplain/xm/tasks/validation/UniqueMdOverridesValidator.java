package com.solum.xplain.xm.tasks.validation;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.xm.excmngmt.rules.value.AssetFilterForm;
import com.solum.xplain.xm.tasks.form.TasksOverrideForm;
import com.solum.xplain.xm.tasks.form.TasksTeamsOverrideForm;
import io.atlassian.fugue.Pair;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class UniqueMdOverridesValidator
    implements ConstraintValidator<UniqueTaskOverrides, List<TasksOverrideForm>> {

  @Override
  public boolean isValid(List<TasksOverrideForm> value, ConstraintValidatorContext context) {
    return value.stream()
        .map(TasksOverrideForm::overrideTeams)
        .flatMap(Collection::stream)
        .map(TasksTeamsOverrideForm::getFilter)
        .map(this::unwindAssetClasses)
        .flatMap(Collection::stream)
        .collect(
            collectingAndThen(
                groupingBy(Pair::left, mapping(Pair::right, toUnmodifiableList())),
                OverrideGroupValidationUtils::validateGroupFilters));
  }

  private List<Pair<AssetClass, AssetFilterForm>> unwindAssetClasses(AssetFilterForm v) {
    return Optional.ofNullable(v.getAssetClasses()).stream()
        .flatMap(Collection::stream)
        .map(assetClass -> Pair.pair(assetClass, v))
        .toList();
  }
}
