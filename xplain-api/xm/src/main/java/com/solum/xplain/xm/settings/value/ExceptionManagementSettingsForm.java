package com.solum.xplain.xm.settings.value;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.xm.settings.IpvValueCurrencyType;
import com.solum.xplain.xm.settings.IpvValueNavLevel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

public record ExceptionManagementSettingsForm(
    @NotNull IpvValueCurrencyType currencyType,
    @NotNull IpvValueNavLevel navLevel,
    @Valid @NotNull NewVersionFormV2 versionForm) {}
