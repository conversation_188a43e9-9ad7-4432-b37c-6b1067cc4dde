package com.solum.xplain.xm.workflow.steps.md;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.CachingPreliminaryDataService;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import com.solum.xplain.xm.workflow.state.MdPreliminaryStateMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

/**
 * Saves verified preliminary data to the database and updates the entry status of this process to
 * VERIFIED.
 *
 * <p>The preliminary data will be cached automatically by ObjectId in a near cache for a short time
 * so that, if we go straight into overlay with no breaks, we can fetch it quickly using {@link
 * com.solum.xplain.xm.excmngmt.process.CachingPreliminaryDataService#findPreliminaryAndRemoveFromCache(ObjectId)}.
 *
 * <p>In addition, we separately need to maintain an indefinite shared set of preliminary data
 * associated with the MDK/pricepoint so we know the set of records to fetch. This step also updates
 * that set which is held as a distributed set in the data grid. The set (and all the associated
 * preliminary records) are removed once the data has been fetched for the overlay context creation.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SavePreliminaryStep
    implements ServiceStepExecutor<MdPreliminaryState, MdPreliminaryContext> {
  private final MdPreliminaryStateMapper mdPreliminaryStateMapper;
  private final CachingPreliminaryDataService cachingPreliminaryDataService;

  @Override
  public void runStep(StepStateOps<MdPreliminaryState, MdPreliminaryContext> ops) {
    try {
      InstrumentResultPreliminary result =
          mdPreliminaryStateMapper.toInstrumentResultPreliminary(
              ops.getInitialState(), ops.getContext(), ops);
      ops.submitBulkDataModification(new InstrumentResultPreliminaryCreate(result));
      cachingPreliminaryDataService.addToMdkSet(result);
      ops.setOutcome(
          new MutablePropertyValues()
              .add(MdPreliminaryState.Fields.entryStatus, EntryResultStatus.VERIFIED));

    } catch (NullPointerException e) {
      log.warn(
          "SavePreliminaryStep could not convert result to preliminary. Was the dashboard deleted?",
          e);
    }
  }
}
