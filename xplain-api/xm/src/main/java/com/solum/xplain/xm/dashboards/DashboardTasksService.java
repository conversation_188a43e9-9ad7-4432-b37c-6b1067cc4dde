package com.solum.xplain.xm.dashboards;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.settings.entity.TaskDefaultTeams;
import com.solum.xplain.core.settings.repository.TaskDefaultTeamsSettingsRepository;
import com.solum.xplain.core.sockets.events.EventType;
import com.solum.xplain.xm.tasks.ExceptionManagementTaskExecution;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.repository.IpvTaskExecutionRepository;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import com.solum.xplain.xm.tasks.service.TaskNotificationService;
import com.solum.xplain.xm.tasks.view.TaskCountsGroupView;
import java.util.List;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DashboardTasksService {
  private final TaskExecutionRepository taskExecutionRepository;
  private final IpvTaskExecutionRepository ipvTaskExecutionRepository;
  private final TaskNotificationService taskNotificationService;
  private final TaskDefaultTeamsSettingsRepository taskDefaultTeamsSettingsRepository;

  public List<TaskCountsGroupView> taskCounts(String dashboardId) {
    return taskExecutionRepository.countsView(dashboardId).stream().toList();
  }

  public <T extends ExceptionManagementTaskExecution> List<T> getTasks(String dashboardId) {
    return (List<T>) getMdTasks(dashboardId);
  }

  private List<TaskExecution> getMdTasks(String dashboardId) {
    return taskExecutionRepository.executions(dashboardId);
  }

  public void notifyTasksChanged(List<? extends ExceptionManagementTaskExecution> tasks) {
    notifyTasksChangedWithDefaultTeams(
        tasks.stream().filter(TaskExecution.class::isInstance).toList(),
        taskDefaultTeamsSettingsRepository::getTaskDefaultTeams);
  }

  private void notifyTasksChangedWithDefaultTeams(
      List<? extends ExceptionManagementTaskExecution> tasks, Supplier<TaskDefaultTeams> teamsSup) {
    if (tasks.isEmpty()) {
      return;
    }
    var defaultTeams = teamsSup.get();
    CollectionUtils.toGroupMapConcurrent(tasks, this::eventType).entrySet().stream()
        .filter(e -> !IterableUtils.isEmpty(e.getValue()))
        .forEach(e -> taskNotificationService.notifyUsers(e.getKey(), e.getValue(), defaultTeams));
  }

  @NonNull
  private <T extends ExceptionManagementTaskExecution> String eventType(T t) {
    if (t instanceof TaskExecution execution) {
      return switch (execution.getTaskExceptionManagementType()) {
        case PRELIMINARY_BATCH -> EventType.MD_PRELIMINARY_BATCH_UPDATED;
        case PRELIMINARY -> EventType.MD_PRELIMINARY_UPDATED;
        case OVERLAY -> EventType.MD_OVERLAY_UPDATED;
      };
    }
    throw new IllegalArgumentException("Unexpected task type");
  }
}
