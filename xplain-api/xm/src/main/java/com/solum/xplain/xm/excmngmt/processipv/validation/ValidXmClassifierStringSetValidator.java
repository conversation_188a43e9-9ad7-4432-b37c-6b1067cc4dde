package com.solum.xplain.xm.excmngmt.processipv.validation;

import static com.google.common.collect.ImmutableSet.toImmutableSet;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.xm.classifiers.XmClassifiersProvider;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ValidXmClassifierStringSetValidator
    implements ConstraintValidator<ValidXmClassifierStringSet, Object> {

  private final XmClassifiersProvider classifiersProvider;
  private String classifier;

  @Override
  public void initialize(ValidXmClassifierStringSet constraint) {
    this.classifier = constraint.classifier();
  }

  private Set<String> fetchValues() {
    return classifiersProvider.classifiers().stream()
        .filter(c -> classifier.equals(c.getId()))
        .map(Classifier::getValues)
        .flatMap(List::stream)
        .map(Classifier::getId)
        .collect(toImmutableSet());
  }

  @Override
  public boolean isValid(Object obj, ConstraintValidatorContext context) {
    if (obj == null) {
      return true;
    } else {
      Set<String> values = fetchValues();
      if (obj instanceof String) {
        return values.contains(obj);
      } else if (obj instanceof Enum<?> enumObj) {
        return values.contains(enumObj.name());
      } else {
        return values.contains(obj.toString());
      }
    }
  }
}
