package com.solum.xplain.xm.dashboards.sockets;

import com.solum.xplain.core.authentication.NoRoleAuthorization;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@RestController
@RequestMapping("/sockets/dashboard")
@AllArgsConstructor
public class DashboardSocketController {

  private final DashboardSocketService dashboardSocketService;

  @GetMapping("/{dashboardId}")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeDashboard(@PathVariable String dashboardId) {
    return ResponseEntity.of(dashboardSocketService.subscribeDashboard(dashboardId).toOptional());
  }

  @ModelAttribute
  public void setResponseHeader(HttpServletResponse response) {
    response.setHeader("X-Accel-Buffering", "no");
  }
}
