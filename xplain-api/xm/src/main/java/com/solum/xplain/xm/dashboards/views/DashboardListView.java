package com.solum.xplain.xm.dashboards.views;

import com.solum.xplain.xm.dashboards.enums.DashboardType;
import java.time.LocalDateTime;
import lombok.experimental.FieldNameConstants;

/**
 * @param mdExceptionManagementSetup For MARKET_DATA
 * @param trsMdExceptionManagementSetup For MARKET_DATA
 * @param vdExceptionManagementSetup For VALUATION_DATA
 * @param dateRange Curve / Valuation date
 */
@FieldNameConstants
public record DashboardListView(
    String id,
    DashboardType type,
    MdExceptionManagementSetupView mdExceptionManagementSetup,
    TrsMdExceptionManagementSetupView trsMdExceptionManagementSetup,
    VdExceptionManagementSetupView vdExceptionManagementSetup,
    String marketDataGroup,
    DateRangeView dateRange,
    LocalDateTime createdAt,
    String createdBy,
    LocalDateTime finishedAt,
    LocalDateTime startedAt)
    implements HasDashboardStatus {}
