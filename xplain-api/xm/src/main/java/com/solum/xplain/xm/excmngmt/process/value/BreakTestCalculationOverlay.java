package com.solum.xplain.xm.excmngmt.process.value;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak;
import com.solum.xplain.xm.excmngmt.process.enums.BreakProviderType;
import com.solum.xplain.xm.excmngmt.rules.BreakTest;
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.lang.NonNull;

/**
 * Overlay tests trigger a break only for the Primary provider, whereas for the Secondary provider
 * test is applied and value is calculated, however, it never triggers a break.
 *
 * <p>Overlay tests have a concept of a "child test". <code>TestType
 * .PRIMARY_VS_SECONDARY</code> overlay test might have a single child test, whereas same child test
 * might belong to several parent (dependant) tests. In overlay calculations child test is applied
 * and then calculated values are used in dependent test calculations. Test without a child test
 * uses provider values.
 *
 * <p>Child test is applied even if the parent test is disabled. If child test is disabled, but the
 * parent is enabled, child test is applied but does not trigger a break.
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
class BreakTestCalculationOverlay implements Serializable {

  private static final boolean ONLY_CALCULATE = true;

  private final String entityId;
  private final BreakTest breakTest; // "child" test
  private final List<BreakTest> dependentTests; // PRIMARY_VS_SECONDARY tests

  public static BreakTestCalculationOverlay ofBreakTest(
      @NonNull String entityId, @NonNull BreakTest breakTest) {
    return new BreakTestCalculationOverlay(entityId, breakTest, new ArrayList<>());
  }

  public void acceptDependant(BreakTest dependantTest) {
    var dependsOnBreakTest = Objects.equals(entityId, dependantTest.getParentTest().getParentId());
    if (isTrue(dependantTest.getEnabled()) && dependsOnBreakTest) {
      dependentTests.add(dependantTest);
    }
  }

  public List<InstrumentResultBreak> resolveBreak(InstrumentMarketDataBreakCalculatorOverlay data) {
    if (!breakTest.matches(data.getInstrument())) {
      return List.of();
    }

    var breakTestResults = processBreakTest(data);
    var dependentTestsResults = processDependantTests(testData(data, breakTestResults));
    return join(breakTestResults, dependentTestsResults);
  }

  private InstrumentMarketDataBreakCalculatorOverlay testData(
      InstrumentMarketDataBreakCalculatorOverlay data, List<InstrumentResultBreak> testResults) {
    if (IterableUtils.isEmpty(testResults)) {
      return data;
    }
    return data.toMeasureProcessed(
        providerMeasuredValue(testResults, BreakProviderType.PRIMARY),
        providerMeasuredValue(testResults, BreakProviderType.SECONDARY));
  }

  private BigDecimal providerMeasuredValue(
      List<InstrumentResultBreak> results, BreakProviderType providerType) {
    return results.stream()
        .filter(r -> Objects.equals(r.getProviderType(), providerType))
        .findFirst()
        .map(InstrumentResultBreak::getProviderValue)
        .map(EntryResultBreakByProvider::getValue)
        .orElse(null);
  }

  private List<InstrumentResultBreak> processBreakTest(
      InstrumentMarketDataBreakCalculatorOverlay data) {
    boolean isEnabled = isTrue(breakTest.getEnabled());
    if (CollectionUtils.isNotEmpty(dependentTests) || isEnabled) {
      return processTest(breakTest, data);
    } else {
      return List.of();
    }
  }

  private List<InstrumentResultBreak> processDependantTests(
      InstrumentMarketDataBreakCalculatorOverlay data) {
    return dependentTests.stream()
        .filter(BreakTest::getEnabled)
        .filter(t -> t.matches(data.getInstrument()))
        .map(test -> processTest(test, data))
        .flatMap(Collection::stream)
        .toList();
  }

  private List<InstrumentResultBreak> processTest(
      BreakTest test, InstrumentMarketDataBreakCalculatorOverlay data) {
    return switch (test.getType()) {
      case DAY_TO_DAY -> dayToDayTest(test, data);
      case VALUE -> valueTest(test, data);
      case PRIMARY_VS_SECONDARY -> primaryVsSecondaryTest(test, data);
      default -> throw new IllegalArgumentException("Unexpected test type:" + test.getType());
    };
  }

  private List<InstrumentResultBreak> dayToDayTest(
      BreakTest test, InstrumentMarketDataBreakCalculatorOverlay data) {
    return instrumentResults(
        test,
        test.resolveFirstThreshold(data.getInstrument()),
        dayToDayBreakSupplier(BreakProviderType.PRIMARY, data, test, !test.getEnabled()),
        dayToDayBreakSupplier(BreakProviderType.SECONDARY, data, test, ONLY_CALCULATE));
  }

  private Supplier<EntryResultBreakByProvider> dayToDayBreakSupplier(
      BreakProviderType providerType,
      InstrumentMarketDataBreakCalculatorOverlay data,
      BreakTest test,
      boolean calculationOnly) {
    if (calculationOnly) {
      return () ->
          data.dayOnDay(providerType, test.getMeasureType(), EntryResultResolver.calculationOnly());
    } else {
      return () ->
          data.dayOnDay(
              providerType, test.getMeasureType(), test.resultResolver(data.getInstrument()));
    }
  }

  private List<InstrumentResultBreak> valueTest(
      BreakTest test, InstrumentMarketDataBreakCalculatorOverlay data) {
    return instrumentResults(
        test,
        test.resolveFirstThreshold(data.getInstrument()),
        valueBreakSupplier(BreakProviderType.PRIMARY, data, test, !test.getEnabled()),
        valueBreakSupplier(BreakProviderType.SECONDARY, data, test, ONLY_CALCULATE));
  }

  private Supplier<EntryResultBreakByProvider> valueBreakSupplier(
      BreakProviderType providerType,
      InstrumentMarketDataBreakCalculatorOverlay data,
      BreakTest test,
      boolean calculationOnly) {
    if (calculationOnly) {
      return () ->
          data.value(
              providerType,
              test.getMeasureType(),
              test.zScoreObservationPeriodNormalized(),
              EntryResultResolver.calculationOnly());
    } else {
      return () ->
          data.value(
              providerType,
              test.getMeasureType(),
              test.zScoreObservationPeriodNormalized(),
              test.resultResolver(data.getInstrument()));
    }
  }

  private List<InstrumentResultBreak> primaryVsSecondaryTest(
      BreakTest test, InstrumentMarketDataBreakCalculatorOverlay data) {
    return List.of(
        instrumentResult(
            test,
            test.resolveFirstThreshold(data.getInstrument()),
            null,
            data.providerDiff(test.getMeasureType(), test.resultResolver(data.getInstrument()))));
  }

  private List<InstrumentResultBreak> instrumentResults(
      BreakTest test,
      BigDecimal threshold,
      Supplier<EntryResultBreakByProvider> primary,
      Supplier<EntryResultBreakByProvider> secondary) {
    var results = new ImmutableList.Builder<InstrumentResultBreak>();
    results.add(instrumentResult(test, threshold, BreakProviderType.PRIMARY, primary.get()));
    results.add(instrumentResult(test, threshold, BreakProviderType.SECONDARY, secondary.get()));
    return results.build();
  }

  private InstrumentResultBreak instrumentResult(
      BreakTest test,
      BigDecimal threshold,
      BreakProviderType providerType,
      EntryResultBreakByProvider providerValue) {
    var result = new InstrumentResultBreak();
    result.setBreakTestName(test.getName());
    result.setBreakTestType(test.getType().getName());
    result.setMeasureType(test.getMeasureType());
    result.setOperator(test.getOperator());
    result.setThreshold(threshold);
    result.setProviderType(providerType);
    result.setProviderValue(providerValue);
    result.setParentBreakTestName(parentTestName(test)); // "Child Test" in UI
    return result;
  }

  private String parentTestName(BreakTest test) {
    return test.getParentTest() == null ? null : test.getParentTest().getName();
  }

  public boolean isApplicable(InstrumentDefinition instrument) {
    return breakTest.matches(instrument);
  }

  public boolean isApplicable(MeasureType measureType) {
    return breakTest.getMeasureType() == measureType;
  }

  public Tenor getObservationPeriod() {
    return breakTest.zScoreObservationPeriodNormalized();
  }
}
