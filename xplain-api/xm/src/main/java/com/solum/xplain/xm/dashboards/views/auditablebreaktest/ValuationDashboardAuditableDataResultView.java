package com.solum.xplain.xm.dashboards.views.auditablebreaktest;

import java.util.List;

public record ValuationDashboardAuditableDataResultView(
    String id,
    String taskId,
    DashboardAllIpvProviderView providersData,
    DashboardBreakTestTradeView trade,
    List<DashboardBreakTestView> breakTests,
    List<DashboardBreakTestEventView> events,
    DashboardBreakTestsResolutionView resolution) {}
