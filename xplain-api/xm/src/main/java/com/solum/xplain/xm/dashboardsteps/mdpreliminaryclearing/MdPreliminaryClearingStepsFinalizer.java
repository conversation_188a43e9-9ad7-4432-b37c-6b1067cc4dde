package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing;

import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import com.solum.xplain.xm.tasks.value.UniqueExcptMngmntTask;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class MdPreliminaryClearingStepsFinalizer {

  private final DashboardStepProcessor processor;
  private final TaskExecutionRepository taskExecutionRepository;

  public MdPreliminaryClearingStepsFinalizer(
      DashboardStepProcessor processor, TaskExecutionRepository taskExecutionRepository) {
    this.processor = processor;
    this.taskExecutionRepository = taskExecutionRepository;
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> execute(Dashboard dashboard) {
    return completeSteps(processor.getMdSteps(dashboard));
  }

  private Either<List<ErrorItem>, List<DashboardEntryMd>> completeSteps(
      List<DashboardEntryMd> clearingSteps) {
    return clearingSteps.stream()
        .filter(e -> e.getStatus() == StepStatus.IN_PROGRESS)
        .filter(e -> e.getStep() == DashboardStep.MD_PRELIMINARY_CLEARING)
        .filter(this::isStepCompleted)
        .map(DashboardEntryMd::completed)
        .map(DashboardEntryMd.class::cast)
        .collect(Collectors.collectingAndThen(toList(), processor::updateMdSteps))
        .map(updatedSteps -> clearingSteps);
  }

  private boolean isStepCompleted(DashboardEntryMd entry) {
    var uniqueTask =
        UniqueExcptMngmntTask.preliminaryTask(entry.getDashboardId(), entry.getResultId());
    return taskExecutionRepository.allTasksCompleted(uniqueTask);
  }
}
