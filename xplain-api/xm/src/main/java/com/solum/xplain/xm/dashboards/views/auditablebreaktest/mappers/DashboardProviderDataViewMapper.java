package com.solum.xplain.xm.dashboards.views.auditablebreaktest.mappers;

import com.solum.xplain.xm.dashboards.views.auditablebreaktest.DashboardProviderDataView;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface DashboardProviderDataViewMapper {

  @Mapping(source = "bidAskType", target = "dataType")
  DashboardProviderDataView from(ProviderData providerDataValue);

  List<DashboardProviderDataView> from(List<ProviderData> providerDataValue);
}
