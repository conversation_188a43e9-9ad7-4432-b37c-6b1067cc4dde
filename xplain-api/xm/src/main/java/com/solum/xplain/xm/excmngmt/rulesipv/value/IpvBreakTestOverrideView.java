package com.solum.xplain.xm.excmngmt.rulesipv.value;

import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IpvBreakTestOverrideView extends BaseBreakTestOverrideView {
  private TradeFilter tradeFilter;
}
