package com.solum.xplain.xm.tasks.form;

import com.solum.xplain.xm.excmngmt.rulesipv.value.ProductTypeFilterForm;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IpvTasksTeamsOverrideForm extends TeamsOverrideForm {

  @Valid @NotNull private ProductTypeFilterForm filter;
}
