package com.solum.xplain.xm.excmngmt.rules.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidBreakTestObservationPeriodValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ValidBreakTestObservationPeriod {
  String message() default
      "{com.solum.xplain.api.excmngmt.rules.validation"
          + ".ValidBreakTestObservationPeriod"
          + ".message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
