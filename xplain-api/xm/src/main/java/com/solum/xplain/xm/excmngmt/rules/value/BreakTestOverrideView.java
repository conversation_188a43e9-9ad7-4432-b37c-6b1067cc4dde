package com.solum.xplain.xm.excmngmt.rules.value;

import com.solum.xplain.xm.excmngmt.rules.filter.OverrideAssetFilter;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestOverrideView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BreakTestOverrideView extends BaseBreakTestOverrideView {
  private OverrideAssetFilter overrideAssetFilter;
  private TenorBucketFilterView tenorFilter;
}
