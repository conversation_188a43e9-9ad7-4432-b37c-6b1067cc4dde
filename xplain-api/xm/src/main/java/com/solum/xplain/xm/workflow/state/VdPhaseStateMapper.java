package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultResolution;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue;
import com.solum.xplain.xm.excmngmt.processipv.value.BundledProviderData;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper
public abstract class VdPhaseStateMapper {
  @Autowired HistoricStepInstanceMapper historicStepInstanceMapper;

  public abstract BundledProviderData toBundledProviderData(VdPhaseState phaseState);

  @AfterMapping
  void replacePrimaryValueWithBaseValue(
      @MappingTarget BundledProviderData bundledProviderData, VdPhaseState phaseState) {
    bundledProviderData
        .get(IpvProvidersType.P1)
        .ifPresent(
            p -> {
              if (p.getPv() == null) {
                if (phaseState.getBaseValue() != null) {
                  p.setPv(new ProviderDataValue(phaseState.getBaseValue(), null));
                }
              } else {
                p.getPv().setValue(phaseState.getBaseValue());
              }
            });
  }

  @Mapping(target = "id", ignore = true)
  @Mapping(
      target = "dashboardId",
      expression =
          "java(com.solum.xplain.xm.workflow.XmWorkflowService.dashboardIdFromBusinessKey(ops.getRootBusinessKey()))")
  @Mapping(target = "taskId", ignore = true)
  @Mapping(target = "valuationDate", source = "context.stateDate.actualDate")
  @Mapping(target = "ipvDataGroupId", source = "context.vdg.entityId")
  @Mapping(target = "pricingSlot", source = "context.pricingSlot")
  @Mapping(target = "slaDeadline", source = "context.slaDeadline")
  @Mapping(target = "trade", source = "context.trade")
  @Mapping(target = "calculationCurrency", source = "context.calculationCurrency")
  @Mapping(target = "status", constant = "VERIFIED")
  @Mapping(target = "resolvedValue", source = "state.baseValue")
  @Mapping(target = "primaryProviderData", source = "state.primary")
  @Mapping(target = "secondaryProviderData", source = "state.secondary")
  @Mapping(target = "tertiaryProviderData", source = "state.tertiary")
  @Mapping(target = "quaternaryProviderData", source = "state.quaternary")
  @Mapping(target = "breakTests", source = "state.breakTestResults")
  @Mapping(
      target = "appliedTestsCount",
      expression =
          "java(com.solum.xplain.xm.excmngmt.HasEntryResult.countAppliedTests(state.getBreakTestResults()))")
  @Mapping(target = "hasBreaks", source = "state.hasBreak")
  @Mapping(
      target = "maxTriggeredThresholdLevel",
      expression =
          "java(com.solum.xplain.xm.excmngmt.HasEntryResult.maxTriggeredThresholdLevel(state.getBreakTestResults()))")
  @Mapping(target = "resolution", source = "state")
  @Mapping(
      target = "previousStatuses",
      expression = "java(historicStepInstanceMapper.toEntryResultStatusHistories(ops))")
  @Mapping(target = "modifiedBy", source = "state.approver")
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "withBreakTests", ignore = true)
  public abstract IpvTradeResultOverlay toTradeResultOverlay(
      VdPhaseState state,
      VdPhaseContext context,
      @Context StepStateOps<VdPhaseState, VdPhaseContext> ops);

  @Mapping(
      target = "value",
      conditionExpression = "java(state.getResolution() != null)",
      source = "baseValue")
  abstract IpvTradeResultResolution toTradeResultResolution(VdPhaseState state);
}
