package com.solum.xplain.xm.excmngmt.process.view.viewconfig;

import static com.solum.xplain.xm.excmngmt.process.view.InstrumentResultViewFieldName.THRESHOLD_LEVEL;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnDefinitionGroupBuilder;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldType;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentResultViewFieldName;
import java.util.function.Predicate;

/**
 * This builder creates a labelled column definition group view, by adding columns if field names
 * match a predicate. Another predicate determines if the column is hidden when the group is
 * collapsed.
 */
class ColumnDefinitionGroupBuilder extends AbstractColumnDefinitionGroupBuilder {
  public ColumnDefinitionGroupBuilder(
      Predicate<String> matcher, String label, Predicate<String> hidden) {
    super(matcher, label, hidden);
  }

  /**
   * Simple implementation to determine the default precision for a field.
   *
   * <p>Numeric fields have a default precision of 5 decimal places, except for {@value
   * InstrumentResultViewFieldName#THRESHOLD_LEVEL} which has a precision of 0. Non-numeric fields
   * have a null precision.
   *
   * @param field the field to return precision for.
   * @return precision in decimal places, or null if a non-numeric field
   */
  @Override
  protected Integer precision(FieldDefinitionView field) {
    if (field.type() == FieldType.NUMBER) {
      return switch (field.name()) {
        case THRESHOLD_LEVEL -> 0;
        default -> 5;
      };
    }
    return null;
  }
}
