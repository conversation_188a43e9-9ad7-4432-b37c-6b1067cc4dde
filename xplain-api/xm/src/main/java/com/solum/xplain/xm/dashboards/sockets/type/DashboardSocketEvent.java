package com.solum.xplain.xm.dashboards.sockets.type;

import com.solum.xplain.core.sockets.SocketReference;
import com.solum.xplain.core.sockets.events.SocketEvent;
import com.solum.xplain.core.sockets.events.TargetedSocketEvent;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DashboardSocketEvent {

  private static final String DASHBOARD = "DASHBOARD";

  public static SocketEvent dashboardEvent(String id, String eventType) {
    return new TargetedSocketEvent(DASHBOARD, List.of(id), eventType, null);
  }

  public static SocketReference dashboardReference(String id) {
    return new SocketReference(DASHBOARD, id);
  }
}
