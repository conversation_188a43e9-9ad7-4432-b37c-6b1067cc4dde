package com.solum.xplain.xm.tasks.granularity.marketdata.fx;

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup.FX;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_PAIRS;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter;
import com.solum.xplain.xm.tasks.TaskMapper;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.granularity.marketdata.BaseMdTaskGranularityRule;
import java.util.List;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class GranularityByFxDomesticCcy extends BaseMdTaskGranularityRule {

  public GranularityByFxDomesticCcy(TaskMapper mapper, List<InstrumentType> supportedInstruments) {
    super(mapper, supportedInstruments);
  }

  @Override
  public List<TaskExecution> split(TaskExecution taskExecution) {
    var assetFilter = taskExecution.getAssetFilter();
    var assetClasses = assetFilter.permissibleAssetClasses(allAssetClasses());
    return assetFilter
        .separateAssetClassesAndSplitOn(
            v -> v.getGroup() == FX, assetClasses, this::splitDomesticCcy)
        .stream()
        .map(filter -> mapper.copy(taskExecution, filter))
        .toList();
  }

  private List<AssetFilter> splitDomesticCcy(AssetFilter assetFilter) {
    return assetFilter.permissibleFxPairs(FX_SWAP_PAIRS).stream()
        .collect(groupingBy(CurrencyPair::getBase, mapping(CurrencyPair::toString, toList())))
        .values()
        .stream()
        .map(fxPairs -> AssetFilter.copyWithFxPairs(assetFilter, fxPairs))
        .toList();
  }
}
