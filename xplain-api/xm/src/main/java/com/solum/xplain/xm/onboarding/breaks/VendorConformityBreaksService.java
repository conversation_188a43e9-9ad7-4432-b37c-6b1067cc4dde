package com.solum.xplain.xm.onboarding.breaks;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.PRIMARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.QUATERNARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.SECONDARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.TERTIARY;
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.AC_P1;
import static com.solum.xplain.xm.onboarding.breaks.OnboardingBreakTestData.ofVendor;
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.FAILED;
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED;
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.SUCCESS;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static io.atlassian.fugue.extensions.step.Steps.begin;
import static java.util.Optional.ofNullable;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.ipv.data.IpvDataRepository;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType;
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem;
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak;
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics;
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettings;
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettingsResolver;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class VendorConformityBreaksService {

  private final OnboardingBreakTestCalculationsProvider breakTestCalculationsProvider;
  private final IpvDataRepository ipvDataRepository;

  public List<LogItem> processBreaks(
      @NonNull List<OnboardingReportItem> items,
      @NonNull TradeCompanySettingsResolver settingsResolver,
      @NonNull BitemporalDate stateDate) {

    var calculations = breakTestCalculationsProvider.provideCalculations(stateDate, AC_P1);

    return items.stream()
        .filter(i -> i.getVendorCheckStatus() == REQUESTED)
        .map(i -> processItem(i, calculations, settingsResolver))
        .flatMap(Collection::stream)
        .toList();
  }

  private List<LogItem> processItem(
      OnboardingReportItem item,
      OnboardingBreakTestCalculations calculations,
      TradeCompanySettingsResolver settingsResolver) {
    var trade = item.getTrade();
    var vendorProcessingResults =
        settingsResolver
            .tradeSettings(trade)
            .map(
                s ->
                    begin(vendorData(s, s.primary(), trade))
                        .then(p1 -> vendorData(s, s.secondary(), trade))
                        .then((p1, p2) -> vendorData(s, s.tertiary(), trade))
                        .then((p1, p2, p3) -> vendorData(s, s.quaternary(), trade))
                        .yield((p1, p2, p3, p4) -> process(item, calculations, p1, p2, p3, p4)))
            .flatMap(d -> d);

    if (vendorProcessingResults.isLeft()) {
      var error = vendorProcessingResults.left().get();
      log.debug(
          "Unable to process vendor conformity check for {}. Error {}",
          trade.getExternalTradeId(),
          error);
      item.setVendorCheckStatus(FAILED);
      item.setVendorCheckMessage(error.getDescription());
      return List.of(error);
    }
    return List.of();
  }

  private OnboardingReportItem process(
      @NonNull OnboardingReportItem item,
      @NonNull OnboardingBreakTestCalculations calculations,
      @NonNull Optional<OnboardingVendorMetrics> p1,
      @NonNull Optional<OnboardingVendorMetrics> p2,
      @NonNull Optional<OnboardingVendorMetrics> p3,
      @NonNull Optional<OnboardingVendorMetrics> p4) {
    p1.ifPresent(item::setVendorPrimaryMetrics);
    p2.ifPresent(item::setVendorSecondaryMetrics);
    p3.ifPresent(item::setVendorTertiaryMetrics);
    p4.ifPresent(item::setVendorQuaternaryMetrics);

    var builder = ImmutableList.<OnboardingTradeResultBreak>builder();
    p1.map(p -> processProvider(PRIMARY, p, calculations, item)).ifPresent(builder::addAll);
    p2.map(p -> processProvider(SECONDARY, p, calculations, item)).ifPresent(builder::addAll);
    p3.map(p -> processProvider(TERTIARY, p, calculations, item)).ifPresent(builder::addAll);
    p4.map(p -> processProvider(QUATERNARY, p, calculations, item)).ifPresent(builder::addAll);
    var results = builder.build();
    item.setBreakTests(results);
    if (!item.hasXplainVendorProvider()) {
      var triggered = results.stream().anyMatch(OnboardingTradeResultBreak::isTriggered);
      item.setVendorCheckStatus(triggered ? FAILED : SUCCESS);
    }
    return item;
  }

  private List<OnboardingTradeResultBreak> processProvider(
      @NonNull IpvBreakProviderType providersType,
      @NonNull OnboardingVendorMetrics providerMetrics,
      @NonNull OnboardingBreakTestCalculations calculations,
      @NonNull OnboardingReportItem item) {
    if (providerMetrics.hasXplainProvider()) {
      return List.of();
    }
    var data = ofVendor(providerMetrics, item.getNavOnVendorOnboardingDate());
    return calculations.process(item.getTrade(), data, providersType);
  }

  private Either<ErrorItem, Optional<OnboardingVendorMetrics>> vendorData(
      TradeCompanySettings settings, String provider, Trade trade) {
    if (StringUtils.isEmpty(provider)) {
      return right(Optional.empty());
    }

    var onboardingDate = trade.getVendorOnboardingDate();
    if (onboardingDate == null) {
      return left(OBJECT_NOT_FOUND.entity("NAV Effective date is required"));
    } else if (XPLAIN_PROVIDER_CODE.equals(provider)) {
      return right(Optional.of(OnboardingVendorMetrics.ofXplain()));
    }
    return ofNullable(settings.groupId())
        .map(g -> ipvDataRepository.getValueAtDate(g, trade.getKey(), provider, onboardingDate))
        .orElseGet(() -> left(OBJECT_NOT_FOUND.entity("Unable to resolve VD group")))
        .map(OnboardingVendorMetrics::ofVendor)
        .map(Optional::of);
  }
}
