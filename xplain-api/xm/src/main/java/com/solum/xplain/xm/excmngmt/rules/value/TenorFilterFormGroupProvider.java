package com.solum.xplain.xm.excmngmt.rules.value;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.google.common.collect.ImmutableList;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class TenorFilterFormGroupProvider implements DefaultGroupSequenceProvider<TenorFilterForm> {
  @Override
  public List<Class<?>> getValidationGroups(TenorFilterForm filterForm) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(TenorFilterForm.class);
    if (filterForm == null) {
      return builder.build();
    }
    if (isTrue(filterForm.allowAllTenors())) {
      builder.add(AllTenorsGroup.class);
    } else {
      builder.add(TenorBucketsGroup.class);
    }
    return builder.build();
  }
}
