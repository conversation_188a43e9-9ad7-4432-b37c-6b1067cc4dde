package com.solum.xplain.xm.excmngmt.processipv.data;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.excmngmt.ResolvableResult;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.processipv.form.ResolutionForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class IpvTradeResultResolution implements ResolvableResult {

  private TradeResultResolutionType resolution;
  private String resolutionComment;
  private ExceptionManagementEvidence resolutionEvidence;

  private BigDecimal value;

  private String providerName;
  private IpvProvidersType providerType;

  private String approvalComment; // aka verification comment
  private ExceptionManagementEvidence approvalEvidence;

  public static IpvTradeResultResolution newOf() {
    return new IpvTradeResultResolution();
  }

  public static IpvTradeResultResolution newOf(
      ResolutionForm resolutionForm, ExceptionManagementEvidence evidence) {
    var r = new IpvTradeResultResolution();
    r.setResolution(resolutionForm.resolutionType());
    r.setResolutionComment(resolutionForm.comment());
    r.setResolutionEvidence(evidence);
    return r;
  }

  public Optional<ErrorItem> resolveValue(
      Trade trade, Supplier<Optional<BigDecimal>> valueSupplier) {
    var optionalValue = valueSupplier.get();
    if (optionalValue.isPresent()) {
      setValue(optionalValue.get());
      return Optional.empty();
    }
    return Optional.of(
        OPERATION_NOT_ALLOWED.entity("Resolved value is empty for trade " + trade.getKey()));
  }

  public void resolveProvider(String provider, IpvProvidersType providerType) {
    setProviderName(provider);
    setProviderType(providerType);
  }

  @EqualsAndHashCode.Include(replaces = "value")
  private BigDecimal normalisedValue() {
    return value == null ? null : value.stripTrailingZeros();
  }
}
