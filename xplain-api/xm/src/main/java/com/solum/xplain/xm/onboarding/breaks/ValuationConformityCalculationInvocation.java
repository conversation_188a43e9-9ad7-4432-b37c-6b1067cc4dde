package com.solum.xplain.xm.onboarding.breaks;

import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Eithers.filterLeft;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.calculationapi.OnboardingCalculation;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.onboarding.OnboardingReportRepository;
import com.solum.xplain.xm.onboarding.entity.OnboardingReport;
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class ValuationConformityCalculationInvocation {

  private final CompanyPortfolioSettingsResolver settingsResolver;
  private final OnboardingCalculation onboardingCalculation;
  private final OnboardingReportRepository onboardingReportRepository;
  private final DashboardMarketDataSourceResolver marketDataSourceResolver;

  public List<LogItem> invokeValuations(
      List<OnboardingReportItem> reportItems, OnboardingReport report) {
    var groupsOnTradeDate =
        valuationGroups(
            reportItems,
            OnboardingReportItem::requiresValuationOnTradeDate,
            i -> i.getTradeDetails().getInfo().getTradeDate());
    var groupsOnOnboardingDate =
        valuationGroups(
            reportItems,
            OnboardingReportItem::requiresValuationOnOnboardingDate,
            Trade::getVendorOnboardingDate);

    var settingsByStateDate =
        Stream.concat(groupsOnTradeDate.stream(), groupsOnOnboardingDate.stream())
            .map(ValuationGroup::valuationDate)
            .distinct()
            .map(tradeDate -> newOf(tradeDate, report.getRecordDate()))
            .collect(toMap(identity(), settingsResolver::portfoliosSettings));

    var results =
        Stream.concat(groupsOnTradeDate.stream(), groupsOnOnboardingDate.stream())
            .distinct()
            .map(
                gr ->
                    new ValuationRequest(
                        gr,
                        groupsOnTradeDate.contains(gr),
                        groupsOnOnboardingDate.contains(gr),
                        report,
                        settingsByStateDate))
            .map(ValuationRequest::invoke)
            .toList();

    var errors = ImmutableList.copyOf(filterLeft(results));
    if (!errors.isEmpty()) {
      log.debug("Unable to invoke valuation conformity calculation: Errors {}", errors);
      return List.copyOf(errors);
    }
    return List.of();
  }

  private List<ValuationGroup> valuationGroups(
      List<OnboardingReportItem> items,
      Predicate<OnboardingReportItem> itemsFilter,
      Function<Trade, LocalDate> dateExtraction) {
    return items.stream()
        .filter(itemsFilter)
        .map(OnboardingReportItem::getTrade)
        .collect(groupingBy(Trade::getPortfolioId, mapping(dateExtraction, toSet())))
        .entrySet()
        .stream()
        .flatMap(e -> e.getValue().stream().map(d -> new ValuationGroup(e.getKey(), d)))
        .toList();
  }

  @Value
  private class ValuationRequest {

    ValuationGroup valuationGroup;
    boolean hasOnTrade;
    boolean hasOnVersion;
    OnboardingReport report;
    Map<BitemporalDate, List<PortfolioSettings<CompanyLegalEntitySettingsView>>>
        settingsByStateDate;

    private Either<ErrorItem, EntityId> invoke() {
      return resolveSettings()
          .flatMap(this::invokeWithSettings)
          .map(this::storeCalculationId)
          .leftMap(this::storeFailedItems);
    }

    private Either<ErrorItem, EntityId> invokeWithSettings(
        PortfolioSettings<CompanyLegalEntitySettingsView> settings) {
      var sourceType =
          marketDataSourceResolver.resolve(
              settings.getSettings().getValuationSettings().getMarketDataGroupId(),
              valuationGroup.valuationDate());
      return onboardingCalculation.calculate(
          report.getId(),
          valuationGroup.valuationDate(),
          report.getCreatedBy(),
          settings,
          sourceType,
          newOf(valuationGroup.valuationDate(), report.getRecordDate()));
    }

    private Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>> resolveSettings() {
      return settingsByStateDate
          .get(newOf(valuationGroup.valuationDate(), report.getRecordDate()))
          .stream()
          .filter(v -> v.getView().getId().equals(valuationGroup.portfolioId()))
          .findAny()
          .map(Either::<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>>right)
          .orElse(left(OBJECT_NOT_FOUND.entity("Valuation settings not found")));
    }

    private EntityId storeCalculationId(EntityId calculationId) {
      onboardingReportRepository.updateValuationItemsCalculationId(
          report.getId(),
          valuationGroup.portfolioId(),
          valuationGroup.valuationDate(),
          new ObjectId(calculationId.getId()),
          hasOnTrade,
          hasOnVersion);
      return calculationId;
    }

    private ErrorItem storeFailedItems(ErrorItem e) {
      var valuationInvocationError =
          CALCULATION_ERROR.entity(
              String.format(
                  "Failed to invoke valuation on date %s: %s",
                  valuationGroup.valuationDate(), e.getDescription()));
      onboardingReportRepository.updateFailedValuationItems(
          report.getId(),
          valuationGroup.portfolioId(),
          valuationGroup.valuationDate(),
          valuationInvocationError.getDescription(),
          hasOnTrade,
          hasOnVersion);
      return valuationInvocationError;
    }
  }

  private record ValuationGroup(String portfolioId, LocalDate valuationDate) {}
}
