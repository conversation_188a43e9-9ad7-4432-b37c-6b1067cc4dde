package com.solum.xplain.xm.excmngmt.evidence;

import static com.solum.xplain.core.files.FileOperations.FILES_COLLECTION;
import static com.solum.xplain.core.files.FileOperations.METADATA_FIELD;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.files.FileOperations;
import com.solum.xplain.core.utils.PathUtils;
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent;
import io.atlassian.fugue.Either;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

@Repository
public class ExceptionManagementEvidenceRepository {
  private final MongoOperations mongoTemplate;
  private final FileOperations<EvidenceFileMetadata> fileOperations;

  public ExceptionManagementEvidenceRepository(
      GridFsTemplate gridFsTemplate, MongoOperations mongoTemplate) {
    this.fileOperations = new FileOperations<>(gridFsTemplate);
    this.mongoTemplate = mongoTemplate;
  }

  public Either<ErrorItem, ExceptionManagementEvidence> saveFile(
      List<String> dashboardIds, MultipartFile file) {
    return fileOperations
        .saveFile(file, metadata(dashboardIds, file))
        .map(fileId -> new ExceptionManagementEvidence(fileId.getId(), file.getOriginalFilename()));
  }

  public Either<ErrorItem, FileResponseEntity> exportFile(String fileId) {
    return fileOperations.exportFile(Query.query(resultFileCriteria(fileId)));
  }

  @EventListener
  public void removeDashboardReference(DashboardDeletedEvent event) {
    var dashboardId = event.getDashboardId();
    var dashboardPath =
        PathUtils.joinPaths(METADATA_FIELD, EvidenceFileMetadata.Fields.dashboardIds);
    mongoTemplate.updateMulti(
        Query.query(where(dashboardPath).is(dashboardId)),
        new Update().pull(dashboardPath, dashboardId),
        FILES_COLLECTION);
  }

  private Criteria resultFileCriteria(String fileId) {
    return where(UNDERSCORE_ID).is(new ObjectId(fileId));
  }

  private EvidenceFileMetadata metadata(List<String> dashboardIds, MultipartFile file) {
    var metadata = new EvidenceFileMetadata();
    metadata.setDashboardIds(dashboardIds);
    metadata.setContentType(file.getContentType());
    return metadata;
  }
}
