package com.solum.xplain.xm.dashboards.entity;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.view.UniqueTaskCountKey;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DashboardEntryMdBatch extends DashboardEntry {

  public static DashboardEntryMdBatch newOf(String dashboardId, DashboardStep step) {
    var entry = new DashboardEntryMdBatch();
    entry.setDashboardId(dashboardId);
    entry.setStep(step);
    entry.setStatus(StepStatus.NOT_STARTED);
    return entry;
  }

  public static DashboardEntryMdBatch newOfMarketDataUpload(String dashboardId) {
    return newOf(dashboardId, DashboardStep.MARKET_DATA_BATCH_UPLOAD);
  }

  public static DashboardEntryMdBatch newOfPreliminaryRun(String dashboardId) {
    return newOf(dashboardId, DashboardStep.MD_BATCH_PRELIMINARY_RUN);
  }

  public static DashboardEntryMdBatch newOfPreliminaryClearing(
      String dashboardId, Long breakCount) {
    var entry = newOf(dashboardId, DashboardStep.MD_BATCH_PRELIMINARY_CLEARING);
    entry.setBreaksCount(breakCount);
    return entry;
  }

  @Override
  public Optional<UniqueTaskCountKey> taskCountKey() {
    if (getStep() == DashboardStep.MD_BATCH_PRELIMINARY_CLEARING) {
      return Optional.of(
          UniqueTaskCountKey.taskCountKey(TaskExceptionManagementType.PRELIMINARY_BATCH));
    }
    return Optional.empty();
  }

  @Override
  public boolean isSameEntry(DashboardEntry entry) {
    if (entry instanceof DashboardEntryMdBatch mdEntry) {
      return super.isSameEntry(mdEntry);
    }
    return false;
  }
}
