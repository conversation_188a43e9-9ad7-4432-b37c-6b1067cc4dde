package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static com.solum.xplain.core.portfolio.PortfolioProjections.tradeDetailsProjection;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.xm.excmngmt.processipv.value.IpvTradeFilterUtils.fromTradeFilterForm;
import static com.solum.xplain.xm.workflow.XmWorkflowService.businessKeyFromDashboardId;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.replaceRoot;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.ComparisonOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Cond.when;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.AggregationUtils;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.config.properties.IpvExceptionManagementProperties;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.utils.PathUtils;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent;
import com.solum.xplain.xm.dashboards.views.DashboardPortfolio;
import com.solum.xplain.xm.dashboards.views.PortfolioResultCountView;
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm;
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm.IndividualOverrideForm;
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvDataGroupPortfolioView;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultResolution;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeFilterForm;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvExceptionManagementCountedFilters;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvExceptionManagementTestValuesDistribution;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvPortfolioItemResultFilter;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvExceptionManagementCountedFiltersView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvPortfolioItemResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.trade.ResolvedIpvTrade;
import com.solum.xplain.xm.excmngmt.value.BreakTestHistory;
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory;
import com.solum.xplain.xm.tasks.repository.IpvTaskDefinitionRepository;
import com.solum.xplain.xm.workflow.repository.XmProcessExecutionQueryRepository;
import io.atlassian.fugue.Either;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.bson.BsonNull;
import org.bson.Document;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.VariableOperators.Map;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class IpvExceptionManagementCalculationRepository {
  private static final String OVERRIDE_NOT_ALLOWED =
      "Only verified entries can be overridden. Not all passed entries can be overridden";

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final IpvExceptionManagementResultMapper mapper;
  private final IpvExceptionManagementProperties ipvExceptionManagementProperties;
  private final IpvTaskDefinitionRepository taskDefinitionRepository;
  private final XmProcessExecutionQueryRepository xmProcessExecutionQueryRepository;

  /**
   * Cleans up all overlay data for a deleted dashboard.
   *
   * @param event event fired when the dashboard is deleted
   */
  @EventListener
  void removeDashboardReference(DashboardDeletedEvent event) {
    delete(event.getDashboardId());
  }

  public void delete(String dashboardId) {
    mongoOperations
        .remove(IpvTradeResultOverlay.class)
        .matching(where(IpvTradeResultOverlay.Fields.dashboardId).is(dashboardId))
        .all();
  }

  public IpvExceptionManagementCountedFiltersView getOverlayTasksFilter(
      List<String> taskIds, TradeFilterForm filter) {
    var filters =
        mongoOperations
            .query(IpvTradeResultOverlay.class)
            .matching(
                countedFiltersQuery(where(IpvTradeResultOverlay.Fields.taskId).in(taskIds), filter))
            .stream()
            .collect(
                IpvExceptionManagementCountedFilters::empty,
                IpvExceptionManagementCountedFilters::add,
                IpvExceptionManagementCountedFilters::merge);
    return mapper.toView(filters);
  }

  public List<IpvExceptionManagementTestValuesDistribution> resultValuesDistribution(
      List<String> taskIds, TradeFilterForm filter) {
    var operations =
        List.of(
            match(fromTradeFilterForm(filter).and(IpvTradeResultOverlay.Fields.taskId).in(taskIds)),
            project(IpvTradeResultOverlay.Fields.breakTests),
            unwind(IpvTradeResultOverlay.Fields.breakTests),
            match(
                where(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.breakTests,
                            TradeResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.triggeredThreshold))
                    .ne(null)
                    .and(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.breakTests,
                            TradeResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.triggered))
                    .is(true)
                    .and(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.breakTests,
                            TradeResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.value))
                    .exists(true)
                    .ne(null)),
            group(
                    joinPaths(
                        IpvTradeResultOverlay.Fields.breakTests,
                        TradeResultBreak.Fields.breakTestId),
                    joinPaths(
                        IpvTradeResultOverlay.Fields.breakTests,
                        TradeResultBreak.Fields.breakTestName),
                    joinPaths(
                        IpvTradeResultOverlay.Fields.breakTests,
                        TradeResultBreak.Fields.measureType))
                .push(
                    joinPaths(
                        IpvTradeResultOverlay.Fields.breakTests,
                        TradeResultBreak.Fields.providerValue,
                        EntryResultBreakByProvider.Fields.value))
                .as(IpvExceptionManagementTestValuesDistribution.Fields.values)
                .addToSet(
                    joinPaths(
                        IpvTradeResultOverlay.Fields.breakTests,
                        TradeResultBreak.Fields.providerValue,
                        EntryResultBreakByProvider.Fields.triggeredThreshold))
                .as(IpvExceptionManagementTestValuesDistribution.Fields.thresholds),
            project(
                    IpvExceptionManagementTestValuesDistribution.Fields.values,
                    IpvExceptionManagementTestValuesDistribution.Fields.thresholds,
                    IpvExceptionManagementTestValuesDistribution.Fields.measureType)
                .and(joinPaths(UNDERSCORE_ID, TradeResultBreak.Fields.breakTestId))
                .as(IpvExceptionManagementTestValuesDistribution.Fields.breakTestId)
                .and(joinPaths(UNDERSCORE_ID, TradeResultBreak.Fields.breakTestName))
                .as(IpvExceptionManagementTestValuesDistribution.Fields.breakTestName),
            match(
                where(IpvExceptionManagementTestValuesDistribution.Fields.values)
                    .ne(List.of())
                    .and(IpvExceptionManagementTestValuesDistribution.Fields.thresholds)
                    .ne(List.of())),
            sort(
                Sort.Direction.ASC,
                IpvExceptionManagementTestValuesDistribution.Fields.breakTestName));

    return mongoOperations
        .aggregate(
            newAggregation(IpvTradeResultOverlay.class, operations),
            IpvExceptionManagementTestValuesDistribution.class)
        .getMappedResults();
  }

  private Query countedFiltersQuery(Criteria criteria, TradeFilterForm formFilter) {
    return query(fromTradeFilterForm(formFilter)).addCriteria(criteria);
  }

  public ScrollableEntry<IpvTradeOverlayResultView> overlayItems(
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {

    List<IpvTradeOverlayResultView> views =
        xmProcessExecutionQueryRepository.getIpvClearingViews(
            taskIds,
            taskDefinitionRepository.getTasksDefinitions(BitemporalDate.newOfNow()),
            isTrue(displayFilter.getOnlyCurvesWithBreaks()),
            isTrue(displayFilter.getIncludeHeld()),
            tableFilter.criteria(IpvTradeOverlayResultView.class, conversionService),
            scrollRequest.withDefaultSort(Sort.by(IpvTradeOverlayResultView.Fields.id)));

    return ScrollableEntry.of(views, scrollRequest);
  }

  public ScrollableEntry<IpvPortfolioItemResultView> portfolioResultItems(
      String dashboardId,
      String portfolioId,
      IpvExceptionManagementPhase phase,
      @Nullable List<String> tradeIds,
      IpvPortfolioItemResultFilter resultFilter,
      TableFilter filter,
      ScrollRequest scrollRequest) {

    var filterCriteria = portfolioCriteria(portfolioId).andOperator(resultFilter.toCriteria());
    if (tradeIds != null && !tradeIds.isEmpty()) {
      filterCriteria =
          filterCriteria
              .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.entityId))
              .in(tradeIds);
    }
    var operations = portfolioResultItemsProjection(dashboardId, phase, filterCriteria, filter);

    operations.addAll(
        new ScrollSortOperations(scrollRequest, PortfolioItemFlatView.Fields.tradeId).build());
    var items =
        mongoOperations
            .aggregate(
                newAggregation(IpvTradeResultOverlay.class, operations.build()),
                IpvPortfolioItemResultView.class)
            .getMappedResults();
    return ScrollableEntry.of(items, scrollRequest);
  }

  public ScrollableEntry<IpvPortfolioItemResultView> allPortfolioResultItems(
      String dashboardId,
      IpvExceptionManagementPhase phase,
      IpvPortfolioItemResultFilter resultFilter,
      TableFilter filter,
      ScrollRequest scrollRequest) {

    Criteria filterCriteria = resultFilter.toCriteria();
    var operations = portfolioResultItemsProjection(dashboardId, phase, filterCriteria, filter);

    operations.addAll(
        new ScrollSortOperations(scrollRequest, PortfolioItemFlatView.Fields.tradeId).build());
    var items =
        mongoOperations
            .aggregate(
                newAggregation(IpvTradeResultOverlay.class, operations.build()),
                IpvPortfolioItemResultView.class)
            .getMappedResults();
    return ScrollableEntry.of(items, scrollRequest);
  }

  public Stream<IpvPortfolioItemResultView> portfolioResultItemsStream(
      String dashboardId,
      String portfolioId,
      IpvExceptionManagementPhase phase,
      TableFilter filter) {
    var filterCriteria = portfolioCriteria(portfolioId);
    var operations = portfolioResultItemsProjection(dashboardId, phase, filterCriteria, filter);
    return mongoOperations.aggregateStream(
        newAggregation(IpvTradeResultOverlay.class, operations.build()),
        IpvPortfolioItemResultView.class);
  }

  public Stream<IpvPortfolioItemResultView> allPortfoliosResultItemsStream(
      String dashboardId, List<DashboardPortfolio> portfolios, IpvExceptionManagementPhase phase) {
    if (portfolios.isEmpty()) {
      return Stream.empty();
    }
    var filterCriteria = new Criteria();
    filterCriteria =
        portfolios.stream()
            .map(
                p ->
                    where(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
                        .is(p.portfolio().getEntityId()))
            .collect(Collectors.collectingAndThen(toList(), filterCriteria::orOperator));

    var operations =
        portfolioResultItemsProjection(
            dashboardId, phase, filterCriteria, TableFilter.emptyTableFilter());
    return mongoOperations.aggregateStream(
        newAggregation(IpvTradeResultOverlay.class, operations.build()),
        IpvPortfolioItemResultView.class);
  }

  public List<PortfolioResultCountView> portfolioResultStatusCounts(
      String dashboardId, IpvExceptionManagementPhase phase) {
    List<PortfolioResultCountView> counts =
        xmProcessExecutionQueryRepository.getDashboardPortfolioResultCounts(
            businessKeyFromDashboardId(dashboardId).toString());
    if (counts.isEmpty()) {
      // Workflow records have been removed, so we need to get the count from the results instead.
      var isVerified = where(IpvTradeResultOverlay.Fields.status).is(EntryResultStatus.VERIFIED);

      var operations =
          List.of(
              match(
                  where(IpvTradeResultOverlay.Fields.dashboardId)
                      .is(dashboardId)
                      .and(IpvTradeResultOverlay.Fields.phase)
                      .is(phase)),
              project(IpvDataGroupPortfolioView.Fields.ipvDataGroupId)
                  .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
                  .as(IpvDataGroupPortfolioView.Fields.portfolioId)
                  .and(when(isVerified).then(1).otherwise(0))
                  .as(PortfolioResultCountView.Fields.verifiedCount),
              group(IpvDataGroupPortfolioView.Fields.portfolioId)
                  // TODO: totalCount needs to come from the VdDashboardState.trades, grouped by
                  // portfolio.
                  .count()
                  .as(PortfolioResultCountView.Fields.totalCount)
                  .sum(PortfolioResultCountView.Fields.verifiedCount)
                  .as(PortfolioResultCountView.Fields.verifiedCount),
              project(
                      PortfolioResultCountView.Fields.totalCount,
                      PortfolioResultCountView.Fields.verifiedCount)
                  .and(UNDERSCORE_ID)
                  .as(PortfolioResultCountView.Fields.portfolioId));

      counts =
          mongoOperations
              .aggregateAndReturn(PortfolioResultCountView.class)
              .by(newAggregation(IpvTradeResultOverlay.class, operations))
              .all()
              .getMappedResults();
    }
    return counts;
  }

  public List<EntryBreakHistory> portfolioDashboardBreaksHistory(
      String dashboardId, IpvExceptionManagementPhase phase, String portfolioId) {
    var operations =
        List.of(
            match(
                where(IpvTradeResultOverlay.Fields.dashboardId)
                    .is(dashboardId)
                    .and(IpvTradeResultOverlay.Fields.phase)
                    .is(phase)
                    .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
                    .is(portfolioId)
                    .and(IpvTradeResultOverlay.Fields.hasBreaks)
                    .is(true)),
            project()
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.key))
                .as(EntryBreakHistory.Fields.uniqueKey)
                .and(
                    Map.itemsOf(
                            ArrayOperators.arrayOf(IpvTradeResultOverlay.Fields.breakTests)
                                .filter()
                                .as(AggregationUtils.AGGREGATION_VAR)
                                .by(
                                    valueOf(
                                            PathUtils.joinPaths(
                                                AggregationUtils.AGGREGATION_VAR,
                                                TradeResultBreak.Fields.providerValue,
                                                EntryResultBreakByProvider.Fields.triggered))
                                        .equalToValue(true)))
                        .as(AggregationUtils.AGGREGATION_VAR)
                        .andApply(
                            e ->
                                new Document()
                                    .append(
                                        BreakTestHistory.Fields.breakTestId,
                                        joinPaths(
                                            AggregationUtils.AGGREGATION_VAR_REF,
                                            TradeResultBreak.Fields.breakTestId))
                                    .append(
                                        BreakTestHistory.Fields.daysBreaking,
                                        joinPaths(
                                            AggregationUtils.AGGREGATION_VAR_REF,
                                            TradeResultBreak.Fields.daysBreaking))))
                .as(EntryBreakHistory.Fields.breaksHistory));

    return mongoOperations
        .aggregateAndReturn(EntryBreakHistory.class)
        .by(newAggregation(IpvTradeResultOverlay.class, operations))
        .all()
        .getMappedResults();
  }

  /**
   * Get overlay data for the specified portfolio, VDG and date. Phase is optional - if not supplied
   * then the latest phase is returned.
   *
   * @param phase optional phase to restrict results to
   * @param portfolioId the portfolio ID to return data for
   * @param ipvDataGroupId the valuation data group to return data for
   * @param date the valuation date of this dashboard to return data for
   * @return the list of provider data and resolved values from a completed dashboard phase
   */
  public List<ResolvedIpvTrade> getResolvedTradeData(
      Optional<IpvExceptionManagementPhase> phase,
      String portfolioId,
      String ipvDataGroupId,
      LocalDate date) {
    Criteria criteria =
        where(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
            .is(portfolioId)
            .and(IpvTradeResultOverlay.Fields.ipvDataGroupId)
            .is(ipvDataGroupId)
            .and(IpvTradeResultOverlay.Fields.valuationDate)
            .is(date)
            .and(IpvTradeResultOverlay.Fields.status)
            .is(EntryResultStatus.VERIFIED);

    List<AggregationOperation> phaseOperations =
        phase
            .map(
                p ->
                    List.<AggregationOperation>of(
                        match(where(IpvTradeResultOverlay.Fields.phase).is(p))))
            .orElse(
                List.of(
                    group(IpvTradeResultOverlay.Fields.phase).addToSet("$$ROOT").as("trades"),
                    sort(Sort.Direction.DESC, UNDERSCORE_ID),
                    limit(1),
                    unwind("trades"),
                    replaceRoot("trades")));

    var operations = ImmutableList.<AggregationOperation>builder();
    operations.add(
        match(criteria),
        project(
                IpvTradeResultOverlay.Fields.resolvedValue,
                IpvTradeResultOverlay.Fields.phase,
                IpvTradeResultOverlay.Fields.valuationDate,
                IpvTradeResultOverlay.Fields.primaryProviderData,
                IpvTradeResultOverlay.Fields.secondaryProviderData,
                IpvTradeResultOverlay.Fields.tertiaryProviderData,
                IpvTradeResultOverlay.Fields.quaternaryProviderData)
            .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.key))
            .as(ResolvedIpvTrade.Fields.key)
            .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.notional))
            .as(ResolvedIpvTrade.Fields.notional));
    operations.addAll(phaseOperations);

    return mongoOperations
        .aggregateAndReturn(ResolvedIpvTrade.class)
        .by(newAggregation(IpvTradeResultOverlay.class, operations.build()))
        .all()
        .getMappedResults();
  }

  public List<ResolvedIpvTrade> getFirstPhaseResults(String dashboardId, String ipvDataGroupId) {
    var operations =
        List.of(
            match(
                where(IpvTradeResultOverlay.Fields.dashboardId)
                    .is(dashboardId)
                    .and(IpvTradeResultOverlay.Fields.ipvDataGroupId)
                    .is(ipvDataGroupId)
                    .and(IpvTradeResultOverlay.Fields.phase)
                    .is(IpvExceptionManagementPhase.OVERLAY_1)
                    .and(IpvTradeResultOverlay.Fields.status)
                    .is(EntryResultStatus.VERIFIED)),
            project(
                    IpvTradeResultOverlay.Fields.resolvedValue,
                    IpvTradeResultOverlay.Fields.phase,
                    IpvTradeResultOverlay.Fields.valuationDate,
                    IpvTradeResultOverlay.Fields.primaryProviderData,
                    IpvTradeResultOverlay.Fields.secondaryProviderData,
                    IpvTradeResultOverlay.Fields.tertiaryProviderData,
                    IpvTradeResultOverlay.Fields.quaternaryProviderData)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.key))
                .as(ResolvedIpvTrade.Fields.key)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.notional))
                .as(ResolvedIpvTrade.Fields.notional));

    return mongoOperations
        .aggregateAndReturn(ResolvedIpvTrade.class)
        .by(newAggregation(IpvTradeResultOverlay.class, operations))
        .all()
        .getMappedResults();
  }

  public Stream<Trade> getFirstPhaseTrades(
      String dashboardId, String portfolioId, List<ProductType> productTypes) {
    var operations =
        List.of(
            match(
                where(IpvTradeResultOverlay.Fields.dashboardId)
                    .is(dashboardId)
                    .and(IpvTradeResultOverlay.Fields.status)
                    .is(EntryResultStatus.VERIFIED)
                    .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
                    .is(portfolioId)
                    .and(IpvTradeResultOverlay.Fields.phase)
                    .is(IpvExceptionManagementPhase.OVERLAY_1)
                    .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.productType))
                    .in(productTypes)),
            replaceRoot(IpvTradeResultOverlay.Fields.trade));

    return mongoOperations
        .aggregateAndReturn(Trade.class)
        .by(newAggregation(IpvTradeResultOverlay.class, operations))
        .stream();
  }

  public Either<ErrorItem, List<EntityId>> overrideResults(
      String dashboardId,
      IpvExceptionManagementPhase phase,
      ApplyOverrideForm form,
      ExceptionManagementEvidence evidence,
      XplainPrincipal currentUser) {
    var currentTs = LocalDateTime.now();
    var auditUser = AuditUser.of(currentUser);
    var resolutions = CollectionUtils.toMap(form.overrides(), IndividualOverrideForm::id);

    var criteria =
        where(IpvTradeResultOverlay.Fields.id)
            .in(resolutions.keySet())
            .and(IpvTradeResultOverlay.Fields.dashboardId)
            .is(dashboardId)
            .and(IpvTradeResultOverlay.Fields.phase)
            .is(phase)
            .and(IpvTradeResultOverlay.Fields.status)
            .is(EntryResultStatus.VERIFIED);

    var overlayItems =
        mongoOperations.query(IpvTradeResultOverlay.class).matching(criteria).stream()
            .map(i -> overrideResult(i, resolutions, evidence, auditUser, currentTs))
            .toList();
    if (overlayItems.size() != resolutions.size()) {
      return Either.left(OPERATION_NOT_ALLOWED.entity(OVERRIDE_NOT_ALLOWED));
    }

    return Either.right(saveResult(overlayItems));
  }

  private IpvTradeResultOverlay overrideResult(
      IpvTradeResultOverlay tradeResultOverlay,
      java.util.Map<String, IndividualOverrideForm> forms,
      ExceptionManagementEvidence evidence,
      AuditUser currentUser,
      LocalDateTime overrideTs) {
    var overrideForm = forms.get(tradeResultOverlay.getId()).override();

    tradeResultOverlay.overrideResult(
        overrideForm.newValue(), overrideForm.comment(), evidence, currentUser, overrideTs);
    return tradeResultOverlay;
  }

  private ImmutableList.Builder<AggregationOperation> portfolioResultItemsProjection(
      String dashboardId,
      IpvExceptionManagementPhase phase,
      Criteria filterCriteria,
      TableFilter filter) {

    var tradePath = joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.tradeDetails);

    return ImmutableList.<AggregationOperation>builder()
        .add(
            match(
                where(IpvTradeResultOverlay.Fields.dashboardId)
                    .is(dashboardId)
                    .and(IpvTradeResultOverlay.Fields.phase)
                    .is(phase)),
            match(filterCriteria),
            tradeDetailsProjection(tradePath)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.productType))
                .as(PortfolioItemFlatView.Fields.tradeInfoTradeType)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.externalTradeId))
                .as(PortfolioItemFlatView.Fields.tradeInfoExternalTradeId)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.entityId))
                .as(PortfolioItemFlatView.Fields.tradeId)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
                .as(PortfolioItemFlatView.Fields.portfolioId)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.key))
                .as(IpvPortfolioItemResultView.Fields.key)
                .and(
                    joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioExternalId))
                .as(IpvPortfolioItemResultView.Fields.portfolioExternalId)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.externalCompanyId))
                .as(IpvPortfolioItemResultView.Fields.externalCompanyId)
                .and(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.externalEntityId))
                .as(IpvPortfolioItemResultView.Fields.externalEntityId)
                .and(
                    projectIfVerified(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.resolution,
                            IpvTradeResultResolution.Fields.resolution)))
                .as(IpvPortfolioItemResultView.Fields.resolution)
                .and(projectIfVerified(IpvTradeResultOverlay.Fields.resolvedValue))
                .as(IpvPortfolioItemResultView.Fields.resolvedValue)
                .and(
                    projectIfVerified(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.resolution,
                            IpvTradeResultResolution.Fields.providerName)))
                .as(IpvPortfolioItemResultView.Fields.resolutionProviderName)
                .and(
                    projectIfVerified(
                        joinPaths(
                            IpvTradeResultOverlay.Fields.resolution,
                            IpvTradeResultResolution.Fields.providerType)))
                .as(IpvPortfolioItemResultView.Fields.resolutionProviderType)
                .and(projectIfVerified(IpvTradeResultOverlay.Fields.modifiedAt))
                .as(IpvPortfolioItemResultView.Fields.verifiedAt)
                .and(projectIfVerified(IpvTradeResultOverlay.Fields.slaDeadline))
                .as(IpvPortfolioItemResultView.Fields.slaDeadline)
                .and(projectIfVerified(IpvTradeResultOverlay.Fields.pricingSlot))
                .as(IpvPortfolioItemResultView.Fields.pricingSlot)
                .and(projectIfVerified(IpvTradeResultOverlay.Fields.calculationCurrency))
                .as(IpvPortfolioItemResultView.Fields.calculationCurrency),
            match(filter.criteria(IpvPortfolioItemResultView.class, conversionService)));
  }

  private ConditionalOperators.Cond projectIfVerified(String fieldPath) {
    var isVerified = where(IpvTradeResultOverlay.Fields.status).is(EntryResultStatus.VERIFIED);
    return when(isVerified).thenValueOf("$" + fieldPath).otherwise(BsonNull.VALUE);
  }

  private Criteria portfolioCriteria(String portfolioId) {
    return where(joinPaths(IpvTradeResultOverlay.Fields.trade, Trade.Fields.portfolioId))
        .is(portfolioId);
  }

  public BreakCountView overlayBreaksCount(List<String> taskIds) {
    return xmProcessExecutionQueryRepository.getVdBreakCountView(
        taskIds, taskDefinitionRepository.getTasksDefinitions(BitemporalDate.newOfNow()));
  }

  private List<EntityId> saveResult(List<IpvTradeResultOverlay> items) {
    items.forEach(mongoOperations::save);
    return items.stream().map(IpvTradeResultOverlay::getId).map(EntityId::entityId).toList();
  }
}
