package com.solum.xplain.xm.excmngmt.rulesipv.value;

import com.google.common.collect.ImmutableList;
import java.util.List;
import java.util.Objects;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class IpvBreakTestGroupProvider implements DefaultGroupSequenceProvider<IpvBreakTestForm> {

  @Override
  public List<Class<?>> getValidationGroups(IpvBreakTestForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(IpvBreakTestForm.class);
    if (form == null || Objects.isNull(form.getType())) {
      return builder.build();
    }

    builder.add(
        switch (form.getType()) {
          case STALE_VALUE -> StaleTestGroup.class;
          case VALUE -> ValueTestGroup.class;
          case DAY_TO_DAY -> DayToDayGroup.class;
          case DAY_TO_DAY_SIGN -> DayToDaySignTestGroup.class;
          case PRIMARY_VS_SECONDARY, PRIMARY_VS_TERTIARY, PRIMARY_VS_QUATERNARY ->
              ProviderToProviderGroup.class;
          case NULL_VALUE -> NullTestGroup.class;
          case ZERO_VALUE -> ZeroTestGroup.class;
        });

    return builder.build();
  }
}
