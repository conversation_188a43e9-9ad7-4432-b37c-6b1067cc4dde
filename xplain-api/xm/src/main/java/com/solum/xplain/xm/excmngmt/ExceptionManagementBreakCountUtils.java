package com.solum.xplain.xm.excmngmt;

import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_SUBMISSION;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.arrayOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Cond.when;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import java.util.List;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;

public class ExceptionManagementBreakCountUtils {

  private ExceptionManagementBreakCountUtils() {}

  public static ConditionalOperators.Cond countByStatusOperator(EntryResultStatus... statuses) {
    return when(new Criteria()
            .andOperator(
                where(InstrumentResultOverlay.Fields.status).in(List.of(statuses)),
                where(InstrumentResultOverlay.Fields.hasBreaks).is(true)))
        .then(1)
        .otherwise(0);
  }

  public static ConditionalOperators.Cond breaksCountOperator() {
    return when(where(InstrumentResultOverlay.Fields.hasBreaks).is(true)).then(1).otherwise(0);
  }

  public static ProjectionOperation projectBaseCounts(String breakTestPath) {
    return project()
        .and(breaksCountOperator())
        .as(BreakCountView.Fields.breaksCount)
        .and(arrayOf(breakTestPath).length())
        .as(BreakCountView.Fields.totalTestsCount)
        .and(InstrumentResultOverlay.Fields.appliedTestsCount)
        .as(BreakCountView.Fields.appliedTestsCount)
        .and(countByStatusOperator(VERIFIED, WAITING_SUBMISSION))
        .as(BreakCountView.Fields.verifiedCount)
        .and(countByStatusOperator(WAITING_APPROVAL, VERIFIED, WAITING_SUBMISSION))
        .as(BreakCountView.Fields.resolvedCount);
  }

  public static GroupOperation groupForBreakCounts() {
    return group()
        .count()
        .as(BreakCountView.Fields.totalCount)
        .sum(BreakCountView.Fields.resolvedCount)
        .as(BreakCountView.Fields.resolvedCount)
        .sum(BreakCountView.Fields.verifiedCount)
        .as(BreakCountView.Fields.verifiedCount)
        .sum(BreakCountView.Fields.breaksCount)
        .as(BreakCountView.Fields.breaksCount)
        .sum(BreakCountView.Fields.totalTestsCount)
        .as(BreakCountView.Fields.totalTestsCount)
        .sum(BreakCountView.Fields.appliedTestsCount)
        .as(BreakCountView.Fields.appliedTestsCount);
  }
}
