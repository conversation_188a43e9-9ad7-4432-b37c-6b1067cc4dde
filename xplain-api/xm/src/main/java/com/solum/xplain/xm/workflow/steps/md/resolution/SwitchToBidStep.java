package com.solum.xplain.xm.workflow.steps.md.resolution;

import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SwitchToBidStep implements ServiceStepExecutor<MdOverlayState, MdOverlayContext> {

  @Override
  public void runStep(StepStateOps<MdOverlayState, MdOverlayContext> ops) {
    if (ops.getContext().pricePoint() == ValueBidAskType.BID) {
      ops.throwError(Errors.currentPricePointError(ops.getContext()));
    }

    ProviderData providerDataForBid = ops.getContext().primaryProviderData(ValueBidAskType.BID);

    if (providerDataForBid == null || providerDataForBid.getValue() == null) {
      ops.throwError(Errors.resolvedValueIsEmpty(ops.getContext()));
    } else {
      AttributedValue valuePendingApproval =
          new AttributedValue(
              providerDataForBid.getValue(), null, providerDataForBid.getProvider());

      ops.setOutcome(
          new MutablePropertyValues()
              .add(MdOverlayState.Fields.valuePendingApproval, valuePendingApproval)
              .add(MdOverlayState.Fields.entryStatus, EntryResultStatus.WAITING_APPROVAL));
    }
  }
}
