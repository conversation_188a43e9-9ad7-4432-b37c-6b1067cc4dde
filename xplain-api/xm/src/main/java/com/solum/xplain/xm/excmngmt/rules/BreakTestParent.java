package com.solum.xplain.xm.excmngmt.rules;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "parentTest")
@FieldNameConstants
public class BreakTestParent implements Serializable {

  private String parentId;
  private String name;
}
