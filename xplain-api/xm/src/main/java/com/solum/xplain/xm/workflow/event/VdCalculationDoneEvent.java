package com.solum.xplain.xm.workflow.event;

import java.io.Serializable;

/**
 * This event is fired when a calculation relating to a valuation data dashboard has completed. It
 * is published to all nodes in the cluster on receiving a {@link
 * com.solum.xplain.calculation.events.TradesCalculationFinishedEvent}, that relates to a valuation
 * data worflow dashboard, on any single node.
 *
 * @param calculationId the id of the calculation which completed
 */
public record VdCalculationDoneEvent(String calculationId) implements Serializable {}
