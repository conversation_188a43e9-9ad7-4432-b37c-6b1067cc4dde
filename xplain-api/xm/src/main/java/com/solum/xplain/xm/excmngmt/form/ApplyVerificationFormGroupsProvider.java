package com.solum.xplain.xm.excmngmt.form;

import com.google.common.collect.ImmutableList;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

@NullMarked
public class ApplyVerificationFormGroupsProvider
    implements DefaultGroupSequenceProvider<ApplyVerificationForm> {
  @Override
  public List<Class<?>> getValidationGroups(@Nullable ApplyVerificationForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(ApplyVerificationForm.class);
    if (form == null) {
      return builder.build();
    }

    return builder.build();
  }
}
