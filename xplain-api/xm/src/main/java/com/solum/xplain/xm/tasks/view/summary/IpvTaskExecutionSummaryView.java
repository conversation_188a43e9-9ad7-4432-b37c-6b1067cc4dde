package com.solum.xplain.xm.tasks.view.summary;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.tasks.view.ExceptionManagementTaskExecutionView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Data
@ToString(callSuper = true)
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class IpvTaskExecutionSummaryView extends ExceptionManagementTaskExecutionView {
  @NonNull private IpvExceptionManagementPhase type;
  @NonNull private TradeFilterSummary filterSummary;

  @NonNull private String ipvDataGroupId;
  @NonNull private String ipvDataGroupName;
  @NonNull private PricingSlot pricingSlot;
  @Nullable private SlaDeadline slaDeadline;

  public IpvTaskExecutionSummaryView editable(XplainPrincipal user) {
    this.setEditable(isEditableForUser(user));
    return this;
  }
}
