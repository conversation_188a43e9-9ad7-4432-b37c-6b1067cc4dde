package com.solum.xplain.xm.tasks.view.summary;

import static com.solum.xplain.xm.tasks.view.summary.TaskSummaryUtils.ALL_FILTERS;
import static com.solum.xplain.xm.tasks.view.summary.TaskSummaryUtils.FILTER_JOIN_ON;
import static com.solum.xplain.xm.tasks.view.summary.TaskSummaryUtils.NO_FILTERS;
import static com.solum.xplain.xm.tasks.view.summary.TaskSummaryUtils.filterSummary;
import static com.solum.xplain.xm.tasks.view.summary.TaskSummaryUtils.fxPairSummary;

import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public record AssetFilterSummary(
    String assetClassSummary,
    String rateCcysSummary,
    String irInstrumentsSummary,
    String sectorsSummary,
    String fxPairsSummary) {

  public static AssetFilterSummary allFilters() {
    return new AssetFilterSummary(ALL_FILTERS, ALL_FILTERS, ALL_FILTERS, ALL_FILTERS, ALL_FILTERS);
  }

  public static AssetFilterSummary summaryOf(AssetFilter assetFilter) {
    var assetClassSummary =
        filterSummary(
            assetFilter.getAssetClasses(),
            AssetClass::getLabel,
            AssetFilterSummary::assetClassNameResolver);
    var rateCcysSummary = NO_FILTERS;
    var irInstrumentsSummary = NO_FILTERS;
    var sectorsSummary = NO_FILTERS;
    var fxPairsSummary = NO_FILTERS;

    var assetGroupName = assetClassNameResolver(assetFilter.getAssetClasses());

    if (assetGroupName.equals(CoreAssetGroup.CREDIT.getLabel())) {
      sectorsSummary = filterSummary(assetFilter.getCreditSectors(), CreditSector::getLabel);

    } else if (assetGroupName.equals(CoreAssetGroup.FX.getLabel())) {
      fxPairsSummary = fxPairSummary(assetFilter.getFxPairs());

    } else if (assetGroupName.equals(CoreAssetGroup.RATES.getLabel())) {
      rateCcysSummary = filterSummary(assetFilter.getRateCcys(), Function.identity());
      irInstrumentsSummary =
          filterSummary(assetFilter.getIrInstruments(), InstrumentType::getLabel);
    } else {
      rateCcysSummary = ALL_FILTERS;
      irInstrumentsSummary = ALL_FILTERS;
      sectorsSummary = ALL_FILTERS;
      fxPairsSummary = ALL_FILTERS;
    }

    return new AssetFilterSummary(
        assetClassSummary, rateCcysSummary, irInstrumentsSummary, sectorsSummary, fxPairsSummary);
  }

  private static String assetClassNameResolver(List<AssetClass> assetClasses) {
    return assetClasses.stream()
        .map(AssetClass::getGroup)
        .map(AssetGroup::getLabel)
        .distinct()
        .collect(Collectors.joining(FILTER_JOIN_ON));
  }
}
