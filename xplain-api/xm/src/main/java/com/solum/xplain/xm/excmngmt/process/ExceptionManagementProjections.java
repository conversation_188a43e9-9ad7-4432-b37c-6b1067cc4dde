package com.solum.xplain.xm.excmngmt.process;

import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR;
import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR_REF;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.mongo.TernaryOperations.onlyWhenNotNull;
import static com.solum.xplain.xm.excmngmt.processipv.ThresholdLevelProjections.getIntegerAsThresholdLevelName;
import static org.springframework.data.mongodb.core.aggregation.AccumulatorOperators.Max.maxOf;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.ROOT;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.AggregationVariable.PREFIX;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.ArrayToObject.arrayToObject;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.Filter.filter;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.Reduce.Variable.THIS;
import static org.springframework.data.mongodb.core.aggregation.ComparisonOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Switch.CaseOperator.when;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.ifNull;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.switchCases;
import static org.springframework.data.mongodb.core.aggregation.ConvertOperators.ToObjectId.toObjectId;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.ObjectOperators.MergeObjects.merge;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.Let.ExpressionVariable.newVariable;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.mapItemsOf;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.data.Instrument;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.data.NonRequiredProviderData;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentResultBreakView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentResultResolutionView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentView;
import com.solum.xplain.xm.excmngmt.process.view.ProviderDataView;
import com.solum.xplain.xm.tasks.ExceptionManagementTaskExecution;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.bson.BsonNull;
import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators.ArrayElemAt;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators.Reduce;
import org.springframework.data.mongodb.core.aggregation.BooleanOperators;
import org.springframework.data.mongodb.core.aggregation.BooleanOperators.And;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.EvaluationOperators.Expr;
import org.springframework.data.mongodb.core.aggregation.LookupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExceptionManagementProjections {
  public static final String REF_DATA = "refData";
  public static final String TASK_DATA = "taskData";
  private static final String CURVE_CONFIGURATION = "curveConfiguration";
  private static final String LEGAL_ENTITY = "legalEntity";
  private static final List<Object> EMPTY_LIST = List.of();

  /**
   * Projection from {@link com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary}
   * to {@link com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView}.
   *
   * @return projection to include in an aggregation pipeline
   */
  public static ProjectionOperation instrumentPreliminaryResultViewProjection() {
    String instrument = InstrumentResultPreliminary.Fields.instrument;

    // spotless:off
    return addPreliminaryProviderDataFields(addBreakTestFields(addInstrumentFields(project(
            InstrumentResultPreliminary.Fields.id,
            InstrumentResultPreliminary.Fields.valuationDate,
            InstrumentResultPreliminary.Fields.status,
            // InstrumentResultResolution -> InstrumentResultResolutionView (identical)
            InstrumentResultPreliminary.Fields.resolution,
            InstrumentResultPreliminary.Fields.modifiedBy,
            InstrumentResultPreliminary.Fields.modifiedAt,
            InstrumentResultPreliminary.Fields.providerData,
            // List<EntryResultStatusHistory> -> List<EntryResultStatusHistoryView> (identical)
            InstrumentResultPreliminary.Fields.previousStatuses)
            .and(deriveWorkflowStatus(joinPaths(TASK_DATA, ExceptionManagementTaskExecution.Fields.status), InstrumentResultPreliminary.Fields.status))
            .as(InstrumentPreliminaryResultView.Fields.stepStatus)
            .and(joinPaths(InstrumentResultPreliminary.Fields.providerData, ProviderData.Fields.bidAskType)).as(InstrumentPreliminaryResultView.Fields.bidAskType)
            .and(getIntegerAsThresholdLevelName(InstrumentResultPreliminary.Fields.maxTriggeredThresholdLevel)).as(InstrumentResultPreliminary.Fields.maxTriggeredThresholdLevel)
        , instrument), InstrumentResultPreliminary.Fields.breakTests, InstrumentPreliminaryResultView.Fields.breakTests), InstrumentResultPreliminary.Fields.allProvidersData, joinPaths(InstrumentResultPreliminary.Fields.providerData, ProviderData.Fields.provider));
    // spotless:on
  }

  /**
   * Projection from {@link com.solum.xplain.workflow.entity.ProcessExecution} with {@link
   * com.solum.xplain.xm.workflow.state.MdPreliminaryState} and {@link
   * com.solum.xplain.xm.workflow.state.MdPreliminaryContext} to {@link
   * com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView}.
   *
   * @return projection to include in an aggregation pipeline
   */
  public static ProjectionOperation wfInstrumentPreliminaryResultViewProjection() {
    var instrumentPath =
        joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.instrument);
    var breakTestsPath =
        joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.breakTestResults);

    ProjectionOperation baseFields =
        project(ProcessExecution.Fields.modifiedAt)
            .and(ProcessExecution.Fields.businessKey)
            .as(UNDERSCORE_ID)
            .and(ProcessExecution.Fields.businessKey)
            .as(InstrumentPreliminaryResultView.Fields.id)
            .and(
                joinPaths(
                    ProcessExecution.Fields.context,
                    MdPreliminaryContext.Fields.stateDate,
                    BitemporalDate.Fields.actualDate))
            .as(InstrumentPreliminaryResultView.Fields.valuationDate)
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdPreliminaryState.Fields.entryStatus))
            .as(InstrumentPreliminaryResultView.Fields.status)
            .and(ProcessExecution.Fields.status)
            .as(InstrumentPreliminaryResultView.Fields.stepStatus)
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdPreliminaryState.Fields.resolution))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolution))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdPreliminaryState.Fields.providerName))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.provider))
            .and(
                onlyWhenNotNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState,
                            MdPreliminaryState.Fields.resolution))
                    .thenValueOf(
                        // Try in order:
                        // 1) value pending approval (set after submitting resolution, cleared when
                        //    verified/rejected)
                        // 2) manual resolution value (set after applying resolution¹, if manual
                        //    override²),
                        // ¹: actually set on submit, but overridden using outcome when viewing as
                        //    resolver
                        // ²: null for other resolution types, so will display as empty
                        ConditionalOperators.IfNull.ifNull(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    MdPreliminaryState.Fields.valuePendingApproval,
                                    AttributedValue.Fields.value))
                            .thenValueOf(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    MdPreliminaryState.Fields.manualResolutionValue))))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.value))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState,
                    MdPreliminaryState.Fields.resolutionComment))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolutionComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState,
                    MdPreliminaryState.Fields.resolutionEvidence))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolutionEvidence))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState,
                    MdPreliminaryState.Fields.approvalComment))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.approvalComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState,
                    MdPreliminaryState.Fields.approvalEvidence))
            .as(
                joinPaths(
                    InstrumentPreliminaryResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.approvalEvidence))
            .and(
                ConditionalOperators.IfNull.ifNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState,
                            MdPreliminaryState.Fields.approver))
                    .thenValueOf(
                        joinPaths(
                            ProcessExecution.Fields.currentState,
                            MdPreliminaryState.Fields.resolver)))
            .as(InstrumentPreliminaryResultView.Fields.modifiedBy)
            .and("[]") // do not return history as the front end doesn't want it and it is expensive
            .as(InstrumentPreliminaryResultView.Fields.previousStatuses)
            .and(
                ArrayOperators.ArrayElemAt.arrayOf(
                        ArrayOperators.Filter.filter(
                                joinPaths(
                                    ProcessExecution.Fields.context,
                                    MdPreliminaryContext.Fields.allProviderData))
                            .as(AGGREGATION_VAR)
                            .by(
                                BooleanOperators.And.and(
                                    valueOf(varField(ProviderData.Fields.provider))
                                        .equalTo(
                                            joinPaths(
                                                ProcessExecution.Fields.context,
                                                MdPreliminaryContext.Fields.provider)),
                                    valueOf(varField(ProviderData.Fields.bidAskType))
                                        .equalTo(
                                            joinPaths(
                                                ProcessExecution.Fields.context,
                                                MdPreliminaryContext.Fields.pricePoint)))))
                    .elementAt(0))
            .as(InstrumentPreliminaryResultView.Fields.providerData)
            .and(joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.pricePoint))
            .as(InstrumentPreliminaryResultView.Fields.bidAskType)
            .and(
                getIntegerAsThresholdLevelName(
                    maxOf(
                        joinPaths(
                            breakTestsPath,
                            InstrumentResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.triggeredThresholdLevel))))
            .as(InstrumentPreliminaryResultView.Fields.maxTriggeredThresholdLevel);

    return addPreliminaryProviderDataFields(
        addBreakTestFields(
            addInstrumentFields(baseFields, instrumentPath),
            breakTestsPath,
            InstrumentPreliminaryResultView.Fields.breakTests),
        joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.allProviderData),
        joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.provider));
    // spotless:on
  }

  /**
   * Projection from {@link com.solum.xplain.xm.excmngmt.process.data.NonRequiredProviderData} to
   * {@link com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView}.
   *
   * @return projection to include in an aggregation pipeline
   */
  public static ProjectionOperation preliminaryNonRequiredDataView() {
    // spotless:off
        var instrumentPath = NonRequiredProviderData.Fields.instrument;

    ProjectionOperation baseFields =
        project(NonRequiredProviderData.Fields.modifiedAt)
            .and(NonRequiredProviderData.Fields.id)
            .as(InstrumentPreliminaryResultView.Fields.id)
            .and(joinPaths(NonRequiredProviderData.Fields.stateDate,
                BitemporalDate.Fields.actualDate))
            .as(InstrumentPreliminaryResultView.Fields.valuationDate)
            .and(NonRequiredProviderData.Fields.entryStatus)
            .as(InstrumentPreliminaryResultView.Fields.status)
            .and(WorkflowStatus.DONE.name())
            .as(InstrumentPreliminaryResultView.Fields.stepStatus)
            .and("[]") // do not return history as the front end doesn't want it and it is expensive
            .as(InstrumentPreliminaryResultView.Fields.previousStatuses)
            .and(AggregationExpression.from(() -> new Document("$literal", EMPTY_LIST))) //empty array as grid will not display otherwise
            .as(InstrumentPreliminaryResultView.Fields.breakTests)
            .and(
                ArrayOperators.ArrayElemAt.arrayOf(
                        ArrayOperators.Filter.filter(NonRequiredProviderData.Fields.allProviderData)
                            .as(AGGREGATION_VAR)
                            .by(
                                BooleanOperators.And.and(
                                    valueOf(varField(ProviderData.Fields.provider))
                                        .equalTo(NonRequiredProviderData.Fields.provider),
                                    valueOf(varField(ProviderData.Fields.bidAskType))
                                        .equalTo(NonRequiredProviderData.Fields.pricePoint))))
                    .elementAt(0))
            .as(InstrumentPreliminaryResultView.Fields.providerData)
            .and(NonRequiredProviderData.Fields.pricePoint)
            .as(InstrumentPreliminaryResultView.Fields.bidAskType);

    return addPreliminaryNonRequiredProviderDataFields(
        addInstrumentFields(baseFields, instrumentPath),
        NonRequiredProviderData.Fields.allProviderData,
        NonRequiredProviderData.Fields.provider);
    // spotless:on
  }

  private static ProjectionOperation addInstrumentFields(
      ProjectionOperation projection, String instrumentPath) {
    String instrumentView = InstrumentPreliminaryResultView.Fields.instrument;

    return projection
        .and(joinPaths(instrumentPath, Instrument.Fields.key))
        .as(joinPaths(instrumentView, InstrumentView.Fields.key))
        .and(joinPaths(instrumentPath, Instrument.Fields.assetClass))
        .as(joinPaths(instrumentView, InstrumentView.Fields.assetClass))
        .and(joinPaths(instrumentPath, Instrument.Fields.assetName))
        .as(joinPaths(instrumentView, InstrumentView.Fields.assetName))
        .and(joinPaths(instrumentPath, Instrument.Fields.underlying))
        .as(joinPaths(instrumentView, InstrumentView.Fields.underlying))
        .and(joinPaths(instrumentPath, Instrument.Fields.sector))
        .as(joinPaths(instrumentView, InstrumentView.Fields.sector))
        .and(joinPaths(instrumentPath, Instrument.Fields.nodeInstrument))
        .as(joinPaths(instrumentView, InstrumentView.Fields.nodeInstrument))
        .and(joinPaths(instrumentPath, Instrument.Fields.assetClassGroup))
        .as(joinPaths(instrumentView, InstrumentView.Fields.assetClassGroup))
        .and(
            ConditionalOperators.when(
                    valueOf(joinPaths(instrumentPath, Instrument.Fields.assetClass))
                        .equalToValue(CoreAssetClass.IR_RATE))
                .thenValueOf(joinPaths(instrumentPath, Instrument.Fields.instrumentType))
                .otherwise(BsonNull.VALUE))
        .as(joinPaths(instrumentView, InstrumentView.Fields.instrumentType))
        .and(joinPaths(instrumentPath, Instrument.Fields.instrumentTypeSort))
        .as(joinPaths(instrumentView, InstrumentView.Fields.instrumentTypeSort))
        .and(joinPaths(instrumentPath, Instrument.Fields.parsedTenor))
        .as(joinPaths(instrumentView, InstrumentView.Fields.parsedTenor));
  }

  private static ProjectionOperation addBreakTestFields(
      ProjectionOperation projection, String breakTestsPath, String alias) {
    return projection
        .and(mapItemsOf(breakTestsPath).as(AGGREGATION_VAR).andApply(breakTestViewMapping()))
        .as(alias);
  }

  private static ProjectionOperation addPreliminaryNonRequiredProviderDataFields(
      ProjectionOperation projection, String allProviderDataPath, String providerPath) {
    return projection
        .and(dataForProvider(allProviderDataPath, providerPath))
        .as(InstrumentPreliminaryResultView.Fields.thisProviderData);
  }

  private static ProjectionOperation addPreliminaryProviderDataFields(
      ProjectionOperation projection, String allProvidersDataPath, String providerPath) {
    return projection
        .and(
            Reduce.arrayOf(allProvidersDataPath)
                .withInitialValue(new Document())
                .reduce(
                    merge(Reduce.Variable.VALUE)
                        .mergeWithValuesOf(
                            keyValuePair(
                                thisField(ProviderData.Fields.provider),
                                Reduce.arrayOf(
                                        filter(rootField(allProvidersDataPath))
                                            .as(AGGREGATION_VAR)
                                            .by(
                                                valueOf(varField(ProviderData.Fields.provider))
                                                    .equalTo(
                                                        thisField(ProviderData.Fields.provider))))
                                    .withInitialValue(
                                        new Document()
                                            .append(
                                                ProviderDataView.Fields.provider,
                                                thisField(ProviderData.Fields.provider)))
                                    .reduce(
                                        merge(Reduce.Variable.VALUE)
                                            .mergeWithValuesOf(partialProviderDataView()))))))
        .as(InstrumentPreliminaryResultView.Fields.allProvidersData)
        .and(dataForProvider(allProvidersDataPath, providerPath))
        .as(InstrumentPreliminaryResultView.Fields.thisProviderData);
  }

  /**
   * Lookup stage to get reference data from {@link com.solum.xplain.xm.dashboards.entity.Dashboard}
   * for an {@link InstrumentResultOverlay}, ready for {@link
   * #instrumentOverlayResultViewProjection()}. The reference data will contain the matching {@link
   * com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver} as {@value
   * #CURVE_CONFIGURATION} and {@link
   * com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver} as {@value
   * #LEGAL_ENTITY} for the record, taken from {@code
   * mdExceptionManagementSetup.curveConfigurationResolvers} and {@code
   * trsMdExceptionManagementSetup.entitySettings} respectively.
   *
   * <p>This lookup should be unwound before using the projection. Note that this is optimised to
   * avoid intermediate data as part of <a
   * href='https://www.mongodb.com/docs/manual/core/aggregation-pipeline-optimization/#-lookup----unwind-coalescence'>Aggregation
   * Pipeline Optimization</a>.
   *
   * @return lookup stage to create reference data property as {@value #REF_DATA}.
   */
  public static LookupOperation instrumentOverlayRefDataLookup() {
    // spotless:off
    return lookup().from(Dashboard.COLLECTION_NAME)
        .let(
            newVariable(InstrumentResultOverlay.Fields.dashboardId).forExpression(toObjectId("$" + InstrumentResultOverlay.Fields.dashboardId)),
            newVariable(InstrumentResultOverlay.Fields.curveConfigurationId).forExpression(toObjectId("$" + InstrumentResultOverlay.Fields.curveConfigurationId)),
            newVariable(InstrumentResultOverlay.Fields.companyId).forField(InstrumentResultOverlay.Fields.companyId),
            newVariable(InstrumentResultOverlay.Fields.legalEntityId).forField(InstrumentResultOverlay.Fields.legalEntityId)
        )
        .pipeline(
            match(
                Expr.valueOf(valueOf(UNDERSCORE_ID).equalTo(PREFIX + InstrumentResultOverlay.Fields.dashboardId))
            ),
            project()
                .andExclude(UNDERSCORE_ID)
                .and(ArrayElemAt.arrayOf(
                    filter(joinPaths(Dashboard.Fields.mdExceptionManagementSetup, MdExceptionManagementSetup.Fields.curveConfigurationResolvers))
                        .as(AGGREGATION_VAR)
                        .by(
                            valueOf(varField(UNDERSCORE_ID))
                                .equalTo(PREFIX + InstrumentResultOverlay.Fields.curveConfigurationId))
                ).elementAt(0))
                .as(CURVE_CONFIGURATION)
                .and(ArrayElemAt.arrayOf(
                    filter(joinPaths(Dashboard.Fields.trsMdExceptionManagementSetup, TrsMdExceptionManagementSetup.Fields.entitySettings))
                        .as(AGGREGATION_VAR)
                        .by(And.and(
                                valueOf(varField(LegalEntityTrsDataProviderResolver.Fields.companyId))
                                    .equalTo(PREFIX + InstrumentResultOverlay.Fields.companyId),
                                valueOf(varField(LegalEntityTrsDataProviderResolver.Fields.legalEntityId))
                                    .equalTo(PREFIX + InstrumentResultOverlay.Fields.legalEntityId)
                            )
                        )
                ).elementAt(0))
                .as(LEGAL_ENTITY)
        )
        .as(REF_DATA);
    // spotless:on
  }

  public static LookupOperation taskRefDataLookup(String taskIdField) {
    // spotless:off
    return lookup().from(TaskExecution.TASK_EXECUTION_COLLECTION)
        .let(
            newVariable(taskIdField).forExpression(toObjectId("$" + taskIdField))
        ).pipeline(
            match(
                Expr.valueOf(valueOf(UNDERSCORE_ID).equalTo(PREFIX + taskIdField))
            ),
            project(ExceptionManagementTaskExecution.Fields.status)
                .andExclude(UNDERSCORE_ID)
        )
        .as(TASK_DATA);
    //spotless:on
  }

  /**
   * Projection from {@link com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay} to
   * {@link com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView}. {@value
   * #REF_DATA} property must have been added to {@code InstrumentResultOverlay} using {@link
   * #instrumentOverlayRefDataLookup()} (and unwind) prior to this projection.
   *
   * @return projection to include in an aggregation pipeline
   */
  public static ProjectionOperation instrumentOverlayResultViewProjection() {
    String instrument = InstrumentResultOverlay.Fields.instrument;
    String instrumentView = InstrumentOverlayResultView.Fields.instrument;

    // spotless:off
    return project(
        InstrumentResultOverlay.Fields.id,
        InstrumentResultOverlay.Fields.curveConfigurationId,
        InstrumentResultOverlay.Fields.companyId,
        InstrumentResultOverlay.Fields.legalEntityId,
        InstrumentResultOverlay.Fields.status,
        // InstrumentResultResolution -> InstrumentResultResolutionView (identical)
        InstrumentResultOverlay.Fields.resolution,
        InstrumentResultOverlay.Fields.previousOverlayValue,
        InstrumentResultOverlay.Fields.modifiedBy,
        InstrumentResultOverlay.Fields.modifiedAt,
        // List<EntryResultStatusHistory> -> List<EntryResultStatusHistoryView> (identical)
        InstrumentResultOverlay.Fields.previousStatuses)
        .and(deriveWorkflowStatus(joinPaths(TASK_DATA, ExceptionManagementTaskExecution.Fields.status), InstrumentResultOverlay.Fields.status))
        .as(InstrumentOverlayResultView.Fields.stepStatus)
        // Instrument fields
        .and(joinPaths(instrument, Instrument.Fields.key)).as(joinPaths(instrumentView, InstrumentView.Fields.key))
        .and(joinPaths(instrument, Instrument.Fields.assetClassGroup)).as(joinPaths(instrumentView, InstrumentView.Fields.assetClassGroup))
        .and(joinPaths(instrument, Instrument.Fields.assetClass)).as(joinPaths(instrumentView, InstrumentView.Fields.assetClass))
        .and(joinPaths(instrument, Instrument.Fields.assetName)).as(joinPaths(instrumentView, InstrumentView.Fields.assetName))
        .and(joinPaths(instrument, Instrument.Fields.underlying)).as(joinPaths(instrumentView, InstrumentView.Fields.underlying))
        .and(ConditionalOperators.when(
              valueOf(joinPaths(instrument, Instrument.Fields.assetClass))
                .equalToValue(CoreAssetClass.IR_RATE))
           .thenValueOf(joinPaths(instrument, Instrument.Fields.instrumentType))
           .otherwise(BsonNull.VALUE))
        .as(joinPaths(instrumentView, InstrumentView.Fields.instrumentType))
        .and(joinPaths(instrument, Instrument.Fields.instrumentTypeSort)).as(joinPaths(instrumentView, InstrumentView.Fields.instrumentTypeSort))
        .and(joinPaths(instrument, Instrument.Fields.sector)).as(joinPaths(instrumentView, InstrumentView.Fields.sector))
        .and(joinPaths(instrument, Instrument.Fields.nodeInstrument)).as(joinPaths(instrumentView, InstrumentView.Fields.nodeInstrument))
        .and(joinPaths(instrument, Instrument.Fields.parsedTenor)).as(joinPaths(instrumentView, InstrumentView.Fields.parsedTenor))
        // Reference data fields
        .and(joinPaths(REF_DATA, CURVE_CONFIGURATION, CurveConfigurationInstrumentResolver.Fields.name))
        .as(InstrumentOverlayResultView.Fields.curveConfigurationName)
        .and(joinPaths(REF_DATA, LEGAL_ENTITY, LegalEntityTrsDataProviderResolver.Fields.companyExternalId))
        .as(InstrumentOverlayResultView.Fields.companyExternalId)
        .and(joinPaths(REF_DATA, LEGAL_ENTITY, LegalEntityTrsDataProviderResolver.Fields.legalEntityExternalId))
        .as(InstrumentOverlayResultView.Fields.legalEntityExternalId)
        // Break test fields
        .and(
            mapItemsOf(InstrumentResultOverlay.Fields.breakTests)
                .as(AGGREGATION_VAR)
                .andApply(breakTestViewMapping()))
        .as(InstrumentOverlayResultView.Fields.breakTests)
        .and(getIntegerAsThresholdLevelName(InstrumentResultOverlay.Fields.maxTriggeredThresholdLevel)).as(InstrumentResultOverlay.Fields.maxTriggeredThresholdLevel)
        // Provider data fields
        .and(joinPaths(InstrumentResultOverlay.Fields.primaryProviderData, ProviderData.Fields.bidAskType)).as(InstrumentOverlayResultView.Fields.bidAskType)
        .and(
            dataForProvider(InstrumentResultOverlay.Fields.allProvidersData,
                joinPaths(InstrumentResultOverlay.Fields.primaryProviderData, ProviderData.Fields.provider)))
        .as(InstrumentOverlayResultView.Fields.primaryProviderData)
        .and(
            dataForProvider(InstrumentResultOverlay.Fields.allProvidersData,
                joinPaths(InstrumentResultOverlay.Fields.secondaryProviderData, ProviderData.Fields.provider)))
        .as(InstrumentOverlayResultView.Fields.secondaryProviderData);
    // spotless:on
  }

  public static AggregationExpression deriveWorkflowStatus(
      String taskStatusField, String entryStatusField) {
    return switchCases(
            when(valueOf(taskStatusField).equalToValue(TaskExecutionStatus.IN_RESOLUTION.name()))
                .then(
                    switchCases(
                            when(valueOf(entryStatusField)
                                    .equalToValue(EntryResultStatus.WAITING_APPROVAL))
                                .then(WorkflowStatus.FINALIZING),
                            when(valueOf(entryStatusField)
                                    .equalToValue(EntryResultStatus.WAITING_RESOLUTION))
                                .then(WorkflowStatus.ACTIVE),
                            when(valueOf(entryStatusField).equalToValue(EntryResultStatus.REJECTED))
                                .then(WorkflowStatus.ACTIVE))
                        .defaultTo(BsonNull.VALUE)),
            when(valueOf(taskStatusField).equalToValue(TaskExecutionStatus.IN_APPROVAL.name()))
                .then(
                    switchCases(
                            when(valueOf(entryStatusField)
                                    .equalToValue(EntryResultStatus.WAITING_APPROVAL))
                                .then(WorkflowStatus.ACTIVE),
                            when(valueOf(entryStatusField)
                                    .equalToValue(EntryResultStatus.WAITING_SUBMISSION))
                                .then(WorkflowStatus.FINALIZING),
                            when(valueOf(entryStatusField).equalToValue(EntryResultStatus.REJECTED))
                                .then(WorkflowStatus.FINALIZING))
                        .defaultTo(BsonNull.VALUE)))
        .defaultTo(BsonNull.VALUE);
  }

  /**
   * Projection from {@link com.solum.xplain.workflow.entity.ProcessExecution} with {@link
   * com.solum.xplain.xm.workflow.state.MdOverlayState} and {@link
   * com.solum.xplain.xm.workflow.state.MdOverlayContext} to {@link
   * com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView}.
   *
   * @return projection to include in an aggregation pipeline
   */
  public static ProjectionOperation wfInstrumentOverlayResultViewProjection() {
    var instrumentPath =
        joinPaths(ProcessExecution.Fields.context, MdOverlayContext.Fields.instrument);
    var breakTestsPath =
        joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.breakTestResults);
    var preliminaryProviderDataPath =
        joinPaths(ProcessExecution.Fields.context, MdOverlayContext.Fields.allPreliminaryData);

    ProjectionOperation baseFields =
        project(ProcessExecution.Fields.modifiedAt)
            .and(ProcessExecution.Fields.businessKey)
            .as(UNDERSCORE_ID)
            .and(ProcessExecution.Fields.businessKey)
            .as(InstrumentOverlayResultView.Fields.id)
            .and(joinPaths(ProcessExecution.Fields.context, MdOverlayContext.Fields.curveConfigId))
            .as(InstrumentOverlayResultView.Fields.curveConfigurationId)
            .and(
                joinPaths(ProcessExecution.Fields.context, MdOverlayContext.Fields.curveConfigName))
            .as(InstrumentOverlayResultView.Fields.curveConfigurationName)
            // TODO: SXSD-9012 TRS companyId, companyExternalId, legalEntityId,
            // legalEntityExternalId
            .and(joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.entryStatus))
            .as(InstrumentOverlayResultView.Fields.status)
            .and(ProcessExecution.Fields.status)
            .as(InstrumentOverlayResultView.Fields.stepStatus)
            .and(joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.resolution))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolution))
            .and(
                joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.providerName))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.provider))
            .and(
                onlyWhenNotNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState, MdOverlayState.Fields.resolution))
                    .thenValueOf(
                        // Try in order:
                        // 1) value pending approval (set after submitting resolution, cleared when
                        //    verified/rejected)
                        // 2) manual resolution value (set after applying resolution¹, if manual
                        //    override²),
                        // ¹: actually set on submit, but overridden using outcome when viewing as
                        //    resolver
                        // ²: null for other resolution types, so will display as empty
                        ConditionalOperators.IfNull.ifNull(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    MdOverlayState.Fields.valuePendingApproval,
                                    AttributedValue.Fields.value))
                            .thenValueOf(
                                joinPaths(
                                    ProcessExecution.Fields.currentState,
                                    MdOverlayState.Fields.manualResolutionValue))))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.value))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdOverlayState.Fields.resolutionComment))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolutionComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdOverlayState.Fields.resolutionEvidence))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.resolutionEvidence))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdOverlayState.Fields.approvalComment))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.approvalComment))
            .and(
                joinPaths(
                    ProcessExecution.Fields.currentState, MdOverlayState.Fields.approvalEvidence))
            .as(
                joinPaths(
                    InstrumentOverlayResultView.Fields.resolution,
                    InstrumentResultResolutionView.Fields.approvalEvidence))
            // do not return previousOverlayValue as the front end doesn't want it and we don't have
            // it in the workflow
            .and(
                ConditionalOperators.IfNull.ifNull(
                        joinPaths(
                            ProcessExecution.Fields.currentState, MdOverlayState.Fields.approver))
                    .thenValueOf(
                        joinPaths(
                            ProcessExecution.Fields.currentState, MdOverlayState.Fields.resolver)))
            .as(InstrumentOverlayResultView.Fields.modifiedBy)
            .and("[]") // do not return history as the front end doesn't want it and it is expensive
            .as(InstrumentOverlayResultView.Fields.previousStatuses)
            .and(joinPaths(ProcessExecution.Fields.context, MdOverlayContext.Fields.pricePoint))
            .as(InstrumentOverlayResultView.Fields.bidAskType)
            .and(
                dataForProvider(
                    preliminaryProviderDataPath,
                    joinPaths(
                        ProcessExecution.Fields.context,
                        MdOverlayContext.Fields.providers,
                        MarketDataProviders.Fields.primary)))
            .as(InstrumentOverlayResultView.Fields.primaryProviderData)
            .and(
                dataForProvider(
                    preliminaryProviderDataPath,
                    joinPaths(
                        ProcessExecution.Fields.context,
                        MdOverlayContext.Fields.providers,
                        MarketDataProviders.Fields.secondary)))
            .as(InstrumentOverlayResultView.Fields.secondaryProviderData)
            .and(
                getIntegerAsThresholdLevelName(
                    maxOf(
                        joinPaths(
                            breakTestsPath,
                            InstrumentResultBreak.Fields.providerValue,
                            EntryResultBreakByProvider.Fields.triggeredThresholdLevel))))
            .as(InstrumentOverlayResultView.Fields.maxTriggeredThresholdLevel);

    return addBreakTestFields(
        addInstrumentFields(baseFields, instrumentPath),
        breakTestsPath,
        InstrumentOverlayResultView.Fields.breakTests);
    // spotless:on
  }

  private static @NonNull Reduce dataForProvider(String allProviderDataPath, String providerPath) {
    String rootedProviderPath = rootField(providerPath);
    return Reduce.arrayOf(
            filter(allProviderDataPath)
                .as(AGGREGATION_VAR)
                .by(valueOf(varField(ProviderData.Fields.provider)).equalTo(rootedProviderPath)))
        .withInitialValue(
            new Document().append(ProviderDataView.Fields.provider, rootedProviderPath))
        .reduce(merge(Reduce.Variable.VALUE).mergeWithValuesOf(partialProviderDataView()));
  }

  private static AggregationExpression breakTestViewMapping() {
    // spotless:off
    return context -> new Document()
        .append(InstrumentResultBreakView.Fields.breakTestName, varField(InstrumentResultBreak.Fields.breakTestName))
        .append(InstrumentResultBreakView.Fields.breakTestType, varField(InstrumentResultBreak.Fields.breakTestType))
        .append(InstrumentResultBreakView.Fields.measureType, varField(InstrumentResultBreak.Fields.measureType))
        .append(InstrumentResultBreakView.Fields.operator, varField(InstrumentResultBreak.Fields.operator))
        .append(InstrumentResultBreakView.Fields.threshold, varField(InstrumentResultBreak.Fields.threshold))
        .append(InstrumentResultBreakView.Fields.thresholdLevel, varField(InstrumentResultBreak.Fields.providerValue, EntryResultBreakByProvider.Fields.triggeredThresholdLevel))
        // This was the label from the enum, now the enum itself
        .append(InstrumentResultBreakView.Fields.providerType, varField(InstrumentResultBreak.Fields.providerType))
        .append(InstrumentResultBreakView.Fields.triggered, varField(InstrumentResultBreak.Fields.providerValue, EntryResultBreakByProvider.Fields.triggered))
        .append(InstrumentResultBreakView.Fields.value, varField(InstrumentResultBreak.Fields.providerValue, EntryResultBreakByProvider.Fields.value))
        .append(InstrumentResultBreakView.Fields.parentBreakTestName, varField(InstrumentResultBreak.Fields.parentBreakTestName))
        .append(InstrumentResultBreakView.Fields.calculationOnly, varField(InstrumentResultBreak.Fields.providerValue, EntryResultBreakByProvider.Fields.calculationOnly))
        .append(InstrumentResultBreakView.Fields.threshold,
            ifNull(varField(InstrumentResultBreak.Fields.providerValue, EntryResultBreakByProvider.Fields.triggeredThreshold))
                .thenValueOf(varField(InstrumentResultBreak.Fields.threshold))
                .toDocument(context));
    // spotless:on
  }

  private static AggregationExpression partialProviderDataView() {
    // spotless:off
    return switchCases(
        when(valueOf(thisField(ProviderData.Fields.bidAskType)).equalToValue(ValueBidAskType.BID.name()))
            .then(new Document()
                .append(ProviderDataView.Fields.bidPreviousValue, thisField(ProviderData.Fields.previousValue))
                .append(ProviderDataView.Fields.bidValue, thisField(ProviderData.Fields.value))),
        when(valueOf(thisField(ProviderData.Fields.bidAskType)).equalToValue(ValueBidAskType.MID.name()))
            .then(new Document()
                .append(ProviderDataView.Fields.midPreviousValue, thisField(ProviderData.Fields.previousValue))
                .append(ProviderDataView.Fields.midValue, thisField(ProviderData.Fields.value))),
        when(valueOf(thisField(ProviderData.Fields.bidAskType)).equalToValue(ValueBidAskType.ASK.name()))
            .then(new Document()
                .append(ProviderDataView.Fields.askPreviousValue, thisField(ProviderData.Fields.previousValue))
                .append(ProviderDataView.Fields.askValue, thisField(ProviderData.Fields.value))));
    // spotless:on
  }

  /**
   * Create a reference to a given {@literal property} prefixed with the {@link
   * org.springframework.data.mongodb.core.aggregation.Aggregation#ROOT} identifier. eg. {@code
   * $$ROOT.product}
   *
   * @param pathComponents the property path components, which will be joined together with dots
   * @return the prefixed property path
   */
  private static String rootField(String... pathComponents) {
    return joinPaths(ROOT, joinPaths(pathComponents));
  }

  /**
   * Create a reference to a given {@literal property} prefixed with the {@link Reduce.Variable}
   * identifier. eg. {@code $$this.product}
   *
   * @param fieldPath the property path
   * @return the prefixed property path
   */
  private static String thisField(String fieldPath) {
    return THIS.referringTo(fieldPath).getName();
  }

  /**
   * Create a reference to a given {@literal property} prefixed with the {@link
   * com.solum.xplain.core.common.AggregationUtils#AGGREGATION_VAR} identifier. eg. {@code
   * $$var.product}
   *
   * @param fieldPath the property path
   * @return the prefixed property path
   */
  private static String varField(String fieldPath) {
    return joinPaths(AGGREGATION_VAR_REF, fieldPath);
  }

  /**
   * Create a reference to a given {@literal property} prefixed with the {@link
   * com.solum.xplain.core.common.AggregationUtils#AGGREGATION_VAR} identifier. eg. {@code
   * $$var.product}
   *
   * @param pathComponents the property path components, which will be joined together with dots
   * @return the prefixed property path
   */
  private static String varField(String... pathComponents) {
    return joinPaths(AGGREGATION_VAR_REF, joinPaths(pathComponents));
  }

  /**
   * Aggregation expression which returns with a document containing a single key/value pair, where
   * the key is taken from a field reference rather than being treated as a literal. This uses the
   * {@link
   * org.springframework.data.mongodb.core.aggregation.ArrayOperators.ArrayToObject#arrayToObject(Object)}
   * aggregation expression to create the object.
   *
   * @param fieldReference the key as a field reference e.g. {@code $myObject.property}
   * @param value the value as an aggregation expression
   * @return an aggregation expression which evaluates to a document containing a single key/value
   *     pair
   */
  private static AggregationExpression keyValuePair(
      String fieldReference, AggregationExpression value) {
    return arrayToObject(
        List.of(
            List.of(new Document().append("k", fieldReference).append("v", value.toDocument()))));
  }
}
