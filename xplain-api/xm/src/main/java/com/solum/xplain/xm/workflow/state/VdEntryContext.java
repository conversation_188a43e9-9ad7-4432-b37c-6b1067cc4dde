package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.workflow.VdXmWorkflowProvider;
import jakarta.annotation.Nullable;
import java.io.Serializable;
import lombok.experimental.FieldNameConstants;

/**
 * Attached immutable context object for a dashboard single-trade workflow ({@value
 * VdXmWorkflowProvider#VD_XM_TRADE_PROCESS_ID}). It is initially populated with the pricing slot,
 * trade and provider details.
 */
@FieldNameConstants
public record VdEntryContext(
    BitemporalDate stateDate,
    IpvDataGroupVo vdg,
    // Only populated if providers.hasXplainProvider()
    String marketDataGroupId,
    PricingSlot pricingSlot,
    SlaDeadline slaDeadline,
    Trade trade,
    @Nullable String calculationCurrency,
    ProvidersVo providers)
    implements Serializable {}
