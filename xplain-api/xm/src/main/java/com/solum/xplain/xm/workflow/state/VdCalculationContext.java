package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.workflow.VdXmWorkflowProvider;
import java.io.Serializable;
import java.util.List;

/**
 * Attached immutable context object for a calculation workflow ({@value
 * VdXmWorkflowProvider#VD_XM_CALC_PROCESS_ID}). It is initially populated with the information
 * required to run an Xplain valuation for a subset of product types in a portfolio.
 */
public record VdCalculationContext(
    AuditUser createdBy,
    CompanyLegalEntityValuationSettingsView settings,
    PortfolioCondensedView portfolio,
    MarketDataSourceType sourceType,
    BitemporalDate stateDate,
    List<ProductType> productTypes)
    implements Serializable {}
