package com.solum.xplain.xm.excmngmt.processipv.value;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeFilterForm;
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter;
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilterMapper;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.query.Criteria;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class IpvTradeFilterUtils {

  public static Criteria fromTradeFilterForm(TradeFilterForm form) {
    var criteria =
        criteria(TradeFilterMapper.TRADE_FILTER_MAPPER.fromForm(form.getProductFilter()));
    withValuesFilter(Trade.Fields.portfolioId, form.getPortfolioIds(), criteria);
    withValuesFilter(Trade.Fields.underlying, form.getUnderlyingIndices(), criteria);

    if (isNotEmpty(form.getMaxTriggeredThresholdLevels())) {
      criteria
          .and(IpvTradeResultOverlay.Fields.maxTriggeredThresholdLevel)
          .in(form.getMaxTriggeredThresholdLevels());
    }

    if (isNotEmpty(form.getBreakingTests())) {
      criteria
          .and(IpvTradeResultOverlay.Fields.breakTests)
          .elemMatch(
              where(TradeResultBreak.Fields.breakTestName)
                  .in(form.getBreakingTests())
                  .and(
                      joinPaths(
                          TradeResultBreak.Fields.providerValue,
                          EntryResultBreakByProvider.Fields.triggered))
                  .is(true));
    }
    return criteria;
  }

  private static Criteria criteria(TradeFilter tradeFilter) {
    Criteria criteria = new Criteria();
    withValuesFilter(Trade.Fields.productType, tradeFilter.getProductTypes(), criteria);
    withValuesFilter(Trade.Fields.currency, tradeFilter.getRateCcys(), criteria);
    withValuesFilter(Trade.Fields.creditSector, tradeFilter.getCreditSectors(), criteria);
    withValuesFilter(Trade.Fields.currencyPair, tradeFilter.getFxPairs(), criteria);
    return criteria;
  }

  private static <T> void withValuesFilter(String field, List<T> values, Criteria criteria) {
    if (isNotEmpty(values)) {
      criteria.and(joinPaths(IpvTradeResultOverlay.Fields.trade, field)).in(values);
    }
  }
}
