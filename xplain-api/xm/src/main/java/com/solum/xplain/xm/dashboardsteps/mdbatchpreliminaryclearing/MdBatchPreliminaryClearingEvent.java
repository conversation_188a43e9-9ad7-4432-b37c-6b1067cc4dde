package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.lang.NonNull;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MdBatchPreliminaryClearingEvent extends DashboardEvent {

  @Builder
  public MdBatchPreliminaryClearingEvent(
      @lombok.NonNull String dashboardId, @lombok.NonNull Type type) {
    super(dashboardId, type);
  }

  @NonNull
  @Override
  public DashboardStep getStep() {
    return DashboardStep.MD_BATCH_PRELIMINARY_CLEARING;
  }
}
