package com.solum.xplain.xm.excmngmt.rules.filter;

import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.extensions.enums.CreditSector;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.collections4.CollectionUtils;

@Data
@FieldNameConstants
public class AssetFilter implements Serializable {

  private List<AssetClass> assetClasses = new ArrayList<>();
  private List<InstrumentType> irInstruments = new ArrayList<>();
  private List<String> rateCcys = new ArrayList<>();
  private List<CreditSector> creditSectors = new ArrayList<>();
  private List<String> fxPairs = new ArrayList<>();

  public boolean matches(InstrumentDefinition i) {
    var matchAssetClass = isEmpty(assetClasses) || assetClasses.contains(i.getAssetClass());

    var matchCcy = true;
    if (Objects.equals(i.getAssetClass().getGroup(), CoreAssetGroup.RATES)) {
      matchCcy = isEmpty(rateCcys) || rateCcys.contains(i.getCurrency());
    }

    var matchInstr = true;
    if (Objects.equals(i.getAssetClass(), CoreAssetClass.IR_RATE)) {
      matchInstr = isEmpty(irInstruments) || irInstruments.contains(i.getInstrument());
    }

    var matchSector = true;
    if (Objects.equals(i.getAssetClass().getGroup(), CoreAssetGroup.CREDIT)) {
      matchSector = isEmpty(creditSectors) || creditSectors.contains(i.getSector());
    }

    var matchCcyPair = true;
    if (Objects.equals(i.getAssetClass().getGroup(), CoreAssetGroup.FX)) {
      matchCcyPair = isEmpty(fxPairs) || fxPairs.contains(i.getFxPair());
    }

    return matchAssetClass && matchCcy && matchInstr && matchSector && matchCcyPair;
  }

  private AssetFilter copyWith(Consumer<AssetFilter> modifyFn) {
    var copy = copy();
    modifyFn.accept(copy);
    return copy;
  }

  public static AssetFilter copyWithAssetClasses(AssetFilter filter, List<AssetClass> ac) {
    return filter.copyWith(f -> f.setAssetClasses(ac));
  }

  public static AssetFilter copyWithInstrument(AssetFilter filter, InstrumentType... irInstrument) {
    var assetClasses =
        Stream.of(irInstrument).map(InstrumentType::getAssetClass).distinct().toList();
    return filter.copyWith(
        c -> {
          c.setAssetClasses(assetClasses);
          c.setIrInstruments(List.of(irInstrument));
          c.setCreditSectors(List.of());
          c.setFxPairs(List.of());
        });
  }

  public static AssetFilter copyWithSectors(AssetFilter filter, List<CreditSector> creditSectors) {
    return filter.copyWith(
        c -> {
          c.setCreditSectors(creditSectors);
          c.setRateCcys(List.of());
          c.setIrInstruments(List.of());
          c.setFxPairs(List.of());
        });
  }

  public static AssetFilter copyWithFxPairs(AssetFilter filter, List<String> fxPairs) {
    return filter.copyWith(
        c -> {
          c.setFxPairs(fxPairs);
          c.setRateCcys(List.of());
          c.setIrInstruments(List.of());
          c.setCreditSectors(List.of());
        });
  }

  public static AssetFilter copyWithRateCcy(AssetFilter filter, List<String> rateCcys) {
    return filter.copyWith(
        c -> {
          c.setRateCcys(rateCcys);
          c.setCreditSectors(List.of());
          c.setFxPairs(List.of());
        });
  }

  public List<AssetFilter> separateAssetClassesAndSplitOn(
      Predicate<AssetClass> assetClassFilterFn,
      List<AssetClass> supportedAssetClasses,
      Function<AssetFilter, List<AssetFilter>> splitFn) {
    var splitAssetClassesFilters =
        withFilteredAssetClasses(supportedAssetClasses, assetClassFilterFn).map(splitFn);
    var remainingAssetClassesFilter =
        withFilteredAssetClasses(supportedAssetClasses, v -> assetClassFilterFn.negate().test(v));

    var combinedFilters = ImmutableList.<AssetFilter>builder();
    splitAssetClassesFilters.ifPresent(combinedFilters::addAll);
    remainingAssetClassesFilter.ifPresent(combinedFilters::add);
    return combinedFilters.build();
  }

  private Optional<AssetFilter> withFilteredAssetClasses(
      List<AssetClass> supportedAssetClasses, Predicate<AssetClass> filterFn) {
    var filtered =
        permissibleAssetClasses(supportedAssetClasses).stream().filter(filterFn).toList();
    return Optional.of(filtered)
        .filter(CollectionUtils::isNotEmpty)
        .map(ac -> copyWithAssetClasses(this, ac));
  }

  public List<AssetClass> permissibleAssetClasses(List<AssetClass> supportedAssetClasses) {
    return List.copyOf(getOrDefault(assetClasses, supportedAssetClasses));
  }

  public List<CurrencyPair> permissibleFxPairs(Collection<CurrencyPair> defaultValues) {
    return Optional.ofNullable(fxPairs)
        .filter(CollectionUtils::isNotEmpty)
        .map(v -> v.stream().map(CurrencyPair::parse).toList())
        .orElse(List.copyOf(defaultValues));
  }

  public Set<String> permissibleIrCurrencies(Set<String> defaultCurrencies) {
    return Set.copyOf(getOrDefault(rateCcys, defaultCurrencies));
  }

  public List<InstrumentType> permissibleInstruments(List<InstrumentType> defaultInstruments) {
    if (irInstruments.isEmpty()) {
      if (assetClasses.isEmpty()) {
        return defaultInstruments;
      }
      return defaultInstruments.stream()
          .filter(v -> assetClasses.contains(v.getAssetClass()))
          .toList();
    }
    return irInstruments;
  }

  private <A> Collection<A> getOrDefault(Collection<A> values, Collection<A> defaultValues) {
    return ofNullable(values).filter(i -> !i.isEmpty()).orElse(defaultValues);
  }

  private AssetFilter copy() {
    var copy = new AssetFilter();
    copy.setAssetClasses(assetClasses);
    copy.setIrInstruments(irInstruments);
    copy.setRateCcys(rateCcys);
    copy.setCreditSectors(creditSectors);
    copy.setFxPairs(fxPairs);
    return copy;
  }
}
