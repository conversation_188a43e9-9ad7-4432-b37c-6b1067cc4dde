package com.solum.xplain.xm.excmngmt.rulesonboarding.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidOnboardingBreakTestFormValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidOnboardingBreakTestForm {
  String message() default
      "{com.solum.xplain.api.excmngmt.rulesonboarding.validation.ValidOnboardingBreakTestForm.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
