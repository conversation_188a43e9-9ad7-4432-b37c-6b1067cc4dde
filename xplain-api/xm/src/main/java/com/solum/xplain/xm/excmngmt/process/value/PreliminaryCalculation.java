package com.solum.xplain.xm.excmngmt.process.value;

import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper.EXC_RESULT_MAPPER;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.market.value.MdkProviderBidAskType;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.excmngmt.process.instrument.ExceptionManagementDataProvider;
import com.solum.xplain.xm.excmngmt.process.instrument.InstrumentRequirements;
import com.solum.xplain.xm.excmngmt.process.instrument.InstrumentRequirementsResolver;
import com.solum.xplain.xm.excmngmt.process.value.InstrumentMarketDataBreakCalculatorPreliminary.InstrumentMarketDataBreakCalculatorPreliminarySupplier;
import io.atlassian.fugue.Pair;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PreliminaryCalculation {

  private static final InstrumentMarketDataBreakCalculatorPreliminarySupplier DEFAULT_CALCULATOR =
      InstrumentMarketDataBreakCalculatorPreliminary::forInstrument;

  private final LocalDate valuationDate;
  private final Map<String, Map<String, MdkProviderBidAskType>> mdkBidAskMappings;
  private final BreakTestCalculationsPreliminary breakTestCalculations;
  private final Function<LocalDate, Map<String, Map<String, Map<ValueBidAskType, BigDecimal>>>>
      allData;
  private final HistoricalMarketData allMarketData;
  private final InstrumentMarketDataBreakCalculatorPreliminarySupplier breakCalculatorSupplier;

  public static PreliminaryCalculation ofDate(
      @NonNull LocalDate valuationDate,
      @NonNull Map<String, Map<String, MdkProviderBidAskType>> mdkBidAskMappings,
      @NonNull BreakTestCalculationsPreliminary btCalc,
      @NonNull
          Function<LocalDate, Map<String, Map<String, Map<ValueBidAskType, BigDecimal>>>> allData,
      @NonNull IntFunction<HistoricalMarketData> historicalResultsProvider) {
    var history =
        btCalc
            .longestStaleDuration()
            .map(historicalResultsProvider::apply)
            .orElse(HistoricalMarketData.EMPTY);

    return new PreliminaryCalculation(
        valuationDate, mdkBidAskMappings, btCalc, allData, history, DEFAULT_CALCULATOR);
  }

  public List<InstrumentResultPreliminary> perform(ExceptionManagementDataProvider dataProvider) {
    var previousDate = dataProvider.previousDate();
    var data =
        PreliminaryCalculationHelper.marketDataProvidersByInstrument(
            allData.apply(valuationDate),
            previousDate != null ? allData.apply(previousDate) : emptyMap());

    return dataProvider.instruments().stream()
        .map(
            i ->
                process(
                    dataProvider.marketDataGroupId(),
                    i,
                    data.get(i.getKey()),
                    dataProvider.requirementsResolver()))
        .flatMap(List::stream)
        .toList();
  }

  /**
   * Processes a given instrument and the associated data to create a list of preliminary results.
   * It first resolves the required providers and their data, then creates a preliminary result for
   * each required provider. It then calculates the break tests for each provider and adds them to
   * the preliminary result.
   */
  private List<InstrumentResultPreliminary> process(
      @NonNull String marketDataGroupId,
      @NonNull InstrumentDefinition instrument,
      @Nullable List<ProviderData> data,
      InstrumentRequirementsResolver requirementsResolver) {
    var instrumentMdkMapping = mdkBidAskMappings.get(instrument.getKey());

    var preliminaries = ImmutableList.<InstrumentResultPreliminary>builder();
    var allRequiredProviders =
        requirementsResolver.requirements(instrument).stream()
            .map(InstrumentRequirements::providers)
            .flatMap(MarketDataProviders::toProvidersStream)
            .distinct()
            .flatMap(r -> resolveProviderData(data, r, instrumentMdkMapping))
            .collect(toMap(v -> Pair.pair(v.getProvider(), v.getBidAskType()), v -> v));
    var instrumentData = allMarketData.instrumentData(instrument.getKey());
    for (Map.Entry<Pair<String, ValueBidAskType>, ProviderData> entry :
        allRequiredProviders.entrySet()) {
      var providerData = entry.getValue();
      var xmInstrument = EXC_RESULT_MAPPER.fromDefinition(instrument);

      var preliminary = new InstrumentResultPreliminary();
      preliminary.setMarketDataGroupId(marketDataGroupId);
      preliminary.setInstrument(xmInstrument);
      preliminary.setProviderData(providerData);
      preliminary.setValuationDate(valuationDate);
      preliminary.setAllProvidersData(List.copyOf(allRequiredProviders.values()));

      var breaksCalculator =
          breakCalculatorSupplier.supply(instrument, instrumentData, providerData);
      preliminary.withBreakTests(breakTestCalculations.processCalc(breaksCalculator));
      preliminaries.add(preliminary);
    }
    return preliminaries.build();
  }

  private static Stream<ProviderData> resolveProviderData(
      @Nullable List<ProviderData> data,
      @NonNull String provider,
      @Nullable Map<String, MdkProviderBidAskType> mdkMapping) {
    return ofNullable(mdkMapping)
        .map(m -> m.get(provider))
        .map(MdkProviderBidAskType::getSupportedValueBidAsks)
        .stream()
        .flatMap(Collection::stream)
        .map(
            bidAskType ->
                ofNullable(data).stream()
                    .flatMap(Collection::stream)
                    .filter(
                        d ->
                            Objects.equals(d.getProvider(), provider)
                                && Objects.equals(d.getBidAskType(), bidAskType))
                    .findAny()
                    .orElse(ProviderData.of(provider, null, null, bidAskType)));
  }

  public interface PreliminaryCalculationSupplier {

    PreliminaryCalculation supply(
        @NonNull LocalDate valuationDate,
        @NonNull Map<String, Map<String, MdkProviderBidAskType>> mdkBidAskMappings,
        @NonNull BreakTestCalculationsPreliminary btCalc,
        @NonNull
            Function<LocalDate, Map<String, Map<String, Map<ValueBidAskType, BigDecimal>>>> allData,
        @NonNull IntFunction<HistoricalMarketData> historicalResultsProvider);
  }

  @Configuration
  public static class PreliminaryCalculationConfig {

    private static final PreliminaryCalculationSupplier DEFAULT_PRELIMINARY_CALCULATION =
        PreliminaryCalculation::ofDate;

    @Bean
    public PreliminaryCalculationSupplier getPreliminaryCalculationSupplier() {
      return DEFAULT_PRELIMINARY_CALCULATION;
    }
  }
}
