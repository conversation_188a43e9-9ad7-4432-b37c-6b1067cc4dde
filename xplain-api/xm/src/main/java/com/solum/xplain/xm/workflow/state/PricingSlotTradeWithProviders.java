package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.core.product.ProductType;
import java.io.Serializable;

/**
 * Represents a trade with its associated pricing slot and providers.
 *
 * @param vdg the valuation data group determined for the trade
 * @param pricingSlot the pricing slot determined for the trade
 * @param portfolioId - portfolio._id for lookup of the trade during workflow
 * @param entityId - portfolioItem.entityId for lookup of the trade during workflow
 * @param productType the product type of the trade
 * @param providers the valuation providers for the trade
 */
public record PricingSlotTradeWithProviders(
    IpvDataGroupVo vdg,
    PricingSlot pricingSlot,
    String portfolioId,
    String entityId,
    ProductType productType,
    ProvidersVo providers)
    implements Serializable {}
