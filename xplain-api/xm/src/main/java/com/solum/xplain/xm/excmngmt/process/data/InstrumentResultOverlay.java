package com.solum.xplain.xm.excmngmt.process.data;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID;
import static java.lang.String.format;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.form.ResolutionForm;
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistory;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.Nullable;

@Data
@Document
@FieldNameConstants
public class InstrumentResultOverlay implements HasInstrumentResult {

  public static final String INSTRUMENT_RESULT_OVERLAY_COLLECTION = "instrumentResultOverlay";

  @Id private String id;
  @NotNull private String dashboardId;
  @NotNull private LocalDate valuationDate;
  @NotNull private String marketDataGroupId;
  @NotNull private String exceptionManagementResultId;
  @NotNull private String taskId;

  @NotNull private Instrument instrument;
  @Nullable private String curveConfigurationId;
  @Nullable private String companyId;
  @Nullable private String legalEntityId;

  @NotNull private EntryResultStatus status;
  @Nullable private InstrumentResultResolution resolution;
  @Nullable private InstrumentResultResolution preliminaryResolution;

  @Nullable private BigDecimal rawValue;

  @Nullable private ProviderData primaryProviderData;
  @Nullable private ProviderData secondaryProviderData;
  @Nullable private List<ProviderData> allProvidersData;

  @Nullable private BigDecimal previousOverlayValue;

  private List<InstrumentResultBreak> breakTests;
  @Nullable private List<InstrumentResultBreak> preliminaryBreaks;
  private long appliedTestsCount;
  private boolean hasBreaks;
  private Integer maxTriggeredThresholdLevel;

  @LastModifiedBy private AuditUser modifiedBy;
  @LastModifiedDate private LocalDateTime modifiedAt;

  private List<EntryResultStatusHistory> previousStatuses = new ArrayList<>();

  public BigDecimal resolvedValue() {
    return ofNullable(getResolution())
        .flatMap(r -> ofNullable(r.getValue()))
        .or(() -> ofNullable(getPrimaryProviderData()).map(ProviderData::getValue))
        .orElse(null);
  }

  public List<InstrumentResultBreak> allBreaks() {
    return Stream.concat(
            Stream.ofNullable(preliminaryBreaks).flatMap(Collection::stream),
            Stream.ofNullable(breakTests)
                .flatMap(Collection::stream)
                .filter(InstrumentResultBreak::hasBreak))
        .toList();
  }

  @Override
  public Optional<ErrorItem> applyResolutionValues(ResolutionForm resolutionForm) {
    return switch (resolutionForm.resolutionType()) {
      case KEEP -> processKeepPrimaryProvider();
      case SWITCH_TO_SECONDARY -> processSwitchProvider();
      case SWITCH_TO_BID -> processSwitchPrices(BID);
      case SWITCH_TO_ASK -> processSwitchPrices(ASK);
      case PREVIOUS_DAY -> processPreviousDay();
      case OVERRIDE_USER -> resolution.resolveValue(instrument, resolutionForm.newValue());
      case DELTA_SECONDARY -> processDeltaSecondary();
      default -> throw new IllegalStateException("Unexpected value: " + resolutionForm);
    };
  }

  private Optional<ErrorItem> processKeepPrimaryProvider() {
    var value = getPrimaryProviderData() == null ? null : getPrimaryProviderData().getValue();
    return resolution.resolveValue(getInstrument(), value);
  }

  private Optional<ErrorItem> processSwitchPrices(ValueBidAskType switchTo) {
    if (primaryProviderData.getBidAskType() == switchTo) {
      return of(
          OPERATION_NOT_ALLOWED.entity(
              format(
                  "Invalid switch from %s to %s for instrument %s",
                  switchTo.getLabel(), switchTo.getLabel(), instrument.getKey())));
    }
    var value =
        ofNullable(getPrimaryProviderData())
            .map(ProviderData::getProvider)
            .flatMap(
                primary ->
                    ofNullable(getAllProvidersData()).stream()
                        .flatMap(Collection::stream)
                        .filter(p -> switchTo == p.getBidAskType())
                        .filter(p -> primary.equals(p.getProvider()))
                        .map(ProviderData::getValue)
                        .filter(Objects::nonNull)
                        .findAny())
            .orElse(null);
    return resolution.resolveValue(getInstrument(), value);
  }

  private Optional<ErrorItem> processSwitchProvider() {
    return ofNullable(getSecondaryProviderData())
        .map(ProviderData::getValue)
        .flatMap(value -> resolution.resolveValue(instrument, value));
  }

  private Optional<ErrorItem> processDeltaSecondary() {
    var value =
        ofNullable(primaryProviderData)
            .map(ProviderData::getPreviousValue)
            .flatMap(
                p ->
                    ofNullable(secondaryProviderData)
                        .filter(v -> allNotNull(v.getValue(), v.getPreviousValue()))
                        .map(v -> v.getValue().subtract(v.getPreviousValue()))
                        .map(p::add))
            .orElse(null);
    return resolution.resolveValue(instrument, value);
  }

  private Optional<ErrorItem> processPreviousDay() {
    var value =
        ofNullable(previousOverlayValue)
            .orElseGet(
                () ->
                    getPrimaryProviderData() == null
                        ? null
                        : getPrimaryProviderData().getPreviousValue());
    return resolution.resolveValue(instrument, value);
  }

  @EqualsAndHashCode.Include(replaces = "rawValue")
  private BigDecimal normalisedRawValue() {
    return rawValue == null ? null : rawValue.stripTrailingZeros();
  }

  @EqualsAndHashCode.Include(replaces = "previousOverlayValue")
  private BigDecimal normalisedPreviousOverlayValue() {
    return previousOverlayValue == null ? null : previousOverlayValue.stripTrailingZeros();
  }
}
