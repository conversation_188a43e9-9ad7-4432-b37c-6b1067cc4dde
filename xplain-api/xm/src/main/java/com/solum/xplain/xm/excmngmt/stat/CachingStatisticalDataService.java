package com.solum.xplain.xm.excmngmt.stat;

import static com.google.common.cache.CacheLoader.from;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.excmngmt.process.InstrumentResultPreliminaryQueryRepository;
import com.solum.xplain.xm.excmngmt.stat.value.PreliminaryZScoreData;
import java.time.Duration;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service to obtain statistical measures for historic clean market data, by querying and caching it
 * in bulk.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CachingStatisticalDataService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(1, ChronoUnit.MINUTES);
  private final InstrumentResultPreliminaryQueryRepository queryRepository;

  private final LoadingCache<String, MarketDataGroupCache> statisticalDataCache =
      CacheBuilder.newBuilder()
          .expireAfterAccess(FRESHNESS_LIMIT)
          .build(from(MarketDataGroupCache::new));

  @RequiredArgsConstructor
  private class MarketDataGroupCache {
    private final String marketDataGroupId;

    private record DateRangeKey(LocalDate fromDate, LocalDate toDate) {}

    private final LoadingCache<
            DateRangeKey, Map<String, Map<ValueBidAskType, List<PreliminaryZScoreData>>>>
        instrumentStatisticsCache =
            CacheBuilder.newBuilder()
                .expireAfterWrite(FRESHNESS_LIMIT)
                .build(from(this::instrumentStatistics));

    public List<PreliminaryZScoreData> zScoreData(
        Tenor sampleWindow,
        InstrumentDefinition instrument,
        ValueBidAskType pricePoint,
        BitemporalDate stateDate)
        throws ExecutionException {
      LocalDate fromDate = stateDate.getActualDate().minus(sampleWindow.getPeriod());
      LocalDate toDate = stateDate.getActualDate();
      DateRangeKey dateRangeKey = new DateRangeKey(fromDate, toDate);
      return instrumentStatisticsCache
          .get(dateRangeKey)
          .getOrDefault(instrument.getKey(), Collections.emptyMap())
          .getOrDefault(pricePoint, Collections.emptyList());
    }

    private Map<String, Map<ValueBidAskType, List<PreliminaryZScoreData>>> instrumentStatistics(
        DateRangeKey dateRange) {
      log.debug(
          "Loading historic preliminary data statistics for {} ({}-{})",
          marketDataGroupId,
          dateRange.fromDate(),
          dateRange.toDate());
      return queryRepository
          .getZScoreData(marketDataGroupId, dateRange.fromDate(), dateRange.toDate())
          .collect(
              Collectors.groupingBy(
                  PreliminaryZScoreData::instKey,
                  Collectors.groupingBy(PreliminaryZScoreData::pricePoint, Collectors.toList())));
    }
  }

  /**
   * Check caches for statistical data for the market data group and sample window back from the
   * state date and fetch if missing, then get the data for the specific instrument from those.
   *
   * @param marketDataGroupId the market data group reference
   * @param sampleWindow the time period before the state date's actual date to retrieve statistics
   *     for
   * @param instrument the instrument to retrieve statistics for
   * @param pricePoint the price point to retrieve statistics for
   * @param stateDate relevant state date
   * @return list of providers with their statistics
   */
  public List<PreliminaryZScoreData> preliminaryZScoreData(
      String marketDataGroupId,
      Tenor sampleWindow,
      InstrumentDefinition instrument,
      ValueBidAskType pricePoint,
      BitemporalDate stateDate) {
    try {
      return statisticalDataCache
          .get(marketDataGroupId)
          .zScoreData(sampleWindow, instrument, pricePoint, stateDate);
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.statisticalDataCache.invalidateAll();
  }
}
