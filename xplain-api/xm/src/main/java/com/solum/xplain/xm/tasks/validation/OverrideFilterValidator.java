package com.solum.xplain.xm.tasks.validation;

import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.xm.excmngmt.form.WithEntryFilterFields;
import java.util.Collection;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

@AllArgsConstructor
public class OverrideFilterValidator {
  private final Collection<String> rateCcys;
  private final Collection<String> fxPairs;
  private final Collection<CreditSector> creditSectors;

  public static OverrideFilterValidator empty() {
    return new OverrideFilterValidator(List.of(), List.of(), List.of());
  }

  public static OverrideFilterValidator newOf(WithEntryFilterFields form) {
    return new OverrideFilterValidator(
        emptyIfNull(form.getRateCcys()),
        emptyIfNull(form.getFxPairs()),
        emptyIfNull(form.getCreditSectors()));
  }

  public OverrideFilterValidator merge(OverrideFilterValidator overridesFilter) {
    return new OverrideFilterValidator(
        CollectionUtils.join(this.rateCcys, overridesFilter.rateCcys),
        CollectionUtils.join(this.fxPairs, overridesFilter.fxPairs),
        CollectionUtils.join(this.creditSectors, overridesFilter.creditSectors));
  }

  public boolean isValid() {
    return BooleanUtils.negate(
        CollectionUtils.hasDuplicates(rateCcys)
            || CollectionUtils.hasDuplicates(fxPairs)
            || CollectionUtils.hasDuplicates(creditSectors));
  }
}
