package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class MdPreliminaryState implements Serializable, MdPhaseState {
  private EntryResultStatus entryStatus = EntryResultStatus.WAITING_RESOLUTION;

  private BigDecimal baseValue;
  private String providerName;
  private AuditUser resolver;
  private InstrumentResultResolutionType resolution;
  private String resolutionComment;
  private ExceptionManagementEvidence resolutionEvidence;
  private BigDecimal manualResolutionValue;

  private AttributedValue valuePendingApproval;

  private AuditUser approver;
  private VerificationStatus approval;
  private String approvalComment;
  private ExceptionManagementEvidence approvalEvidence;
}
