package com.solum.xplain.xm.excmngmt.process.view;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.xm.excmngmt.evidence.value.ExceptionManagementEvidenceView;
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class InstrumentResultResolutionView {

  private InstrumentResultResolutionType resolution;
  private String resolutionComment;

  @ConfigurableViewField(InstrumentResultViewFieldName.RESOLUTION_EVIDENCE)
  private ExceptionManagementEvidenceView resolutionEvidence;

  @ConfigurableViewField(InstrumentResultViewFieldName.RESOLUTION_VALUE)
  private BigDecimal value;

  @ConfigurableViewField(InstrumentResultViewFieldName.RESOLUTION_PROVIDER)
  private String provider;

  private String approvalComment; // aka verification comment

  @ConfigurableViewField(InstrumentResultViewFieldName.APPROVAL_EVIDENCE)
  private ExceptionManagementEvidenceView approvalEvidence;
}
