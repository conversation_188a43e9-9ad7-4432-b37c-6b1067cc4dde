package com.solum.xplain.xm.workflow.steps.md;

import com.solum.xplain.workflow.repository.CacheSettingDataModificationCommand;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import java.time.LocalDateTime;
import lombok.NonNull;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.BulkOperations;

public record InstrumentResultPreliminaryCreate(InstrumentResultPreliminary data)
    implements CacheSettingDataModificationCommand<InstrumentResultPreliminary> {
  @Override
  public Class<? super InstrumentResultPreliminary> getEntity() {
    return InstrumentResultPreliminary.class;
  }

  @Override
  public @NonNull InstrumentResultPreliminary apply(BulkOperations bulkOps) {
    ObjectId id = ObjectId.get();
    data.setId(id.toHexString());
    data.setModifiedAt(LocalDateTime.now());
    bulkOps.insert(data);
    return data;
  }
}
