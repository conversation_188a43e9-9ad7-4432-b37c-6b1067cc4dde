package com.solum.xplain.xm.workflow;

import static com.solum.xplain.workflow.value.AssignmentRuleView.assignableTo;
import static com.solum.xplain.workflow.value.AssignmentRuleView.candidateTeams;
import static com.solum.xplain.workflow.value.AssignmentRuleView.excludedUsers;
import static com.solum.xplain.workflow.value.BoundaryEventView.boundaryEvent;
import static com.solum.xplain.workflow.value.ProcessFlowView.from;
import static com.solum.xplain.workflow.value.UserTaskDefinitionView.withInputField;

import com.solum.xplain.core.viewconfig.value.FieldType;
import com.solum.xplain.workflow.provider.WorkflowProvider;
import com.solum.xplain.workflow.value.CatchingEffect;
import com.solum.xplain.workflow.value.EventType;
import com.solum.xplain.workflow.value.MultiInstance;
import com.solum.xplain.workflow.value.ProcessDefinitionView;
import com.solum.xplain.xm.workflow.form.MdResolutionDecisionForm;
import com.solum.xplain.xm.workflow.form.ResolutionApprovalForm;
import com.solum.xplain.xm.workflow.state.MdDashboardContext;
import com.solum.xplain.xm.workflow.state.MdDashboardState;
import com.solum.xplain.xm.workflow.state.MdKeyContext;
import com.solum.xplain.xm.workflow.state.MdKeyContextCreator;
import com.solum.xplain.xm.workflow.state.MdKeyState;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayContextCreator;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContextCreator;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import com.solum.xplain.xm.workflow.steps.md.AutoRejectInvalidValueStep;
import com.solum.xplain.xm.workflow.steps.md.FindInstrumentDefinitionsStep;
import com.solum.xplain.xm.workflow.steps.md.FindNonRequiredProviderDataStep;
import com.solum.xplain.xm.workflow.steps.md.RejectOverlayResolutionStep;
import com.solum.xplain.xm.workflow.steps.md.RejectPreliminaryResolutionStep;
import com.solum.xplain.xm.workflow.steps.md.RunOverlayBreakTestsStep;
import com.solum.xplain.xm.workflow.steps.md.SaveOverlayStep;
import com.solum.xplain.xm.workflow.steps.md.SavePreliminaryStep;
import com.solum.xplain.xm.workflow.steps.md.SaveUnbrokenPreliminaryStep;
import com.solum.xplain.xm.workflow.steps.md.SetInitialBaseValueStep;
import com.solum.xplain.xm.workflow.steps.md.VerifyOverlayResolutionStep;
import com.solum.xplain.xm.workflow.steps.md.VerifyPreliminaryResolutionStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.DeltaSecondaryStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.IgnoreNullStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.KeepBaseStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.OverrideUserStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.PreviousDayStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.SwitchProviderStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.SwitchToAskStep;
import com.solum.xplain.xm.workflow.steps.md.resolution.SwitchToBidStep;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MdXmWorkflowProvider implements WorkflowProvider {
  public static final String MD_XM_PROCESS_ID = "mdDashboard";
  public static final String MD_XM_KEY_PROCESS_ID = "mdKey";
  public static final String MD_XM_PRELIMINARY_PROCESS_ID = "mdPreliminary";
  public static final String MD_XM_OVERLAY_PROCESS_ID = "mdOverlay";

  public static class Steps {
    public static final String START = "start";
    public static final String FIND_INSTRUMENT_DEFINITIONS = "findInstrumentDefinitions";
    public static final String FIND_NON_RELEVANT_PROVIDER_DATA = "findNonRequiredProviderData";
    public static final String CALL_MD_KEY = "callMdKey";
    public static final String SAVE_UNBROKEN_PRELIMINARY = "saveUnbrokenPreliminary";
    public static final String CALL_MD_PRELIMINARY = "callMdPreliminary";
    public static final String CALL_MD_OVERLAY = "callMdOverlay";
    public static final String RUN_BREAK_TESTS = "runBreakTests";
    public static final String SAVE_RESULT = "saveResult";
    public static final String CHECK_BREAKS = "checkBreaks";
    public static final String SET_INITIAL_BASE_VALUE = "setInitialBaseValue";
    public static final String DECIDE_RESOLUTION = "decideResolution";
    public static final String CHECK_DECISION = "checkDecision";
    public static final String KEEP_BASE_RESOLUTION = "keepBaseResolution";
    public static final String PREVIOUS_DAY_RESOLUTION = "previousDayResolution";
    public static final String SWITCH_TO_BID_RESOLUTION = "switchToBidResolution";
    public static final String SWITCH_TO_ASK_RESOLUTION = "switchToAskResolution";
    public static final String DELTA_SECONDARY_RESOLUTION = "deltaSecondaryResolution";
    public static final String OVERRIDE_USER_RESOLUTION = "overrideUserResolution";
    public static final String SWITCH_PROVIDER_RESOLUTION = "switchProviderResolution";
    public static final String IGNORE_NULL_RESOLUTION = "ignoreNullResolution";
    public static final String APPROVE_RESOLUTION = "approveResolution";
    public static final String CHECK_APPROVAL = "checkApproval";
    public static final String VERIFY_RESOLUTION = "verifyResolution";
    public static final String REJECT_RESOLUTION = "rejectResolution";
    public static final String AUTO_REJECT_INVALID_VALUE = "autoRejectInvalidValue";
  }

  public static class Events {
    public static final String INVALID_RESOLVED_VALUE = "invalidResolvedValue";
  }

  private final FindInstrumentDefinitionsStep findInstrumentDefinitionsStep;
  private final FindNonRequiredProviderDataStep findNonRequiredProviderDataStep;
  private final RunOverlayBreakTestsStep runOverlayBreakTestsStep;
  private final SavePreliminaryStep savePreliminaryStep;
  private final SaveUnbrokenPreliminaryStep saveUnbrokenPreliminaryStep;
  private final SaveOverlayStep saveOverlayStep;
  private final SetInitialBaseValueStep setInitialBaseValueStep;
  private final KeepBaseStep<MdPreliminaryState, MdPreliminaryContext> preliminaryKeepBaseStep;
  private final KeepBaseStep<MdOverlayState, MdOverlayContext> overlayKeepBaseStep;
  private final PreviousDayStep<MdPreliminaryState, MdPreliminaryContext>
      preliminaryPreviousDayStep;
  private final PreviousDayStep<MdOverlayState, MdOverlayContext> overlayPreviousDayStep;
  private final SwitchToBidStep overlaySwitchToBidStep;
  private final SwitchToAskStep overlaySwitchToAskStep;
  private final OverrideUserStep<MdPreliminaryState, MdPreliminaryContext>
      preliminaryOverrideUserStep;
  private final DeltaSecondaryStep overlayDeltaSecondaryStep;
  private final OverrideUserStep<MdOverlayState, MdOverlayContext> overlayOverrideUserStep;
  private final IgnoreNullStep ignoreNullStep;
  private final SwitchProviderStep switchProviderStep;
  private final VerifyPreliminaryResolutionStep verifyPreliminaryResolutionStep;
  private final RejectPreliminaryResolutionStep rejectPreliminaryResolutionStep;
  private final VerifyOverlayResolutionStep verifyOverlayResolutionStep;
  private final RejectOverlayResolutionStep rejectOverlayResolutionStep;
  private final AutoRejectInvalidValueStep<MdPreliminaryState, MdPreliminaryContext>
      autoRejectPreliminaryInvalidValueStep;
  private final AutoRejectInvalidValueStep<MdOverlayState, MdOverlayContext>
      autoRejectOverlayInvalidValueStep;

  private final MdPreliminaryContextCreator mdPreliminaryContextCreator;
  private final MdOverlayContextCreator mdOverlayContextCreator;
  private final MdKeyContextCreator mdKeyContextCreator;

  @Override
  public Collection<ProcessDefinitionView<?, ?>> provideProcessDefinitions() {
    return List.of(
        dashboardProcessDefinition(),
        mdkProcessDefinition(),
        preliminarySubprocessDefinition(),
        overlaySubprocessDefinition());
  }

  private ProcessDefinitionView<MdDashboardState, MdDashboardContext> dashboardProcessDefinition() {
    return ProcessDefinitionView.<MdDashboardState, MdDashboardContext>process(MD_XM_PROCESS_ID)
        .startWith(Steps.START)
        .serviceStep(Steps.FIND_INSTRUMENT_DEFINITIONS, findInstrumentDefinitionsStep, true)
        .serviceStep(Steps.FIND_NON_RELEVANT_PROVIDER_DATA, findNonRequiredProviderDataStep, true)
        .callStep(
            Steps.CALL_MD_KEY, MD_XM_KEY_PROCESS_ID, MultiInstance.PARALLEL, mdKeyContextCreator)
        .flows(
            from(Steps.START).to(Steps.FIND_INSTRUMENT_DEFINITIONS),
            from(Steps.FIND_INSTRUMENT_DEFINITIONS).to(Steps.FIND_NON_RELEVANT_PROVIDER_DATA),
            from(Steps.FIND_NON_RELEVANT_PROVIDER_DATA).to(Steps.CALL_MD_KEY))
        .build();
  }

  private ProcessDefinitionView<MdKeyState, MdKeyContext> mdkProcessDefinition() {
    return ProcessDefinitionView.<MdKeyState, MdKeyContext>process(MD_XM_KEY_PROCESS_ID)
        .startWith(Steps.START)
        .serviceStep(Steps.SAVE_UNBROKEN_PRELIMINARY, saveUnbrokenPreliminaryStep, false)
        .exclusiveGateway(Steps.CHECK_BREAKS)
        // split off a prelim resolution subprocess for all broken MDKs (move breaks from state to
        // context)
        .callStep(
            Steps.CALL_MD_PRELIMINARY,
            MD_XM_PRELIMINARY_PROCESS_ID,
            MultiInstance.PARALLEL,
            mdPreliminaryContextCreator)
        // split off an overlay subprocess for all curve configs/price points where we have prelim
        // data for primary
        .callStep(
            Steps.CALL_MD_OVERLAY,
            MD_XM_OVERLAY_PROCESS_ID,
            MultiInstance.PARALLEL,
            mdOverlayContextCreator)
        .flows(
            from(Steps.START).to(Steps.SAVE_UNBROKEN_PRELIMINARY),
            from(Steps.SAVE_UNBROKEN_PRELIMINARY).to(Steps.CHECK_BREAKS),
            from(Steps.CHECK_BREAKS)
                .when("context.brokenPreliminaryData.empty")
                .to(Steps.CALL_MD_OVERLAY),
            from(Steps.CHECK_BREAKS).to(Steps.CALL_MD_PRELIMINARY), // default flow
            from(Steps.CALL_MD_PRELIMINARY).to(Steps.CALL_MD_OVERLAY))
        .build();
  }

  private ProcessDefinitionView<MdPreliminaryState, MdPreliminaryContext>
      preliminarySubprocessDefinition() {
    return ProcessDefinitionView.<MdPreliminaryState, MdPreliminaryContext>process(
            MD_XM_PRELIMINARY_PROCESS_ID)
        .startWith(Steps.START)
        .serviceStep(Steps.SET_INITIAL_BASE_VALUE, setInitialBaseValueStep, false)
        .userTask(
            Steps.DECIDE_RESOLUTION,
            assignableTo(
                candidateTeams(
                    "@cachingTasksDefinitionService.getTasksDefinition(T(com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType).PRELIMINARY, context.stateDate)?.combinedTaskTeams(context.instrument.assetClass, @cachingTasksDefinitionService.getCachedMdTaskDefaultTeams())?.resolutionTeams"),
                excludedUsers("{state.approver?.userId}")),
            withInputField(MdResolutionDecisionForm.Fields.user, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.resolver),
            withInputField(MdResolutionDecisionForm.Fields.resolution, FieldType.ENUM)
                .asProperty(MdPreliminaryState.Fields.resolution),
            withInputField(MdResolutionDecisionForm.Fields.newValue, FieldType.NUMBER)
                .asProperty(MdPreliminaryState.Fields.manualResolutionValue),
            withInputField(MdResolutionDecisionForm.Fields.comment, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.resolutionComment),
            withInputField(MdResolutionDecisionForm.Fields.evidence, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.resolutionEvidence))
        .exclusiveGateway(Steps.CHECK_DECISION)
        .serviceStep(Steps.KEEP_BASE_RESOLUTION, preliminaryKeepBaseStep, false)
        .serviceStep(Steps.PREVIOUS_DAY_RESOLUTION, preliminaryPreviousDayStep, false)
        .serviceStep(Steps.OVERRIDE_USER_RESOLUTION, preliminaryOverrideUserStep, false)
        .serviceStep(Steps.IGNORE_NULL_RESOLUTION, ignoreNullStep, false)
        .userTask(
            Steps.APPROVE_RESOLUTION,
            assignableTo(
                candidateTeams(
                    "@cachingTasksDefinitionService.getTasksDefinition(T(com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType).PRELIMINARY, context.stateDate)?.combinedTaskTeams(context.instrument.assetClass, @cachingTasksDefinitionService.getCachedMdTaskDefaultTeams())?.approvalTeams"),
                excludedUsers("{state.resolver?.userId}")),
            withInputField(ResolutionApprovalForm.Fields.user, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.approver),
            withInputField(ResolutionApprovalForm.Fields.status, FieldType.ENUM)
                .asProperty(MdPreliminaryState.Fields.approval),
            withInputField(ResolutionApprovalForm.Fields.comment, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.approvalComment),
            withInputField(ResolutionApprovalForm.Fields.evidence, FieldType.STRING)
                .asProperty(MdPreliminaryState.Fields.approvalEvidence))
        .exclusiveGateway(Steps.CHECK_APPROVAL)
        .serviceStep(Steps.VERIFY_RESOLUTION, verifyPreliminaryResolutionStep, false)
        .serviceStep(Steps.SAVE_RESULT, savePreliminaryStep, false)
        .serviceStep(Steps.REJECT_RESOLUTION, rejectPreliminaryResolutionStep, false)
        .boundaryEvents(
            boundaryEvent(
                    Events.INVALID_RESOLVED_VALUE, EventType.ERROR, CatchingEffect.INTERRUPTING)
                .attachedTo(
                    Steps.KEEP_BASE_RESOLUTION,
                    Steps.PREVIOUS_DAY_RESOLUTION,
                    Steps.OVERRIDE_USER_RESOLUTION,
                    Steps.IGNORE_NULL_RESOLUTION))
        .serviceStep(Steps.AUTO_REJECT_INVALID_VALUE, autoRejectPreliminaryInvalidValueStep, false)
        .flows(
            from(Steps.START).to(Steps.SET_INITIAL_BASE_VALUE),
            from(Steps.SET_INITIAL_BASE_VALUE).to(Steps.DECIDE_RESOLUTION),
            from(Steps.DECIDE_RESOLUTION).to(Steps.CHECK_DECISION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).IGNORE_NULL")
                .to(Steps.IGNORE_NULL_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).OVERRIDE_USER")
                .to(Steps.OVERRIDE_USER_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).PREVIOUS_DAY")
                .to(Steps.PREVIOUS_DAY_RESOLUTION),
            from(Steps.CHECK_DECISION).to(Steps.KEEP_BASE_RESOLUTION), // Default branch
            from(Steps.IGNORE_NULL_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.OVERRIDE_USER_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.PREVIOUS_DAY_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.KEEP_BASE_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.APPROVE_RESOLUTION).to(Steps.CHECK_APPROVAL),
            from(Steps.AUTO_REJECT_INVALID_VALUE).to(Steps.DECIDE_RESOLUTION),
            from(Steps.CHECK_APPROVAL)
                .when(
                    "state.approval == T(com.solum.xplain.xm.excmngmt.enums.VerificationStatus).REJECTED")
                .to(Steps.REJECT_RESOLUTION),
            from(Steps.CHECK_APPROVAL).to(Steps.VERIFY_RESOLUTION), // Default branch
            from(Steps.VERIFY_RESOLUTION).to(Steps.SAVE_RESULT),
            from(Steps.REJECT_RESOLUTION).to(Steps.DECIDE_RESOLUTION),
            from(Events.INVALID_RESOLVED_VALUE).to(Steps.AUTO_REJECT_INVALID_VALUE))
        .build();
  }

  private ProcessDefinitionView<MdOverlayState, MdOverlayContext> overlaySubprocessDefinition() {
    return ProcessDefinitionView.<MdOverlayState, MdOverlayContext>process(MD_XM_OVERLAY_PROCESS_ID)
        .startWith(Steps.START)
        .serviceStep(Steps.RUN_BREAK_TESTS, runOverlayBreakTestsStep, false)
        .serviceStep(Steps.SAVE_RESULT, saveOverlayStep, false)
        .exclusiveGateway(Steps.CHECK_BREAKS)
        .userTask(
            Steps.DECIDE_RESOLUTION,
            assignableTo(
                candidateTeams(
                    "@cachingTasksDefinitionService.getTasksDefinition(T(com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType).OVERLAY, context.stateDate)?.combinedTaskTeams(context.instrument.assetClass, @cachingTasksDefinitionService.getCachedMdTaskDefaultTeams())?.resolutionTeams"),
                excludedUsers("{state.approver?.userId}")),
            withInputField(MdResolutionDecisionForm.Fields.user, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.resolver),
            withInputField(MdResolutionDecisionForm.Fields.resolution, FieldType.ENUM)
                .asProperty(MdOverlayState.Fields.resolution),
            withInputField(MdResolutionDecisionForm.Fields.newValue, FieldType.NUMBER)
                .asProperty(MdOverlayState.Fields.manualResolutionValue),
            withInputField(MdResolutionDecisionForm.Fields.comment, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.resolutionComment),
            withInputField(MdResolutionDecisionForm.Fields.evidence, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.resolutionEvidence))
        .exclusiveGateway(Steps.CHECK_DECISION)
        .serviceStep(Steps.KEEP_BASE_RESOLUTION, overlayKeepBaseStep, false)
        .serviceStep(Steps.PREVIOUS_DAY_RESOLUTION, overlayPreviousDayStep, false)
        .serviceStep(Steps.SWITCH_TO_ASK_RESOLUTION, overlaySwitchToAskStep, false)
        .serviceStep(Steps.SWITCH_TO_BID_RESOLUTION, overlaySwitchToBidStep, false)
        .serviceStep(Steps.OVERRIDE_USER_RESOLUTION, overlayOverrideUserStep, false)
        .serviceStep(Steps.DELTA_SECONDARY_RESOLUTION, overlayDeltaSecondaryStep, false)
        .serviceStep(Steps.SWITCH_PROVIDER_RESOLUTION, switchProviderStep, false)
        .userTask(
            Steps.APPROVE_RESOLUTION,
            assignableTo(
                candidateTeams(
                    "@cachingTasksDefinitionService.getTasksDefinition(T(com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType).OVERLAY, context.stateDate)?.combinedTaskTeams(context.instrument.assetClass, @cachingTasksDefinitionService.getCachedMdTaskDefaultTeams())?.approvalTeams"),
                excludedUsers("{state.resolver?.userId}")),
            withInputField(ResolutionApprovalForm.Fields.user, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.approver),
            withInputField(ResolutionApprovalForm.Fields.status, FieldType.ENUM)
                .asProperty(MdOverlayState.Fields.approval),
            withInputField(ResolutionApprovalForm.Fields.comment, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.approvalComment),
            withInputField(ResolutionApprovalForm.Fields.evidence, FieldType.STRING)
                .asProperty(MdOverlayState.Fields.approvalEvidence))
        .exclusiveGateway(Steps.CHECK_APPROVAL)
        .serviceStep(Steps.VERIFY_RESOLUTION, verifyOverlayResolutionStep, false)
        .serviceStep(Steps.REJECT_RESOLUTION, rejectOverlayResolutionStep, false)
        .boundaryEvents(
            boundaryEvent(
                    Events.INVALID_RESOLVED_VALUE, EventType.ERROR, CatchingEffect.INTERRUPTING)
                .attachedTo(
                    Steps.KEEP_BASE_RESOLUTION,
                    Steps.PREVIOUS_DAY_RESOLUTION,
                    Steps.SWITCH_TO_ASK_RESOLUTION,
                    Steps.SWITCH_TO_BID_RESOLUTION,
                    Steps.DELTA_SECONDARY_RESOLUTION,
                    Steps.OVERRIDE_USER_RESOLUTION,
                    Steps.SWITCH_PROVIDER_RESOLUTION))
        .serviceStep(Steps.AUTO_REJECT_INVALID_VALUE, autoRejectOverlayInvalidValueStep, false)
        .flows(
            from(Steps.START).to(Steps.RUN_BREAK_TESTS),
            from(Steps.RUN_BREAK_TESTS).to(Steps.CHECK_BREAKS),
            from(Steps.CHECK_BREAKS).when("state.hasBreak").to(Steps.DECIDE_RESOLUTION),
            from(Steps.CHECK_BREAKS).to(Steps.SAVE_RESULT),
            from(Steps.DECIDE_RESOLUTION).to(Steps.CHECK_DECISION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).PREVIOUS_DAY")
                .to(Steps.PREVIOUS_DAY_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).SWITCH_TO_ASK")
                .to(Steps.SWITCH_TO_ASK_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).SWITCH_TO_BID")
                .to(Steps.SWITCH_TO_BID_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).OVERRIDE_USER")
                .to(Steps.OVERRIDE_USER_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).DELTA_SECONDARY")
                .to(Steps.DELTA_SECONDARY_RESOLUTION),
            from(Steps.CHECK_DECISION)
                .when(
                    "state.resolution == T(com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType).SWITCH_TO_SECONDARY")
                .to(Steps.SWITCH_PROVIDER_RESOLUTION),
            from(Steps.CHECK_DECISION).to(Steps.KEEP_BASE_RESOLUTION), // Default branch
            from(Steps.KEEP_BASE_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.PREVIOUS_DAY_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.SWITCH_TO_ASK_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.SWITCH_TO_BID_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.OVERRIDE_USER_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.DELTA_SECONDARY_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.SWITCH_PROVIDER_RESOLUTION).to(Steps.APPROVE_RESOLUTION),
            from(Steps.APPROVE_RESOLUTION).to(Steps.CHECK_APPROVAL),
            from(Steps.AUTO_REJECT_INVALID_VALUE).to(Steps.DECIDE_RESOLUTION),
            from(Steps.CHECK_APPROVAL)
                .when(
                    "state.approval == T(com.solum.xplain.xm.excmngmt.enums.VerificationStatus).REJECTED")
                .to(Steps.REJECT_RESOLUTION),
            from(Steps.CHECK_APPROVAL).to(Steps.VERIFY_RESOLUTION), // Default branch
            from(Steps.VERIFY_RESOLUTION).to(Steps.SAVE_RESULT),
            from(Steps.REJECT_RESOLUTION).to(Steps.DECIDE_RESOLUTION),
            from(Events.INVALID_RESOLVED_VALUE).to(Steps.AUTO_REJECT_INVALID_VALUE))
        .build();
  }
}
