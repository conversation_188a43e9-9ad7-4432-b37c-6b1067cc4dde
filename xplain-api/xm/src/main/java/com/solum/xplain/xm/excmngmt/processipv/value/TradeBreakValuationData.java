package com.solum.xplain.xm.excmngmt.processipv.value;

import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public record TradeBreakValuationData(Map<String, IpvDataProviderValueView> data) {

  public static TradeBreakValuationData empty() {
    return new TradeBreakValuationData(Map.of());
  }

  public IpvDataProviderValueView providerData(String provider) {
    return Optional.ofNullable(data.get(provider)).orElse(IpvDataProviderValueView.EMPTY);
  }

  public Set<String> uniqueProviders() {
    return data.keySet();
  }

  public Map<String, BigDecimal> toPvs() {
    return data.entrySet().stream()
        .filter(v -> v.getValue().getValue() != null)
        .collect(Collectors.toMap(Entry::getKey, v -> v.getValue().getValue()));
  }
}
