package com.solum.xplain.xm.excmngmt;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository;
import com.solum.xplain.xm.tasks.ExceptionManagementTaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import com.solum.xplain.xm.workflow.XmWorkflowService;
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

@AllArgsConstructor
public abstract class ExceptionManagementService {

  protected static final String NO_ACTIONABLE_ENTRIES_FOUND =
      "No entries found to which this action could be applied.";
  protected final ExceptionManagementEvidenceRepository exceptionManagementEvidenceRepository;
  protected final AuthenticationContext authenticationContext;
  protected final ViewQueryTranslatorFactory viewQueryTranslatorFactory;
  protected final XmStepInstanceQueryRepository xmStepInstanceQueryRepository;
  protected final XmWorkflowService xmWorkflowService;

  protected <T extends ExceptionManagementTaskExecution>
      Either<ErrorItem, ExceptionManagementEvidence> storeEvidence(
          List<T> tasks, @Nullable MultipartFile evidence) {
    if (evidence == null) {
      return Either.right(new ExceptionManagementEvidence());
    }
    var dashboardIds =
        tasks.stream().map(ExceptionManagementTaskExecution::getDashboardId).distinct().toList();
    return exceptionManagementEvidenceRepository.saveFile(dashboardIds, evidence);
  }

  protected <T extends ExceptionManagementTaskExecution> Either<ErrorItem, List<T>> validateTasks(
      List<T> tasks, TaskExecutionStatus status) {
    if (!tasks.stream().allMatch(v -> v.getStatus().equals(status))) {
      return Either.left(
          Error.OPERATION_NOT_ALLOWED.entity(
              "Required action requires all tasks to be in " + "status: " + status));
    }
    return Either.right(tasks);
  }

  protected Either<ErrorItem, ExceptionManagementEvidence> storeWorkflowEvidence(
      List<String> businessKeys, MultipartFile evidence) {
    if (evidence == null) {
      return Either.right(new ExceptionManagementEvidence());
    }

    List<String> dashboardIds = xmWorkflowService.getDashboardIds(businessKeys);
    return exceptionManagementEvidenceRepository.saveFile(dashboardIds, evidence);
  }
}
