package com.solum.xplain.xm.excmngmt.rulesbase;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listMinorVersionedLatest;
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.minorVersionedByIdLatest;
import static com.solum.xplain.core.error.Error.OBJECT_IN_USE;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.SpringDataAggregationOperations.set;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.merge;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.replaceRoot;
import static org.springframework.data.mongodb.core.aggregation.ArithmeticOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.Size.lengthOfArray;
import static org.springframework.data.mongodb.core.aggregation.BooleanOperators.And.and;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.IfNull.ifNull;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Switch.CaseOperator.when;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.switchCases;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.MergeOperation.MergeOperationTarget.collection;
import static org.springframework.data.mongodb.core.aggregation.MergeOperation.WhenDocumentsDontMatch.insertNewDocument;
import static org.springframework.data.mongodb.core.aggregation.MergeOperation.WhenDocumentsMatch.keepExistingDocument;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.value.EnableForm;
import com.solum.xplain.core.common.value.ResequenceForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.MinorVersionedEntity;
import com.solum.xplain.core.common.versions.OrderedMinorVersionedEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestForm;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestView;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestOverrideKey;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ComparisonOperators;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

public abstract class BaseBreakTestRepository<
    I,
    O extends BaseBreakTestOverride<I>,
    T extends BaseBreakTest<I, O, T>,
    F1 extends BaseBreakTestForm,
    F2 extends BaseBreakTestOverrideForm,
    V1 extends BaseBreakTestView,
    V2 extends BaseBreakTestOverrideView> {

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final AuditingHandler auditingHandler;
  private final BaseBreakTestMapper<I, O, T, F1, F2, V1, V2> mapper;
  private final Class<T> genericType;
  private final Class<V1> genericTypeView;

  protected BaseBreakTestRepository(
      MongoOperations mongoOperations,
      ConversionService conversionService,
      AuditingHandler auditingHandler,
      BaseBreakTestMapper<I, O, T, F1, F2, V1, V2> mapper,
      Class<T> genericType,
      Class<V1> genericTypeView) {
    this.mongoOperations = mongoOperations;
    this.conversionService = conversionService;
    this.auditingHandler = auditingHandler;
    this.mapper = mapper;
    this.genericType = genericType;
    this.genericTypeView = genericTypeView;
  }

  protected abstract T newEntity();

  public ScrollableEntry<V1> getAll(
      TableFilter tableFilter, BreakTestFilter filter, ScrollRequest scrollRequest) {
    var baseOperationsBuilder =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest())
            .add(match(filter.criteria()))
            .add(projectToView())
            .add(match(tableFilter.criteria(genericTypeView, conversionService)));

    var aggregation =
        newAggregation(
            genericType,
            ImmutableList.<AggregationOperation>builder()
                .addAll(baseOperationsBuilder.build())
                .add(count().as(COUNT_FIELD))
                .build());

    var totalResult = mongoOperations.aggregate(aggregation, Map.class);
    return ofNullable(totalResult.getUniqueMappedResult())
        .map(r -> r.get(COUNT_FIELD))
        .map(Number.class::cast)
        .map(
            t ->
                ScrollableEntry.of(
                    breakTests(scrollRequest, baseOperationsBuilder.build()),
                    scrollRequest,
                    t.longValue()))
        .orElse(ScrollableEntry.empty());
  }

  public List<T> latestTests(BitemporalDate stateDate) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest(stateDate))
            .add(match(new BreakTestFilter(false).criteria()));

    return mongoOperations
        .aggregate(newAggregation(genericType, operations.build()), genericType)
        .getMappedResults();
  }

  public List<V1> getVersions(String id) {
    var baseOperationsBuilder =
        ImmutableList.<AggregationOperation>builder()
            .add(match(where(MinorVersionedEntity.Fields.entityId).is(id)))
            .add(projectToView());

    return mongoOperations
        .aggregateAndReturn(genericTypeView)
        .by(newAggregation(genericType, baseOperationsBuilder.build()))
        .all()
        .getMappedResults();
  }

  public Either<ErrorItem, V1> getOne(String id) {
    return mongoOperations
        .query(genericType)
        .matching(minorVersionedByIdLatest(id))
        .first()
        .map(mapper::toView)
        .map(Either::<ErrorItem, V1>right)
        .orElse(left(OBJECT_NOT_FOUND.entity("Break test not found")));
  }

  public boolean isParentOfAnotherTest(String id) {
    return existsActiveByCriteria(
        where(joinPaths(BaseBreakTest.Fields.parentTest, BreakTestParent.Fields.parentId)).is(id));
  }

  public boolean existsWithName(String name, String id) {
    return existsActiveByCriteria(
        where(BaseBreakTest.Fields.name).is(name).and(MinorVersionedEntity.Fields.entityId).ne(id));
  }

  public Either<ErrorItem, EntityId> insert(F1 f) {
    return fromForm(f, newEntity())
        .flatMap(T::allowModify)
        .map(auditingHandler::markCreated)
        .map(this::insertWithSequenceNumber)
        .map(t -> entityId(t.getEntityId()));
  }

  /**
   * Use an aggregation operation to count the existing distinct entityIds and set the sequence
   * number on the new record to the count + 1, before merging it in to the collection.
   *
   * @param t the new entity to insert
   * @return the inserted entity, with sequence number set
   */
  private T insertWithSequenceNumber(T t) {
    Document doc = new Document();
    mongoOperations.getConverter().write(t, doc);

    doc.put(OrderedMinorVersionedEntity.Fields.sequence, valueOf("_count").add(1).toDocument());
    String collectionName = mongoOperations.getCollectionName(genericType);
    var aggregation =
        newAggregation(
            genericType,
            group(MinorVersionedEntity.Fields.entityId),
            count().as("_count"),
            replaceRoot(ctx -> doc),
            merge()
                .into(collection(collectionName))
                .on(UNDERSCORE_ID)
                .whenMatched(keepExistingDocument())
                .whenNotMatched(insertNewDocument())
                .build());
    mongoOperations.aggregate(
        aggregation.withOptions(
            Aggregation.newAggregationOptions().allowDiskUse(true).skipOutput().build()),
        genericType);

    // Merge can't return results so have to issue another query to get the only record for this
    // entityId.
    T result =
        mongoOperations.findOne(
            Query.query(where(MinorVersionedEntity.Fields.entityId).is(t.getEntityId())),
            genericType);

    // If this returns null, then we tried to insert into an empty collection where the aggregation
    // pipeline would fail, so we do a normal insert instead. This will only affect the first
    // onboarding break test, as IPV and MD break tests are initialised when the system is created.
    if (result == null) {
      doc.put(OrderedMinorVersionedEntity.Fields.sequence, 1);
      mongoOperations.insert(doc, collectionName);
      result = t;
    }

    return result;
  }

  public Either<ErrorItem, EntityId> update(String id, F1 f) {
    return update(id, e -> fromForm(f, e));
  }

  public Either<ErrorItem, EntityId> enableDisable(String entityId, EnableForm form) {
    Either<ErrorItem, T> entity = breakTestEntity(entityId);
    return entity
        .flatMap(e -> update(e, t -> right(t.enabled(form.getEnabled()))))
        // If we can't disable it (i.e. because it's internal) then set the sequence to null to hide
        // it instead.
        .orElse(
            () ->
                entity.bimap(
                    errorItem -> errorItem,
                    e -> {
                      e.setSequence(form.getEnabled() ? 1 : null);
                      mongoOperations.save(e);
                      return entityId(e.getEntityId());
                    }));
  }

  /**
   * Move the entity with the given entityId to a new sequence number. Uses an aggregation pipeline
   * to update the sequence numbers of all other affected entities in the collection. If the
   * sequence numbers provided are equal, this is a no-op.
   *
   * <p>The entity is moved to replace appear in sequence before the entity currently at {@link
   * ResequenceForm#getToBeforeSequence()} . That means if {@code toBeforeSequence} is above the
   * current {@code fromSequence}, the entity will be moved to {@code toBeforeSequence - 1}, whereas
   * if it is below, it will be moved to {@code toBeforeSequence}.
   *
   * <p>Note that {@link ResequenceForm#getFromSequence()} should match the current sequence number
   * of the entity to be moved. Behaviour is undefined if it does not.
   *
   * @param id the entityId of the entity to move
   * @param form the form containing the sequence numbers to move from and to
   * @return the entityId of the moved entity as provided
   */
  public Either<ErrorItem, EntityId> resequence(String id, ResequenceForm form) {
    if (form.getFromSequence().equals(form.getToBeforeSequence())) {
      return right(entityId(id));
    }

    boolean movingUp = form.getFromSequence() < form.getToBeforeSequence();
    // Figure out the range of other sequence values which have to be adjusted for us to move.
    int lowIncl = movingUp ? form.getFromSequence() + 1 : form.getToBeforeSequence();
    int highExcl = movingUp ? form.getToBeforeSequence() : form.getFromSequence();
    int targetSequence = movingUp ? form.getToBeforeSequence() - 1 : form.getToBeforeSequence();
    AggregationExpression inbetweenCondition =
        and(
            ComparisonOperators.valueOf(OrderedMinorVersionedEntity.Fields.sequence)
                .greaterThanEqualToValue(lowIncl),
            ComparisonOperators.valueOf(OrderedMinorVersionedEntity.Fields.sequence)
                .lessThanValue(highExcl));
    var inbetweenAdjustment =
        movingUp
            ? valueOf(OrderedMinorVersionedEntity.Fields.sequence).subtract(1)
            : valueOf(OrderedMinorVersionedEntity.Fields.sequence).add(1);

    var pipeline =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest())
            .add(
                project(
                    UNDERSCORE_ID,
                    MinorVersionedEntity.Fields.entityId,
                    OrderedMinorVersionedEntity.Fields.sequence),
                set(OrderedMinorVersionedEntity.Fields.sequence)
                    .toValueOf(
                        switchCases(
                                when(ComparisonOperators.valueOf(
                                            MinorVersionedEntity.Fields.entityId)
                                        .equalToValue(id))
                                    .then(targetSequence),
                                when(inbetweenCondition).then(inbetweenAdjustment))
                            .defaultTo(Fields.field(OrderedMinorVersionedEntity.Fields.sequence))),
                merge().into(collection(mongoOperations.getCollectionName(genericType))).build());

    mongoOperations.aggregate(
        Aggregation.newAggregation(genericType, pipeline.build())
            .withOptions(
                Aggregation.newAggregationOptions().allowDiskUse(true).skipOutput().build()),
        genericType);

    return right(entityId(id));
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    if (isParentOfAnotherTest(id)) {
      return left(OBJECT_IN_USE.entity("Break test is in use"));
    }
    return update(id, bt -> right(bt.archived()));
  }

  // Overrides

  public List<V2> getAllOverrides() {
    var baseOperationsBuilder =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest())
            .add(match(new BreakTestFilter(false).criteria()));

    var aggregation = newAggregation(genericType, baseOperationsBuilder.build());
    return mongoOperations.aggregate(aggregation, genericType).getMappedResults().stream()
        .map(mapper::toOverrideViews)
        .flatMap(Collection::stream)
        .toList();
  }

  public List<V2> getOverrides(BreakTestOverrideKey key) {
    return breakTestEntity(key.id()).map(mapper::toOverrideViews).getOrElse(List.of());
  }

  public Either<ErrorItem, V2> getOverride(BreakTestOverrideKey key) {
    return breakTestEntity(key.id())
        .flatMap(e -> breakTestOverride(key, e).map(o -> mapper.fromOverride(o, e.getName())));
  }

  public Either<ErrorItem, EntityId> createOverride(BreakTestOverrideKey key, F2 f) {
    return update(key.id(), v -> v.addOverride(mapper.fromOverrideForm(f)));
  }

  public Either<ErrorItem, EntityId> updateOverride(BreakTestOverrideKey key, F2 form) {
    return updateOverride(
        key, v -> v.updateOverride(key.overrideId(), o -> mapper.fromOverrideForm(form, o)));
  }

  public Either<ErrorItem, EntityId> deleteOverride(BreakTestOverrideKey key) {
    return updateOverride(key, v -> v.removeOverride(key.overrideId()));
  }

  public Either<ErrorItem, EntityId> enableDisableOverride(
      BreakTestOverrideKey key, EnableForm form) {
    return updateOverride(
        key,
        v ->
            v.updateOverride(
                key.overrideId(),
                o -> {
                  o.setEnabled(form.getEnabled());
                  return o;
                }));
  }

  public Either<ErrorItem, EntityId> cloneOverride(BreakTestOverrideKey key) {
    return updateOverride(key, v -> v.cloneOverride(key.overrideId(), mapper::copy));
  }

  private Either<ErrorItem, EntityId> update(
      String id, Function<T, Either<ErrorItem, T>> modifyFn) {
    return breakTestEntity(id).flatMap(e -> update(e, modifyFn));
  }

  private Either<ErrorItem, EntityId> update(
      T breakTestEntity, Function<T, Either<ErrorItem, T>> modifyFn) {
    return breakTestEntity.allowModify().flatMap(entity -> applyFunction(entity, modifyFn));
  }

  private Either<ErrorItem, EntityId> updateOverride(
      BreakTestOverrideKey key, Function<T, Either<ErrorItem, T>> modifyFn) {
    return breakTestEntity(key.id())
        .flatMap(T::allowModify)
        .flatMap(e -> breakTestOverride(key, e).flatMap(o -> applyFunction(e, modifyFn)));
  }

  private Either<ErrorItem, EntityId> applyFunction(
      T entity, Function<T, Either<ErrorItem, T>> modifyFn) {
    return modifyFn
        .apply(mapper.copy(entity))
        .map(
            newValue -> {
              if (!newValue.valueEquals(entity)) {
                mongoOperations.insert(newValue);
              }
              return entityId(newValue.getEntityId());
            });
  }

  private Either<ErrorItem, T> fromForm(F1 f, T v) {
    if (StringUtils.isNotEmpty(f.getParentBreakTestId())) {
      return getOne(f.getParentBreakTestId())
          .leftMap(e -> e.getReason().entity("Parent break test not found"))
          .map(bt -> BreakTestParent.parentTest(bt.getId(), bt.getName()))
          .map(parent -> mapper.fromForm(f, parent, v));
    }
    return right(mapper.fromForm(f, null, v));
  }

  protected boolean existsActiveByCriteria(Criteria criteria) {
    var aggregation =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest())
            .add(match(new BreakTestFilter(false).criteria()))
            .add(match(criteria))
            .add(limit(1))
            .build();
    return !mongoOperations
        .aggregateAndReturn(genericType)
        .by(newAggregation(aggregation))
        .all()
        .getMappedResults()
        .isEmpty();
  }

  // Helpers

  private Either<ErrorItem, T> breakTestEntity(String id) {
    return mongoOperations
        .query(genericType)
        .matching(minorVersionedByIdLatest(id))
        .first()
        .map(Either::<ErrorItem, T>right)
        .orElse(left(OBJECT_NOT_FOUND.entity("Break test not found")));
  }

  private Either<ErrorItem, O> breakTestOverride(BreakTestOverrideKey key, T e) {
    return e.overridesStream()
        .filter(o -> o.getId().equals(key.overrideId()))
        .findAny()
        .map(Either::<ErrorItem, O>right)
        .orElse(left(OBJECT_NOT_FOUND.entity("Break test override not found")));
  }

  private List<V1> breakTests(ScrollRequest scrollRequest, List<AggregationOperation> o) {
    var operationsBuilder = ImmutableList.<AggregationOperation>builder().addAll(o);
    operationsBuilder.addAll(new ScrollSortOperations(scrollRequest, UNDERSCORE_ID).build());
    var aggregation = newAggregation(genericType, operationsBuilder.build());
    return mongoOperations.aggregate(aggregation, genericTypeView).getMappedResults();
  }

  protected ProjectionOperation projectToView() {
    return project(
            BaseBreakTestView.Fields.name,
            BaseBreakTestView.Fields.sequence,
            BaseBreakTestView.Fields.enabled,
            BaseBreakTestView.Fields.operator,
            BaseBreakTestView.Fields.threshold,
            BaseBreakTestView.Fields.comment,
            BaseBreakTestView.Fields.archived)
        .and(MinorVersionedEntity.Fields.entityId)
        .as(UNDERSCORE_ID)
        .and(propertyName(MinorVersionedEntity.Fields.createdBy, AuditUser.Fields.username))
        .as(BaseBreakTestView.Fields.creatorName)
        .and(MinorVersionedEntity.Fields.createdAt)
        .as(BaseBreakTestView.Fields.createdAt)
        .and(lengthOfArray(ifNull(BaseBreakTest.Fields.overrides).then(List.of())))
        .as(BaseBreakTestView.Fields.numberOfOverrides)
        .and(propertyName(BaseBreakTest.Fields.parentTest, BreakTestParent.Fields.parentId))
        .as(BaseBreakTestView.Fields.parentBreakTestId)
        .and(propertyName(BaseBreakTest.Fields.parentTest, BreakTestParent.Fields.name))
        .as(BaseBreakTestView.Fields.parentBreakTestName);
  }
}
