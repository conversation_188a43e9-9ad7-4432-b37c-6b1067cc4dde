package com.solum.xplain.xm.excmngmt.processipv.resolution.classifiers;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvCalculationTestStatus;
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.processipv.resolution.holdresolution.HoldResolutionReasonRepository;
import com.solum.xplain.xm.excmngmt.processipv.resolution.holdresolution.value.HoldResolutionReason;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class IpvExceptionManagementResolutionTypesClassifierProvider {
  public static final String ENTRY_RESULT_STATUS_DETAIL = "entryResultStatusDetail";
  private final HoldResolutionReasonRepository holdResolutionReasonRepository;

  public Classifier ipvResolutions() {
    return new Classifier(
        "ipvExceptionManagementCalculationStatuses",
        null,
        resolutionClassifiers(),
        IpvCalculationTestStatus.class);
  }

  /**
   * This classifier is used for {@link IpvTradeOverlayResultView#getStatusDetail()} and is a
   * combination of {@link EntryResultStatus} without {@code HOLD} and {@link HoldResolutionReason}.
   *
   * @return classifier combining {@code EntryResultStatus} and {@code HoldResolutionReason} values.
   */
  public Classifier entryResultStatusDetail() {
    return new Classifier(
        ENTRY_RESULT_STATUS_DETAIL,
        null,
        Stream.concat(
                Stream.of(EntryResultStatus.values())
                    .filter(v -> v != EntryResultStatus.HOLD)
                    .map(v -> new Classifier(v.name(), v.getLabel())),
                holdResolutionReasonRepository
                    .holdResolutionReasons()
                    .map(r -> new Classifier(r.reasonId(), r.name())))
            .toList());
  }

  private List<Classifier> resolutionClassifiers() {
    return Stream.of(IpvCalculationTestStatus.values())
        .map(
            v ->
                new Classifier(
                    v.name(),
                    v.getAllowableResolutionTypes().stream().map(this::toClassifier).toList()))
        .toList();
  }

  private Classifier toClassifier(TradeResultResolutionType resolutionType) {
    if (resolutionType == TradeResultResolutionType.HOLD) {
      List<Classifier> holdSubTypes =
          holdResolutionReasonRepository
              .holdResolutionReasons()
              .map(r -> new Classifier(r.reasonId(), r.name()))
              .toList();

      return new Classifier(resolutionType.name(), holdSubTypes);
    }
    return new Classifier(resolutionType.name());
  }
}
