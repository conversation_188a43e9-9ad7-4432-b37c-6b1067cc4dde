package com.solum.xplain.xm.dashboardsteps.mdpreliminaryrun;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationService;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Supplier;
import org.springframework.stereotype.Component;

@Component
public class MdPreliminaryRunExecutor {

  private final DashboardStepProcessor processor;
  private final ExceptionManagementCalculationService exceptionManagementService;

  public MdPreliminaryRunExecutor(
      DashboardStepProcessor processor,
      ExceptionManagementCalculationService exceptionManagementService) {
    this.processor = processor;
    this.exceptionManagementService = exceptionManagementService;
  }

  public Either<List<ErrorItem>, EntityId> execute(Dashboard dashboard) {
    var stateDate = processor.getStateDate();
    return processor.performMdStep(
        stateDate, step(dashboard), resultProvider(dashboard, stateDate));
  }

  private DashboardEntryMd step(Dashboard dashboard) {
    return DashboardEntryMd.newOfPreliminaryRun(dashboard.getId());
  }

  private Supplier<Either<ErrorItem, EntityId>> resultProvider(
      Dashboard dashboard, BitemporalDate stateDate) {
    return () -> exceptionManagementService.performPreliminary(dashboard, stateDate);
  }
}
