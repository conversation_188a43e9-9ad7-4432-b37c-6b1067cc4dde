package com.solum.xplain.xm.dashboards.views.auditablebreaktest;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.instrument.InstrumentType;

public record DashboardBreakTestInstrumentView(
    AssetGroup assetClassGroup,
    AssetClass assetClass,
    String currency,
    String fxPair,
    String sector,
    String assetName,
    InstrumentType instrumentType,
    String tenor,
    String nodeInstrument,
    Double parsedTenor,
    String key,
    String mdkName,
    String underlying) {}
