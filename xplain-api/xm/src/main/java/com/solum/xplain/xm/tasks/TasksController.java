package com.solum.xplain.xm.tasks;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_TASK_DEFINITION;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_RUN_TASK_EXECUTION;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_DASHBOARD;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_TASK_DEFINITION;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_TASK_EXECUTION;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.form.TaskExecutionFilter;
import com.solum.xplain.xm.tasks.form.TaskExecutionIdsForm;
import com.solum.xplain.xm.tasks.form.TasksDefinitionForm;
import com.solum.xplain.xm.tasks.service.InstrumentTaskExecutionService;
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService;
import com.solum.xplain.xm.tasks.view.TasksDefinitionView;
import com.solum.xplain.xm.tasks.view.summary.TaskExecutionSummaryView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tasks")
public class TasksController {

  private final TaskDefinitionControllerService definitionService;
  private final MdTaskExecutionService taskExecutionService;

  public TasksController(
      TaskDefinitionControllerService definitionService,
      MdTaskExecutionService taskExecutionService) {
    this.definitionService = definitionService;
    this.taskExecutionService = taskExecutionService;
  }

  @Operation(summary = "Get task definition")
  @GetMapping("/definitions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TASK_DEFINITION)
  public TasksDefinitionView get(@RequestParam TaskExceptionManagementType type) {
    return definitionService.view(type);
  }

  @Operation(summary = "Update task definition")
  @PutMapping("/definitions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TASK_DEFINITION)
  public EntityId update(@Valid @RequestBody TasksDefinitionForm form) {
    return definitionService.update(form);
  }

  // Executions
  @Operation(summary = "Get task executions lists")
  @GetMapping("/executions")
  @CommonErrors
  @ScrolledFiltered
  @PreAuthorize(AUTHORITY_VIEW_DASHBOARD)
  public ResponseEntity<ScrollableEntry<TaskExecutionSummaryView>> getAllExecutions(
      Authentication auth,
      ScrollRequest scrollRequest,
      TableFilter filter,
      @RequestParam(required = false) String dashboardId,
      @RequestParam(required = false) LocalDate stateDate,
      @RequestParam(required = false)
          @Parameter(
              description =
                  "Flag to indicate whether to show all tasks or only those from the last 7 days")
          Boolean showLastWeekOnly) {
    return eitherErrorItemResponse(
        taskExecutionService.list(
            auth,
            filter,
            scrollRequest,
            dashboardId,
            TaskExecutionFilter.adminFilter(),
            stateDate,
            showLastWeekOnly));
  }

  @Operation(summary = "Get team task executions lists")
  @GetMapping("/executions/team")
  @CommonErrors
  @ScrolledFiltered
  @PreAuthorize(AUTHORITY_VIEW_TASK_EXECUTION)
  public ResponseEntity<ScrollableEntry<TaskExecutionSummaryView>> getTeamExecutions(
      Authentication auth,
      ScrollRequest scrollRequest,
      TableFilter filter,
      @RequestParam(required = false) LocalDate stateDate,
      @RequestParam(required = false)
          @Parameter(
              description =
                  "Flag to indicate whether to show all tasks or only those from the last 7 days")
          Boolean showLastWeekOnly) {
    return eitherErrorItemResponse(
        taskExecutionService.list(
            auth,
            filter,
            scrollRequest,
            null,
            TaskExecutionFilter.userFilter(),
            stateDate,
            showLastWeekOnly));
  }

  @Operation(summary = "Cancel own task execution")
  @PostMapping("/executions/{type}/{id}/cancel")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<EntityId> cancelOwnTask(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @PathVariable String id) {
    return eitherErrorItemResponse(service(type).cancelOwnTask(auth, id));
  }

  @Operation(summary = "Cancel any task execution")
  @PostMapping("/executions/{type}/{id}/cancel/admin")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<EntityId> cancelAnyTask(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @PathVariable String id) {
    return eitherErrorItemResponse(service(type).cancelTask(auth, id));
  }

  @Operation(summary = "Start work on tasks")
  @PostMapping("/executions/{type}/start")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> startWork(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @Valid @RequestBody TaskExecutionIdsForm taskIds) {
    return eitherErrorItemsResponse(service(type).startTasksProgress(auth, taskIds.taskIds()));
  }

  @Operation(summary = "Submit work on tasks")
  @PostMapping("/executions/{type}/submit")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> submitTasks(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @Valid @RequestBody TaskExecutionIdsForm taskIds) {
    return eitherErrorItemsResponse(service(type).submitTasksResolution(auth, taskIds.taskIds()));
  }

  @Operation(summary = "Assign task verification")
  @PostMapping("/executions/{type}/start-verify")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> startVerification(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @Valid @RequestBody TaskExecutionIdsForm taskIds) {
    return eitherErrorItemsResponse(service(type).startTasksApproval(auth, taskIds.taskIds()));
  }

  @Operation(summary = "Verify work on tasks")
  @PostMapping("/executions/{type}/verify")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_TASK_EXECUTION)
  public ResponseEntity<List<EntityId>> verifyTasks(
      Authentication auth,
      @PathVariable TaskExceptionManagementType type,
      @Valid @RequestBody TaskExecutionIdsForm taskIds) {
    return eitherErrorItemsResponse(service(type).submitTasksApproval(auth, taskIds.taskIds()));
  }

  private InstrumentTaskExecutionService service(TaskExceptionManagementType type) {
    return taskExecutionService.getTaskService(type);
  }
}
