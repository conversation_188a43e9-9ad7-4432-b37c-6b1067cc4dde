package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

public record DashboardTaskGroup(
    String curveConfigurationId,
    String curveConfigurationName,
    String companyId,
    String companyExternalId,
    String legalEntityId,
    String legalEntityExternalId) {

  public static DashboardTaskGroup newOf(TaskExecution execution) {
    return new DashboardTaskGroup(
        execution.getCurveConfigurationId(),
        execution.getCurveConfigurationName(),
        execution.getCompanyId(),
        execution.getCompanyExternalId(),
        execution.getLegalEntityId(),
        execution.getLegalEntityExternalId());
  }

  @Nullable
  public EntityReference curveConfigurationReference() {
    if (StringUtils.isEmpty(curveConfigurationId)) {
      return null;
    }
    return EntityReference.newOf(curveConfigurationId, curveConfigurationName);
  }

  @Nullable
  public EntityReference companyReference() {
    if (StringUtils.isEmpty(companyId)) {
      return null;
    }
    return EntityReference.newOf(companyId, companyExternalId);
  }

  @Nullable
  public EntityReference legalEntityReference() {
    if (StringUtils.isEmpty(legalEntityId)) {
      return null;
    }
    return EntityReference.newOf(legalEntityId, legalEntityExternalId);
  }
}
