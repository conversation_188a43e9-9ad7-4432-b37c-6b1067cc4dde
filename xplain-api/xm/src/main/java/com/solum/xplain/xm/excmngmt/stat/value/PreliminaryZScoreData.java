package com.solum.xplain.xm.excmngmt.stat.value;

import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import org.springframework.util.Assert;

public record PreliminaryZScoreData(
    String provider,
    ValueBidAskType pricePoint,
    String instKey,
    Double mean,
    Double stdDev,
    int count)
    implements ZScoreData {

  @Override
  public double getMean() {
    Assert.notNull(mean, "Unable to calculate Z-Score. Mean is null");
    return mean;
  }

  @Override
  public double getStdDev() {
    Assert.notNull(stdDev, "Unable to calculate Z-Score. Standard deviation is null");
    return stdDev;
  }
}
