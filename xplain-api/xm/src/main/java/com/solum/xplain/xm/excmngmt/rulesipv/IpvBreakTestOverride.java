package com.solum.xplain.xm.excmngmt.rulesipv;

import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTestOverride;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.NonNull;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class IpvBreakTestOverride extends BaseBreakTestOverride<Trade> {

  private TradeFilter tradeFilter;

  @Override
  public boolean matches(@NonNull Trade trade) {
    return tradeFilter.matches(trade);
  }
}
