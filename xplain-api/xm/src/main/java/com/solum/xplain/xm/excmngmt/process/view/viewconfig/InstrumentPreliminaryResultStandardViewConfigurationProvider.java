package com.solum.xplain.xm.excmngmt.process.view.viewconfig;

import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.provider.StandardViewConfigurationProvider;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import org.springframework.stereotype.Component;

/**
 * Define standard view configurations for {@link InstrumentPreliminaryResultView} and {@link
 * InstrumentOverlayResultView}.
 */
@Component
public class InstrumentPreliminaryResultStandardViewConfigurationProvider
    extends StandardViewConfigurationProvider {

  public InstrumentPreliminaryResultStandardViewConfigurationProvider(
      PaletteService paletteService) {
    super(
        InstrumentPreliminaryResultView.class,
        paletteService,
        "66290a3e3d0c5109c3f62505",
        () -> new DefaultColumnsBuilder(true),
        "66290a3f3d0c5109c3f62506",
        () -> new MinimalColumnsBuilder(true));
  }
}
