package com.solum.xplain.xm.excmngmt.stat.value;

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.google.common.collect.ImmutableMap;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.excmngmt.market.value.ResolvedInstrument;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DiffCalculator {

  private final List<LocalDate> availableDates;
  private final Map<String, Map<LocalDate, Double>> instrumentDiffs;

  public static DiffCalculator of(
      @NotNull List<ResolvedInstrument> data,
      @NotNull Map<String, MarketDataProviders> providerByInstrument,
      boolean primary) {
    UnaryOperator<String> resolveProviderFunc =
        key ->
            ofNullable(providerByInstrument.get(key))
                .map(p -> primary ? p.getPrimary() : p.getSecondary())
                .orElse(null);

    var availableDates =
        data.stream().map(ResolvedInstrument::getDate).distinct().sorted().toList();

    var diffs =
        data.stream().collect(groupingBy(ResolvedInstrument::getInstrumentKey)).entrySet().stream()
            .collect(
                toMap(
                    Map.Entry::getKey,
                    e ->
                        dataForInstrument(
                            availableDates, e.getValue(), resolveProviderFunc.apply(e.getKey()))));

    return new DiffCalculator(availableDates.stream().skip(1).sorted().toList(), diffs);
  }

  private static Map<LocalDate, Double> dataForInstrument(
      List<LocalDate> availableDates, List<ResolvedInstrument> instrumentStats, String provider) {
    var data =
        instrumentStats.stream()
            .filter(i -> i.resolvedData().getProvider().equals(provider))
            .filter(i -> i.resolvedValue() != null)
            .collect(toMap(ResolvedInstrument::getDate, Function.identity()));

    var diffs = new HashMap<LocalDate, Double>();
    LocalDate prevDate = null;
    for (LocalDate date : availableDates) {
      if (prevDate == null) {
        prevDate = date;
        continue;
      }
      var v = data.get(date);
      var vPrev = data.get(prevDate);
      if (allNotNull(v, vPrev) && allNotNull(v.resolvedValue(), vPrev.resolvedValue())) {
        diffs.put(date, v.resolvedValue().subtract(vPrev.resolvedValue()).doubleValue());
      }
      prevDate = date;
    }
    return diffs;
  }

  public Map<String, Either<List<ErrorItem>, Map<LocalDate, Double>>> validatedData() {
    var b = new ImmutableMap.Builder<String, Either<List<ErrorItem>, Map<LocalDate, Double>>>();
    for (var i : instrumentDiffs.entrySet()) {
      validate(i.getValue(), i.getKey())
          .ifPresentOrElse(
              e -> b.put(i.getKey(), left(List.of(e))),
              () -> b.put(i.getKey(), right(i.getValue())));
    }
    return b.build();
  }

  private Optional<ErrorItem> validate(Map<LocalDate, Double> i, String key) {
    return availableDates.stream()
        .filter(date -> i == null || i.get(date) == null)
        .findAny()
        .map(date -> CALCULATION_ERROR.entity("Data not available for " + key + " for " + date));
  }

  public Map<String, Either<List<ErrorItem>, Map<LocalDate, Double>>> validatedData(
      DiffCalculator fallback) {
    var b = new ImmutableMap.Builder<String, Either<List<ErrorItem>, Map<LocalDate, Double>>>();
    for (var i : instrumentDiffs.entrySet()) {
      var instrument = i.getKey();
      validate(i.getValue(), instrument)
          .ifPresentOrElse(
              e -> {
                var f = fallback.instrumentDiffs.get(instrument);
                validate(f, instrument)
                    .ifPresentOrElse(
                        fe -> b.put(instrument, left(List.of(fe))),
                        () -> b.put(instrument, right(f)));
              },
              () -> b.put(instrument, right(i.getValue())));
    }
    return b.build();
  }

  public List<LocalDate> getAvailableDates() {
    return availableDates;
  }

  public interface DiffCalculatorSupplier {
    DiffCalculator supply(
        @NotNull List<ResolvedInstrument> data,
        @NotNull Map<String, MarketDataProviders> providerByInstrument,
        boolean primary);
  }
}
