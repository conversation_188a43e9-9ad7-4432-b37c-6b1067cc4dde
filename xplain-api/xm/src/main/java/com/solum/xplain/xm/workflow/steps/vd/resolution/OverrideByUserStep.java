package com.solum.xplain.xm.workflow.steps.vd.resolution;

import static com.solum.xplain.xm.workflow.steps.vd.resolution.Errors.resolvedValueIsEmpty;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

/** Workflow step to switch the base value to one provided directly by the user. */
@Component
@RequiredArgsConstructor
public class OverrideByUserStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {
  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    if (ops.getInitialState().getManualResolutionValue() == null) {
      ops.throwError(resolvedValueIsEmpty(ops.getContext()));
    }
    AttributedValue valuePendingApproval =
        new AttributedValue(
            ops.getInitialState().getManualResolutionValue(),
            IpvProvidersType.MANUAL,
            ops.getInitialState().getResolver().getUsername());
    ops.setOutcome(
        new MutablePropertyValues()
            .add(VdPhaseState.Fields.valuePendingApproval, valuePendingApproval)
            .add(VdPhaseState.Fields.entryStatus, EntryResultStatus.WAITING_APPROVAL));
  }
}
