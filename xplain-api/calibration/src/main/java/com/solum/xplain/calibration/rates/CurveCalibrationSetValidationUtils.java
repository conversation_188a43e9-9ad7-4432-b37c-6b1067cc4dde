package com.solum.xplain.calibration.rates;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static java.lang.String.format;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataId;
import com.opengamma.strata.market.curve.CurveDefinition;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.RatesCurveGroupEntry;
import com.opengamma.strata.market.observable.IndexQuoteId;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurveCalibrationSetValidationUtils {
  private static final Integer MONTHS_IN_A_YEAR = 12;
  private static final String MISSING_MARKET_DATA_ERROR_TEMPLATE =
      "Market data required by curve %s not found for identifier: %s";
  private static final String MULTIPLE_INDICES_ERROR_TEMPLATE =
      "Multiple indices %s found for calibration set: %s";
  private static final String MISSING_FIXINGS_ERROR_TEMPLATE =
      "At least one fixing is required for %s index in the last 12 months";
  private static final String MULTIPLE_DISCOUNT_CURRENCIES_ERROR_TEMPLATE =
      "Multiple discount currencies %s found for calibration set: %s";
  private static final String NO_CURVES_ERROR_TEMPLATE = "No curves in calibration set: %s";

  public static Either<ErrorItem, List<RatesCurveGroupEntry>> validateCurveGroupEntries(
      Currency currency, List<RatesCurveGroupEntry> curveGroupEntries) {
    if (curveGroupEntries.isEmpty()) {
      var error = CALIBRATION_ERROR.entity(String.format(NO_CURVES_ERROR_TEMPLATE, currency));
      return Either.left(error);
    } else {
      return validateIndices(currency, curveGroupEntries)
          .flatMap(e -> validateCurrencies(currency, e));
    }
  }

  private static Either<ErrorItem, List<RatesCurveGroupEntry>> validateCurrencies(
      Currency currency, List<RatesCurveGroupEntry> curveGroupEntries) {
    return curveGroupEntries.stream()
        .flatMap(e -> e.getDiscountCurrencies().stream())
        .collect(groupingBy(Function.identity(), counting()))
        .entrySet()
        .stream()
        .filter(v -> v.getValue() > 1)
        .findAny()
        .map(Map.Entry::getKey)
        .map(e -> duplicateDiscountCurrenciesError(e, currency))
        .orElse(Either.right(curveGroupEntries));
  }

  private static Either<ErrorItem, List<RatesCurveGroupEntry>> duplicateDiscountCurrenciesError(
      Currency duplicateCurrency, Currency discountCurrency) {
    var errorMsg =
        format(MULTIPLE_DISCOUNT_CURRENCIES_ERROR_TEMPLATE, duplicateCurrency, discountCurrency);
    return Either.left(CALIBRATION_ERROR.entity(errorMsg));
  }

  private static Either<ErrorItem, List<RatesCurveGroupEntry>> validateIndices(
      Currency currency, List<RatesCurveGroupEntry> curveGroupEntries) {
    return curveGroupEntries.stream()
        .flatMap(e -> e.getIndices().stream())
        .collect(groupingBy(Function.identity(), counting()))
        .entrySet()
        .stream()
        .filter(v -> v.getValue() > 1)
        .findAny()
        .map(Map.Entry::getKey)
        .map(e -> multipleIndicesError(e, currency))
        .orElse(Either.right(curveGroupEntries));
  }

  private static Either<ErrorItem, List<RatesCurveGroupEntry>> multipleIndicesError(
      Index index, Currency currency) {
    var errorMsg = format(MULTIPLE_INDICES_ERROR_TEMPLATE, index, currency);
    return Either.left(CALIBRATION_ERROR.entity(errorMsg));
  }

  public static Either<ErrorItem, List<RatesCurveGroupEntry>> validateFixings(
      List<RatesCurveGroupEntry> entries, MarketData marketData) {
    return entries.stream()
        .map(RatesCurveGroupEntry::getIndices)
        .flatMap(v -> v.stream().filter(PriceIndex.class::isInstance))
        .map(index -> validateFixingValue(index, marketData))
        .flatMap(Optional::stream)
        .findFirst()
        .map(Either::<ErrorItem, List<RatesCurveGroupEntry>>left)
        .orElse(Either.right(entries));
  }

  private static Optional<ErrorItem> validateFixingValue(Index priceIndex, MarketData marketData) {
    return Optional.ofNullable(marketData.getTimeSeries(IndexQuoteId.of(priceIndex)))
        .filter(v -> !v.isEmpty())
        .filter(
            v ->
                ChronoUnit.MONTHS.between(v.getLatestDate(), marketData.getValuationDate())
                    > MONTHS_IN_A_YEAR)
        .stream()
        .findAny()
        .map(i -> format(MISSING_FIXINGS_ERROR_TEMPLATE, priceIndex))
        .map(CALIBRATION_ERROR::entity);
  }

  public static Either<ErrorItem, Pair<List<CurveDefinition>, List<CurveDefinition>>>
      validateMarketData(
          Pair<List<CurveDefinition>, List<CurveDefinition>> defPairs, MarketData marketData) {
    return defPairs.getFirst().stream()
        .map(curve -> missingMdValue(curve, marketData))
        .flatMap(Optional::stream)
        .findAny()
        .map(e -> missingMdError(e.getFirst(), e.getSecond()))
        .orElse(Either.right(defPairs));
  }

  private static Optional<Pair<CurveName, ? extends MarketDataId<?>>> missingMdValue(
      CurveDefinition definition, MarketData marketData) {
    return definition.getNodes().stream()
        .map(CurveNode::requirements)
        .flatMap(Set::stream)
        .filter(md -> marketData.findValue(md).isEmpty())
        .findFirst()
        .map(mdId -> Pair.of(definition.getName(), mdId));
  }

  private static Either<ErrorItem, Pair<List<CurveDefinition>, List<CurveDefinition>>>
      missingMdError(CurveName curveName, MarketDataId<?> value) {
    var missingValueId = format("'%s' of class '%s'", value, value.getClass().getSimpleName());
    return Either.left(
        CALIBRATION_ERROR.entity(
            format(MISSING_MARKET_DATA_ERROR_TEMPLATE, curveName.getName(), missingValueId)));
  }
}
