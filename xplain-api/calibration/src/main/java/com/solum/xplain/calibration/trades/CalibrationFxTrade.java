package com.solum.xplain.calibration.trades;

import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD;

import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.product.fx.FxSingle;
import com.opengamma.strata.product.fx.FxSwapTrade;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFxForwardDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import io.atlassian.fugue.Either;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CalibrationFxTrade extends CalibrationTrade<FxSwapTrade> {

  public CalibrationFxTrade(FxSwapTrade trade, String externalTradeId) {
    super(trade, externalTradeId);
  }

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {

    FxSingle fxSingle = getTrade().getProduct().getFarLeg();
    var payAmount = fxSingle.getPayCurrencyAmount();
    var receiveAmount = fxSingle.getReceiveCurrencyAmount();

    var builder =
        ResolvableFxForwardDetails.builder()
            .paymentDate(fxSingle.getPaymentDate())
            .receiveCurrency(receiveAmount.getCurrency())
            .receiveCurrencyAmount(receiveAmount.getAmount())
            .payCurrency(payAmount.getCurrency())
            .payCurrencyAmount(payAmount.getAmount());

    fxSingle
        .getPaymentDateAdjustment()
        .map(BusinessDayAdjustment::getConvention)
        .map(Object::toString)
        .ifPresent(builder::businessDayConvention);
    var details =
        toTradeDetails(builder.build(), fxSingle.getCurrencyPair().toConventional().getBase());

    return Either.right(TradeValue.calibrationTradeValue(FXFWD, details));
  }
}
