package com.solum.xplain.calibration.rates.group.ois.combined;

import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import com.solum.xplain.calibration.rates.group.DiscountingGroups;
import com.solum.xplain.calibration.rates.group.DiscountingGroupsResults;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem;
import com.solum.xplain.calibration.rates.group.ois.localccy.LocalCcyDiscountingGroups;
import com.solum.xplain.calibration.rates.group.single.SingleDiscountingGroups;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode
@ToString
public class OisLocalCcySingleFallbackDiscountingGroups<I> implements DiscountingGroups<I> {

  private final LocalCcyDiscountingGroups<I> localCcyDiscountingGroups;
  private final SingleDiscountingGroups<I> singleDiscountingGroups;

  OisLocalCcySingleFallbackDiscountingGroups(
      LocalCcyDiscountingGroups<I> localCcyDiscountingGroups,
      SingleDiscountingGroups<I> singleDiscountingGroups) {
    this.localCcyDiscountingGroups = localCcyDiscountingGroups;
    this.singleDiscountingGroups = singleDiscountingGroups;
  }

  @Override
  public DiscountingGroupsResults results() {
    return new OisLocalCcySingleFallbackResults(
        localCcyDiscountingGroups.results(), singleDiscountingGroups.results());
  }

  @Override
  public DiscountingGroup itemDiscountingGroup(DiscountableItem<I> item) {
    var localCcy = localCcyDiscountingGroups.itemDiscountingGroup(item);
    if (localCcy != null) {
      return localCcy;
    }
    return singleDiscountingGroups.itemDiscountingGroup(item);
  }
}
