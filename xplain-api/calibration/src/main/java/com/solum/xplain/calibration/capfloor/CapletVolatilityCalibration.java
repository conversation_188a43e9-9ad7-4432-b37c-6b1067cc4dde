package com.solum.xplain.calibration.capfloor;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static java.time.ZoneId.systemDefault;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilityCalibrationResult;
import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilityDefinition;
import com.opengamma.strata.pricer.capfloor.SurfaceIborCapletFloorletVolatilityBootstrapper;
import com.opengamma.strata.pricer.option.RawOptionData;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.solum.xplain.calibration.rates.RatesCalibrationResult;
import com.solum.xplain.calibration.rates.spot01.ShiftedFxRatesProvider;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.slf4j.Logger;

@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class CapletVolatilityCalibration {

  private static final Logger LOG = getLogger(CapletVolatilityCalibration.class);

  private final IborCapletFloorletVolatilityDefinition definition;
  private final RatesCalibrationResult rates;
  private final RawOptionData capFloorData;

  public static Either<ErrorItem, CapletVolatilityCalibration> of(
      VolatilitySurface volatility,
      RatesCalibrationResult rates,
      MarketData marketData,
      ValidNodesFilter validNodesFilter) {
    return Steps.begin(volatility.rawOptionData(marketData, validNodesFilter))
        .then(volatility::capletDefinition)
        .yield((optionData, caplet) -> new CapletVolatilityCalibration(caplet, rates, optionData));
  }

  public Either<ErrorItem, CapletFloorletVolatilityCalibrationResult> calibrate() {
    try {
      LOG.debug("Starting to calibrate set {}", definition.getName());
      var calibrationResult = calibrate(rates.getRatesProvider());
      var shiftedResults =
          rates.getShiftedRatesProviders().stream()
              .map(this::calibrateShifted)
              .flatMap(Optional::stream)
              .toList();

      return Either.right(
          CapletFloorletVolatilityCalibrationResult.of(
              definition, calibrationResult, shiftedResults));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(
          new ErrorItem(
              CALIBRATION_ERROR,
              "Error calibrating volatility curve (cap/floors) "
                  + definition.getName()
                  + ": "
                  + ex.getMessage()));
    } finally {
      LOG.debug("Finished calibrating caplet set {}", definition.getName());
    }
  }

  private IborCapletFloorletVolatilityCalibrationResult calibrate(RatesProvider ratesProvider) {
    var valuationDate = ratesProvider.getValuationDate();
    var calibrationDateTime =
        ZonedDateTime.of(valuationDate.atTime(LocalTime.MIDNIGHT), systemDefault());
    return SurfaceIborCapletFloorletVolatilityBootstrapper.DEFAULT.calibrate(
        definition, calibrationDateTime, capFloorData, ratesProvider);
  }

  private Optional<FxShiftedCapletCalibrationResult> calibrateShifted(
      ShiftedFxRatesProvider shiftedRatesProvider) {
    try {
      var calibrationResult = calibrate(rates.getRatesProvider());
      return Optional.of(
          new FxShiftedCapletCalibrationResult(
              shiftedRatesProvider.getCurrencyPair(), calibrationResult));
    } catch (Exception e) {
      LOG.debug("Error calibrating shifted caplet {}, {}", definition.getName(), e);
      return Optional.empty();
    }
  }
}
