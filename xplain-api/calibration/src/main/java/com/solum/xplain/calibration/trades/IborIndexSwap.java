package com.solum.xplain.calibration.trades;

import static com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies.indexAccrualFrequency;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableSwapDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.index.OffshoreIndices;
import java.time.LocalDate;

public interface IborIndexSwap {

  default ResolvableTradeDetails toSwapFromIndex(
      IborIndex iborIndex,
      LocalDate startDate,
      LocalDate endDate,
      double fixedRate,
      Currency currency,
      double notional) {
    var adjustment = iborIndex.getMaturityDateOffset().getAdjustment();
    var accrualFreq = indexAccrualFrequency(iborIndex);

    ResolvableTradeLegDetails payLeg =
        ResolvableIborLeg.builder()
            .index(OffshoreIndices.convertFromOffshore(iborIndex).getName())
            .dayCount(iborIndex.getDayCount())
            .fixingDateOffset(iborIndex.getFixingDateOffset().getDays())
            .initialValue(0d)
            .accrualFrequency(accrualFreq)
            .paymentFrequency(accrualFreq)
            .currency(currency)
            .notional(notional)
            .paymentOffsetDays(0)
            .paymentCompounding(CompoundingMethod.NONE.name())
            .payReceive(PayReceive.PAY)
            .isOffshore(OffshoreIndices.isOffshore(iborIndex))
            .build();

    // Receive leg (fixed)
    ResolvableTradeLegDetails receiveLeg =
        ResolvableFixedLeg.builder()
            .dayCount(iborIndex.getDayCount())
            .initialValue(fixedRate)
            .accrualFrequency(accrualFreq)
            .paymentFrequency(accrualFreq)
            .currency(currency)
            .notional(notional)
            .paymentOffsetDays(0)
            .payReceive(PayReceive.RECEIVE)
            .paymentCompounding(CompoundingMethod.NONE.name())
            .build();

    return ResolvableSwapDetails.builder()
        .startDate(startDate)
        .endDate(endDate)
        .payLeg(payLeg)
        .receiveLeg(receiveLeg)
        .businessDayAdjustmentType(BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT)
        .businessDayConvention(adjustment.getConvention().getName())
        .stubConvention(StubConvention.SHORT_INITIAL.toString())
        .build();
  }
}
