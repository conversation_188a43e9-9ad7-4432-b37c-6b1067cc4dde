package com.solum.xplain.calibration.trades;

import com.solum.xplain.calibration.value.CalibrationOptionsForm;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.core.company.validation.ValidCompanyId;
import com.solum.xplain.core.portfolio.CompanyPortfolio;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.validation.UniquePortfolioExtId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@UniquePortfolioExtId
public class GenerateCalibrationPortfolioForm implements CompanyPortfolio {

  @Valid @NotNull private final CalibrationOptionsForm calibrationOptions;

  @NotEmpty @ValidIdentifier private final String externalPortfolioId;

  @NotEmpty private final String name;

  @NotEmpty @ValidObjectId @ValidCompanyId private final String companyId;

  @NotEmpty @ValidObjectId private final String entityId;

  @Valid @NotNull private final AllowedTeamsForm allowedTeamsForm;

  public PortfolioCreateForm toPortfolioForm() {
    PortfolioCreateForm form = new PortfolioCreateForm();
    form.setName(name);
    form.setExternalPortfolioId(externalPortfolioId);
    form.setAllowedTeamsForm(allowedTeamsForm);
    form.setCompanyId(companyId);
    form.setEntityId(entityId);
    return form;
  }
}
