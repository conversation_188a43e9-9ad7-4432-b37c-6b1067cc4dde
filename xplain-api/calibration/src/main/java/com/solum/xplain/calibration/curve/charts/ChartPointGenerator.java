package com.solum.xplain.calibration.curve.charts;

import com.opengamma.strata.market.ValueType;
import com.solum.xplain.calibration.curve.charts.calculator.ChartCurvePointCalculatorFactory;
import com.solum.xplain.calibration.curve.charts.value.CurveChartGeneratorData;
import com.solum.xplain.calibration.rates.curvepoint.CurvePointCalculator;
import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvegroup.curve.entity.CurvePoints;
import com.solum.xplain.extensions.constants.PriceIndexConstants;
import java.time.LocalDate;
import java.time.Period;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ChartPointGenerator {
  private final ChartCurvePointCalculatorFactory calculatorFactory;

  public CurvePoints generateChartPoints(
      @NonNull CurveChartGeneratorData generatorData, ChartDateType dateType) {
    var calculator = calculatorFactory.calculator(generatorData);
    var curveDetails = generatorData.curveDetails();
    var chartPoints =
        generateDates(generatorData, dateType).stream()
            .map(date -> calculateChartPoint(date, calculator))
            .toList();
    return CurvePoints.newOf(
        curveDetails.name(), curveDetails.discountingKey(), curveDetails.yValueType(), chartPoints);
  }

  private ChartPoint calculateChartPoint(
      LocalDate date, CurvePointCalculator curvePointCalculator) {
    var point = curvePointCalculator.calculate(date);
    return new ChartPoint(date, point.yValue(), point.xValue());
  }

  private Set<LocalDate> generateDates(CurveChartGeneratorData data, ChartDateType dateType) {
    Set<LocalDate> dates = new TreeSet<>();
    var nodeDates = data.nodeValues().stream().map(CalculatedValueAtDate::getDate).toList();
    var xValueType = data.curveDetails().xValueType();
    if (dateType != ChartDateType.END_OF_MONTH && !ValueType.MONTHS.equals(xValueType)) {
      dates.addAll(nodeDates); // Add all node dates if not EOM and Inflation chart
    }

    var chartStartDate = chartStartDate(data, dateType);
    var endDate = chartEndDate(data, nodeDates, dateType);

    chartStartDate
        .datesUntil(endDate, Period.ofMonths(1))
        .map(date -> adjustDate(xValueType, dateType, date))
        .forEach(dates::add);
    dates.add(endDate);
    return dates;
  }

  private LocalDate chartStartDate(CurveChartGeneratorData data, ChartDateType dateType) {
    var curveDetails = data.curveDetails();
    var xValueType = curveDetails.xValueType();
    var valuationDate = data.valuationDate();
    return curveDetails
        .priceIndex()
        .map(PriceIndexConstants::priceIndexLag)
        .map(lag -> YearMonth.from(valuationDate).minus(lag).atDay(1))
        .orElse(adjustDate(xValueType, dateType, valuationDate));
  }

  private LocalDate chartEndDate(
      CurveChartGeneratorData data, List<LocalDate> nodeDates, ChartDateType dateType) {
    var xValueType = data.curveDetails().xValueType();
    LocalDate endDate =
        nodeDates.stream().max(Comparator.comparing(LocalDate::toEpochDay)).stream()
            .findFirst()
            .orElseThrow(); // Never thrown cause nodes array have at least 2 values

    return adjustDate(xValueType, dateType, endDate);
  }

  private LocalDate adjustDate(ValueType xValueType, ChartDateType type, LocalDate date) {
    if (!ValueType.MONTHS.equals(xValueType)) {
      return type.adjustDate(date);
    }

    return date;
  }
}
