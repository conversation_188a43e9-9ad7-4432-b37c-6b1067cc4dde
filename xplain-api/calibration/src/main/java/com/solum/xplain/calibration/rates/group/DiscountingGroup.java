package com.solum.xplain.calibration.rates.group;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.RateIndex;
import java.util.Set;

public interface DiscountingGroup {

  String key();

  Currency getCurrency();

  boolean supportsDiscount(Currency ccy);

  boolean supportsIndex(Index index);

  Set<String> getCreditNames();

  Set<RateIndex> getCapletIndices();

  Set<RateIndex> getSwaptionIndices();

  default boolean requireCaplets() {
    return !getCapletIndices().isEmpty();
  }

  default boolean requireSwaptions() {
    return !getSwaptionIndices().isEmpty();
  }
}
