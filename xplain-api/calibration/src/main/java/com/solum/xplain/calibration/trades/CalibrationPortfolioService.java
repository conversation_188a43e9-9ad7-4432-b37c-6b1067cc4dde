package com.solum.xplain.calibration.trades;

import static com.solum.xplain.calibration.trades.CalibrationTradesGenerator.calibrationTrades;
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND;
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT;
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE;
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP;
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.CurveDefinition;
import com.solum.xplain.calibration.market.CalibrationMarketDataService;
import com.solum.xplain.calibration.value.CurveShifts;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.embedded.update.ImportUpdatesResolver;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.SingleDateMarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItemEntity;
import com.solum.xplain.core.portfolio.PortfolioItemUniqueKey;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.List;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CalibrationPortfolioService {

  private static final String CALIBRATION_STATUS = "Calibration with portfolio generation %s";
  private static final String MARKET_DATA_MISSING =
      "Required market data not found for one or more curves";
  private static final String DEFAULT_COMMENT = "Calibration generated trade";

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupCurveRepository curveGroupCurveRepository;
  private final CalibrationMarketDataService marketDataService;
  private final PortfolioRepository portfolioRepository;
  private final PortfolioItemWriteRepository portfolioItemWriteRepository;
  private final AuditEntryService auditEntryService;
  private final ReferenceData referenceData;

  public Either<ErrorItem, EntityId> generatePortfolio(
      String curveGroupId, GenerateCalibrationPortfolioForm form, BitemporalDate stateDate) {
    return Steps.begin(curveGroupRepository.getEither(curveGroupId))
        .then(() -> marketData(form, stateDate))
        .yield((cg, md) -> generatePortfolio(form.toPortfolioForm(), cg, md, stateDate))
        .flatMap(Function.identity());
  }

  private Either<ErrorItem, MarketData> marketData(
      GenerateCalibrationPortfolioForm form, BitemporalDate stateDate) {
    var stateKey = form.getCalibrationOptions().curveConfigMarketStateKey(stateDate);
    var mdParams = SingleDateMarketDataExtractionParams.mdParamsWithoutCurves(stateKey);
    return marketDataService.getCalibrationMD(
        form.getCalibrationOptions().getValuationDate(), mdParams);
  }

  private Either<ErrorItem, EntityId> generatePortfolio(
      PortfolioCreateForm form,
      CurveGroupView curveGroup,
      MarketData marketData,
      BitemporalDate stateDate) {
    var referenceData =
        ValuationDateReferenceData.wrap(this.referenceData, marketData.getValuationDate());
    return curveDefinitions(curveGroup, marketData, referenceData, stateDate, false)
        .map(curveDefinitions -> trades(curveDefinitions, marketData, referenceData))
        .map(c -> logErrors(c, curveGroup))
        .flatMap(this::toEither)
        .flatMap(trades -> portfolio(form, trades));
  }

  private Either<ErrorItem, List<ParsableToTradeValue>> toEither(CalibrationTrades trades) {
    return trades.toEither().leftMap(l -> Error.CALIBRATION_ERROR.entity(MARKET_DATA_MISSING));
  }

  private Either<ErrorItem, Iterable<CurveDefinition>> curveDefinitions(
      CurveGroupView curveGroup,
      MarketData marketData,
      ReferenceData referenceData,
      BitemporalDate stateDate,
      boolean allowDropMissingMdNodes) {
    return curveGroupCurveRepository.getActiveCurves(curveGroup.getId(), stateDate).stream()
        .map(
            c ->
                c.curveDefinition(
                    CurveShifts.empty().additionalSpread(),
                    marketData.getValuationDate(),
                    ValidNodesFilter.EMPTY_FILTER,
                    marketData,
                    referenceData,
                    l -> {},
                    false,
                    allowDropMissingMdNodes))
        .collect(Collectors.collectingAndThen(toList(), Eithers::sequenceRight));
  }

  private CalibrationTrades trades(
      Iterable<CurveDefinition> curveDefinitions,
      MarketData marketData,
      ReferenceData referenceData) {
    return calibrationTrades(curveDefinitions, referenceData).resolveCalibrationTrades(marketData);
  }

  private CalibrationTrades logErrors(CalibrationTrades trades, CurveGroupView curveGroup) {
    var status = trades.toEither().fold(l -> "failed", r -> "successful");
    var entry =
        AuditEntry.of(
            CurveGroup.CURVE_GROUP_COLLECTION,
            String.format(CALIBRATION_STATUS, status),
            curveGroup.getId());

    auditEntryService.newEntryWithLogs(entry, trades.getErrors());
    return trades;
  }

  private Either<ErrorItem, EntityId> portfolio(
      PortfolioCreateForm form, List<ParsableToTradeValue> trades) {
    return portfolioRepository
        .insert(form)
        .flatMap(id -> insertTrades(id.getId(), trades).map(ids -> id));
  }

  private Either<ErrorItem, Integer> insertTrades(
      String portfolioId, List<ParsableToTradeValue> trades) {
    var parsedTradeValues =
        trades.stream()
            .collect(
                toMap(
                    t ->
                        new PortfolioItemEntity(
                            PortfolioItemUniqueKey.newOf(portfolioId, t.getExternalTradeId())),
                    ParsableToTradeValue::toTradeValue));

    var entitiesToAdd =
        parsedTradeValues.entrySet().stream()
            .filter(e -> e.getValue().isRight())
            .collect(toMap(Entry::getKey, e -> e.getValue().getOrNull()));

    var updateActions =
        ImportUpdatesResolver.<TradeValue, PortfolioItemEntity>builder()
            .options(
                new ImportOptions(
                    ROOT_DATE, APPEND, STRICT, DEFAULT_COMMENT, UPDATE, KEEP, KEEP, null))
            .entitiesToAppend(entitiesToAdd)
            .build();

    var inserted = portfolioItemWriteRepository.updateFromImport(updateActions);
    var anyError = parsedTradeValues.values().stream().filter(Either::isLeft).findFirst();
    return anyError
        .map(e -> Either.<ErrorItem, Integer>left(e.left().get()))
        .orElseGet(() -> Either.right(inserted));
  }
}
