package com.solum.xplain.calibration.rates.group;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.solum.xplain.calibration.discounting.CalibrationDiscountSubGroup;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.lang.NonNull;

/**
 * A "discounting group" is a collection of indices and currencies defined by a discounting key.
 * Base discounting groups can be split into {@link CalibrationDiscountSubGroup }s for calibration
 * after finding the appropriate curves to calibrate all indices in the group. IR and FX vols do not
 * require calibration and therefore are not part of the discount group.
 */
@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public abstract class BaseDiscountingGroup implements DiscountingGroup {
  private final DiscountingGroupRequirements requirements;

  @NonNull
  public final Set<Currency> getForeignCurrencies() {
    return requirements.foreignCurrencies();
  }

  @NonNull
  public final Set<FloatingRateIndex> getRequiredIndices() {
    return requirements.requiredIndices();
  }

  @NonNull
  public final Set<String> getCreditNames() {
    return requirements.creditNames();
  }

  @NonNull
  public final Set<RateIndex> getCapletIndices() {
    return requirements.capletIndices();
  }

  @NonNull
  public final Set<RateIndex> getSwaptionIndices() {
    return requirements.swaptionIndices();
  }
}
