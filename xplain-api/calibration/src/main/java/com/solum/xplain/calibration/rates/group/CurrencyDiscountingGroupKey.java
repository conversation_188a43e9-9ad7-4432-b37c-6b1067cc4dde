package com.solum.xplain.calibration.rates.group;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;

public record CurrencyDiscountingGroupKey(
    Currency currency, ClearingHouse clearingHouse, boolean offshore) {

  public CurrencyDiscountingGroupKey(Currency currency, DiscountableItemRequirements requirements) {
    this(currency, requirements.getClearingHouse(), requirements.isOffshore());
  }
}
