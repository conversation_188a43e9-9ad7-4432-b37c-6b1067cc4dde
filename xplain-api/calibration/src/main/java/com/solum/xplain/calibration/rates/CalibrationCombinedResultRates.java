package com.solum.xplain.calibration.rates;

import static com.solum.xplain.calibration.rates.charts.CalibratedCurve.fromStrataCurve;
import static java.util.stream.Collectors.toSet;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.market.curve.Curve;
import com.opengamma.strata.market.curve.CurveDefinition;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.InterpolatedNodalCurve;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators;
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import com.solum.xplain.calibration.rates.nodes.CurveNodeValueCalculation;
import com.solum.xplain.calibration.rates.nodes.CurveNodeValueCalculationFactory;
import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import lombok.Data;

@Data
public class CalibrationCombinedResultRates {
  private final Set<CurveDefinition> curves;
  private final RatesCalibrationResult rates;
  private final Supplier<Either<ErrorItem, RatesCalibrationResult>> isdaRates;
  private final ReferenceData referenceData;

  public LocalDate valuationDate() {
    return getRatesProvider().getValuationDate();
  }

  public boolean isEmpty() {
    return curves.isEmpty();
  }

  public boolean allIsdaCompliantDiscountCurves() {
    return getRatesProvider().getDiscountCurves().values().stream()
        .filter(InterpolatedNodalCurve.class::isInstance)
        .map(InterpolatedNodalCurve.class::cast)
        .allMatch(this::hasIsdaCompliantInterpolators);
  }

  private boolean hasIsdaCompliantInterpolators(InterpolatedNodalCurve c) {
    return c.getInterpolator().equals(CurveInterpolators.PRODUCT_LINEAR)
        && c.getExtrapolatorLeft().equals(CurveExtrapolators.FLAT)
        && c.getExtrapolatorRight().equals(CurveExtrapolators.PRODUCT_LINEAR);
  }

  public List<CalibratedCurve> calibratedCurves(DiscountingGroup discounting) {
    var ratesProvider = getRatesProvider();
    var primaryCurves = new ArrayList<Curve>();
    primaryCurves.addAll(
        ratesProvider.getDiscountCurves().entrySet().stream()
            .filter(e -> discounting.supportsDiscount(e.getKey()))
            .map(Map.Entry::getValue)
            .toList());
    primaryCurves.addAll(
        getRatesProvider().getIndexCurves().entrySet().stream()
            .filter(e -> discounting.supportsIndex(e.getKey()))
            .map(Map.Entry::getValue)
            .toList());

    return ratesProvider.getCurves().values().stream()
        .map(c -> fromStrataCurve(c, ratesProvider, discounting.key(), primaryCurves.contains(c)))
        .toList();
  }

  public Optional<Currency> getResolvedDiscountCurrency(CurveName curveName) {
    return rates.getRatesProvider().getDiscountCurves().entrySet().stream()
        .filter(v -> v.getValue().getName().equals(curveName))
        .map(Map.Entry::getKey)
        .findFirst();
  }

  public List<CalculatedValueAtDate> calculateNodeValues(CurveName curveName) {
    var ratesProvider = rates.getRatesProvider();
    return curves.stream()
        .filter(v -> v.getName().equals(curveName))
        .map(curveDef -> CurveNodeValueCalculationFactory.calculation(ratesProvider, curveDef))
        .map(CurveNodeValueCalculation::nodeDateValues)
        .findFirst()
        .orElse(List.of());
  }

  public Optional<CalibratedCurve> calibratedCurve(CurveName curveName) {
    return Checked.now(
            () -> CalibrationUtils.ratesProviderCurve(rates.getRatesProvider(), curveName))
        .map(c -> fromStrataCurve(c, rates.getRatesProvider(), null, true))
        .toOptional();
  }

  public List<String> nodesUsedInCalibration(CurveName curveName) {
    return curves.stream()
        .filter(v -> v.getName().equals(curveName))
        .flatMap(d -> d.getNodes().stream().map(CurveNode::getLabel))
        .toList();
  }

  public List<CalculatedValueAtDate> calculateDiscountFactors(
      CurveName curveName, ReferenceData referenceData) {
    final Set<LocalDate> dates =
        curves.stream()
            .filter(v -> v.getName().equals(curveName))
            .map(
                d ->
                    d.getNodes().stream()
                        .map(
                            n -> n.date(rates.getRatesProvider().getValuationDate(), referenceData))
                        .collect(toSet()))
            .findFirst()
            .orElse(Collections.emptySet());

    return getResolvedDiscountCurrency(curveName).map(ccy -> points(dates, ccy)).orElse(List.of());
  }

  private List<CalculatedValueAtDate> points(Set<LocalDate> dates, Currency currency) {
    return dates.stream()
        .map(
            nd ->
                new CalculatedValueAtDate(
                    nd, rates.getRatesProvider().discountFactor(currency, nd)))
        .toList();
  }

  public ImmutableRatesProvider getRatesProvider() {
    return rates.getRatesProvider();
  }
}
