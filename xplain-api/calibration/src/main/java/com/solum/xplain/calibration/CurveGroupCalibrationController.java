package com.solum.xplain.calibration;

import com.solum.xplain.calibration.trades.CalibrationPortfolioService;
import com.solum.xplain.calibration.trades.GenerateCalibrationPortfolioForm;
import com.solum.xplain.calibration.value.CalibrateCurveForm;
import com.solum.xplain.calibration.value.CalibrationResponse;
import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EitherResultOrErrorResponseEntity;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.lock.ResourceValidationService;
import com.solum.xplain.core.lock.XplainLock;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@RequestMapping("/curve-group")
public class CurveGroupCalibrationController {

  private final CurveGroupCalibrationService curveGroupCalibrationService;
  private final CalibrationPortfolioService calibrationPortfolioService;
  private final ResourceValidationService resourceValidationService;

  @Operation(summary = "Calibrate curve group")
  @CommonErrors
  @PostMapping("/{groupId}/calibrate")
  @PreAuthorize(Authorities.AUTHORITY_RUN_CURVE_CONFIG)
  @RequireLock(name = XplainLock.CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<CalibrationResponse> calibrate(
      @PathVariable("groupId") String groupId, @Valid @RequestBody CalibrateCurveForm form) {
    return EitherResultOrErrorResponseEntity.eitherErrorItemResponse(
        calibrationBitemporalDate(form)
            .flatMap(d -> curveGroupCalibrationService.calibrate(groupId, form, d)));
  }

  private Either<ErrorItem, BitemporalDate> calibrationBitemporalDate(CalibrateCurveForm form) {
    var options = form.getCalibrationOptions();
    return resourceValidationService.ifLocksAvailable(
        List.of(
            XplainLock.newOf(XplainLock.FIXINGS_LOCK_ID),
            XplainLock.newOf(XplainLock.MARKET_DATA_LOCK_ID, options.getMarketDataGroupId()),
            XplainLock.newOf(XplainLock.MDK_LOCK_ID),
            XplainLock.newOf(XplainLock.VALUATION_SETTINGS_LOCK_ID)),
        () -> BitemporalDate.newOf(options.getStateDate()));
  }

  @Operation(summary = "Generate calibration portfolio")
  @CommonErrors
  @PostMapping("/{groupId}/calibrate/generate-portfolio")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = XplainLock.TRADES_LOCK_ID)
  public ResponseEntity<EntityId> generateCalibrationPortfolio(
      @PathVariable("groupId") String groupId,
      @Valid @RequestBody GenerateCalibrationPortfolioForm form) {
    return EitherResultOrErrorResponseEntity.eitherErrorItemResponse(
        calibrationPortfolioBitemporalDate(form)
            .flatMap(d -> calibrationPortfolioService.generatePortfolio(groupId, form, d)));
  }

  private Either<ErrorItem, BitemporalDate> calibrationPortfolioBitemporalDate(
      GenerateCalibrationPortfolioForm form) {
    var options = form.getCalibrationOptions();
    return resourceValidationService.ifLocksAvailable(
        List.of(
            XplainLock.newOf(XplainLock.CURVE_CONFIGURATION_LOCK_ID),
            XplainLock.newOf(XplainLock.FIXINGS_LOCK_ID),
            XplainLock.newOf(XplainLock.MARKET_DATA_LOCK_ID, options.getMarketDataGroupId()),
            XplainLock.newOf(XplainLock.MDK_LOCK_ID)),
        () -> BitemporalDate.newOf(options.getStateDate()));
  }
}
