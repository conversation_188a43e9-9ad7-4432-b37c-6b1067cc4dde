package com.solum.xplain.calibration.trades;

import static com.solum.xplain.core.portfolio.CoreProductType.INFLATION;
import static com.solum.xplain.core.portfolio.CoreProductType.IRS;
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY;
import static com.solum.xplain.core.portfolio.trade.TradeValue.calibrationTradeValue;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.opengamma.strata.product.swap.InflationRateCalculation;
import com.opengamma.strata.product.swap.NotionalSchedule;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.Swap;
import com.opengamma.strata.product.swap.SwapLeg;
import com.opengamma.strata.product.swap.SwapLegType;
import com.opengamma.strata.product.swap.SwapTrade;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableInflationLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableOvernightLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableSwapDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.index.OffshoreIndices;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CalibrationSwapTrade extends CalibrationTrade<SwapTrade> {

  public CalibrationSwapTrade(SwapTrade trade, String externalTradeId) {
    super(trade, externalTradeId);
  }

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    ImmutableList<SwapLeg> legs = getTrade().getProduct().getLegs();
    var tradeCurrency =
        legs.stream()
            .filter(e -> e.getPayReceive().isPay())
            .map(SwapLeg::getCurrency)
            .findFirst()
            .or(() -> getTrade().getProduct().allCurrencies().stream().findAny())
            .orElseThrow(
                () -> new IllegalArgumentException("Swap must have at least one currency"));
    return toResolvableDetails()
        .map(d -> calibrationTradeValue(resolveProductType(), toTradeDetails(d, tradeCurrency)));
  }

  private CoreProductType resolveProductType() {
    ImmutableList<SwapLeg> legs = getTrade().getProduct().getLegs();
    if (legs.stream().map(SwapLeg::getCurrency).distinct().count() > 1) {
      return XCCY;
    } else if (legs.stream().anyMatch(l -> l.getType() == SwapLegType.INFLATION)) {
      return INFLATION;
    } else {
      return IRS;
    }
  }

  private Either<ErrorItem, ResolvableTradeDetails> toResolvableDetails() {
    return Steps.begin(toResolvableLeg(getTrade().getProduct(), Swap::getPayLeg))
        .then(() -> toResolvableLeg(getTrade().getProduct(), Swap::getReceiveLeg))
        .yield(this::swapDetails);
  }

  private ResolvableSwapDetails swapDetails(
      ResolvableTradeLegDetails payLeg, ResolvableTradeLegDetails receiveLeg) {
    return ResolvableSwapDetails.builder()
        .startDate(getTrade().getProduct().getStartDate().getUnadjusted())
        .endDate(getTrade().getProduct().getEndDate().getUnadjusted())
        .businessDayConvention(businessDayConvention())
        .businessDayAdjustmentType(businessDayAdjustmentType())
        .payLeg(payLeg)
        .receiveLeg(receiveLeg)
        .rollConvention(rollConvention())
        .stubConvention(stubConvention())
        .notionalScheduleInitialExchange(
            notionalScheduleExchange(NotionalSchedule::isInitialExchange))
        .notionalScheduleFinalExchange(notionalScheduleExchange(NotionalSchedule::isFinalExchange))
        .build();
  }

  private String stubConvention() {
    return rateCalculationLegsStream()
        .map(RateCalculationSwapLeg::getAccrualSchedule)
        .findFirst()
        .flatMap(PeriodicSchedule::getStubConvention)
        .map(StubConvention::toString)
        .orElse(null);
  }

  private String rollConvention() {
    return rateCalculationLegsStream()
        .map(RateCalculationSwapLeg::getAccrualSchedule)
        .findFirst()
        .flatMap(PeriodicSchedule::getRollConvention)
        .map(Object::toString)
        .orElse(null);
  }

  private boolean notionalScheduleExchange(Function<NotionalSchedule, Boolean> exchangeFn) {
    return rateCalculationLegsStream()
        .map(RateCalculationSwapLeg::getNotionalSchedule)
        .map(exchangeFn)
        .findFirst()
        .orElse(false);
  }

  private String businessDayConvention() {
    return rateCalculationLegsStream()
        .map(RateCalculationSwapLeg::getAccrualSchedule)
        .map(PeriodicSchedule::getBusinessDayAdjustment)
        .map(BusinessDayAdjustment::getConvention)
        .map(Object::toString)
        .findFirst()
        .orElse(BusinessDayConventions.FOLLOWING.getName());
  }

  private BusinessDayAdjustmentType businessDayAdjustmentType() {
    if (resolveProductType() == INFLATION) {
      return BusinessDayAdjustmentType.PAYMENT_ONLY;
    }
    return rateCalculationLegsStream()
        .filter(
            v ->
                v.getAccrualSchedule()
                    .getBusinessDayAdjustment()
                    .equals(BusinessDayAdjustment.NONE))
        .findFirst()
        .map(l -> BusinessDayAdjustmentType.PAYMENT_ONLY)
        .orElse(BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT);
  }

  private Stream<RateCalculationSwapLeg> rateCalculationLegsStream() {
    return getTrade().getProduct().getLegs().stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast);
  }

  private Either<ErrorItem, ResolvableTradeLegDetails> toResolvableLeg(
      Swap swap, Function<Swap, Optional<SwapLeg>> swapLegFn) {
    return swapLegFn
        .apply(swap)
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .map(this::toResolvableLeg)
        .orElse(Either.left(Error.CONVERSION_ERROR.entity("Required leg missing!")));
  }

  private Either<ErrorItem, ResolvableTradeLegDetails> toResolvableLeg(RateCalculationSwapLeg leg) {
    var calculation = leg.getCalculation();
    if (calculation instanceof IborRateCalculation) {
      return Either.right(toResolvableLeg(leg, (IborRateCalculation) calculation));
    } else if (calculation instanceof FixedRateCalculation) {
      return Either.right(toResolvableLeg(leg, (FixedRateCalculation) calculation));
    } else if (calculation instanceof InflationRateCalculation) {
      return Either.right(toResolvableLeg(leg, (InflationRateCalculation) calculation));
    } else if (calculation instanceof OvernightRateCalculation) {
      return Either.right(toResolvableLeg(leg, (OvernightRateCalculation) calculation));
    } else {
      return Either.left(Error.CONVERSION_ERROR.entity("Unknown calculation type!"));
    }
  }

  private ResolvableTradeLegDetails toResolvableLeg(
      RateCalculationSwapLeg swapLeg, IborRateCalculation iborRateCalculation) {
    var iborIndex = OffshoreIndices.convertFromOffshore(iborRateCalculation.getIndex());
    return ResolvableIborLeg.builder()
        .paymentCompounding(swapLeg.getPaymentSchedule().getCompoundingMethod().toString())
        .index(iborIndex.getName())
        .dayCount(iborRateCalculation.getDayCount())
        .fixingDateOffset(iborRateCalculation.getFixingDateOffset().getDays())
        .initialValue(
            iborRateCalculation.getSpread().map(ValueSchedule::getInitialValue).orElse(0D))
        .accrualFrequency(swapLeg.getAccrualSchedule().getFrequency())
        .paymentFrequency(swapLeg.getPaymentSchedule().getPaymentFrequency())
        .currency(swapLeg.getNotionalSchedule().getCurrency())
        .notional(swapLeg.getNotionalSchedule().getAmount().getInitialValue())
        .paymentOffsetDays(swapLeg.getPaymentSchedule().getPaymentDateOffset().getDays())
        .payReceive(swapLeg.getPayReceive())
        .isOffshore(OffshoreIndices.isOffshore(iborRateCalculation.getIndex()))
        .build();
  }

  private ResolvableTradeLegDetails toResolvableLeg(
      RateCalculationSwapLeg swapLeg, FixedRateCalculation fixedRateCalculation) {
    var fixedAccrual =
        fixedRateCalculation
            .getFutureValueNotional()
            .map(n -> FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE)
            .orElse(null);
    return ResolvableFixedLeg.builder()
        .dayCount(fixedRateCalculation.getDayCount())
        .paymentCompounding(swapLeg.getPaymentSchedule().getCompoundingMethod().toString())
        .initialValue(fixedRateCalculation.getRate().getInitialValue())
        .accrualFrequency(swapLeg.getAccrualSchedule().getFrequency())
        .paymentFrequency(swapLeg.getPaymentSchedule().getPaymentFrequency())
        .currency(swapLeg.getNotionalSchedule().getCurrency())
        .notional(swapLeg.getNotionalSchedule().getAmount().getInitialValue())
        .paymentOffsetDays(swapLeg.getPaymentSchedule().getPaymentDateOffset().getDays())
        .payReceive(swapLeg.getPayReceive())
        .accrualMethod(fixedAccrual)
        .build();
  }

  private ResolvableTradeLegDetails toResolvableLeg(
      RateCalculationSwapLeg swapLeg, InflationRateCalculation calculation) {
    return ResolvableInflationLeg.builder()
        .paymentCompounding(swapLeg.getPaymentSchedule().getCompoundingMethod().toString())
        .index(calculation.getIndex().toString())
        .inflationLag(calculation.getLag().toString())
        .indexCalculationMethod(calculation.getIndexCalculationMethod().toString())
        .accrualFrequency(swapLeg.getAccrualSchedule().getFrequency())
        .paymentFrequency(swapLeg.getPaymentSchedule().getPaymentFrequency())
        .currency(swapLeg.getNotionalSchedule().getCurrency())
        .notional(swapLeg.getNotionalSchedule().getAmount().getInitialValue())
        .paymentOffsetDays(swapLeg.getPaymentSchedule().getPaymentDateOffset().getDays())
        .payReceive(swapLeg.getPayReceive())
        .build();
  }

  private ResolvableTradeLegDetails toResolvableLeg(
      RateCalculationSwapLeg swapLeg, OvernightRateCalculation calculation) {
    var overnightIndex = OffshoreIndices.convertFromOffshore(calculation.getIndex());
    return ResolvableOvernightLeg.builder()
        .paymentCompounding(swapLeg.getPaymentSchedule().getCompoundingMethod().toString())
        .index(overnightIndex.getName())
        .dayCount(calculation.getDayCount())
        .rateCutOffDays(calculation.getRateCutOffDays())
        .paymentOffsetDays(swapLeg.getPaymentSchedule().getPaymentDateOffset().getDays())
        .accrualFrequency(swapLeg.getAccrualSchedule().getFrequency())
        .paymentFrequency(swapLeg.getPaymentSchedule().getPaymentFrequency())
        .currency(swapLeg.getNotionalSchedule().getCurrency())
        .notional(swapLeg.getNotionalSchedule().getAmount().getInitialValue())
        .overnightAccrualMethod(calculation.getAccrualMethod())
        .initialValue(calculation.getSpread().map(ValueSchedule::getInitialValue).orElse(0d))
        .payReceive(swapLeg.getPayReceive())
        .isOffshore(OffshoreIndices.isOffshore(calculation.getIndex()))
        .build();
  }
}
