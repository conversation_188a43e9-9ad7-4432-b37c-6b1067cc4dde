package com.solum.xplain.calibration.credit;

import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.CurveName;
import com.solum.xplain.calibration.rates.CalibrationCombinedResultRates;
import com.solum.xplain.calibration.settings.CalibrationSettingsService;
import com.solum.xplain.calibration.value.CalibrationOptions;
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveRepository;
import com.solum.xplain.core.error.ErrorItem;
import java.util.List;
import java.util.function.Consumer;
import org.springframework.stereotype.Service;

@Service
public class CreditCurvesCalibrationService {

  private final CurveGroupCreditCurveRepository creditCurveRepository;
  private final CalibrationSettingsService settingsService;

  public CreditCurvesCalibrationService(
      CurveGroupCreditCurveRepository creditCurveRepository,
      CalibrationSettingsService settingsService) {
    this.creditCurveRepository = creditCurveRepository;
    this.settingsService = settingsService;
  }

  public void calibrateCreditCurves(
      CalibrationOptions calibrationOptions,
      MarketData marketData,
      CalibrationCombinedResultRates curveResults,
      Consumer<List<ErrorItem>> errorConsumer) {
    var curveGroupId = calibrationOptions.getCurveGroup().getId();
    var stateDate = calibrationOptions.getMarketStateKey().getStateDate();

    var discountCurrencies = curveResults.getRatesProvider().getDiscountCurrencies();
    var curvesFilter = CreditCurvesCalibration.currencyFilter(discountCurrencies);
    var creditCurves = creditCurveRepository.getActiveCurves(curveGroupId, stateDate);
    var calibration =
        CreditCurvesCalibration.newOf(
            creditCurves,
            marketData,
            calibrationOptions.getNodesFilter(),
            curveResults.getReferenceData(),
            errorConsumer,
            settingsService.forceIsdaInterpolators(stateDate));
    calibration
        .calibrate(curveResults, curvesFilter)
        .ifPresent(results -> storeResults(curveGroupId, results));
  }

  private void storeResults(String curveGroupId, CreditsCalibrationResult results) {
    creditCurveRepository.updateCalibrationResults(
        curveGroupId,
        results.getCreditRatesProvider().getValuationDate(),
        name -> results.getChart(CurveName.of(name)));
  }
}
