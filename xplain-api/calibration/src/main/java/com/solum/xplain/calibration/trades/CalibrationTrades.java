package com.solum.xplain.calibration.trades;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.Collection;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CalibrationTrades {
  private final List<ParsableToTradeValue> trades;
  private final List<ErrorItem> errors;

  public static CalibrationTrades newOf(
      List<Either<List<ErrorItem>, ParsableToTradeValue>> eithers) {
    var errors =
        eithers.stream()
            .filter(Either::isLeft)
            .map(e -> e.left().get())
            .flatMap(Collection::stream)
            .toList();
    var trades = eithers.stream().filter(Either::isRight).map(Either::getOrNull).toList();
    return new CalibrationTrades(trades, errors);
  }

  public Either<List<ErrorItem>, List<ParsableToTradeValue>> toEither() {
    return Eithers.cond(!trades.isEmpty(), errors, trades);
  }

  public List<ParsableToTradeValue> getTrades() {
    return trades;
  }

  public List<ErrorItem> getErrors() {
    return errors;
  }
}
