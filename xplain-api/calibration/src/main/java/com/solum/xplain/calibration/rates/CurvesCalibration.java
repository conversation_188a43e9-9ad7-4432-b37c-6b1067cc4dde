package com.solum.xplain.calibration.rates;

import static com.solum.xplain.calibration.rates.CalibrationUtils.calibrationCombinedResultRates;
import static com.solum.xplain.calibration.settings.CalibrationInflationSeasonalities.ofSettings;
import static com.solum.xplain.core.error.Error.CALIBRATION_INFO;
import static com.solum.xplain.core.utils.async.AsyncUtils.runAsyncWithCurrentContext;
import static io.atlassian.fugue.Eithers.filterLeft;
import static io.atlassian.fugue.Eithers.filterRight;
import static java.lang.String.format;
import static java.util.List.of;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.SeasonalityDefinition;
import com.solum.xplain.calibration.discounting.CalibrationDiscountSubGroup;
import com.solum.xplain.calibration.rates.set.CalibrationBundle;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.Nullable;
import org.slf4j.Logger;

@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class CurvesCalibration {
  private static final String CALIBRATION_INFO_LOG = "Calibration %s. %s";
  private static final Logger LOG = getLogger(CurvesCalibration.class);

  private final CurvesCalibrationOptions options;
  private final CurveCalibrationSetProvider calibrationSetProvider;

  public CurvesCalibration(CurvesCalibrationOptions opts) {
    this(
        opts,
        new CurveCalibrationSetProvider(
            opts.getMarketData(),
            opts.getNodesFilter(),
            opts.getReferenceData(),
            opts.getWarningsConsumer()));
  }

  public CalibrationCombinedResultRates calibrate(
      CalibrationBundle bundle,
      @Nullable Currency discountingCcy,
      boolean calibrateShiftedFx,
      boolean allowDropMissingMdNodes) {
    LOG.debug("Starting calibrate rates");
    List<CalibrationSetResult> results = List.of();
    try {
      var calibrationResults =
          runAsyncWithCurrentContext(
              () ->
                  bundle.getCalibrationDiscountingSubGroups().parallelStream()
                      .map(group -> logCalibrationGroup(group, bundle.getDescription()))
                      .map(
                          group ->
                              calibrationSet(group, calibrateShiftedFx, allowDropMissingMdNodes)
                                  .flatMap(
                                      set -> set.calibrateRates(options.getCalibrationMeasures())))
                      .toList());
      options.getWarningsConsumer().accept(ImmutableList.copyOf(filterLeft(calibrationResults)));
      results = ImmutableList.copyOf(filterRight(calibrationResults));
    } catch (ExecutionException | InterruptedException e) {
      LOG.warn("Exception occurred during rates calibration: ", e);
      Thread.currentThread().interrupt();
    } finally {
      LOG.debug("Finished calibrate rates");
    }
    // Discounting currency will be null if we are calibrating local discounting
    LOG.debug(
        "Calibration discounting currency = {}, Bundle currency = {}",
        discountingCcy,
        bundle.getCurrency());
    return calibrationCombinedResultRates(
        results, discountingCcy, options.getMarketData(), options.getReferenceData());
  }

  private CalibrationDiscountSubGroup logCalibrationGroup(
      CalibrationDiscountSubGroup group, String bundleDescription) {
    var logInfo = format(CALIBRATION_INFO_LOG, bundleDescription, group.groupDescription());
    options.getWarningsConsumer().accept(of(CALIBRATION_INFO.entity(logInfo)));
    return group;
  }

  private Either<ErrorItem, CurveCalibrationSet> calibrationSet(
      CalibrationDiscountSubGroup group,
      boolean calibrateShiftedFx,
      boolean allowDropMissingMdNodes) {
    var currency = group.discountCurve().getResolvedDiscountCurrency();
    var entries = group.calibrationEntries();
    var seasonality = calcSeasonality(group.calibrationEntries());
    return calibrationSetProvider.calibrationSet(
        currency, entries, calibrateShiftedFx, seasonality, allowDropMissingMdNodes);
  }

  private Map<CurveName, SeasonalityDefinition> calcSeasonality(Set<CalibrationEntry> entries) {
    var result =
        ofSettings(options.getInflationSeasonalitySettings())
            .calcSeasonality(options.getMarketData(), entries);
    options.getWarningsConsumer().accept(result.seasonalityErrors());
    return result.calculatedSeasonalities();
  }
}
