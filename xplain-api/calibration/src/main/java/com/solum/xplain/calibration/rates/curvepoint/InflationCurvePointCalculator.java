package com.solum.xplain.calibration.rates.curvepoint;

import static java.time.temporal.ChronoUnit.MONTHS;

import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import java.time.LocalDate;
import java.time.YearMonth;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class InflationCurvePointCalculator implements CurvePointCalculator {
  private final LocalDate valuationDate;
  private final PriceIndexCalculationMethod calculationMethod;
  private final CurveValueCalculator<Long> valueCalculator;

  @Override
  public CurvePoint calculate(LocalDate date) {
    long xValue = months(YearMonth.from(date));
    if (calculationMethod == PriceIndexCalculationMethod.MONTHLY) {
      return new CurvePoint(xValue, calculateMonthEndPriceIndex(date));
    } else {
      return new CurvePoint(xValue, calculateInterpolatedPriceIndex(date));
    }
  }

  private double calculateInterpolatedPriceIndex(LocalDate endDate) {
    var endObservation = YearMonth.from(endDate);
    var endSecondObservation = YearMonth.from(endDate.plusMonths(1));

    var xValue1 = months(endObservation);
    var xValue2 = months(endSecondObservation);

    double weight = 1d - (endDate.getDayOfMonth() - 1d) / endDate.lengthOfMonth();
    double indexValue1 = valueCalculator.calculate(xValue1);
    double indexValue2 = valueCalculator.calculate(xValue2);

    return weight * indexValue1 + (1d - weight) * indexValue2;
  }

  private double calculateMonthEndPriceIndex(LocalDate endDate) {
    var endObservation = YearMonth.from(endDate);
    var xValue = months(endObservation);
    return valueCalculator.calculate(xValue);
  }

  private Long months(YearMonth date) {
    return YearMonth.from(valuationDate).until(date, MONTHS);
  }
}
