package com.solum.xplain.calibration.credit;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.tuple.Pair;
import com.opengamma.strata.pricer.credit.CreditDiscountFactors;
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider;
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities;
import com.opengamma.strata.pricer.credit.RecoveryRates;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;
import lombok.Data;
import org.slf4j.Logger;

@Data
public class CreditRatesDataProvider {

  private static final Logger LOG = getLogger(CreditRatesDataProvider.class);

  private final LocalDate valuationDate;
  private final Map<Currency, CreditDiscountFactors> creditDiscountFactors;
  private final Map<StandardId, RecoveryRates> recoveryRates;
  private final Map<Pair<StandardId, Currency>, LegalEntitySurvivalProbabilities> probabilities;

  public CreditRatesDataProvider(
      LocalDate valuationDate,
      Map<Currency, CreditDiscountFactors> creditDiscountFactors,
      Map<StandardId, RecoveryRates> recoveryRates,
      Map<Pair<StandardId, Currency>, LegalEntitySurvivalProbabilities> probabilities) {
    this.valuationDate = valuationDate;
    this.creditDiscountFactors = ImmutableMap.copyOf(creditDiscountFactors);
    this.recoveryRates = ImmutableMap.copyOf(recoveryRates);
    this.probabilities = ImmutableMap.copyOf(probabilities);
  }

  public Either<ErrorItem, ImmutableCreditRatesProvider> toImmutableCreditRatesProvider() {
    try {
      return right(
          ImmutableCreditRatesProvider.builder()
              .valuationDate(valuationDate)
              .discountCurves(creditDiscountFactors)
              .recoveryRateCurves(recoveryRates)
              .creditCurves(probabilities)
              .build());
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return left(
          new ErrorItem(CALIBRATION_ERROR, "Error creating credit rates. " + ex.getMessage()));
    }
  }

  public Optional<ImmutableCreditRatesProvider> toImmutableCreditRatesProvider(
      UnaryOperator<ErrorItem> logError) {
    var provider = toImmutableCreditRatesProvider();
    provider.left().forEach(logError::apply);
    return provider.toOptional();
  }
}
