package com.solum.xplain.calibration.rates.group.discountable;

import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Set;

public interface DiscountableItemIndexResolver<I> {

  Either<ErrorItem, FloatingRateIndex> resolveIndex(
      DiscountableItem<I> item, Set<FloatingRateIndex> availableIndices);
}
