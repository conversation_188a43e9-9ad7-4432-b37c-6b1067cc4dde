package com.solum.xplain.calibration.discounting;

import static com.solum.xplain.calibration.discounting.DiscountingUtils.discountEntry;
import static java.util.Objects.requireNonNull;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calibration.rates.CalibrationEntry;
import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * The atomic unit of the curve calibration process. A discount subgroup holds discount curve(s)
 * along with a set of calibration entries (curves). A CalibrationDiscountSubGroup object is sent to
 * OG for isolated calibration. CalibrationDiscountSubGroup's are subsets of a {@link
 * DiscountingGroup}, and can be of type base or foreign. A base discount subgroup contains only a
 * discount curve, while a foreign discount subgroup contains a base discount curve and a foreign
 * discount curve.
 */
@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CalibrationDiscountSubGroup {

  private static final String BASE_DESCRIPTION_PATTERN =
      "Base discount group with discount curve %s [%s], curves %s";
  private static final String FOREIGN_DESCRIPTION_PATTERN =
      "Foreign discount group with base discount curve %s [%s], foreign discount curve %s [%s], curves %s";
  private final CalibrationEntry baseDiscountEntry;
  private final CalibrationEntry discountEntry;
  private final Set<CalibrationEntry> calibrationEntries;

  public static CalibrationDiscountSubGroup newOfBase(
      Currency discountCurrency, Set<CalibrationEntry> entries) {
    return new CalibrationDiscountSubGroup(null, discountCurrency, entries);
  }

  public static CalibrationDiscountSubGroup newOfForeign(
      CalibrationEntry baseDiscountEntry,
      Currency discountCurrency,
      Set<CalibrationEntry> entries) {
    return new CalibrationDiscountSubGroup(baseDiscountEntry, discountCurrency, entries);
  }

  private CalibrationDiscountSubGroup(
      CalibrationEntry baseDiscountEntry,
      Currency discountCurrency,
      Set<CalibrationEntry> calibrationEntries) {
    this.baseDiscountEntry = baseDiscountEntry;
    this.discountEntry =
        requireNonNull(
            discountEntry(discountCurrency, calibrationEntries),
            "calibration entries must contain discount curve for discount currency!");
    this.calibrationEntries = calibrationEntries;
  }

  public Set<CalibrationEntry> calibrationEntries() {
    return calibrationEntries;
  }

  public CalibrationEntry discountCurve() {
    return discountEntry;
  }

  public String groupDescription() {
    var dscCurveName = discountEntry.getCurveName();
    var allEntriesNames = calibrationEntries.stream().map(CalibrationEntry::getCurveName).toList();
    if (baseDiscountEntry != null) {
      return String.format(
          FOREIGN_DESCRIPTION_PATTERN,
          baseDiscountEntry.getCurveName(),
          baseDiscountEntry.getResolvedDiscountCurrency(),
          dscCurveName,
          discountEntry.getResolvedDiscountCurrency(),
          allEntriesNames);
    }
    return String.format(
        BASE_DESCRIPTION_PATTERN,
        dscCurveName,
        discountEntry.getResolvedDiscountCurrency(),
        allEntriesNames);
  }
}
