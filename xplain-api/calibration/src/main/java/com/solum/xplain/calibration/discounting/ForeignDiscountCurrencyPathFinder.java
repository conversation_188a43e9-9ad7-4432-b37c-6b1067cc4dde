package com.solum.xplain.calibration.discounting;

import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import java.util.List;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

/**
 * Class for finding {@link ForeignDiscountCurrencyPath}s. If we want to discount USD rates in EUR,
 * we could find a foreign discount path between EUR and USD in three different ways: - A direct
 * path - e.g., via the XCCY pair EUR/USD - A triangulated path (using the triangulation currency
 * GBP) - e.g., via the XCCY pairs EUR/GBP,GBP/USD - Any other path - e.g., via the XCCY pairs
 * EUR/AUD,AUD/NZD,NZD/USD
 */
@EqualsAndHashCode
public class ForeignDiscountCurrencyPathFinder {

  @NonNull private final List<CurrencyPair> xccyPairs;
  @NonNull private final Currency baseCcy;
  @NonNull private final Currency triangulationCcy;

  public ForeignDiscountCurrencyPathFinder(
      @NonNull List<CurrencyPair> xccyPairs,
      @NonNull Currency baseCcy,
      @NonNull Currency triangulationCcy) {
    this.xccyPairs = xccyPairs;
    this.baseCcy = baseCcy;
    this.triangulationCcy = triangulationCcy;
  }

  public Optional<ForeignDiscountCurrencyPath> resolvePathTo(Currency targetCcy) {
    return resolveDirectPath(targetCcy)
        .or(() -> resolveTriangulatedPath(targetCcy))
        .or(() -> resolveAnyOrderedPath(targetCcy));
  }

  private Optional<ForeignDiscountCurrencyPath> resolveDirectPath(Currency targetCcy) {
    boolean hasDirect = hasPair(baseCcy, targetCcy);
    if (hasDirect) {
      return Optional.of(new ForeignDiscountCurrencyPath(List.of(baseCcy, targetCcy)));
    }
    return Optional.empty();
  }

  private Optional<ForeignDiscountCurrencyPath> resolveTriangulatedPath(Currency targetCcy) {
    boolean hasBaseTriangulation = hasPair(baseCcy, triangulationCcy);
    boolean hasTriangulationTarget = hasPair(triangulationCcy, targetCcy);
    if (hasBaseTriangulation && hasTriangulationTarget) {
      return Optional.of(
          new ForeignDiscountCurrencyPath(List.of(baseCcy, triangulationCcy, targetCcy)));
    }
    return Optional.empty();
  }

  private Optional<ForeignDiscountCurrencyPath> resolveAnyOrderedPath(Currency targetCcy) {
    return resolveDownstreamPath(new ForeignDiscountCurrencyPath(List.of(baseCcy)), targetCcy);
  }

  private Optional<ForeignDiscountCurrencyPath> resolveDownstreamPath(
      ForeignDiscountCurrencyPath upperPath, Currency targetCcy) {
    Optional<ForeignDiscountCurrencyPath> pathToTarget = Optional.empty();

    for (Currency intermediateCcy : EXPLICIT_CURRENCIES) {
      if (upperPath.hasPart(intermediateCcy)) {
        continue;
      }
      boolean hasUpperIntermediate = hasPair(upperPath.getLast(), intermediateCcy);
      if (hasUpperIntermediate) {
        var upperIntermediatePath =
            new ForeignDiscountCurrencyPath(
                ImmutableList.<Currency>builder()
                    .addAll(upperPath.getPaths())
                    .add(intermediateCcy)
                    .build());
        if (targetCcy.equals(intermediateCcy)) {
          return Optional.of(upperIntermediatePath);
        }

        if (pathToTarget.isEmpty()) {
          pathToTarget = resolveDownstreamPath(upperIntermediatePath, targetCcy);
        }
      }
    }
    return pathToTarget;
  }

  private boolean hasPair(Currency ccy1, Currency ccy2) {
    return xccyPairs.stream().anyMatch(pair -> pair.contains(ccy1) && pair.contains(ccy2));
  }
}
