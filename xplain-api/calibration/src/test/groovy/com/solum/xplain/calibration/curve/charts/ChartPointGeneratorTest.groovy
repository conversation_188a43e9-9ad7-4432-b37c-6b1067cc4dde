package com.solum.xplain.calibration.curve.charts

import com.opengamma.strata.market.ValueType
import com.solum.xplain.calibration.curve.charts.calculator.ChartCurvePointCalculatorFactory
import com.solum.xplain.calibration.curve.charts.value.CurveChartGeneratorData
import com.solum.xplain.calibration.curve.charts.value.CurveDetails
import com.solum.xplain.calibration.rates.curvepoint.CurvePoint
import com.solum.xplain.calibration.rates.curvepoint.CurvePointCalculator
import com.solum.xplain.core.common.value.CalculatedValueAtDate
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import java.time.LocalDate
import spock.lang.Specification

class ChartPointGeneratorTest extends Specification {
  ChartCurvePointCalculatorFactory calculatorFactory = Mock()

  ChartPointGenerator generator = new ChartPointGenerator(calculatorFactory)

  def "should return IR curve chart points with ACTUAL_DATE"() {
    setup:
    def curve = Mock(Curve)
    curve.yValueType() >> ValueType.ZERO_RATE
    curve.xValueType() >> ValueType.YEAR_FRACTION
    curve.name >> "EUR 3M"

    def calibrationResult = Mock(CurveCalibrationResult)
    calibrationResult.valuationDate >> LocalDate.of(2020, 1, 15)
    calibrationResult.isCalibrated() >> true
    calibrationResult.nodeValues >> [
      new CalculatedValueAtDate(LocalDate.of(2023, 1, 1), 1),
      new CalculatedValueAtDate(LocalDate.of(2023, 12, 1), 12)
    ]

    def generatorData = buildChartGeneratorData(curve, calibrationResult)
    mockCalculator(generatorData)

    when:
    def result = generator.generateChartPoints(generatorData, ChartDateType.ACTUAL_DATE)

    then:
    result.curveName == "EUR 3M"
    result.discountingKey == "EUR"
    var points = result.getPoints()
    points.size() == 49

    def dates = result.getPoints().date
    dates[0] == LocalDate.of(2020, 1, 15)
    dates[1] == LocalDate.of(2020, 2, 15)
    dates[48] == LocalDate.of(2023, 12, 01)
  }

  def "should return IR curve chart points END_OF_MONTH"() {
    setup:
    def curve = Mock(Curve)
    curve.yValueType() >> ValueType.ZERO_RATE
    curve.xValueType() >> ValueType.YEAR_FRACTION
    curve.name >> "EUR 3M"

    def calibrationResult = Mock(CurveCalibrationResult)
    calibrationResult.valuationDate >> LocalDate.of(2020, 1, 15)
    calibrationResult.isCalibrated() >> true
    calibrationResult.nodeValues >> [
      new CalculatedValueAtDate(LocalDate.of(2023, 1, 1), 1),
      new CalculatedValueAtDate(LocalDate.of(2023, 12, 1), 12)
    ]
    def generatorData = buildChartGeneratorData(curve, calibrationResult)
    mockCalculator(generatorData)

    when:
    def result = generator.generateChartPoints(generatorData, ChartDateType.END_OF_MONTH)

    then:
    result.curveName == "EUR 3M"
    result.discountingKey == "EUR"
    var points = result.getPoints()
    points.size() == 48

    def dates = result.getPoints().date
    dates[0] == LocalDate.of(2020, 1, 31)
    dates[1] == LocalDate.of(2020, 2, 29)

    !dates.contains(LocalDate.of(2023, 1, 1))
    !dates.contains(LocalDate.of(2023, 12, 1))
  }

  def "should return Inflation chart points"() {
    setup:
    def curve = Mock(Curve)
    curve.yValueType() >> ValueType.PRICE_INDEX
    curve.xValueType() >> ValueType.MONTHS
    curve.name >> "EU AI CPI"

    def calibrationResult = Mock(CurveCalibrationResult)
    calibrationResult.valuationDate >> LocalDate.of(2020, 1, 15)
    calibrationResult.isCalibrated() >> true
    calibrationResult.nodeValues >> [
      new CalculatedValueAtDate(LocalDate.of(2023, 1, 1), 1),
      new CalculatedValueAtDate(LocalDate.of(2023, 12, 1), 12)
    ]

    def generatorData = buildChartGeneratorData(curve, calibrationResult)
    mockCalculator(generatorData)

    when:
    def result = generator.generateChartPoints(generatorData, dateType)

    then:
    result.curveName == "EU AI CPI"
    result.discountingKey == "EUR"
    var points = result.getPoints()
    points.size() == 51

    def dates = result.getPoints().date
    dates[0] == LocalDate.of(2019, 10, 1)
    dates[1] == LocalDate.of(2019, 11, 1)
    dates[50] == LocalDate.of(2023, 12, 1)

    where:
    dateType << [
      ChartDateType.END_OF_MONTH,
      ChartDateType.ACTUAL_DATE] // Inflation curve ignore date type and always use 1st of month
  }


  def mockCalculator(CurveChartGeneratorData generatorData) {
    def calculator = Mock(CurvePointCalculator)
    1 * calculatorFactory.calculator(generatorData) >> calculator
    calculator.calculate(_ as LocalDate) >> { LocalDate date -> new CurvePoint(1, 1) }
    calculator
  }

  static CurveChartGeneratorData buildChartGeneratorData(Curve curve, CurveCalibrationResult result) {
    var curveDetails = new CurveDetails(
      curve.getName(),
      "EUR",
      curve.xValueType(),
      curve.yValueType(),
      curve.getInterpolator(),
      curve.getExtrapolatorLeft(),
      curve.getExtrapolatorRight())

    var generatorData =
      new CurveChartGeneratorData(
      curveDetails,
      result.getInflationAdjustmentType(),
      result.getInflationSeasonalityAdjustment(),
      result.getNodeValues(),
      result.getValuationDate())
    return generatorData
  }
}
