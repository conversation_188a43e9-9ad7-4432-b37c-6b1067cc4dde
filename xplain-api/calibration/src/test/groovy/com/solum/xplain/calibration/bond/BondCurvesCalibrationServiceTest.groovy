package com.solum.xplain.calibration.bond

import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.calibration.value.CalibrationOptions
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveBuilder
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveNodeBuilder
import com.solum.xplain.core.curvegroup.curvebond.BondCurveRepository
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.market.MarketDataSample
import java.time.LocalDate
import spock.lang.Specification

class BondCurvesCalibrationServiceTest extends Specification {

  def "should calibrate and store result for bond curves"() {
    setup:
    def errors = []
    def curve = new BondCurveBuilder()
    .nodes([
      new BondCurveNodeBuilder().maturityDate(LocalDate.of(2023, 1, 1)).build(),
      new BondCurveNodeBuilder().maturityDate(LocalDate.of(2024, 1, 1)).build()])
    .build()

    def stateDate = BitemporalDate.newOfNow()
    def curveDate = stateDate.getActualDate()
    def stateForm = new CurveConfigMarketStateKey("mdId", stateDate, curveDate, "ccId", RAW_PRIMARY,
    InstrumentPriceRequirements.bidRequirements())
    def options = new CalibrationOptions(curveGroup(), EMPTY_FILTER, null, stateForm, null, null, null, null)

    def repository = Mock(BondCurveRepository)
    1 * repository.getActiveCurves(options.getCurveGroup().getId(), stateDate) >> [curve]

    def service = new BondCurvesCalibrationService(repository, ReferenceData.standard())

    when:
    def result = service.calibrate(options, MarketDataSample.ogMarketData(), l -> errors.addAll(l))

    then:
    result != null
    errors.isEmpty()
    1 * repository.updateCalibrationResults({
      r ->
      r.curveId == curve.getEntityId()
      r.stateDate == stateDate.getActualDate()
      r.curveDate == curveDate
      r.marketDataGroupId == "mdId"
      r.marketDataSource == RAW_PRIMARY.name()
      r.priceRequirements == InstrumentPriceRequirements.bidRequirements()
      r.chartPoints != null
    })
  }
}
