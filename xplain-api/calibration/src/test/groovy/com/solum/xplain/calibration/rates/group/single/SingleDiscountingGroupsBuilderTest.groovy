package com.solum.xplain.calibration.rates.group.single

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.LCH
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.NONE

import com.opengamma.strata.basics.index.FloatingRateIndex
import com.opengamma.strata.basics.index.IborIndices
import com.solum.xplain.calibration.rates.group.IndexDiscountingGroupKey
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemIndexResolver
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import java.util.function.Consumer
import spock.lang.Specification

class SingleDiscountingGroupsBuilderTest extends Specification {
  Consumer<ErrorItem> ERRORS_CONSUMER = e -> { }
  Set<FloatingRateIndex> GROUP_INDICES = [] as Set

  def "should correctly build single discountings"() {
    setup:
    def indexResolver = Mock(DiscountableItemIndexResolver)
    def builder = new SingleDiscountingGroupsBuilder<>(indexResolver, GROUP_INDICES)

    def item1 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item1, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item1.requirements() >> new DiscountableItemRequirements(EUR, false, NONE, null, null, null, null, Set.of())
    builder.withItem(item1)

    def item2 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item2, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item2.requirements() >> new DiscountableItemRequirements(EUR, false, NONE, null, null, null, null, Set.of())
    builder.withItem(item2)
    def discounting = builder.build(ERRORS_CONSUMER) as SingleDiscountingGroups

    expect:
    discounting.getDiscountingGroups().size() == 1
    discounting.getDiscountingGroups().containsKey(new IndexDiscountingGroupKey(IborIndices.EUR_EURIBOR_3M, NONE))
  }

  def "should correctly build single discountings with onshore/offshore"() {
    setup:
    def indexResolver = Mock(DiscountableItemIndexResolver)
    def builder = new SingleDiscountingGroupsBuilder<>(indexResolver, GROUP_INDICES)

    def item1 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item1, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item1.requirements() >> new DiscountableItemRequirements(EUR, false, NONE, null, null, null, null, Set.of())
    builder.withItem(item1)

    def item2 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item2, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item2.requirements() >> new DiscountableItemRequirements(EUR, true, NONE, null, null, null, null, Set.of())
    builder.withItem(item2)
    def discounting = builder.build(ERRORS_CONSUMER) as SingleDiscountingGroups

    expect:
    discounting.getDiscountingGroups().size() == 1
    discounting.getDiscountingGroups().containsKey(new IndexDiscountingGroupKey(IborIndices.EUR_EURIBOR_3M, NONE))
  }

  def "should correctly build single discountings with onshore/offshore and clearing house"() {
    setup:
    def indexResolver = Mock(DiscountableItemIndexResolver)
    def builder = new SingleDiscountingGroupsBuilder<>(indexResolver, GROUP_INDICES)

    def item1 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item1, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item1.requirements() >> new DiscountableItemRequirements(EUR, false, NONE, null, null, null, null, Set.of())
    builder.withItem(item1)

    def item2 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item2, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item2.requirements() >> new DiscountableItemRequirements(EUR, true, NONE, null, null, null, null, Set.of())
    builder.withItem(item2)

    def item3 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item3, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item3.requirements() >> new DiscountableItemRequirements(EUR, true, LCH, null, null, null, null, Set.of())
    builder.withItem(item3)

    def item4 = Mock(DiscountableItem)
    1 * indexResolver.resolveIndex(item4, GROUP_INDICES) >> Either.right(IborIndices.EUR_EURIBOR_3M)
    item4.requirements() >> new DiscountableItemRequirements(EUR, true, LCH, null, null, null, null, Set.of())
    builder.withItem(item4)

    def discounting = builder.build(ERRORS_CONSUMER) as SingleDiscountingGroups

    expect:
    discounting.getDiscountingGroups().size() == 2
    discounting.getDiscountingGroups().containsKey(new IndexDiscountingGroupKey(IborIndices.EUR_EURIBOR_3M, NONE))
    discounting.getDiscountingGroups().containsKey(new IndexDiscountingGroupKey(IborIndices.EUR_EURIBOR_3M, LCH))
  }
}
