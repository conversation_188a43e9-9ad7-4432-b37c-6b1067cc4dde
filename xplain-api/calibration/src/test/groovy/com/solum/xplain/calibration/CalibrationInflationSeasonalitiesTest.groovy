package com.solum.xplain.calibration

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.calibration.settings.CalibrationInflationSeasonalities.ofSettings
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurUsdFx
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.frInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.gbpInflation

import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.market.ShiftType
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.SeasonalityDefinition
import com.opengamma.strata.market.observable.IndexQuoteId
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.calibration.value.CurveShifts
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.settings.entity.CurveSeasonality
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings
import com.solum.xplain.core.settings.inflation.CalculatedCurveSeasonality
import com.solum.xplain.core.settings.inflation.InflationCalculation
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class CalibrationInflationSeasonalitiesTest extends Specification {

  def "should calculate seasonalities for calibration sets"() {
    setup:
    def failedCalculation = Mock(InflationCalculation)
    failedCalculation.calculate(_ as MarketData) >> Either.left(Error.CALCULATION_ERROR.entity("ERR"))

    def gbRpiAdjustment = Mock(CurveSeasonality)
    gbRpiAdjustment.resolveCalculation() >> failedCalculation
    gbRpiAdjustment.getPriceIndex() >> "GB-RPI"

    def sucessfullCalculation = Mock(InflationCalculation)
    def calculatedSeasonality = Mock(CalculatedCurveSeasonality)
    calculatedSeasonality.seasonalityDefinition() >> SeasonalityDefinition.of(DoubleArray.of(12, { i -> 1d }), ShiftType.SCALED)
    sucessfullCalculation.calculate(_ as MarketData) >> Either.right(calculatedSeasonality)

    def euCpiAdjustment = Mock(CurveSeasonality)
    euCpiAdjustment.resolveCalculation() >> sucessfullCalculation
    euCpiAdjustment.getPriceIndex() >> "EU-AI-CPI"

    def settings = new InflationSeasonalitySettings(curveSeasonalities: [gbRpiAdjustment, euCpiAdjustment])

    def calc = ofSettings(settings)

    def ts = LocalDateDoubleTimeSeries.builder().put(LocalDate.of(2016, 11, 1), 100).build()
    def marketData = Mock(MarketData)
    _ * marketData.valuationDate >> LocalDate.parse("2017-01-01")
    _ * marketData.getTimeSeries(IndexQuoteId.of(PriceIndices.GB_RPI)) >> ts

    def entries = [
      new CalibrationEntry(eurUsdFx(), CurveShifts.empty(), EUR),
      new CalibrationEntry(frInflation(), CurveShifts.empty(), EUR),
      new CalibrationEntry(gbpInflation(), CurveShifts.empty(), EUR),
      new CalibrationEntry(eurInflation(), CurveShifts.empty(), EUR)
    ]

    when:
    def result = calc.calcSeasonality(marketData, Set.copyOf(entries))

    then:
    result != null
    result.calculatedSeasonalities().size() == 1
    result.calculatedSeasonalities().get(CurveName.of("EU AI CPI")) != null
    result.seasonalityErrors().size() == 1
    result.seasonalityErrors().get(0).reason == Error.CALIBRATION_WARNING
    result.seasonalityErrors().get(0).description == "ERR"
  }
}
