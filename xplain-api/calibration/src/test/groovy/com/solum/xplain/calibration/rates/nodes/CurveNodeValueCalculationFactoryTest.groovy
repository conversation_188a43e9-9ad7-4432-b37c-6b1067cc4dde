package com.solum.xplain.calibration.rates.nodes

import static com.solum.xplain.calibration.CalibrationSetSample.sampleCurveGroupDefinition
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.pricer.curve.RatesCurveCalibrator
import com.solum.xplain.calibration.rates.curvepoint.InflationCurvePointCalculator
import com.solum.xplain.calibration.rates.curvepoint.InterestRatesCurvePointCalculator
import spock.lang.Specification

class CurveNodeValueCalculationFactoryTest extends Specification {
  static CURVE_GROUP = sampleCurveGroupDefinition()
  static RATES_PROVIDER = RatesCurveCalibrator.standard().calibrate(CURVE_GROUP, ogMarketData(), ReferenceData.standard())

  def "should return price index chart calculation"() {
    setup:
    def curve = CURVE_GROUP.findCurveDefinition(CurveName.of("EU AI CPI")).orElse(null)
    def calculation = CurveNodeValueCalculationFactory.calculation(RATES_PROVIDER, curve)
    expect:
    calculation.curve.name.name == "EU AI CPI"
    calculation.pointCalculator instanceof InflationCurvePointCalculator
  }

  def "should return zero rate chart calculation"() {
    setup:
    def curve = CURVE_GROUP.findCurveDefinition(CurveName.of("EUR EONIA")).orElse(null)
    def calculation = CurveNodeValueCalculationFactory.calculation(RATES_PROVIDER, curve)
    expect:
    calculation.curve.name.name == "EUR EONIA"
    calculation.pointCalculator instanceof InterestRatesCurvePointCalculator
  }
}
