package com.solum.xplain.calibration.rates.curvepoint


import com.opengamma.strata.basics.date.DayCounts
import java.time.LocalDate
import spock.lang.Specification

class InterestRatesChartPointCalculatorTest extends Specification {
  LocalDate VALUATION_DATE = LocalDate.of(2022,1,1)

  def "should interest rate curve point"() {
    setup:
    def valueCalculator = Mock(CurveValueCalculator)
    def calculation = new InterestRatesCurvePointCalculator(VALUATION_DATE, DayCounts.ACT_365F, valueCalculator)

    when:
    def points = calculation.calculate(LocalDate.of(2023, 1,1))

    then:
    valueCalculator.calculate(1.0) >> 1.1

    and:
    points.xValue() == 1.0d
    points.yValue() == 1.1d

    when:
    points = calculation.calculate(LocalDate.of(2024, 1,1))

    then:
    valueCalculator.calculate(2.0) >> 2.1

    and:
    points.xValue() == 2.0d
    points.yValue() == 2.1d
  }
}
