package com.solum.xplain.calibration.rates.group.ois.combined

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD

import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemCurrencyResolver
import com.solum.xplain.calibration.rates.group.ois.localccy.LocalCcyDiscountingGroups
import com.solum.xplain.calibration.rates.group.ois.localccy.LocalCcyDiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.single.SingleDiscountingGroups
import com.solum.xplain.calibration.rates.group.single.SingleDiscountingGroupsBuilder
import com.solum.xplain.core.error.ErrorItem
import java.util.function.Consumer
import spock.lang.Specification

class OisLocalCcySingleFallbackDiscountingGroupsBuilderTest extends Specification {
  Consumer<ErrorItem> ERRORS_CONSUMER = e -> { }

  def "should correctly propagate trades to corresponding discounting builder"() {
    setup:
    def localCcyDiscountingBuilder = Mock(LocalCcyDiscountingGroupsBuilder)
    def liborDiscountingsBuilder = Mock(SingleDiscountingGroupsBuilder)
    def availableDiscountCurrencies = Set.of(EUR)
    def currencyResolver = Mock(DiscountableItemCurrencyResolver)
    def builder = new OisLocalCcySingleFallbackDiscountingGroupsBuilder<>(localCcyDiscountingBuilder,
    liborDiscountingsBuilder, availableDiscountCurrencies, currencyResolver)

    def item1 = Mock(DiscountableItem)
    def item2 = Mock(DiscountableItem)

    when:
    builder.withItem(item1)
    builder.withItem(item2)

    then:
    1 * currencyResolver.resolveCurrency(item1) >> EUR
    1 * currencyResolver.resolveCurrency(item2) >> USD
    1 * localCcyDiscountingBuilder.withItem(item1)
    0 * localCcyDiscountingBuilder.withItem(item2)
    0 * liborDiscountingsBuilder.withItem(item1)
    1 * liborDiscountingsBuilder.withItem(item2)
  }

  def "should correctly build and merge discountings"() {
    setup:
    def localCcyDiscountingBuilder = Mock(LocalCcyDiscountingGroupsBuilder)
    def liborDiscountingsBuilder = Mock(SingleDiscountingGroupsBuilder)
    def availableDiscountCurrencies = Set.of(EUR)
    def currencyResolver = Mock(DiscountableItemCurrencyResolver)
    def builder = new OisLocalCcySingleFallbackDiscountingGroupsBuilder<>(localCcyDiscountingBuilder,
    liborDiscountingsBuilder, availableDiscountCurrencies, currencyResolver)


    def localCcyRes = Mock(LocalCcyDiscountingGroups)
    1 * localCcyDiscountingBuilder.build(ERRORS_CONSUMER) >> localCcyRes
    def liborRes = Mock(SingleDiscountingGroups)
    1 * liborDiscountingsBuilder.build(ERRORS_CONSUMER) >> liborRes

    when:
    def res = builder.build(ERRORS_CONSUMER)

    then:
    res == new OisLocalCcySingleFallbackDiscountingGroups<>(localCcyRes, liborRes)
  }
}
