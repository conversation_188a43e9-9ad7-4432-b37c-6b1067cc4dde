package com.solum.xplain.calibration.rates

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT

import com.google.common.collect.ImmutableList
import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.market.ShiftType
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.ConstantNodalCurve
import com.opengamma.strata.market.curve.Curve
import com.opengamma.strata.market.curve.CurveDefinition
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveNode
import com.opengamma.strata.market.curve.DefaultCurveMetadata
import com.opengamma.strata.market.curve.InflationNodalCurve
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.market.curve.NodalCurve
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.market.param.LabelDateParameterMetadata
import com.opengamma.strata.market.param.ParameterMetadata
import com.opengamma.strata.market.param.TenorDateParameterMetadata
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.calibration.rates.group.DiscountingGroup
import com.solum.xplain.core.calibration.CurveSample
import java.time.LocalDate
import spock.lang.Specification

class CalibrationCombinedResultRatesTest extends Specification {
  DoubleArray XVALUES_SPREAD = DoubleArray.of(1.5d, 2.5d, 4.5d)
  DoubleArray XVALUES_MONTHS = DoubleArray.of(1d, 2d, 3d)
  DoubleArray YVALUES = DoubleArray.of(0.04d, 0.045d, 0.05d)
  List<Double> SEASONALITIES = [1.0d, 2.0d, 3.0d, 4.0d, 5.0d, 6.0d, 7.0d, 8.0d, 9.0d, 10.0d, 11.0d, 0.0d]
  CurveInterpolator INTERPOLATOR = CurveInterpolators.LINEAR

  static VALUATION_DATE = LocalDate.parse("2015-01-01")
  static REF_DATA = ReferenceData.standard()

  def "should return calibrated curves"() {
    setup:
    def marketData = Mock(MarketData)
    _ * marketData.getValuationDate() >> VALUATION_DATE

    def discounting = Mock(DiscountingGroup)
    _ * discounting.key() >> "EUR"
    _ * discounting.supportsDiscount(EUR) >> true
    _ * discounting.supportsIndex(EUR_EURIBOR_3M) >> true

    def ratesProvider = ImmutableRatesProvider
    .builder(VALUATION_DATE)
    .indexCurve(EUR_EURIBOR_3M, buildCurve("EUR 3M"))
    .indexCurve(USD_LIBOR_3M, buildCurve("USD 3M"))
    .priceIndexCurve(PriceIndices.GB_RPI, buildInflationCurve("GB RPI"))
    .discountCurve(USD, buildCurve("EUR1"))
    .discountCurve(EUR, buildCurve("EURIBOR2"))
    .timeSeries(EUR_EURIBOR_3M, LocalDateDoubleTimeSeries.empty())
    .build()

    def ratesResult = new RatesCalibrationResult(ratesProvider, [])
    def result = new CalibrationCombinedResultRates([] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)
    when:
    def recorded = result.calibratedCurves(discounting).sort(false, {
      it.getName()
    })

    then:
    recorded.size() == 5
    recorded[0] != null
    recorded[0].name == "EUR 3M"
    recorded[0].discountingKey == "EUR"
    recorded[0].primary
    recorded[0].YValueType == "ZeroRate"
    recorded[0].interpolator == "Linear"
    recorded[0].extrapolatorLeft == "Flat"
    recorded[0].extrapolatorRight == "Flat"
    recorded[0].chartPoints.size() == 3

    // All node dates taken from Metadata
    recorded[0].chartPoints.get(0).date == LocalDate.parse("2015-01-01")
    recorded[0].chartPoints.get(1).date == LocalDate.parse("2015-02-01")
    recorded[0].chartPoints.get(2).date == LocalDate.parse("2015-03-01")

    recorded[1] != null
    recorded[1].name == "EUR1"
    recorded[1].discountingKey == "EUR"
    !recorded[1].primary
    recorded[1].YValueType == "ZeroRate"
    recorded[1].chartPoints.size() == 3
    recorded[1].chartPoints.get(0).date == LocalDate.parse("2015-01-01")
    recorded[1].chartPoints.get(1).date == LocalDate.parse("2015-02-01")
    recorded[1].chartPoints.get(2).date == LocalDate.parse("2015-03-01")

    recorded[3] != null
    recorded[3].name == "GB RPI"
    recorded[3].discountingKey == "EUR"
    !recorded[3].primary
    recorded[3].YValueType == "PriceIndex"
    recorded[3].interpolator == "Linear"
    recorded[3].extrapolatorLeft == "Flat"
    recorded[3].extrapolatorRight == "Flat"
    recorded[3].inflationAdjustmentType == "RELATIVE"
    recorded[3].inflationSeasonalityAdjustment == SEASONALITIES

    //Only nodes from Metadata cause fixings not present
    recorded[3].chartPoints.size() == 3
    recorded[3].chartPoints.get(0).date == LocalDate.parse("2015-02-01")
    recorded[3].chartPoints.get(1).date == LocalDate.parse("2015-03-01")
    recorded[3].chartPoints.get(2).date == LocalDate.parse("2015-04-01")
  }

  def "should return isda compliant false for discount curve with interpolator LINEAR"() {
    setup:
    def ratesProvider = ImmutableRatesProvider.builder(LocalDate.now()).discountCurve(EUR, CurveSample.discountCurve()).build()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])

    when:
    def result = new CalibrationCombinedResultRates([] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    then:
    !result.allIsdaCompliantDiscountCurves()
  }

  def "should return isda compliant true for discount curve with ISDA interpolators"() {
    setup:
    def ratesProvider = ImmutableRatesProvider.builder(LocalDate.now()).discountCurve(EUR, CurveSample.discountCurveIsdaInterpolators()).build()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])

    when:
    def result = new CalibrationCombinedResultRates([] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    then:
    result.allIsdaCompliantDiscountCurves()
  }

  def "should return isda compliant false for discount curves LINEAR and ISDA"() {
    setup:
    def ratesProvider = ImmutableRatesProvider.builder(LocalDate.now())
    .discountCurve(EUR, CurveSample.discountCurve())
    .discountCurve(USD, CurveSample.discountCurveIsdaInterpolators())
    .build()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])

    when:
    def result = new CalibrationCombinedResultRates([] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    then:
    !result.allIsdaCompliantDiscountCurves()
  }

  def "should return calibrated curve"() {
    setup:
    def ratesProvider = ratesProvider()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])
    def result = new CalibrationCombinedResultRates([] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    expect:
    result.calibratedCurve(CurveName.of("EUR EONIA")) != null
  }

  def "should calculate discount factors"() {
    setup:
    def ratesProvider = ratesProvider()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])

    def mockCurve = Mock(CurveDefinition)
    mockCurve.getName() >> CurveName.of("EUR EONIA")
    def mockNode = Mock(CurveNode)
    mockNode.date(ratesProvider.getValuationDate(), REF_DATA) >> LocalDate.of(2015, 1, 1)
    1 * mockCurve.getNodes() >> ImmutableList.of(mockNode, mockNode)
    def result = new CalibrationCombinedResultRates([mockCurve] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    expect:
    def discountFactors = result.calculateDiscountFactors(CurveName.of("EUR EONIA"), REF_DATA)
    discountFactors.size() == 1
    result.calculateDiscountFactors(CurveName.of("EUR 3M"), REF_DATA).isEmpty()
  }

  def "should calculate nodes in calibration"() {
    setup:
    def ratesProvider = ratesProvider()
    def ratesResult = new RatesCalibrationResult(ratesProvider, [])

    def mockCurve = Mock(CurveDefinition)
    mockCurve.getName() >> CurveName.of("EUR EONIA")
    1 * mockCurve.getNodes() >> ImmutableList.of(Mock(CurveNode), Mock(CurveNode))

    def result = new CalibrationCombinedResultRates([mockCurve] as Set<CurveDefinition>, ratesResult, () -> ratesResult, REF_DATA)

    expect:
    def resultingNodes = result.nodesUsedInCalibration(CurveName.of("EUR EONIA"))
    resultingNodes.size() == 2
    result.nodesUsedInCalibration(CurveName.of("EUR 3M")).isEmpty()
  }

  static ImmutableRatesProvider ratesProvider() {
    var metadata = DefaultCurveMetadata.builder()
    .curveName("EUR EONIA")
    .xValueType(ValueType.YEAR_FRACTION)
    .yValueType(ValueType.ZERO_RATE)
    .dayCount(DayCounts.ACT_365F)
    .parameterMetadata([TenorDateParameterMetadata.of(VAL_DT.plusYears(1), Tenor.TENOR_1Y)])
    .build()
    ImmutableRatesProvider
    .builder(VAL_DT)
    .discountCurve(EUR, ConstantNodalCurve.of(
    DefaultCurveMetadata.builder().curveName("EUR EONIA")
    .xValueType(ValueType.YEAR_FRACTION)
    .yValueType(ValueType.ZERO_RATE)
    .dayCount(DayCounts.ACT_365F)
    .build(),
    0,
    0.01)
    .withMetadata(metadata))
    .indexCurve(EUR_EURIBOR_3M, ConstantNodalCurve.of(
    DefaultCurveMetadata.builder().curveName("EUR 3M")
    .xValueType(ValueType.YEAR_FRACTION)
    .yValueType(ValueType.ZERO_RATE)
    .dayCount(DayCounts.ACT_365F)
    .build(),
    0,
    0.01))
    .build()
  }

  Curve buildInflationCurve(String curveName) {
    def metadata = curveMetadata(curveName, ValueType.MONTHS, ValueType.PRICE_INDEX)
    def underlyingCurve = InterpolatedNodalCurve.of(
    metadata, XVALUES_MONTHS, YVALUES, INTERPOLATOR, CurveExtrapolators.FLAT, CurveExtrapolators.FLAT)
    def shifted = underlyingCurve.withNode(-1, 0.1, ParameterMetadata.empty())
    return InflationNodalCurve.of(shifted, DoubleArray.copyOf(SEASONALITIES), ShiftType.RELATIVE)
  }

  NodalCurve buildCurve(String curveName) {
    def metadata = curveMetadata(curveName, ValueType.YEAR_FRACTION, ValueType.ZERO_RATE)
    return InterpolatedNodalCurve.of(
    metadata, XVALUES_SPREAD, YVALUES, INTERPOLATOR, CurveExtrapolators.FLAT, CurveExtrapolators.FLAT)
  }

  def curveMetadata(String curveName, ValueType xValueType, ValueType yValueType) {
    List<ParameterMetadata> parameters = [
      LabelDateParameterMetadata.of(LocalDate.of(2015, 1, 1), "NODE1"),
      LabelDateParameterMetadata.of(LocalDate.of(2015, 2, 1), "NODE2"),
      LabelDateParameterMetadata.of(LocalDate.of(2015, 3, 1), "NODE3")
    ]
    DefaultCurveMetadata.builder()
    .curveName(curveName)
    .xValueType(xValueType)
    .yValueType(yValueType)
    .dayCount(DayCounts.ACT_365F)
    .parameterMetadata(parameters)
    .build()
  }
}
