package com.solum.xplain.calibration.rates.set

import static OisCalibrationSubGroupsResolver.oisDiscountGroups
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis

import com.solum.xplain.calibration.value.CalculationShifts
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies
import java.time.LocalDate
import spock.lang.Specification

class CalibrationBundleResolverTest extends Specification {

  def "should resolve calibration bundle"() {
    setup:
    def stateDate = LocalDate.of(2023, 1, 1,)
    def shifts = Mock(CalculationShifts)
    def curves = [eurOis()]
    def resolver = new CalibrationBundleResolver(shifts, curves, { it -> it }, EUR, stateDate)

    def groupsProvider = oisDiscountGroups(IndexBasedDiscountCurrencies.getOf(EUR), Set.of(), Set.of())

    when:
    def result = resolver.resolve(groupsProvider)

    then:
    result.isRight()
    result.getOrNull().description == "OIS (Dual) EUR"
    result.getOrNull().currency == EUR
    result.getOrNull().calibrationDiscountingSubGroups.size() == 1
  }
}
