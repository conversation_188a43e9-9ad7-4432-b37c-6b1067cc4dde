package com.solum.xplain.calibration.rates

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA
import static com.opengamma.strata.basics.index.OvernightIndices.USD_FED_FUND
import static com.opengamma.strata.basics.index.PriceIndices.EU_AI_CPI
import static com.solum.xplain.calibration.rates.set.OisCalibrationSubGroupsResolver.oisDiscountGroups
import static com.solum.xplain.calibration.rates.set.SingleCalibrationSubGroupsResolver.ofSingleDiscounting
import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.getOf
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur6m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurUsdBasis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurUsdFx
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.gbpUsdFx
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.usd3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.usdOis
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_USD
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.LIBOR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static com.solum.xplain.core.settings.value.StrippingCurvePriority.TENOR_1M
import static com.solum.xplain.core.settings.value.StrippingCurvePriority.TENOR_3M
import static com.solum.xplain.core.settings.value.StrippingCurvePriority.TENOR_6M
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static io.atlassian.fugue.Eithers.filterLeft
import static io.atlassian.fugue.Eithers.filterRight
import static io.atlassian.fugue.Eithers.sequenceRight

import com.opengamma.strata.basics.currency.CurrencyPair
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import spock.lang.Specification
import spock.lang.Unroll

class CurveGroupCalibrationsResolverTest extends Specification {

  @Unroll
  def "should resolve expected sets #result when OIS #ccy #curves"() {
    setup:
    def productSettingsResolver = Mock(ProductSettingsResolver)
    productSettingsResolver.resolveXccyCcy(Set.of(EUR, USD)) >> Optional.of(EUR)
    productSettingsResolver.resolveFxCcy(CurrencyPair.of(EUR, USD)) >> EUR

    def indexResolver = Mock(DiscountingIndexResolver)
    indexResolver.resolveDiscountingIndex(USD_LIBOR_3M, Set.of()) >> right(USD_LIBOR_3M)

    def resolver = new CurveGroupCalibrationsResolver(
      productSettingsResolver,
      indexResolver, ccy.getCurrency())

    expect:
    def sets = resolver.splitCurvesIntoGroupResolvers(OIS, curves)
    sequenceRight(sets).isRight()
    sequenceRight(sets).getOrNull().sort(false) == result.sort()

    where:
    ccy            | curves                    | result
    DISCOUNT_USD   | [eurUsdBasis()]           | [oisDiscountGroups(getOf(USD), Set.of(EUR), Set.of(USD_LIBOR_3M, EUR_EURIBOR_3M))]
    LOCAL_CURRENCY | [eurUsdBasis(), eurOis()] | [oisDiscountGroups(getOf(EUR), Set.of(USD), Set.of(USD_LIBOR_3M, EUR_EURIBOR_3M, EUR_EONIA))]
    LOCAL_CURRENCY | [eurUsdFx(), eurOis()]    | [oisDiscountGroups(getOf(EUR), Set.of(USD), Set.of(EUR_EONIA))]
    DISCOUNT_USD   | []                        | []
  }

  @Unroll
  def "should resolve expected sets #result when OIS LOCAL fallback to LIBOR #curves"() {
    setup:
    def productSettingsResolver = Mock(ProductSettingsResolver)
    productSettingsResolver.resolveInflationCurvePriorities() >> [TENOR_3M]
    productSettingsResolver.resolveXccyCcy(Set.of(EUR, USD)) >> Optional.of(USD)
    productSettingsResolver.resolveFxCcy(CurrencyPair.of(EUR, USD)) >> EUR
    productSettingsResolver.resolveFxCurvePriorities() >> [TENOR_6M]

    def indexResolver = Mock(DiscountingIndexResolver)
    indexResolver.resolveDiscountingIndex(EUR_EURIBOR_6M, _) >> right(EUR_EURIBOR_6M)
    indexResolver.resolveDiscountingIndex(EUR_EURIBOR_3M, _) >> right(EUR_EURIBOR_3M)
    indexResolver.resolveDiscountingIndex(USD_LIBOR_3M, _) >> right(USD_LIBOR_3M)
    indexResolver.resolveDiscountingIndexFromCcy(EUR, [TENOR_3M], _) >> right(EUR_EURIBOR_6M)
    indexResolver.resolveDiscountingIndexFromCcy(EUR, [TENOR_6M], _) >> right(USD_LIBOR_3M)

    def resolver = new CurveGroupCalibrationsResolver(
      productSettingsResolver,
      indexResolver, null)

    expect:
    def sets = resolver.splitCurvesIntoGroupResolvers(OIS, curves)
    sequenceRight(sets).isRight()
    sequenceRight(sets).getOrNull().sort(false) == result.sort()

    where:
    curves                                       | result
    [eur6m(), eurInflation()]                    | [ofSingleDiscounting(EUR_EURIBOR_6M, Set.of(), Set.of(EUR_EURIBOR_6M, EUR_EURIBOR_3M, EU_AI_CPI))]
    [eur6m(), eurInflation(), usdOis(), usd3m()] | [
      oisDiscountGroups(getOf(USD), Set.of(), Set.of(USD_FED_FUND, USD_LIBOR_3M)),
      ofSingleDiscounting(EUR_EURIBOR_6M, Set.of(), Set.of(EUR_EURIBOR_6M, EUR_EURIBOR_3M, EU_AI_CPI))
    ]
    [eur3m(), usd3m(), eurUsdBasis()]            | [
      ofSingleDiscounting(EUR_EURIBOR_3M, Set.of(), Set.of(EUR_EURIBOR_3M)),
      ofSingleDiscounting(USD_LIBOR_3M, Set.of(EUR), Set.of(USD_LIBOR_3M, EUR_EURIBOR_3M))
    ]
  }

  def "should resolve expected sets when LIBOR"() {
    setup:
    def productSettingsResolver = Mock(ProductSettingsResolver)
    1 * productSettingsResolver.resolveInflationCurvePriorities() >> [TENOR_3M]
    1 * productSettingsResolver.resolveFxCcy(CurrencyPair.of(EUR, USD)) >> EUR
    1 * productSettingsResolver.resolveFxCcy(CurrencyPair.of(GBP, USD)) >> GBP
    2 * productSettingsResolver.resolveFxCurvePriorities() >> [TENOR_1M]

    def curves = [eur3m(), usd3m(), eurUsdFx(), gbpUsdFx(), eurInflation()]
    def indexResolver = Mock(DiscountingIndexResolver)
    1 * indexResolver.resolveDiscountingIndex(USD_LIBOR_3M, Set.of(EUR_EURIBOR_3M, USD_LIBOR_3M, EU_AI_CPI)) >> right(USD_LIBOR_3M)
    1 * indexResolver.resolveDiscountingIndex(EUR_EURIBOR_3M, Set.of(EUR_EURIBOR_3M, USD_LIBOR_3M, EU_AI_CPI)) >> left(OBJECT_NOT_FOUND.entity("Index not found"))
    1 * indexResolver.resolveDiscountingIndexFromCcy(EUR, [TENOR_3M], Set.of(EUR_EURIBOR_3M, USD_LIBOR_3M, EU_AI_CPI)) >> right(EUR_EURIBOR_3M)
    1 * indexResolver.resolveDiscountingIndexFromCcy(EUR, [TENOR_1M], Set.of(EUR_EURIBOR_3M, USD_LIBOR_3M, EU_AI_CPI)) >> left(OBJECT_NOT_FOUND.entity("Fx not found"))
    1 * indexResolver.resolveDiscountingIndexFromCcy(GBP, [TENOR_1M], Set.of(EUR_EURIBOR_3M, USD_LIBOR_3M, EU_AI_CPI)) >> right(USD_LIBOR_3M)

    def resolver = new CurveGroupCalibrationsResolver(productSettingsResolver, indexResolver, null)

    when:
    def sets = resolver.splitCurvesIntoGroupResolvers(LIBOR, curves)
    def expectedLefts = filterLeft(sets).toList()
    def expectedRights = filterRight(sets).toList().sort { it.calibrationCurrency() }

    then:
    expectedLefts.size() == 2
    expectedLefts[0] == OBJECT_NOT_FOUND.entity("Index not found")
    expectedLefts[1] == OBJECT_NOT_FOUND.entity("Fx not found")

    expectedRights.size() == 2
    expectedRights[0] == ofSingleDiscounting(EUR_EURIBOR_3M, Set.of(), Set.of(EU_AI_CPI))
    expectedRights[1] == ofSingleDiscounting(USD_LIBOR_3M, Set.of(GBP), Set.of(USD_LIBOR_3M))
  }
}
