package com.solum.xplain.calibration.rates.set

import static OisCalibrationSubGroupsResolver.oisDiscountGroups

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies
import spock.lang.Specification

class OisCalibrationSubGroupsResolverTest extends Specification {

  def "should get correct explicit currencies resolver description"() {
    setup:
    def resolver = oisDiscountGroups(IndexBasedDiscountCurrencies.getOf(Currency.EUR), Set.of(), Set.of())

    when:
    def result = resolver.description()

    then:
    result == "OIS (Dual) EUR"
  }
}
