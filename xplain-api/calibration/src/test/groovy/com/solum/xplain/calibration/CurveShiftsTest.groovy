package com.solum.xplain.calibration

import com.solum.xplain.calibration.value.CurveShifts
import com.solum.xplain.calibration.value.NodeShift
import spock.lang.Specification
import spock.lang.Unroll

class CurveShiftsTest extends Specification {

  @Unroll
  "should return #result if curveId is #curveId"() {
    setup:
    def shifts = new CurveShifts(curveId, [])

    expect:
    shifts.isForCurve("curveId") == result

    where:
    curveId   | result
    "other"   | false
    "curveId" | true
  }

  @Unroll
  "should return result #result when searching for node #nodeId"() {
    setup:
    def shift = Mock(NodeShift)
    shift.nodeIdIs("nodeId") >> true
    shift.resolvedAdditionalSpread() >> 1
    def shifts = new CurveShifts("", [shift])
    expect:
    shifts.additionalSpread().applyAsDouble(nodeId) == result

    where:
    nodeId   | result
    "nodeId" | 1d
    "other"  | 0d
  }
}
