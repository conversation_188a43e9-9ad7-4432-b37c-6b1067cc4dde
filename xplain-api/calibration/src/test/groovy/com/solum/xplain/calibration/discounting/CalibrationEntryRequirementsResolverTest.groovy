package com.solum.xplain.calibration.discounting

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import spock.lang.Specification

class CalibrationEntryRequirementsResolverTest extends Specification {

  def "should resolve required available indices for calibration entry"() {
    setup:
    def entry = Mock(CalibrationEntry)
    entry.curveId() >> "explicit"
    entry.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_1M, IborIndices.EUR_EURIBOR_3M)
    entry.resolvedDiscountCurrency >> Currency.EUR
    entry.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry2 = Mock(CalibrationEntry)
    entry2.index() >> Optional.of(IborIndices.EUR_EURIBOR_3M)
    entry2.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_6M)
    entry2.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry3 = Mock(CalibrationEntry)
    entry3.index() >> Optional.of(IborIndices.EUR_EURIBOR_6M)
    entry3.requiredIndices() >> List.of()
    entry3.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry4 = Mock(CalibrationEntry)
    entry4.index() >> Optional.of(IborIndices.EUR_EURIBOR_12M)
    entry4.requiredIndices() >> List.of()
    entry4.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry5 = Mock(CalibrationEntry)
    entry5.index() >> Optional.of(IborIndices.EUR_EURIBOR_1M)
    entry5.requiredIndices() >> List.of()
    entry5.matchesClearingHouse(ClearingHouse.NONE) >> true


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(), [entry2, entry3, entry4, entry5])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 3
    entries.containsAll([entry5, entry3, entry2])
  }

  def "should resolve required available indices for calibration entry with inflation curve"() {
    setup:
    def entry = Mock(CalibrationEntry)
    entry.curveId() >> "explicit"
    entry.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_1M, IborIndices.EUR_EURIBOR_3M)
    entry.resolvedDiscountCurrency >> Currency.EUR
    entry.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry2 = Mock(CalibrationEntry)
    entry2.index() >> Optional.of(IborIndices.EUR_EURIBOR_3M)
    entry2.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_6M)
    entry2.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry3 = Mock(CalibrationEntry)
    entry3.index() >> Optional.of(IborIndices.EUR_EURIBOR_6M)
    entry3.requiredIndices() >> List.of()
    entry3.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry4 = Mock(CalibrationEntry)
    entry4.index() >> Optional.of(IborIndices.EUR_EURIBOR_12M)
    entry4.requiredIndices() >> List.of()
    entry4.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry5 = Mock(CalibrationEntry)
    entry5.isCurveType(CurveType.INFLATION_INDEX) >> true
    entry5.projectionCurrency() >> Optional.of(Currency.EUR)
    entry5.index() >> Optional.of(PriceIndices.EU_AI_CPI)
    entry5.requiredIndices() >> List.of()
    entry5.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry6 = Mock(CalibrationEntry)
    entry6.isCurveType(CurveType.INFLATION_INDEX) >> true
    entry6.projectionCurrency() >> Optional.of(Currency.USD)
    entry6.index() >> Optional.of(PriceIndices.US_CPI_U)
    entry6.requiredIndices() >> List.of()
    entry6.matchesClearingHouse(ClearingHouse.NONE) >> true


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(PriceIndices.US_CPI_U, PriceIndices.EU_AI_CPI), [entry2, entry3, entry4, entry5, entry6])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 3
    entries.containsAll([entry2, entry3, entry5])
  }

  def "should resolve required available indices for calibration entry with inflation curve with clearing house"() {
    setup:
    def entry = Mock(CalibrationEntry)
    entry.curveId() >> "explicit"
    entry.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_1M, IborIndices.EUR_EURIBOR_3M)
    entry.resolvedDiscountCurrency >> Currency.EUR
    entry.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry2 = Mock(CalibrationEntry)
    entry2.index() >> Optional.of(IborIndices.EUR_EURIBOR_3M)
    entry2.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_6M)
    entry2.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry3 = Mock(CalibrationEntry)
    entry3.index() >> Optional.of(IborIndices.EUR_EURIBOR_6M)
    entry3.requiredIndices() >> List.of()
    entry3.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry4 = Mock(CalibrationEntry)
    entry4.index() >> Optional.of(IborIndices.EUR_EURIBOR_12M)
    entry4.requiredIndices() >> List.of()
    entry4.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry5 = Mock(CalibrationEntry)
    entry5.isCurveType(CurveType.INFLATION_INDEX) >> true
    entry5.projectionCurrency() >> Optional.of(Currency.EUR)
    entry5.index() >> Optional.of(PriceIndices.EU_AI_CPI)
    entry5.requiredIndices() >> List.of()
    entry5.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry6 = Mock(CalibrationEntry)
    entry6.isCurveType(CurveType.INFLATION_INDEX) >> true
    entry6.projectionCurrency() >> Optional.of(Currency.USD)
    entry6.index() >> Optional.of(PriceIndices.US_CPI_U)
    entry6.requiredIndices() >> List.of()
    entry6.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry7 = Mock(CalibrationEntry)
    entry7.isCurveType(CurveType.INFLATION_INDEX) >> true
    entry7.projectionCurrency() >> Optional.of(Currency.EUR)
    entry7.index() >> Optional.of(PriceIndices.EU_AI_CPI)
    entry7.requiredIndices() >> List.of()
    entry7.matchesClearingHouse(ClearingHouse.LCH) >> true


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(PriceIndices.US_CPI_U, PriceIndices.EU_AI_CPI), [entry2, entry3, entry4, entry5, entry6, entry7])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 3
    entries.containsAll([entry2, entry3, entry5])

    when:
    resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(PriceIndices.US_CPI_U, PriceIndices.EU_AI_CPI), [entry2, entry3, entry4, entry5, entry6, entry7])
    def lchEntries = resolver.requiredEntries()

    then:
    lchEntries.size() == 3
    lchEntries.containsAll([entry2, entry3, entry5])
  }

  def "should return empty list when no entries available"() {
    setup:
    def entry = Mock(CalibrationEntry)

    entry.curveId() >> "id"
    entry.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_1M)


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(), [])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 0
  }

  def "should not return self"() {
    setup:
    def entry = Mock(CalibrationEntry)

    entry.curveId() >> "id"
    entry.index() >> Optional.of(IborIndices.EUR_EURIBOR_1M)
    entry.requiredIndices() >> List.of(IborIndices.EUR_EURIBOR_1M)


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(), [entry])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 0
  }

  def "should return correctly resolved required index entries"() {
    setup:
    def entry = Mock(CalibrationEntry)

    entry.curveId() >> "id"
    entry.index() >> Optional.of(OvernightIndices.USD_SOFR)
    entry.requiredIndices() >> List.of()
    entry.resolvedDiscountCurrency >> Currency.USD
    entry.matchesClearingHouse(ClearingHouse.NONE) >> true

    def entry2 = Mock(CalibrationEntry)
    entry2.index() >> Optional.of(IborIndices.USD_LIBOR_3M)
    entry2.requiredIndices() >> List.of(IborIndices.USD_LIBOR_3M, IborIndices.USD_LIBOR_6M)
    entry2.matchesClearingHouse(ClearingHouse.NONE) >> true


    def entry3 = Mock(CalibrationEntry)
    entry3.index() >> Optional.of(IborIndices.USD_LIBOR_6M)
    entry3.requiredIndices() >> List.of(IborIndices.USD_LIBOR_3M, IborIndices.USD_LIBOR_6M)
    entry3.matchesClearingHouse(ClearingHouse.NONE) >> true


    def resolver = CalibrationEntryRequirementsResolver.resolver(entry, ClearingHouse.NONE, Set.of(IborIndices.USD_LIBOR_3M), [entry, entry2, entry3])
    when:
    def entries = resolver.requiredEntries()

    then:
    entries.size() == 2
    entries.containsAll([entry2, entry3])
  }
}
