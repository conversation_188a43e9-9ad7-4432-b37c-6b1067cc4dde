package com.solum.xplain.calibration.curve

import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static java.util.Collections.emptyList
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calibration.curve.charts.ChartDateType
import com.solum.xplain.calibration.helpers.MockMvcConfiguration
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@WebMvcTest(controllers = [CalibrationCurvesController])
@MockMvcConfiguration
class CalibrationCurvesControllerTest extends Specification {

  @SpringBean
  private CurveChartsControllerService service = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper

  @WithMockUser
  def "should get curve chart points"() {
    setup:
    def groupId = "groupId"
    def curveId = "1"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/$curveId/chart-points")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getCurvePoints(groupId, curveId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(emptyList())

    and:
    results.response.status == 200
  }


  def "should get all curves calibration results csv"() {
    setup:
    def groupId = "groupId"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/results/csv")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getAllCurvesPointsCsv(groupId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    and:
    results.response.status == 200
  }

  def "should get single curve calibration results csv"() {
    setup:
    def groupId = "groupId"
    def curveId = "EUR 3M"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/$curveId/results/csv")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getCurvesPointsCsv(groupId, curveId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    and:
    results.response.status == 200
  }

  def "should get all curves chart points csv"() {
    setup:
    def groupId = "groupId"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/all/chart-points/csv")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getAllCurvesChartPointsCsv(groupId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    and:
    results.response.status == 200
  }

  def "should get all curves discount factors csv"() {
    setup:
    def groupId = "groupId"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/all/discount-factors/csv")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getAllCurvesDiscountPointsCsv(groupId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    and:
    results.response.status == 200
  }

  def "should get all inflation curves market rates csv"() {
    setup:
    def groupId = "groupId"
    def valuationDate = "2016-01-01"
    def expectedCalibrationOptions = new CalibratedCurveChartOptions(parse(valuationDate), CalculationDiscountingType.DISCOUNT_GBP, CalculationStrippingType.LIBOR, ChartDateType.ACTUAL_DATE)

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/curves/all/market-rates/csv")
      .param("valuationDate", valuationDate)
      .param("discountingType", CalculationDiscountingType.DISCOUNT_GBP.name())
      .param("calibrationStrippingType", CalculationStrippingType.LIBOR.name())
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getAllCurvesMarketPointsCsv(groupId, CurveMarketSample.MARKET_STATE_FORM, expectedCalibrationOptions) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    and:
    results.response.status == 200
  }
}
