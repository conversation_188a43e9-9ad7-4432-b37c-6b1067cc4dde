package com.solum.xplain.calibration.rates

import static com.opengamma.strata.basics.currency.Currency.CAD
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurUsdBasis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurUsdOisOis
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CAD_CDOR_3M_EUR_EURIBOR_3M

import com.opengamma.strata.basics.currency.CurrencyPair
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import io.atlassian.fugue.Either
import spock.lang.Specification
import spock.lang.Unroll

class CurveDiscountableIndexResolverTest extends Specification {

  @Unroll
  def "should resolve xccy index #result when #curve"() {
    setup:
    def dscIndexResolver = Mock(DiscountingIndexResolver)
    1 * dscIndexResolver.resolveDiscountingIndexFromCcy(EUR, [], _) >> Either.right(result)

    def productSettingsResolver = Mock(ProductSettingsResolver)
    productSettingsResolver.resolveFxCcy(CurrencyPair.of(EUR, USD)) >> EUR
    productSettingsResolver.resolveFxCcy(CurrencyPair.of(EUR, CAD)) >> EUR
    1 * productSettingsResolver.resolveFxCurvePriorities() >> []

    def curveDiscountableIndexResolver = new CurveDiscountableIndexResolver(productSettingsResolver, dscIndexResolver)

    def item = Mock(DiscountableItem)
    1 * item.originalItem() >> curve

    expect:
    curveDiscountableIndexResolver.resolveIndex(item, Set.of()).getOrNull() == result

    where:
    curve                                                                              | result
    eurUsdBasis()                                                                      | USD_LIBOR_3M
    eurUsdOisOis()                                                                     | USD_SOFR
    new CurveBuilder()
      .name("EUR/CAD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, CAD_CDOR_3M_EUR_EURIBOR_3M.name)])
      .build()                                                                       | EUR_EURIBOR_3M
  }
}
