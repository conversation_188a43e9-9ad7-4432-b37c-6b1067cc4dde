package com.solum.xplain.calibration.trades


import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static java.time.LocalDate.now

import com.solum.xplain.calibration.value.CalibrationOptionsForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import spock.lang.Specification

class GenerateCalibrationPortfolioFormTest extends Specification {

  def "should correctly generate portfolio form"() {
    setup:
    def form = new GenerateCalibrationPortfolioForm(
      new CalibrationOptionsForm(
      "mId",
      "ccId",
      "OVERLAY",
      STATE_DATE.getActualDate(),
      now(),
      now(),
      null),
      "EXTERNAL_ID",
      "NAME",
      "companyId",
      "entityId",
      new AllowedTeamsForm(true, [])
      )

    when:
    def result = form.toPortfolioForm()

    then:
    result.externalPortfolioId == "EXTERNAL_ID"
    result.name == "NAME"
    result.allowedTeamsForm == new AllowedTeamsForm(true, [])
  }
}
