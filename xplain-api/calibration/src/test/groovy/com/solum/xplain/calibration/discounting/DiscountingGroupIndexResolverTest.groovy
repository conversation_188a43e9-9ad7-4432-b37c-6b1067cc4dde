package com.solum.xplain.calibration.discounting

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_12M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT

import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.settings.value.StrippingCurvePriority
import spock.lang.Specification

class DiscountingGroupIndexResolverTest extends Specification {
  static FX_PRIORITY = [StrippingCurvePriority.TENOR_3M, StrippingCurvePriority.TENOR_6M, StrippingCurvePriority.OIS]
  static RESOLVER = new DiscountingIndexResolver(OisConfigurations.of(VAL_DT))

  def "should fail to resolve dsc index when missing curve"() {
    when:
    def res = RESOLVER.resolveDiscountingIndex(EUR_EURIBOR_3M, Set.of(USD_LIBOR_3M))

    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting: EUR-EURIBOR-3M"
  }

  def "should correctly resolve SWAP ccy different index"() {
    when:
    def res = RESOLVER.resolveDiscountingIndex(EUR_EURIBOR_3M, Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve fxfwd index"() {
    when:
    def res = RESOLVER.resolveDiscountingIndexFromCcy(EUR, FX_PRIORITY, Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }


  def "should correctly resolve fxfwd index no index no xccy no USD then error"() {
    when:
    def res = RESOLVER.resolveDiscountingIndexFromCcy(EUR, FX_PRIORITY, Set.of(GBP_LIBOR_12M))

    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting ccy EUR. Priorities [TENOR_3M, TENOR_6M, OIS]"
  }
}
