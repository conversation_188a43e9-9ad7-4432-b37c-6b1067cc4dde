package com.solum.xplain.calibration.rates.curvepoint

import static com.opengamma.strata.basics.ReferenceData.standard
import static com.opengamma.strata.product.swap.PriceIndexCalculationMethod.INTERPOLATED
import static com.opengamma.strata.product.swap.PriceIndexCalculationMethod.MONTHLY
import static com.solum.xplain.calibration.CalibrationSetSample.strataCurveGroupAdditionalInflation
import static com.solum.xplain.calibration.CalibrationSetSample.strataCurveGroupInflation
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData

import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.pricer.curve.RatesCurveCalibrator
import java.time.LocalDate
import spock.lang.Specification

class InflationChartPointCalculatorTest extends Specification {
  LocalDate VALUATION_DATE = LocalDate.of(2022, 1, 1)

  def "should calculate price index MONTHLY node values"() {
    setup:
    def valueCalculator = Mock(CurveValueCalculator)
    def calculation = new InflationCurvePointCalculator(VALUATION_DATE, MONTHLY, valueCalculator)

    when:
    def values = calculation.calculate(LocalDate.of(2022, 3, 1))

    then:
    valueCalculator.calculate(2.0) >> 1.0

    and:
    values.xValue() == 2.0d
    values.yValue() == 1.0d

    when:
    values = calculation.calculate(LocalDate.of(2023, 6, 20))

    then:
    valueCalculator.calculate(17.0) >> 15.0

    and:
    values.xValue() == 17.0d
    values.yValue() == 15.0d
  }

  def "should calculate price index INTERPOLATED node values"() {
    setup:
    def valueCalculator = Mock(CurveValueCalculator)
    def calculation = new InflationCurvePointCalculator(VALUATION_DATE, INTERPOLATED, valueCalculator)

    when:
    def values = calculation.calculate(LocalDate.of(2022, 3, 15))

    then:
    valueCalculator.calculate(2.0) >> 1.0
    valueCalculator.calculate(3.0) >> 2.0

    and:
    values.xValue() == 2.0d
    values.yValue() == 1.4516129032258065d

    when:
    values = calculation.calculate(LocalDate.of(2022, 3, 20))

    then:
    valueCalculator.calculate(2.0) >> 1.0
    valueCalculator.calculate(3.0) >> 2.0

    and:
    values.xValue() == 2.0d
    values.yValue() == 1.6129032258064515d
  }
}
