package com.solum.xplain.core.calendar.repository

import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.opengamma.strata.basics.date.HolidayCalendarId
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar
import com.solum.xplain.core.calendar.event.CustomHolidayCalendarUpdatedEvent
import com.solum.xplain.core.calendar.form.CustomHolidayCalendarForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.VersionedEntity
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.datagrid.ClusterEventPublisher
import jakarta.annotation.Resource
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.Month
import java.time.temporal.ChronoUnit
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CustomHolidayCalendarRepositoryTest extends IntegrationSpecification {
  private static final LocalDate VIP_DAY = LocalDate.of(2023, Month.SEPTEMBER, 29)
  private static final LocalDate CHRISTMAS_DAY = LocalDate.of(2023, Month.DECEMBER, 25)
  private static final LocalDate JUNETEENTH_DAY = LocalDate.of(2023, Month.JUNE, 19)
  private static final LocalDate INDEPENDENCE_DAY = LocalDate.of(2023, Month.JULY, 4)
  private static final LocalDate WORKING_WEDNESDAY =LocalDate.of(2023, Month.NOVEMBER, 22)
  private static final Set<DayOfWeek> WEEKENDS = [DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY]
  private static final Set<LocalDate> HOLIDAYS = [VIP_DAY, CHRISTMAS_DAY, JUNETEENTH_DAY, INDEPENDENCE_DAY]
  private static final Set<LocalDate> WORKING_DAYS = [WORKING_WEDNESDAY]

  @Resource
  MongoOperations operations

  @Resource
  CustomHolidayCalendarRepository customHolidayCalendarRepository

  @SpringBean
  ClusterEventPublisher eventPublisher = Mock(ClusterEventPublisher)

  XplainPrincipal creator = UserBuilder.user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.clearContext()
    operations.remove(new Query(), CustomHolidayCalendar)
  }

  def "should read calendar"() {
    given:
    def inserted = operations.insert(CustomHolidayCalendar.newOf(HolidayCalendarIds.USNY).tap {
      setHolidays(HOLIDAYS)
      setWeekendDays(WEEKENDS)
      setWorkingDays(WORKING_DAYS)
    })

    when:
    def found = customHolidayCalendarRepository.findActiveByCalendarId(HolidayCalendarIds.USNY, BitemporalDate.newOfNow())

    then:
    found.isPresent()
    found.ifPresent {
      assert it.entityId == "USNY"
      assert it.weekendDays == WEEKENDS
      assert it.holidays == HOLIDAYS
      assert it.workingDays == WORKING_DAYS
      assert it.id == inserted.id
      assert it.validFrom == inserted.validFrom
      assert it.recordDate == inserted.recordDate.truncatedTo(ChronoUnit.MILLIS)
      assert it.modifiedAt == inserted.modifiedAt.truncatedTo(ChronoUnit.MILLIS)
      assert it.modifiedBy == inserted.modifiedBy
      assert it.state == State.ACTIVE
    }
  }

  def "should ignore archived calendar"() {
    given:
    operations.insert(CustomHolidayCalendar.newOf(HolidayCalendarIds.USNY).tap {
      setHolidays(holidays)
      setWeekendDays(WEEKENDS)
      setWorkingDays(WORKING_DAYS)
      setState(State.ARCHIVED)
    })

    when:
    def found = customHolidayCalendarRepository.findActiveByCalendarId(HolidayCalendarIds.USNY, BitemporalDate.newOfNow())

    then:
    !found.isPresent()
  }

  def "should read all active calendars"() {
    given:
    operations.insertAll([
      CustomHolidayCalendar.newOf(HolidayCalendarIds.USNY).tap {
        setHolidays(HOLIDAYS)
        setWeekendDays(WEEKENDS)
        setWorkingDays(WORKING_DAYS)
      },
      CustomHolidayCalendar.newOf(HolidayCalendarIds.GBLO).tap {
        setHolidays(new HashSet<>(HOLIDAYS).tap { remove(JUNETEENTH_DAY) })
        setWeekendDays(WEEKENDS)
        setWorkingDays(WORKING_DAYS)
      },
      CustomHolidayCalendar.newOf(HolidayCalendarIds.AUSY).tap {
        setHolidays(HOLIDAYS)
        setWeekendDays(WEEKENDS)
        setWorkingDays(WORKING_DAYS)
        setState(State.ARCHIVED)
      }
    ])

    when:
    def found = customHolidayCalendarRepository.findAllActive(BitemporalDate.newOfNow())

    then:
    found.size() == 2
    found.stream().anyMatch {
      it.getHolidayCalendarId() == HolidayCalendarIds.USNY
    }
    found.stream().anyMatch {
      it.getHolidayCalendarId() == HolidayCalendarIds.GBLO
    }
    found.stream().noneMatch {
      it.getHolidayCalendarId() == HolidayCalendarIds.AUSY
    }
  }

  def "should insert new root calendar"() {
    given:
    def form = CustomHolidayCalendarForm.builder()
      .holidays(HOLIDAYS)
      .weekendDays(WEEKENDS)
      .workingDays(WORKING_DAYS)
      .versionForm(NewVersionFormV2.builder()
      .comment("new version")
      .validFrom(LocalDate.EPOCH)
      .stateDate(LocalDate.now())
      .futureVersionsAction(FutureVersionsAction.KEEP)
      .build()).build()

    when:
    def entityId = customHolidayCalendarRepository.createCalendar(HolidayCalendarIds.FRPA, form)

    then:
    entityId.id == "FRPA"
    def records = findAll(HolidayCalendarIds.FRPA)
    records.size() == 1

    def inserted = records[0]
    inserted.recordDate != null
    inserted.modifiedAt != null
    inserted.modifiedBy != null
    inserted.modifiedBy.name == creator.name
    inserted.state == State.ACTIVE
    inserted.holidays == form.holidays
    inserted.weekendDays == form.weekendDays
    inserted.workingDays == form.workingDays
    inserted.validFrom == form.versionForm.validFrom
    inserted.comment == form.versionForm.comment
    inserted.entityId == entityId.id

    and:
    1 * eventPublisher.publishEvent(_ as CustomHolidayCalendarUpdatedEvent) >> { CustomHolidayCalendarUpdatedEvent event ->
      assert event.calendarId == HolidayCalendarIds.FRPA
    }
  }

  def "should update root calendar with new minor version"() {
    given:
    def old = operations.insert(CustomHolidayCalendar.newOf(HolidayCalendarIds.DEFR).tap {
      setHolidays(HOLIDAYS)
      setWeekendDays(WEEKENDS)
      setWorkingDays([] as Set<LocalDate>)
    })
    def form = CustomHolidayCalendarForm.builder()
      .holidays(HOLIDAYS)
      .weekendDays(WEEKENDS)
      .workingDays(WORKING_DAYS)
      .versionForm(NewVersionFormV2.builder()
      .comment("new version")
      .validFrom(LocalDate.EPOCH)
      .stateDate(LocalDate.now())
      .futureVersionsAction(FutureVersionsAction.KEEP)
      .build()).build()

    when:
    def entityId = customHolidayCalendarRepository.updateCalendar(old, form)

    then:
    entityId.id == "DEFR"
    def records = findAll(HolidayCalendarIds.DEFR)
    records.size() == 2

    def inserted = records[0]
    def existing = records[1]
    inserted.recordDate > existing.recordDate
    inserted.validFrom == existing.validFrom
    inserted.modifiedAt > existing.modifiedAt
    inserted.modifiedBy != null
    inserted.modifiedBy.name == creator.name
    inserted.state == State.ACTIVE
    inserted.holidays == form.holidays
    inserted.weekendDays == form.weekendDays
    inserted.workingDays == form.workingDays
    inserted.validFrom == form.versionForm.validFrom
    inserted.comment == form.versionForm.comment
    inserted.entityId == entityId.id
    existing.valueEquals(old)

    and:
    1 * eventPublisher.publishEvent(_ as CustomHolidayCalendarUpdatedEvent) >> { CustomHolidayCalendarUpdatedEvent event ->
      assert event.calendarId == HolidayCalendarIds.DEFR
    }
  }

  def "should not update root calendar with new minor version if content hasn't changed"() {
    given:
    def old = operations.insert(CustomHolidayCalendar.newOf(HolidayCalendarIds.DEFR).tap {
      setHolidays(HOLIDAYS)
      setWeekendDays(WEEKENDS)
      setWorkingDays(WORKING_DAYS)
    })
    def form = CustomHolidayCalendarForm.builder()
      .holidays(HOLIDAYS)
      .weekendDays(WEEKENDS)
      .workingDays(WORKING_DAYS)
      .versionForm(NewVersionFormV2.builder()
      .comment("new version")
      .validFrom(LocalDate.EPOCH)
      .stateDate(LocalDate.now())
      .futureVersionsAction(FutureVersionsAction.KEEP)
      .build()).build()

    when:
    def entityId = customHolidayCalendarRepository.updateCalendar(old, form)

    then:
    entityId.id == "DEFR"
    def records = findAll(HolidayCalendarIds.DEFR)
    records.size() == 1

    def existing = records[0]
    existing.validFrom == old.validFrom
    existing.recordDate == old.recordDate.truncatedTo(ChronoUnit.MILLIS)
    existing.modifiedAt == old.modifiedAt.truncatedTo(ChronoUnit.MILLIS)
    existing.modifiedBy == old.modifiedBy
    existing.valueEquals(old)
    existing.comment != form.versionForm.comment

    and: // Event will still be published even if there was actually no change
    1 * eventPublisher.publishEvent(_ as CustomHolidayCalendarUpdatedEvent) >> { CustomHolidayCalendarUpdatedEvent event ->
      assert event.calendarId == HolidayCalendarIds.DEFR
    }
  }

  private List<CustomHolidayCalendar> findAll(HolidayCalendarId calendarId) {
    operations
      .query(CustomHolidayCalendar).matching(query(where(VersionedEntity.Fields.entityId).is(calendarId.name))
      .with(Sort.by(DESC, VersionedEntity.Fields.validFrom, VersionedEntity.Fields.recordDate)))
      .all()
  }
}
