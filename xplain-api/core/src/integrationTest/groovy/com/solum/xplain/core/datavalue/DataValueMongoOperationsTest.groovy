package com.solum.xplain.core.datavalue

import static org.springframework.data.mongodb.core.query.Criteria.where

import com.solum.xplain.core.common.value.ArchiveForm
import com.solum.xplain.core.datavalue.csv.DataValueForUpdate
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.AuditorAware
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class DataValueMongoOperationsTest extends IntegrationSpecification {

  static LocalDate VALUE_DATE = LocalDate.ofEpochDay(0)

  @Resource
  MongoOperations operations
  @Resource
  AuditorAware<AuditUser> auditUserAuditorAware

  DataValueMongoOperations dataOperations
  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
    dataOperations = new DataValueMongoOperations(TestDataValueHolder, auditUserAuditorAware, operations)
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), TestDataValueHolder)
  }

  def "should insert new value holder"() {
    when:
    def entityId = dataOperations.insert(dataValue())

    then:
    def loaded = operations.findById(entityId.id, TestDataValueHolder)
    with(loaded) {
      id == entityId.id
      date == LocalDate.ofEpochDay(0)
      archivedAt == null
      archivedBy == null
      values.size() == 1
      values[0].value == 1d
      values[0].recordDate != null
      values[0].modifiedBy == AuditUser.of(user)
    }
  }

  @Unroll
  def "should get whether has changes after #timestamp"() {
    setup:
    operations.insertAll([
      new TestDataValueHolder(
      field: "filtered",
      date: VALUE_DATE,
      values: [new TestDataValue(value: 1d, recordDate: VALUE_DATE.atStartOfDay())]
      ),
      new TestDataValueHolder(
      field: "filtered",
      date: VALUE_DATE,
      archivedAt: VALUE_DATE.atStartOfDay().plusHours(2),
      values: [new TestDataValue(value: 1d, recordDate: VALUE_DATE.atStartOfDay())]
      ),
      new TestDataValueHolder(
      field: "notFiltered",
      date: VALUE_DATE,
      values: [new TestDataValue(value: 1d, recordDate: VALUE_DATE.atStartOfDay().plusHours(4))]
      ),
    ])

    expect:
    dataOperations.hasChangesAfter(where("field").is("filtered"), timestamp) == hasChanges

    where:
    timestamp                               | hasChanges
    VALUE_DATE.atStartOfDay().minusHours(1) | true
    VALUE_DATE.atStartOfDay().plusHours(1)  | true
    VALUE_DATE.atStartOfDay().plusHours(2)  | false
  }

  def "should return entity"() {
    setup:
    def valueHolder = operations.save(dataValue())

    when:
    def loaded = dataOperations.entity(valueHolder.id)

    then:
    loaded.isRight()
    with(loaded.getOrNull() as TestDataValueHolder) {
      id == valueHolder.id
      date == VALUE_DATE
      archivedAt == null
      archivedBy == null
      values.size() == 1
      values[0].value == 1d
    }
  }

  def "should update value"() {
    setup:
    def valueHolder = operations.save(dataValue())

    when:
    dataOperations.updateValue(valueHolder.id, new TestDataValue(value: 2d))

    then:
    def loaded = dataOperations.entity(valueHolder.id)
    loaded.isRight()
    with(loaded.getOrNull() as TestDataValueHolder) {
      id == valueHolder.id
      date == VALUE_DATE
      archivedAt == null
      archivedBy == null
      values.size() == 2
      values[0].value == 1d
      values[1].value == 2d
      values[1].modifiedBy == AuditUser.of(user)
      values[1].recordDate != null
    }
  }

  def "should not update if value equals"() {
    setup:
    def valueHolder = operations.save(dataValue())

    when:
    dataOperations.updateValue(valueHolder.id, new TestDataValue(value: 1d))

    then:
    def loaded = dataOperations.entity(valueHolder.id)
    loaded.isRight()
    with(loaded.getOrNull() as TestDataValueHolder) {
      values.size() == 1
    }
  }

  def "should archive value"() {
    setup:
    def valueHolder = operations.save(dataValue())

    when:
    dataOperations.archiveValue(valueHolder.id, new ArchiveForm("archived"))

    then:
    def loaded = operations.findById(valueHolder.id, TestDataValueHolder)
    loaded.archivedAt != null
    loaded.archivedBy == AuditUser.of(user)
    loaded.archivalComment == "archived"
  }

  def "should append entities from import"() {
    setup:
    def updateResolver = DataValueUpdateResolver.<TestDataValue, TestDataValueHolder> builder()
      .entitiesToAppend([dataValue()])
      .build()

    when:
    dataOperations.updateForImport(updateResolver)

    then:
    def loaded = operations.findAll(TestDataValueHolder)
    loaded.size() == 1
    with(loaded[0]) {
      id != null
      values.size() == 1
      values[0].recordDate != null
      values[0].modifiedBy == AuditUser.of(user)
    }
  }

  def "should replace entities from import"() {
    setup:
    def existingValue = dataValue()
    def newValue = new TestDataValue(value: 1d, otherValue: 2d)
    operations.insert(existingValue)
    def updateResolver = DataValueUpdateResolver.<TestDataValue, TestDataValueHolder> builder()
      .entitiesToReplace([new DataValueForUpdate<>(existingValue, newValue)])
      .build()

    when:
    dataOperations.updateForImport(updateResolver)

    then:
    def loaded = operations.findAll(TestDataValueHolder)
    loaded.size() == 1
    with(loaded[0]) {
      id != null
      values.size() == 2
      values[1].value == 1d
      values[1].otherValue == 2d
      values[1].recordDate != null
      values[1].modifiedBy == AuditUser.of(user)
    }
  }

  def "should archive entities from import"() {
    setup:
    def existingValue = dataValue()
    operations.insert(existingValue)
    def updateResolver = DataValueUpdateResolver.<TestDataValue, TestDataValueHolder> builder()
      .comment("COMMENT")
      .entitiesToArchive([existingValue])
      .build()

    when:
    dataOperations.updateForImport(updateResolver)

    then:
    def loaded = operations.findById(existingValue.id, TestDataValueHolder)
    loaded.archivedBy == AuditUser.of(user)
    loaded.archivedAt != null
    loaded.archivalComment == "COMMENT"
  }

  static dataValue() {
    new TestDataValueHolder(
      date: VALUE_DATE,
      values: [new TestDataValue(value: 1d)]
      )
  }
}
