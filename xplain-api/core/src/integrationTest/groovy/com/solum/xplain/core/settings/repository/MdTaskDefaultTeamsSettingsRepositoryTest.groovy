package com.solum.xplain.core.settings.repository

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.settings.entity.IpvTaskDefaultTeams
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams
import com.solum.xplain.core.settings.form.TaskDefaultTeamsForm
import com.solum.xplain.core.teams.Team
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamForm
import com.solum.xplain.core.teams.value.TeamNameView
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class MdTaskDefaultTeamsSettingsRepositoryTest extends IntegrationSpecification {
  @Resource
  MongoOperations operations

  @Resource
  TaskDefaultTeamsSettingsRepository repository

  @Resource
  TeamRepository teamRepository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), MdTaskDefaultTeams.class)
    operations.remove(new Query(), Team.class)
  }

  def "should save task default teams"() {

    setup:
    EntityId teamId = teamRepository.insert(new TeamForm( "test", "testextid")).orNull
    def team = operations.findById(teamId.id, Team.class)
    repository.saveTaskDefaultTeams(new TaskDefaultTeamsForm(
      team.id.toHexString(),
      team.id.toHexString(),
      ))
    expect:
    def loaded = operations.findAll(MdTaskDefaultTeams)
    loaded.size() == 1
    loaded[0].defaultApprovalTeam.id == team.id.toHexString()
    loaded[0].defaultApprovalTeam.name == team.name
    loaded[0].defaultResolutionTeam.id == team.id.toHexString()
    loaded[0].defaultResolutionTeam.name == team.name
  }

  def "should get task default teams"() {
    setup:
    operations.save(new MdTaskDefaultTeams(
      defaultApprovalTeam: new TeamNameView(id: "id", name: "name"),
      defaultResolutionTeam: new TeamNameView(id: "id", name: "name")
      ))
    expect:
    def loaded = repository.getTaskDefaultTeams()
    loaded.defaultResolutionTeam == new TeamNameView(id: "id", name: "name")
    loaded.defaultApprovalTeam == new TeamNameView(id: "id", name: "name")
    loaded.recordDate != null
  }

  def "should get task default teams view"() {
    setup:
    operations.save(new MdTaskDefaultTeams(
      defaultApprovalTeam: new TeamNameView(id: "id", name: "name"),
      defaultResolutionTeam: new TeamNameView(id: "id", name: "name")
      ))

    expect:
    def loaded = repository.getTaskDefaultTeamsView()
    loaded.defaultResolutionTeam == new TeamNameView(id: "id", name: "name")
    loaded.defaultApprovalTeam == new TeamNameView(id: "id", name: "name")
    loaded.recordDate != null
    loaded.modifiedAt != null
    loaded.modifiedBy == creator.name
  }

  def "should save IPV task default teams"() {

    setup:
    def team = operations.save(new Team(name: "test"))
    repository.saveIpvDefaultTeams(new TaskDefaultTeamsForm(
      team.id.toHexString(),
      team.id.toHexString(),
      ))
    expect:
    def loaded = operations.findAll(IpvTaskDefaultTeams)
    loaded.size() == 1
    loaded[0].defaultApprovalTeam.id == team.id.toHexString()
    loaded[0].defaultApprovalTeam.name == team.name
    loaded[0].defaultResolutionTeam.id == team.id.toHexString()
    loaded[0].defaultResolutionTeam.name == team.name
  }

  def "should get IPV task default teams"() {
    setup:
    operations.save(new IpvTaskDefaultTeams(
      defaultApprovalTeam: new TeamNameView(id: "id", name: "name"),
      defaultResolutionTeam: new TeamNameView(id: "id", name: "name")
      ))
    expect:
    def loaded = repository.getIpvTaskDefaultTeams()
    loaded.defaultResolutionTeam == new TeamNameView(id: "id", name: "name")
    loaded.defaultApprovalTeam == new TeamNameView(id: "id", name: "name")
    loaded.recordDate != null
  }

  def "should get IPV task default teams view"() {
    setup:
    operations.save(new IpvTaskDefaultTeams(
      defaultApprovalTeam: new TeamNameView(id: "id", name: "name"),
      defaultResolutionTeam: new TeamNameView(id: "id", name: "name")
      ))

    expect:
    def loaded = repository.getIpvTaskDefaultTeamsView()
    loaded.defaultResolutionTeam == new TeamNameView(id: "id", name: "name")
    loaded.defaultApprovalTeam == new TeamNameView(id: "id", name: "name")
    loaded.recordDate != null
    loaded.modifiedAt != null
    loaded.modifiedBy == creator.name
  }
}
