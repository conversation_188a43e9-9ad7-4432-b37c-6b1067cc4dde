package com.solum.xplain.core.settings.repository

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings
import com.solum.xplain.core.settings.entity.CurveConvexityAdjustment
import com.solum.xplain.core.settings.form.ConvexityAdjustmentsSettingsForm
import com.solum.xplain.core.settings.form.CurveConvexityAdjustmentForm
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ConvexitySettingsRepositoryTest extends IntegrationSpecification {

  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.now(), LocalDateTime.now().plusHours(1))

  @Resource
  MongoOperations operations

  @Resource
  ConvexitySettingsRepository repository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), ConvexityAdjustmentsSettings.class)
  }

  def "should get convexity settings"() {
    setup:
    def settings = new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(0), state: State.ACTIVE)
    operations.insert(settings)
    when:
    def result = repository.getConvexitySettings(STATE_DATE)
    then:
    result != null
    result.validFrom == LocalDate.ofEpochDay(0)
    result.modifiedAt != null
    result.modifiedBy.name == creator.name
  }

  def "should get convexity settings view"() {
    setup:
    def settings = new ConvexityAdjustmentsSettings(
      validFrom: LocalDate.ofEpochDay(0),
      state: State.ACTIVE,
      curveConvexityAdjustments: ["1": new CurveConvexityAdjustment()])
    operations.insert(settings)
    when:
    def result = repository.entityView(STATE_DATE)
    then:
    result != null
    result.validFrom == LocalDate.ofEpochDay(0)
    result.modifiedAt != null
    result.modifiedBy == creator.name
    result.curveConvexityAdjustments == ["1": new CurveConvexityAdjustment()]
  }

  def "should get empty convexity settings"() {
    when:
    def result = repository.getConvexitySettings(STATE_DATE)
    then:
    result != null
    result.validFrom == LocalDate.ofEpochDay(0)
    result.curveConvexityAdjustments == Collections.emptyMap()
  }

  def "should update convexity settings"() {
    setup:
    def settings = new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(0), state: State.ACTIVE)
    def form = new ConvexityAdjustmentsSettingsForm(
      ["EUR3M": new CurveConvexityAdjustmentForm(BigDecimal.valueOf(0.5d),
        BigDecimal.valueOf(1.1d),
        ["1Y": BigDecimal.valueOf(0.1d)]
        )],
      new NewVersionFormV2("comment", LocalDate.ofEpochDay(1), LocalDate.ofEpochDay(1), FutureVersionsAction.KEEP))
    operations.insert(settings)
    when:
    def result = repository.updateConvexitySettings(LocalDate.ofEpochDay(0), form)
    then:
    result.isRight()
    def loaded = operations.findAll(ConvexityAdjustmentsSettings)
    loaded.size() == 2
    loaded[1].validFrom == LocalDate.ofEpochDay(1)
    loaded[1].comment == "comment"
    loaded[1].state == State.ACTIVE
    loaded[1].curveConvexityAdjustments == ["EUR3M": new CurveConvexityAdjustment(
      mean: BigDecimal.valueOf(0.5d),
      zeroTenorVol: BigDecimal.valueOf(1.1d),
      volatilities: ["1Y": BigDecimal.valueOf(0.1d)]
      )]
  }

  def "should get convexity settings versions major"() {
    setup:
    operations.insert(new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(0)))
    operations.insert(new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(0)))
    when:
    def result = repository.entityVersions()
    then:
    result.size() == 2
    result[0].validFrom == LocalDate.ofEpochDay(0)
  }

  def "should delete convexity settings"() {
    setup:
    def settings = new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(1))
    operations.insert(settings)
    when:
    def result = repository.deleteConvexitySettingsVersion(LocalDate.ofEpochDay(1))
    then:
    result.isRight()
    def loaded = operations.findAll(ConvexityAdjustmentsSettings)
    loaded.size() == 2
    loaded[1].state == State.DELETED
  }

  def "should fail deleting convexity settings version with ROOT version"() {
    setup:
    def settings = new ConvexityAdjustmentsSettings(validFrom: LocalDate.ofEpochDay(0))
    operations.insert(settings)
    when:
    def result = repository.deleteConvexitySettingsVersion(LocalDate.ofEpochDay(0))
    then:
    result.isLeft()
    result.left()[0].reason == OPERATION_NOT_ALLOWED
    result.left()[0].description == "Can not delete first version!"
  }
}
