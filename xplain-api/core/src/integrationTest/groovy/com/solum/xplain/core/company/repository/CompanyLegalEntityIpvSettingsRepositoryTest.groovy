package com.solum.xplain.core.company.repository

import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY
import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.CompanySettingsType
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyIpvSettings
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings
import com.solum.xplain.core.company.entity.IpvValuationProviders
import com.solum.xplain.core.company.events.CompanyArchived
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated
import com.solum.xplain.core.company.form.CompanyLegalEntityIpvSettingsForm
import com.solum.xplain.core.company.form.IpvValuationProvidersForm
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup
import com.solum.xplain.core.ipv.group.events.IpvDataGroupUpdated
import com.solum.xplain.core.ipv.group.form.IpvDataGroupForm
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.users.AuditUser
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CompanyLegalEntityIpvSettingsRepositoryTest extends IntegrationSpecification {

  def static VALID_FROM = parse("2020-01-01")
  def static FUTURE_VALID_FROM = parse("2100-01-01")

  def static STATE_DATE = BitemporalDate.newOf(VALID_FROM)

  @Resource
  MongoOperations operations

  @Resource
  CompanyLegalEntityIpvSettingsRepository repository

  XplainPrincipal creator

  def setup() {
    creator = user("creatorId")
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), CompanyLegalEntityIpvSettings)
    operations.remove(new Query(), Company)
    operations.remove(new Query(), CompanyIpvSettings)
    operations.remove(new Query(), IpvDataGroup)
  }

  def "should get legal entity ipv settings"() {
    setup:
    operations.insert(companySettings())
    operations.insert(defaultIpvSettings())

    expect:
    def result = repository.companyEntityIpvSettingsView("companyId", "entityId", STATE_DATE)
    with(result) {
      settingsType == CompanySettingsType.DEFAULT
      products.size() == 1
      products.get(FXFWD).ipvDataGroupView.entityId == "groupId"
      products.get(FXFWD).ipvDataGroupView.name == "name"
      products.get(FXFWD) != null
      products.get(FXFWD).primary == "p"
      products.get(FXFWD).secondary == "s"
      products.get(FXFWD).tertiary == "t"
      validFrom == VALID_FROM.minusYears(5)
    }
  }

  def "should update company entity ipv settings"() {
    setup:
    def entitySettings = operations.insert(bespokeIpvSettings())
    when:
    def result = repository.updateCompanyEntityIpvSettings(
    entitySettings.entityId,
    entitySettings.validFrom,
    new CompanyLegalEntityIpvSettingsForm(
    "DEFAULT",
    null,
    [:],
    NewVersionFormV2.builder().validFrom(VALID_FROM).build()
    )
    )
    then:
    result.isRight()
    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2
    def res = loaded[0]

    res.validFrom == VALID_FROM
    res.settingsType == CompanySettingsType.DEFAULT
    res.products == []
    res.slaDeadline == null
  }

  def "should update company entity ipv settings when only default exist"() {
    setup:
    def entitySettings = operations.insert(defaultIpvSettings())
    def md = operations.save(new IpvDataGroup(name: "name"))

    when:
    def result = repository.updateCompanyEntityIpvSettings(
    entitySettings.entityId,
    entitySettings.validFrom,
    new CompanyLegalEntityIpvSettingsForm(
    "BESPOKE",
    SlaDeadline.LDN_1000_T_PLUS,
    [(FXOPT): new IpvValuationProvidersForm(md.id, "p", "s", "t", "q")],
    NewVersionFormV2.builder().validFrom(VALID_FROM).build()
    )
    )
    then:
    result.isRight()
    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2
    def res = loaded[0]
    res.validFrom == VALID_FROM
    res.settingsType == CompanySettingsType.BESPOKE
    res.slaDeadline == SlaDeadline.LDN_1000_T_PLUS
    res.products.size() == 1
    res.products.get(0) != null
    res.products.get(0).productType == FXOPT
    res.products.get(0).ipvDataGroup.entityId == md.id
    res.products.get(0).ipvDataGroup.name == md.name
    res.products.get(0).primary == "p"
    res.products.get(0).secondary == "s"
    res.products.get(0).tertiary == "t"
    res.products.get(0).quaternary == "q"
  }

  def "should update company entity ipv settings back to default"() {
    setup:
    def entitySettings = operations.insert(bespokeIpvSettings())

    when:
    def result = repository.updateCompanyEntityIpvSettings(
    entitySettings.entityId,
    entitySettings.validFrom,
    new CompanyLegalEntityIpvSettingsForm(
    "DEFAULT",
    null,
    null,
    NewVersionFormV2.builder().validFrom(VALID_FROM).build()
    )
    )
    then:
    result.isRight()
    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2
    def res = loaded[0]
    res.validFrom == VALID_FROM
    res.settingsType == CompanySettingsType.DEFAULT
    res.products == []
    res.slaDeadline == null
  }


  def "should get company entity ipv settings versions"() {
    setup:
    def entitySettings = operations.insert(bespokeIpvSettings())

    when:
    def result = repository.entityValuationSettingsVersions(entitySettings.entityId)
    then:
    result.size() == 1
    def view = result.get(0)
    view.validFrom == VALID_FROM.minusYears(5)
  }

  def "should update ipv data group name"() {
    setup:
    def group = operations.insert(new IpvDataGroup(name: "name"))
    def group2 = operations.insert(new IpvDataGroup(name: "name2"))
    operations.insert(bespokeIpvSettings(
    c -> c.products = [
      ipvValuationProviders(FXFWD, EntityReference.newOfNullable(group.id, group.name)),
      ipvValuationProviders(FXOPT, EntityReference.newOfNullable(group2.id, group2.name)),
      ipvValuationProviders(IRS, EntityReference.newOfNullable(group.id, group.name)),
      ipvValuationProviders(XCCY, null)
    ])
    )
    operations.insert(bespokeIpvSettings(
    c -> c.products = [
      ipvValuationProviders(FXFWD, EntityReference.newOfNullable(group.id, group.name)),
      ipvValuationProviders(XCCY, null)
    ])
    )

    when:
    var event = IpvDataGroupUpdated.newOf(group, new IpvDataGroupForm("new name", null, null, null))
    repository.onIpvDataGroupUpdate(event)

    then:
    def loaded = operations.findAll(CompanyLegalEntityIpvSettings.class)

    loaded.size() == 2

    def res1 = loaded[0]
    res1.products.size() == 4
    res1.products[0].productType == FXFWD
    res1.products[0].ipvDataGroup.entityId == group.id
    res1.products[0].ipvDataGroup.name == "new name"
    res1.products[1].productType == FXOPT
    res1.products[1].ipvDataGroup.entityId == group2.id
    res1.products[1].ipvDataGroup.name == group2.name
    res1.products[2].productType == IRS
    res1.products[2].ipvDataGroup.entityId == group.id
    res1.products[2].ipvDataGroup.name == "new name"
    res1.products[3].productType == XCCY
    res1.products[3].ipvDataGroup == null

    def res2 = loaded[1]
    res2.products.size() == 2
    res2.products[0].productType == FXFWD
    res2.products[0].ipvDataGroup.entityId == group.id
    res2.products[0].ipvDataGroup.name == "new name"
    res2.products[1].productType == XCCY
    res2.products[1].ipvDataGroup == null
  }

  def "should archive company ipv settings"() {
    setup:
    operations.insert(bespokeIpvSettings { c -> c.validFrom = NewVersionFormV2.ROOT_DATE })

    when:
    repository.onCompanyArchived(new CompanyArchived("companyId"))
    then:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.ARCHIVED
  }

  def "should create default company entity IPV settings"() {
    setup:
    repository.onCompanyEntityCreated(new CompanyLegalEntityCreated("companyId", "entityId"))
    expect:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 1
  }

  def "should delete entity ipv settings"() {
    setup:
    operations.insert(bespokeIpvSettings { c -> c.validFrom = NewVersionFormV2.ROOT_DATE })
    when:
    repository.onCompanyEntityArchived(new CompanyLegalEntityArchived("companyId", "entityId"))
    then:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.ARCHIVED
  }

  def "should load future versions dates list"() {
    setup:
    def settings = bespokeIpvSettings()
    def futureSettings = bespokeIpvSettings()
    futureSettings.state = State.ARCHIVED
    futureSettings.validFrom = FUTURE_VALID_FROM
    operations.insertAll([settings, futureSettings])

    when:
    def result = repository.getFutureVersions(settings.entityId, settings.validFrom)

    then:
    result.dates == [futureSettings.validFrom]
  }

  def "should mark as deleted company ipv settings"() {
    setup:
    def settings = bespokeIpvSettings()
    operations.insert(settings)

    when:
    def result = repository.delete(settings.entityId, settings.validFrom)

    then:
    result.right
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.DELETED
  }

  private List<CompanyLegalEntityIpvSettings> allSortedValidFromDesc(String entityId) {
    operations.query(CompanyLegalEntityIpvSettings)
    .matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "validFrom", "recordDate")))
    .all()
  }

  def "should return valuation settings views for given entity ids"() {
    setup:
    def setting1 = new CompanyLegalEntityIpvSettings(
    settingsType: CompanySettingsType.BESPOKE,
    entityId: "entityId1",
    companyId: "companyId",
    validFrom: VALID_FROM.minusYears(1),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusDays(1)
    )

    def setting2 = new CompanyLegalEntityIpvSettings(
    settingsType: CompanySettingsType.BESPOKE,
    entityId: "entityId2",
    companyId: "companyId",
    validFrom: VALID_FROM.minusYears(1),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusDays(1)
    )

    def settingOther = new CompanyLegalEntityIpvSettings(
    settingsType: CompanySettingsType.BESPOKE,
    entityId: "otherId",
    companyId: "companyId",
    validFrom: VALID_FROM.minusYears(1),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusDays(1)
    )

    operations.insertAll([setting1, setting2, settingOther])

    when:
    def result = repository.getValuationSettingsView(["entityId1", "entityId2"], STATE_DATE)

    then:
    result.size() == 2
    result.containsKey("entityId1")
    result.containsKey("entityId2")

    with(result["entityId1"]) {
      entityId == "entityId1"
      settingsType == CompanySettingsType.BESPOKE
    }

    with(result["entityId2"]) {
      entityId == "entityId2"
      settingsType == CompanySettingsType.BESPOKE
    }
  }


  static def companySettings() {
    new CompanyIpvSettings(
    entityId: "companyId",
    modifiedBy: new AuditUser("userId", "username", "name"),
    modifiedAt: LocalDateTime.now(),
    settingsType: CompanySettingsType.DEFAULT,
    products: [
      new IpvValuationProviders(
      productType: FXFWD,
      ipvDataGroup: new EntityReference(
      entityId: "groupId",
      name: "name"),
      primary: "p",
      secondary: "s",
      tertiary: "t")
    ],
    validFrom: VALID_FROM.minusYears(10),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1),
    )
  }

  static def bespokeIpvSettings(Closure c = { e -> e }) {
    new CompanyLegalEntityIpvSettings(
    settingsType: CompanySettingsType.BESPOKE,
    entityId: "entityId",
    companyId: "companyId",
    products: [ipvValuationProviders(FXFWD, EntityReference.newOfNullable("groupId2", "name2")),],
    validFrom: VALID_FROM.minusYears(5),
    state: State.ACTIVE
    ).with(true, c)
  }

  static def ipvValuationProviders(ProductType productType = FXFWD, EntityReference ipvDataGroup = EntityReference.newOfNullable("groupId", "name")) {
    new IpvValuationProviders(
    productType: productType,
    ipvDataGroup: ipvDataGroup,
    primary: "p",
    secondary: "s",
    tertiary: "t")
  }

  static def defaultIpvSettings(Closure c = { e -> e }) {
    new CompanyLegalEntityIpvSettings(
    settingsType: CompanySettingsType.DEFAULT,
    entityId: "entityId",
    companyId: "companyId",
    validFrom: VALID_FROM.minusYears(5),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1),
    ).with(true, c)
  }
}
