package com.solum.xplain.core.portfolio.repository

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.common.GroupRequest.emptyGroupRequest
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE_TIME
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.portfolio.CoreProductType.CDS
import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION
import static com.solum.xplain.core.portfolio.PortfolioBuilder.portfolio
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.allocationTrade
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.shared.utils.filter.FilterOperation.EQUAL
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.time.LocalDate.now
import static java.time.LocalDate.ofEpochDay
import static java.time.LocalDate.parse
import static java.util.Collections.emptyList
import static org.hamcrest.Matchers.containsInAnyOrder
import static org.hamcrest.Matchers.iterableWithSize
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.filter.VersionedEntityFilter
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.core.portfolio.PortfolioBuilder
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.VersionedTradeEntity
import com.solum.xplain.core.portfolio.builder.ResolvableFxForwardDetails
import com.solum.xplain.core.portfolio.form.PortfolioItemSearchForm
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.trade.OnboardingDetails
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.teams.Team
import com.solum.xplain.extensions.enums.PositionType
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.domain.Sort.Order
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class PortfolioItemRepositoryTest extends IntegrationSpecification {

  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 01))
  private static Sort DEFAULT_SORT = Sort.by(VersionedTradeEntity.Fields.productType, PortfolioItem.Fields.externalPortfolioId)

  @Resource
  PortfolioItemRepository portfolioItemRepository
  @Resource
  MongoOperations operations

  def creator = user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), PortfolioItem)
    operations.remove(new Query(), Portfolio)
    operations.remove(new Query(), Team)
  }

  def "should serialize and store portfolio item"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    operations.insert(item)
    when:
    def loaded = operations.findById(item.id, PortfolioItem.class)
    then:
    loaded.tradeDetails == item.tradeDetails
  }

  def "should load portfolio item"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)

    def items = [
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId())
    ]
    items[0].description = "Comment"
    operations.insertAll(items)
    def firstItem = items[0]

    when:
    def loaded = portfolioItemRepository.portfolioItemLatest(portfolio.id, items[0].entityId, STATE_DATE)
    then:
    loaded.isRight()
    def loadedItem = loaded.getOrNull()
    loadedItem.tradeDetails == firstItem.tradeDetails
    loadedItem.clientMetrics == new ClientMetrics(presentValue: 10.0)
    loadedItem.modifiedBy.name == "Full Name"
    loadedItem.modifiedAt != null
    loadedItem.recordFrom != null
    loadedItem.recordTo == null
    loadedItem.validities.size() == 1
    loadedItem.validities[0].recordTo != null
    loadedItem.validFrom == ofEpochDay(0)
    loadedItem.validTo == null
    loadedItem.validities[0].validTo == MAX_DATE
    loadedItem.description == "Comment"
    loadedItem.onboardingDetails == firstItem.onboardingDetails != null
    loadedItem.allocationTradeDetails == firstItem.allocationTradeDetails != null
  }

  def "should load active portfolio items stream"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def items = [
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId())
    ]
    items[0].description = "Comment"
    items[0].state = State.DELETED
    operations.insertAll(items)
    when:
    def loaded = portfolioItemRepository.activePortfolioItemsStream(portfolio.id, STATE_DATE)
    then:
    loaded.toList().size() == 1
  }

  def "should return active matching trade for company tree external ids"() {
    setup:
    def portfolioItem = new PortfolioItem()
    portfolioItem.externalCompanyId = "externalCompanyId"
    portfolioItem.externalEntityId = "externalEntityId"
    portfolioItem.externalPortfolioId = "externalPortfolioId"
    portfolioItem.externalTradeId = "externalTradeId"

    portfolioItem.validFrom = STATE_DATE.actualDate
    portfolioItem.state = State.ACTIVE
    portfolioItem.recordFrom = STATE_DATE.recordDate.minusHours(1)
    portfolioItem.recordTo = MAX_DATE_TIME
    portfolioItem.validities = [
      new DateRangeVersionValidity(MAX_DATE, STATE_DATE.recordDate.minusHours(1), MAX_DATE_TIME)
    ]

    operations.insert(portfolioItem)

    when:
    def matches = portfolioItemRepository.matchingActiveAndCompanyTreeExternalIds(
      STATE_DATE,
      inExternalCompanyId,
      inExternalEntityId,
      inPortfolioId,
      inExternalTradeId
      )

    then:
    expMatches === matches

    where:
    inExternalCompanyId | inExternalEntityId  | inPortfolioId         | inExternalTradeId | expMatches
    "externalCompanyId" | "externalEntityId"  | "externalPortfolioId" | "externalTradeId" | true
    "n"                 | "externalEntityId"  | "externalPortfolioId" | "externalTradeId" | false
    "externalCompanyId" | "n"                 | "externalPortfolioId" | "externalTradeId" | false
    "externalCompanyId" | "externalEntityId"  | "n"                   | "externalTradeId" | false
    "externalCompanyId" | "externalEntityId"  | "externalPortfolioId" | "n"               | false
  }

  def "should load active portfolio items for onboarding when #xplainCheck #marketCheck #vendorCheck #expectedSize"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)

    def items = [
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()).tap { it.setOnboardingDetails(new OnboardingDetails(xplainCostCheck: true, marketConfCheck: true, vendorCheck: true)) },
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()).tap { it.setOnboardingDetails(new OnboardingDetails(xplainCostCheck: false, marketConfCheck: false, vendorCheck: false)) },
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()).tap { it.setOnboardingDetails(new OnboardingDetails(xplainCostCheck: true, marketConfCheck: false, vendorCheck: false)) },
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId()).tap { it.setOnboardingDetails(new OnboardingDetails(xplainCostCheck: true, marketConfCheck: true, vendorCheck: false)) },
    ]
    operations.insertAll(items)

    expect:
    def loaded = portfolioItemRepository.conformityRequiredTrades(portfolio.id, STATE_DATE, xplainCheck, marketCheck, vendorCheck)
    loaded.size() == expectedSize

    where:
    xplainCheck | marketCheck | vendorCheck | expectedSize
    false       | false       | false       | 0
    true        | false       | false       | 3
    false       | true        | false       | 2
    false       | false       | true        | 1
  }

  def "should calculate correct portfolio items count"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def trade1_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    def trade1_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    trade1_2.validFrom = now()
    trade1_2.entityId = trade1_1.entityId
    trade1_2.state = State.DELETED

    def trade2_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    trade2_1.validTo = now().minusDays(1)
    trade2_1.validities[0].validTo = trade2_1.validTo

    def trade2_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    trade2_2.validFrom = now().minusDays(1)
    trade2_2.entityId = trade2_1.entityId
    trade2_2.state = State.ARCHIVED

    def trade3_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    trade3_1.validTo = now()
    trade3_1.validities[0].validTo = trade3_1.validTo

    def trade3_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    trade3_2.validFrom = now()
    trade3_2.entityId = trade3_1.entityId

    operations.insertAll([trade1_1, trade1_2, trade2_1, trade2_2, trade3_1, trade3_2])
    expect:
    portfolioItemRepository.portfolioTradesCount(portfolio.id, new BitemporalDate(date)) == count
    where:
    date               | count
    ofEpochDay(0)      | 3
    now().minusDays(1) | 2
    now()              | 2
  }

  def "should calculate correct multiple portfolio items counts"() {
    given:
    def portfolio1 = portfolio()
    def portfolio2 = new PortfolioBuilder().name("Second portfolio").externalPortfolioId("SECOND").build()

    def portfolios = [portfolio1, portfolio2]
    operations.insertAll(portfolios)

    def trade1_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    def trade1_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    trade1_2.validFrom = now()
    trade1_2.entityId = trade1_1.entityId
    trade1_2.state = State.DELETED

    def trade2_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    trade2_1.validTo = now().minusDays(1)
    trade2_1.validities[0].validTo = trade2_1.validTo

    def trade2_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    trade2_2.validFrom = now().minusDays(1)
    trade2_2.entityId = trade2_1.entityId
    trade2_2.state = State.ARCHIVED

    def trade3_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    trade3_1.validTo = now()
    trade3_1.validities[0].validTo = trade3_1.validTo

    def trade3_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio1.id)
    trade3_2.validFrom = now()
    trade3_2.entityId = trade3_1.entityId

    def trade4_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio2.id)
    trade4_1.validTo = now()
    trade4_1.validities[0].validTo = trade4_1.validTo

    def trade4_2 = PortfolioItemBuilder.irsPortfolioItem(portfolio2.id)
    trade4_2.validFrom = now()
    trade4_2.entityId = trade4_1.entityId
    trade4_2.state = State.ARCHIVED


    operations.insertAll([trade1_1, trade1_2, trade2_1, trade2_2, trade3_1, trade3_2, trade4_1, trade4_2])

    when:
    def result = portfolioItemRepository.portfoliosTradesCount([portfolio1.id, portfolio2.id], BitemporalDate.newOf(date))

    then:
    result[portfolio1.id] == count1
    result[portfolio2.id] == count2

    where:
    date               | count1 | count2
    ofEpochDay(0)      | 3      | 1
    now().minusDays(1) | 2      | 1
    now()              | 2      | null
  }

  def "should load future versions"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def version1 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version1.validFrom = ofEpochDay(0)
    version1.validities[0].validTo = now()
    def version2 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version2.validFrom = now()
    version2.validities[0].validTo = now().plusDays(1)
    version2.entityId = version1.entityId
    def version3 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version3.validFrom = now().plusDays(1)
    version3.validities[0].validTo = now().plusDays(2)
    version3.state = State.DELETED
    version3.entityId = version1.entityId
    def version4 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version4.validFrom = now().plusDays(2)
    version4.validities[0].validTo = now().plusDays(3)
    version4.validities[0].recordFrom = LocalDateTime.now().minusDays(10)
    version4.validities[0].recordTo = LocalDateTime.now().plusDays(5)
    version4.state = State.ACTIVE
    version4.entityId = version1.entityId
    def version4_1 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version4_1.validFrom = version4.validFrom
    version4_1.validities[0].recordFrom = version4.recordTo
    version4_1.validities[0].validTo = version4.validTo
    version4_1.state = State.ARCHIVED
    version4_1.entityId = version1.entityId
    def version5 = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    version5.validFrom = now().plusDays(3)
    version5.state = State.DELETED
    version5.entityId = version1.entityId


    operations.insertAll([version1, version2, version3, version4, version4_1, version5])
    when:
    def loaded = portfolioItemRepository.futureVersions(portfolio.id, new PortfolioItemSearchForm("tradeId", ofEpochDay(0)))
    then:
    loaded.getDates() == [now(), now().plusDays(2)]
  }

  def "should get whether has future versions"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def portfolioItem = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    portfolioItem.validFrom = now()
    operations.insert(portfolioItem)

    when:
    def result = portfolioItemRepository.hasFutureVersions(portfolio.id, ofEpochDay(0))

    then:
    result
  }

  def "should load portfolio items"() {
    setup:
    def tradeDate = parse("2016-01-01")
    def portfolio = portfolio()
    operations.insert(portfolio)
    def tradeInfo = TradeDetailsBuilder.tradeInfoDetails(tradeDate)
    tradeInfo.csaDiscountingGroup = "EUR"
    def portfolioItem = PortfolioItemBuilder.trade(IRS, TradeDetailsBuilder.irsDetails(tradeDate, tradeInfo), portfolio.id, "1")
    portfolioItem.description = "comments"
    portfolioItem.externalIdentifiers = [new ExternalIdentifier("id1", "source1")]
    operations.insertAll([
      portfolioItem,
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "2", tradeDate),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "3", tradeDate)
    ])
    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(0, 2, Sort.by("entityId")),
      emptyGroupRequest(),
      )
    then:
    loaded.lastRow == null
    loaded.content.size() == 2

    loaded.endRow == 2
    def loadedItem = loaded.content[0]
    loadedItem.entityId == portfolioItem.entityId
    loadedItem.productType == IRS
    loadedItem.externalTradeId == "1"
    loadedItem.key == "externalCompanyId_externalEntityId_extPortfolioId_1"
    loadedItem.tradeDetails.info.counterParty == "IRS_COUNTER_PARTY"
    loadedItem.tradeDetails.info.tradeDate == tradeDate
    loadedItem.tradeDetails.info.csaDiscountingGroup == "EUR"
    loadedItem.clientMetrics.presentValue == 10.0
    loadedItem.tradeDetails.payLeg.currency == "EUR"
    loadedItem.tradeDetails.payLeg.notional == 10000000d
    loadedItem.tradeDetails.payLeg.accrualFrequency == "12M"
    loadedItem.tradeDetails.payLeg.initialValue == 0.025d
    loadedItem.tradeDetails.payLeg.index == "Fixed"
    loadedItem.tradeDetails.payLeg.dayCount == "30U/360"
    loadedItem.tradeDetails.receiveLeg.currency == "EUR"
    loadedItem.tradeDetails.receiveLeg.notional == 10000000d
    loadedItem.tradeDetails.receiveLeg.accrualFrequency == "3M"
    loadedItem.tradeDetails.receiveLeg.initialValue == null
    loadedItem.tradeDetails.receiveLeg.index == "EUR-EURIBOR-3M"
    loadedItem.tradeDetails.receiveLeg.dayCount == "Act/360"
    loadedItem.modifiedBy == "Full Name"
    loadedItem.modifiedAt != null
    loadedItem.validFrom != null
    loadedItem.description == "comments"
    loadedItem.onboardingDetails == portfolioItem.onboardingDetails != null
    loadedItem.tradeInfoExternalIdentifiers == portfolioItem.externalIdentifiers != null
  }

  @Unroll
  def "when #startRow, #endRow and #groupRequest are given then last row count is #expectedLastRowSize"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    createFortyPortfolioItems(portfolio.id)

    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(startRow, endRow, DEFAULT_SORT),
      groupRequest
      )

    then:
    loaded.lastRow == expectedLastRowSize
    loaded.content.size() == expectedContentSize
    loaded.endRow == endRow

    where:
    startRow | endRow | groupRequest                                                                 | expectedLastRowSize | expectedContentSize
    0        | 10     | emptyGroupRequest()                                                          | null                | 10
    0        | 40     | emptyGroupRequest()                                                          | 40                  | 40
    41       | 50     | emptyGroupRequest()                                                          | 41                  | 0
    35       | 45     | emptyGroupRequest()                                                          | 40                  | 5
    35       | 45     | GroupRequest.of(['externalTradeId'], emptyList())                            | 40                  | 5
    0        | 10     | GroupRequest.of(['tradeDetails.info.tradeDate', 'productType'], emptyList()) | 1                   | 1
    0        | 10     | GroupRequest.of(['productType'], emptyList())                                | 3                   | 3
    0        | 70     | GroupRequest.of(['productType'], emptyList())                                | 3                   | 3
  }

  def "should load grouped portfolio items"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    operations.insertAll([
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    ])
    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(0, 4, DEFAULT_SORT), GroupRequest.of(["productType", "tradeDetails.info.tradeDate"], ["IRS"]),
      )
    then:
    loaded.lastRow == 1
    loaded.content.size() == 1

    def loadedItem = loaded.content[0]
    loadedItem.productType == IRS
    loadedItem.tradeDetails.info.tradeDate == parse("2018-04-12")
    loadedItem.tradeDetails.info.counterParty == null
  }


  @Unroll
  def "should load group portfolio items: #count #groupRequest #scrollRequest"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    operations.insertAll([
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    ])

    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(
      portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      scrollRequest,
      groupRequest)

    then:
    loaded.content.size() == count

    where:
    count | groupRequest                         | scrollRequest
    4     | emptyGroupRequest()                  | ScrollRequest.of(0, 10)
    1     | GroupRequest.of(["productType"], []) | ScrollRequest.of(0, 10, DEFAULT_SORT)
    1     | GroupRequest.of(["productType"], []) | ScrollRequest.of(0, 10, Sort.by(Order.desc("productType")))
  }


  def "should load portfolio items with sort"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    operations.insertAll([
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "1"),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "3"),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "2"),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "4").tap({ c -> c.state = State.ARCHIVED }),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "5").tap({ c -> c.state = State.DELETED })
    ])
    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(0, 2, Sort.by(Direction.ASC, "externalTradeId")), emptyGroupRequest())
    then:
    loaded.content.size() == 2
    loaded.content[0].externalTradeId == "1"
    loaded.content[1].externalTradeId == "2"
    loaded.lastRow == null
  }

  def "should return empty result of no portfolio items"() {
    def portfolio = portfolio()
    setup:
    operations.insert(portfolio)
    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(portfolio.id,
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(0, 1),
      emptyGroupRequest())
    then:
    loaded.content.size() == 0
    loaded.lastRow == 0
  }

  def "should return empty result if no portfolio"() {
    when:
    def loaded = portfolioItemRepository.portfolioItemsWithKeyView(ObjectId.get().toString(),
      STATE_DATE,
      emptyTableFilter(),
      VersionedEntityFilter.active(),
      ScrollRequest.of(0, 2),
      emptyGroupRequest())
    then:
    loaded.content.size() == 0
    loaded.lastRow == 0
  }

  def "should load portfolio items for export"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def items = [
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.id).setState(State.ARCHIVED)
    ]
    operations.insertAll(items)
    when:
    def loaded = portfolioItemRepository.portfolioItemsStream(portfolio.id,
      STATE_DATE,
      Sort.by(Direction.DESC, "tradeDetails.info.externalTradeId"),
      new TableFilter([new SimpleFilterClause("tradeDetails.payLeg.currency", EQUAL, "EUR")]))
      .toList()
    then:
    loaded.size() == 2
    loaded[0].clientMetrics.presentValue == 10
  }

  def "should load portfolio distinct counterparties"() {
    setup:
    def tradeDate = parse("2017-01-01")
    def builder = ResolvableFxForwardDetails.builder()
      .businessDayConvention("MODIFIED_FOLLOWING")
      .paymentDate(tradeDate)
      .payCurrencyAmount(-11000)
      .payCurrency(USD)
      .receiveCurrency(EUR)
      .receiveCurrencyAmount(10000)
      .build()
    def trade1 = builder.toTradeDetails(new TradeInfoDetails(counterParty: "counter1", tradeCurrency: "EUR"))
    def trade2 = builder.toTradeDetails(new TradeInfoDetails(counterParty: "counter1", tradeCurrency: "EUR"))
    def trade3 = builder.toTradeDetails(new TradeInfoDetails(counterParty: "counter2", tradeCurrency: "EUR"))
    def trade4 = builder.toTradeDetails(new TradeInfoDetails(tradeCurrency: "EUR"))

    def portfolio = portfolio()
    operations.insert(portfolio)
    operations.insertAll([
      PortfolioItemBuilder.trade(FXFWD, trade1, portfolio.id),
      PortfolioItemBuilder.trade(FXFWD, trade2, portfolio.id),
      PortfolioItemBuilder.trade(FXFWD, trade3, portfolio.id),
      PortfolioItemBuilder.trade(FXFWD, trade4, portfolio.id)
    ])
    when:
    def loaded = portfolioItemRepository.counterparties(portfolio.id, STATE_DATE)
    then:
    that loaded, iterableWithSize(2)
    that loaded, containsInAnyOrder("counter1", "counter2")
  }

  @Unroll
  def "should assert query by external trade id #externalTradeId - required for uniqueness validation  #tradeId"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def portfolioItem = PortfolioItemBuilder.irsPortfolioItem(portfolio.id, "someExternalTradeId")
      .tap { c -> c.state = state }

    operations.insertAll([portfolioItem])
    expect:
    resWithoutTradeId == portfolioItemRepository.hasPortfolioItemByExternalTradeId(STATE_DATE, portfolio.id,
      externalTradeId, null)
    resWithTradeId == portfolioItemRepository.hasPortfolioItemByExternalTradeId(STATE_DATE, portfolio.id,
      externalTradeId, tradeId != null ? tradeId : portfolioItem.entityId)
    where:
    externalTradeId          | tradeId                | state          | resWithoutTradeId | resWithTradeId
    "someExternalTradeId"    | null                   | State.ACTIVE   | true              | false
    "anotherExternalTradeId" | null                   | State.ACTIVE   | false             | false
    "someExternalTradeId"    | "tradeIdForExternalId" | State.ACTIVE   | true              | true
    "anotherExternalTradeId" | "tradeIdForExternalId" | State.ACTIVE   | false             | false
    "someExternalTradeId"    | "tradeIdForExternalId" | State.ARCHIVED | false             | false
  }


  def "should return resolved valuation data keys"() {
    setup:
    def groupKeys = [
      "externalCompanyId_externalEntityId_extPortfolioId_tradeId",
      "externalCompanyId_externalEntityId_extPortfolioId_extTradeId"
    ]
    def portfolio = portfolio()
    operations.insert(portfolio)
    def portfolioItemIrs = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    portfolioItemIrs.portfolioArchivedAt = LocalDateTime.now()
    def portfolioItemCds = PortfolioItemBuilder.trade(CDS, TradeDetailsBuilder.cdsTradeDetails(STATE_DATE.getActualDate()), portfolio.id, "extTradeId")
    operations.insertAll([portfolioItemIrs, portfolioItemCds])
    when:
    def result = portfolioItemRepository.resolvedValuationDataKeys(STATE_DATE, groupKeys as Set<String>)
    then:
    result.size() == 1
    result[0] == "externalCompanyId_externalEntityId_extPortfolioId_extTradeId"
  }

  def "should return all valuation data keys"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def portfolioItemIrs = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    portfolioItemIrs.portfolioArchivedAt = LocalDateTime.now()
    def portfolioItemCds = PortfolioItemBuilder.trade(CDS, TradeDetailsBuilder.cdsTradeDetails(STATE_DATE.getActualDate()), portfolio.id, "extTradeId")
    operations.insertAll([portfolioItemIrs, portfolioItemCds])
    when:
    def result = portfolioItemRepository.allValuationDataKeys([portfolio.id], STATE_DATE)
    then:
    result.size() == 2
    result.containsAll([
      "externalCompanyId_externalEntityId_extPortfolioId_tradeId",
      "externalCompanyId_externalEntityId_extPortfolioId_extTradeId"
    ])
  }

  def "should get item versions"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item =
      operations.insert(PortfolioItemBuilder.irsPortfolioItem(portfolio.id))
    def itemMinor = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    itemMinor.setEntityId(item.getEntityId())
    itemMinor.setComment("latest")
    operations.insert(itemMinor)
    def itemAnotherVersion = PortfolioItemBuilder.irsPortfolioItem(portfolio.id)
    itemAnotherVersion.setEntityId(item.getEntityId())
    itemAnotherVersion.setValidFrom(VAL_DT)
    operations.insert(itemAnotherVersion)

    when:
    def result = portfolioItemRepository.getVersions(portfolio.getId(), item.entityId)

    then:
    result.size() == 3
    result[0].validFrom == ofEpochDay(0)
    result[0].tradeDetails.receiveLeg.index == "EUR-EURIBOR-3M"
    result[0].recordFrom != null
    result[0].recordTo == null
    result[0].validities.size() == 1
    result[0].validities[0].recordTo == MAX_DATE_TIME
    result[1].validFrom == ofEpochDay(0)
    result[1].validTo == null
    result[1].validities.size() == 1
    result[1].validities[0].validTo == MAX_DATE
    result[1].tradeDetails.receiveLeg.index == "EUR-EURIBOR-3M"
    result[2].validFrom == VAL_DT
    result[2].validTo == null
    result[2].validities.size() == 1
    result[2].validities[0].validTo == MAX_DATE
    result[2].tradeDetails.receiveLeg.index == "EUR-EURIBOR-3M"
  }

  private void createFortyPortfolioItems(String portfolioId) {
    def tradeDate = parse("2016-01-01")
    def portfolioIrsItems = new IntRange(1, 10).stream()
      .map({ it -> PortfolioItemBuilder.irsPortfolioItem(portfolioId, it.toString(), tradeDate) })
      .toList()
    operations.insertAll(portfolioIrsItems)
    def portfolioCdsItems = new IntRange(11, 20).stream()
      .map({ it -> PortfolioItemBuilder.trade(CDS, TradeDetailsBuilder.cdsTradeDetails(tradeDate), portfolioId, it.toString()) })
      .toList()
    operations.insertAll(portfolioCdsItems)
    def portfolioSwaptionItems = new IntRange(21, 40).stream()
      .map({ it -> PortfolioItemBuilder.trade(SWAPTION, TradeDetailsBuilder.swaptionTradeDetails(tradeDate), portfolioId, it.toString()) })
      .toList()
    operations.insertAll(portfolioSwaptionItems)
  }

  def "should load active portfolio items by reference trade id scrolled"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)

    def t1 = allocationTrade(portfolio.getId(), "t1").tap { it.description = "comments"}
    t1.allocationTradeDetails.referenceTradeId = "REF_1"
    def t2 = allocationTrade(portfolio.getId(), "t2")
    t2.allocationTradeDetails.referenceTradeId = "REF_2"
    def t3 = allocationTrade(portfolio.getId(), "t3")
    t3.allocationTradeDetails.referenceTradeId = "REF_1"
    t3.state = State.DELETED
    def t4 = allocationTrade(portfolio.getId(), "t4")
    t4.allocationTradeDetails.referenceTradeId = "REF_1"
    t4.portfolioArchivedAt = LocalDateTime.now().minusDays(5)
    operations.insertAll([t1, t2, t3, t4])

    when:
    def loaded = portfolioItemRepository.itemsByRefTradeId("REF_1", STATE_DATE, ScrollRequest.of(0, 10, Sort.unsorted()), emptyTableFilter())

    then:
    loaded.getLastRow() == 1
    loaded.getContent().size() == 1
    loaded.getContent()[0].getValidFrom() == t1.getValidFrom()
    loaded.getContent()[0].getVersion() == t1.getVersion()
    loaded.getContent()[0].getTradeId() == t1.getEntityId()
    loaded.getContent()[0].getExternalTradeId() == "t1"
    loaded.getContent()[0].getPortfolioId() == portfolio.getId()
    loaded.getContent()[0].getExternalPortfolioId() == "extPortfolioId"
    loaded.getContent()[0].getExternalCompanyId() == "externalCompanyId"
    loaded.getContent()[0].getExternalEntityId() == "externalEntityId"
    loaded.getContent()[0].getAllocationNotional() == 1000
    loaded.getContent()[0].getPositionType() == PositionType.BUY
    loaded.getContent()[0].getTradeCounterparty() == "cp"
    loaded.getContent()[0].getTradeCounterpartyType() == CounterpartyType.BILATERAL
    loaded.getContent()[0].getClientMetrics() == new ClientMetrics(presentValue: 123)
    loaded.getContent()[0].getResolvedTradeCounterparty() == "INFO_CP"
    loaded.getContent()[0].getResolvedTradeCounterpartyType() == CounterpartyType.CLEARED
    loaded.getContent()[0].getResolvedClientMetrics() == new ClientMetrics(presentValue: 10)
    loaded.getContent()[0].getResolvedTradeDate() == LocalDate.of(2017, 12, 20)
    loaded.getContent()[0].getResolvedDescription() == "comments"
    loaded.getContent()[0].getOnboardingDetails() == t1.getOnboardingDetails()
  }

  def "should load active portfolio items by reference trade id count"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)

    def t1 = allocationTrade(portfolio.getId(), "t1")
    t1.allocationTradeDetails.referenceTradeId = "REF_1"
    def t2 = allocationTrade(portfolio.getId(), "t2")
    t2.allocationTradeDetails.referenceTradeId = "REF_2"
    def t3 = allocationTrade(portfolio.getId(), "t3")
    t3.allocationTradeDetails.referenceTradeId = "REF_1"
    t3.state = State.DELETED
    operations.insertAll([t1, t2, t3])

    when:
    def loaded = portfolioItemRepository.itemsByRefTradeIdCount("REF_1", new BitemporalDate(ofEpochDay(0)))

    then:
    loaded == 1L
  }

  def "should load sorted portfolio items stream"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def stateDate = BitemporalDate.newOfNow()
    def sort = Sort.by(VersionedTradeEntity.Fields.externalTradeId)

    def items = [
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId(), "B"),
      PortfolioItemBuilder.irsPortfolioItem(portfolio.getId(), "A")
    ]
    operations.insertAll(items)

    when:
    def loaded = portfolioItemRepository.portfoliosItemsStream(Set.of(portfolio.id), stateDate, sort).toList()

    then:
    loaded.externalTradeId == ["A", "B"]
  }
}
