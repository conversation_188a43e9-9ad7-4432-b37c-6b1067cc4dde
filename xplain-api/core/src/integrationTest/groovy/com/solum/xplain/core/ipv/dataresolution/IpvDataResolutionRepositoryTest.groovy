package com.solum.xplain.core.ipv.dataresolution

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.data.entity.IpvDataValue
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class IpvDataResolutionRepositoryTest extends IntegrationSpecification {

  static final VALUATION_DATE = LocalDate.of(2021, 1, 1)
  static final VALUATION_DATE_ANOTHER = LocalDate.of(2021, 2, 2)

  static final KEY = "EXT_COMPANY_ID_EXT_ENTITY_ID_EXT_PORTFOLIO_ID_EXT_TRADE_ID"
  static final KEY_ANOTHER = "EXT_COMPANY_ID_EXT_ENTITY_ID_EXT_PORTFOLIO_ID_EXT_TRADE_ID_ANOTHER"

  static final PROVIDER = "PROVIDER"

  @Resource
  IpvDataResolutionRepository repository

  @Resource
  MongoOperations operations

  def cleanup() {
    operations.remove(new Query(), IpvDataGroup)
    operations.remove(new Query(), IpvDataValue)
  }

  def "should get non-resolved data"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: false,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherGroup = operations.insert(new IpvDataGroup())
    def anotherGroup_anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: anotherGroup.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def result

    when: "group not specified"
    result = repository.getNonResolvedKeys().toList()

    then:
    result.size() == 2
    result[0] == new IpvDataGroupDateKey(
      anotherKey_pv.groupId,
      anotherKey_pv.date,
      anotherKey_pv.key,
      )
    result[1] == new IpvDataGroupDateKey(
      anotherGroup_anotherKey_pv.groupId,
      anotherGroup_anotherKey_pv.date,
      anotherGroup_anotherKey_pv.key,
      )

    when: "group specified"
    result = repository.getNonResolvedKeys(group.id).toList()

    then:
    result.size() == 1
    result[0] == new IpvDataGroupDateKey(
      anotherKey_pv.groupId,
      anotherKey_pv.date,
      anotherKey_pv.key,
      )
  }

  @Unroll
  def "should update values in group resolved=null"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: false,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_anotherDate = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: false,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherGroup = operations.insert(new IpvDataGroup())
    def anotherGroup_anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: anotherGroup.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    when:
    def result = repository.updateValuesNonResolved(group.id)

    then:
    result == 3

    operations.findById(pv.id, IpvDataValue).resolved == null
    operations.findById(pv_anotherDate.id, IpvDataValue).resolved == null
    operations.findById(anotherKey_pv.id, IpvDataValue).resolved == null
    operations.findById(anotherGroup_anotherKey_pv.id, IpvDataValue).resolved
  }

  def "should update values with keys as resolved"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_anotherDate = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherGroup_pv = operations.insert(new IpvDataValue(
      groupId: "OTHER_ID",
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    when:
    def result = repository.updateAllValuesUnresolved(Set.of(KEY))

    then:
    result == 3

    operations.findById(anotherKey_pv.id, IpvDataValue).resolved
    !operations.findById(pv.id, IpvDataValue).resolved
    !operations.findById(pv_anotherDate.id, IpvDataValue).resolved
    !operations.findById(anotherGroup_pv.id, IpvDataValue).resolved
  }

  def "should update values with keys and date as resolved = #resolved"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_anotherDate = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: false,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherGroup_pv = operations.insert(new IpvDataValue(
      groupId: "ANOTHER",
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    when:
    def result = repository.updateValuesResolved(
      group.id,
      Set.of(KEY),
      VALUATION_DATE,
      resolved
      )

    then:
    result == 1

    operations.findById(pv.id, IpvDataValue).resolved == resolved
    operations.findById(anotherGroup_pv.id, IpvDataValue).resolved == null
    operations.findById(pv_anotherDate.id, IpvDataValue).resolved
    !operations.findById(anotherKey_pv.id, IpvDataValue).resolved

    where:
    resolved << [true, false]
  }

  def "should update values with keys in range as resolved"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_anotherDate = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    when:
    def result = repository.updateAllValuesNonResolved(
      Set.of(KEY),
      VALUATION_DATE,
      VALUATION_DATE)

    then:
    result == 1

    operations.findById(pv.id, IpvDataValue).resolved == null
    operations.findById(pv_anotherDate.id, IpvDataValue).resolved
    operations.findById(anotherKey_pv.id, IpvDataValue).resolved
  }

  def "should update values for given groupId and valuation date as un-resolved"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def anotherGroup = operations.insert(new IpvDataGroup())

    def pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_anotherDate = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE_ANOTHER,
      key: KEY,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def anotherKey_pv = operations.insert(new IpvDataValue(
      groupId: group.id,
      date: VALUATION_DATE,
      key: KEY_ANOTHER,
      provider: PROVIDER,
      resolved: true,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    def pv_fromDifferentGroup = operations.insert(new IpvDataValue(
      groupId: anotherGroup.id,
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      resolved: null,
      values: [new IpvDataValueVersion(value: BigDecimal.ONE)]
      ))

    when:
    def result = repository.updateValuesAsUnresolved(group.id, VALUATION_DATE)

    then:
    result == 1

    operations.findById(pv.id, IpvDataValue).resolved == false
    operations.findById(pv_anotherDate.id, IpvDataValue).resolved
    operations.findById(anotherKey_pv.id, IpvDataValue).resolved
    operations.findById(pv_fromDifferentGroup.id, IpvDataValue).resolved == null
  }
}
