package com.solum.xplain.core.curvegroup.volatility

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.archived
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder.surface
import static com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder.surfaceWith
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.time.LocalDate.now
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.SwaptionValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import com.solum.xplain.core.curvegroup.volatility.entity.CapletVolatilityNode
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceNode
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceSkew
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeForm
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeForm
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewForm
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupVolatilityRepositoryTest extends IntegrationSpecification {
  def static VALID_FROM = parse("2020-01-01")
  def static FUTURE_VALID_FROM = parse("2100-01-01")

  @Resource
  CurveGroupVolatilityRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), MarketDataGroup)
    operations.remove(new Query(), MarketDataValue)
    operations.remove(new Query(), VolatilitySurface)
    operations.remove(new Query(), CurveGroup)
  }

  def "should create volatility surface"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new VolatilitySurfaceForm(
      name: "AED 3M Vols",
      XInterpolator: "Linear",
      XExtrapolatorLeft: "Flat",
      XExtrapolatorRight: "Flat",
      YInterpolator: "Linear",
      YExtrapolatorLeft: "Flat",
      YExtrapolatorRight: "Flat",
      skewType: VolatilitySurfaceType.ATM_ONLY.name(),
      sabr: true,
      sabrBeta: 0.01,
      sabrShift: 0.02,
      capletValuationModel: CapletValuationModel.NORMAL.name(),
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.createSurface(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT version
    assertAutofilledFields(loaded[0])
    assertUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "AED 3M Vols"
  }

  def "should insert volatility surface with archived from ROOT date"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new VolatilitySurfaceForm(
      name: "AED 3M Vols",
      XInterpolator: "Linear",
      XExtrapolatorLeft: "Flat",
      XExtrapolatorRight: "Flat",
      YInterpolator: "Linear",
      YExtrapolatorLeft: "Flat",
      YExtrapolatorRight: "Flat",
      skewType: VolatilitySurfaceType.ATM_ONLY.name(),
      sabr: true,
      sabrBeta: 0.01,
      sabrShift: 0.02,
      capletValuationModel: CapletValuationModel.NORMAL.name(),
      versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build()
      )

    when:
    def result = repository.createSurface(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "AED 3M Vols"

    // New major ROOT ARCHIVED version
    assertAutofilledFields(loaded[1])
    assertUpdateFormFields(loaded[1], form)
    loaded[1].state == State.ARCHIVED
    loaded[1].validFrom == ROOT_DATE
    loaded[1].comment == form.versionForm.comment
    loaded[1].curveGroupId == group.id
    loaded[1].name == "AED 3M Vols"
  }

  @Unroll
  def "should update on insert if surface with the same name exists for state #initialState"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .state(initialState)
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    def form = new VolatilitySurfaceForm(
      name: surface.name,
      XInterpolator: surface.xInterpolator,
      XExtrapolatorLeft: surface.xExtrapolatorLeft,
      XExtrapolatorRight: surface.xExtrapolatorRight,
      YInterpolator: surface.yInterpolator,
      YExtrapolatorLeft: surface.yExtrapolatorLeft,
      YExtrapolatorRight: surface.yExtrapolatorRight,
      skewType: surface.skewType.name(),
      sabr: true, // Updated
      sabrBeta: 0.0666, // Updated
      sabrShift: 0.0666, // Updated
      capletValuationModel: surface.capletValuationModel.name(),
      versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).stateDate(VALID_FROM).build()
      )

    when:
    def result = repository.createSurface(surface.curveGroupId, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == surface.curveGroupId
    loaded[0].name == surface.name

    // Initial major ROOT version
    surface.valueEquals(loaded[1])

    where:
    initialState << [State.ACTIVE, State.ARCHIVED, State.DELETED]
  }

  def "should update surface"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    def form = new VolatilitySurfaceUpdateForm(
      xInterpolator: "LogLinear", //Updated
      xExtrapolatorLeft: surface.xExtrapolatorLeft,
      xExtrapolatorRight: surface.xExtrapolatorRight,
      yInterpolator: "LogLinear", //Updated
      yExtrapolatorLeft: surface.yExtrapolatorLeft,
      yExtrapolatorRight: surface.yExtrapolatorRight,
      skewType: VolatilitySurfaceType.MONEYNESS.name(), //Updated
      sabr: true, //Updated
      sabrBeta: 0.0666, //Updated
      sabrShift: 0.0666, //Updated
      capletValuationModel: CapletValuationModel.NORMAL.name(), //Updated
      nodes: [new VolatilityNodeForm(tenor: "1Y", expiry: "1M")], //Updated
      skewNodes: [new VolatilitySurfaceSkewForm(skewValue: 1.0)], //Updated
      capletVolatilities: [new CapletVolatilityNodeForm(strike: 2.0, tenor: "1W")], //Updated
      versionForm: NewVersionFormV2.newDefault(),
      )

    when:
    def result = repository.updateSurface(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      form
      )

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    assertUpdateFormFields(loaded[0], form)
    loaded[0].nodes[0].tenor == form.nodes[0].tenor
    loaded[0].nodes[0].expiry == form.nodes[0].expiry
    loaded[0].skewNodes[0].skewValue == form.skewNodes[0].skewValue
    loaded[0].capletVolatilities[0].strike == form.capletVolatilities[0].strike
    loaded[0].capletVolatilities[0].tenor == form.capletVolatilities[0].tenor
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == surface.curveGroupId
    loaded[0].name == surface.name

    // Initial major ROOT version
    surface.valueEquals(loaded[1])
  }

  def "should update surface and remove future versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    def surfaceInFuture = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .entityId(surface.entityId)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(surfaceInFuture)

    def form = new VolatilitySurfaceUpdateForm(
      xInterpolator: "LogLinear", //Updated
      xExtrapolatorLeft: surface.xExtrapolatorLeft,
      xExtrapolatorRight: surface.xExtrapolatorRight,
      yInterpolator: "LogLinear", //Updated
      yExtrapolatorLeft: surface.yExtrapolatorLeft,
      yExtrapolatorRight: surface.yExtrapolatorRight,
      versionForm: NewVersionFormV2.builder()
      .validFrom(VALID_FROM)
      .futureVersionsAction(FutureVersionsAction.DELETE)
      .build()
      )
    when:
    def result = repository.updateSurface(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 4

    // Future Major (DELETED)
    loaded[0].validFrom == FUTURE_VALID_FROM
    loaded[0].state == State.DELETED

    // Future major
    loaded[1].validFrom == FUTURE_VALID_FROM
    loaded[1].state == State.ACTIVE

    // New major
    loaded[2].validFrom == VALID_FROM
    loaded[2].state == State.ACTIVE

    // Initial major (ROOT)
    loaded[3].validFrom == ROOT_DATE
    loaded[3].state == State.ACTIVE
  }

  def "should not update surface when no changes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    def form = new VolatilitySurfaceForm(
      name: surface.name,
      XInterpolator: surface.xInterpolator,
      XExtrapolatorLeft: surface.xExtrapolatorLeft,
      XExtrapolatorRight: surface.xExtrapolatorRight,
      YInterpolator: surface.yInterpolator,
      YExtrapolatorLeft: surface.yExtrapolatorLeft,
      YExtrapolatorRight: surface.yExtrapolatorRight,
      skewType: surface.skewType.name(),
      sabr: surface.sabr,
      sabrBeta: surface.sabrBeta,
      sabrShift: surface.sabrShift,
      capletValuationModel: surface.capletValuationModel.name(),
      nodes: [], // as in surface
      skewNodes: [], // as in surface
      capletVolatilities: [], // as in surface
      versionForm: NewVersionFormV2.builder().validFrom(surface.validFrom).build()
      )

    when:
    def result = repository.updateSurface(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    loaded[0].valueEquals(surface)
  }

  def "should archive surface"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    when:
    def form = new ArchiveEntityForm(NewVersionFormV2.newDefault())
    def result = repository.archiveSurface(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      form
      )

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)

    loaded.size() == 2
    // New ARCHIVED major version
    assertAutofilledFields(loaded[0])
    loaded[0].state == State.ARCHIVED
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment

    // Initial version
    loaded[1].valueEquals(surface)
  }

  def "should DELETE surface"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(surface)

    when:
    def result = repository.deleteSurface(surface.curveGroupId, surface.entityId, surface.validFrom)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    loaded[0].validFrom == surface.validFrom
    loaded[0].state == State.DELETED

    loaded[1].validFrom == surface.validFrom
    loaded[1].state == State.ACTIVE
  }

  def "should fail delete surface with status DELETED"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .state(State.DELETED)
      .build()
    operations.insert(surface)

    when:
    def result = repository.deleteSurface(surface.curveGroupId, surface.entityId, surface.validFrom)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED


    def loaded = allSortedValidFromDesc(surface.entityId)
    loaded.size() == 1

    loaded[0].validFrom == surface.validFrom
    loaded[0].state == State.DELETED
  }

  def "should load #filter surface views"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def archivedSurface = new VolatilitySurfaceBuilder().curveGroupId(group.id).state(State.ARCHIVED).build()
    operations.insert(archivedSurface)
    def activeSurface = new VolatilitySurfaceBuilder().curveGroupId(group.id).state(State.ACTIVE).build()
    operations.insert(activeSurface)
    def deletedSurface = new VolatilitySurfaceBuilder().curveGroupId(group.id).state(State.DELETED).build()
    operations.insert(deletedSurface)

    when:
    def loaded = repository.getSurfaceViews(group.id, now(), filter, emptyTableFilter())

    then:
    loaded.size() == 1

    where:
    filter << [archived(), active()]
  }

  def "should load sorted surfaces"() {
    setup:
    def sort = Sort.by("name")
    def stateDate = BitemporalDate.newOfNow()
    def surface = surfaceWith({ c -> c.name("EUR 3M Vols") })
    def surface2 = surfaceWith({ c -> c.name("GBP 6M Vols") })
    def surface3 = surfaceWith({ c -> c.name("CHF 3M Vols") })
    operations.insertAll([surface, surface2, surface3])

    when:
    def loaded = repository.getSurfaceViews(surface.curveGroupId, stateDate, active(), emptyTableFilter(), sort)

    then:
    loaded.size() == 3
    loaded.name == ["CHF 3M Vols", "EUR 3M Vols", "GBP 6M Vols"]
  }

  @Unroll
  def "should load surfaces of latest version with table filter #tableFilter"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface1v1 = new VolatilitySurfaceBuilder()
      .name("EUR 3M Vols")
      .curveGroupId(group.id)
      .build()
    def surface1v2 = new VolatilitySurfaceBuilder()
      .entityId(surface1v1.entityId)
      .curveGroupId(surface1v1.curveGroupId)
      .name(surface1v1.name)
      .validFrom(parse("2017-12-31"))
      .XInterpolator(CurveInterpolators.TIME_SQUARE.name)
      .YInterpolator(CurveInterpolators.TIME_SQUARE.name)
      .build()
    def surface2 = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .name("AED 3M Vols")
      .XInterpolator(CurveInterpolators.TIME_SQUARE.name)
      .YInterpolator(CurveInterpolators.TIME_SQUARE.name)
      .build()
    operations.insertAll([surface1v1, surface1v2, surface2])

    when:
    def loaded = repository.getSurfaceViews(
      group.id,
      parse("2018-01-01"),
      active(),
      tableFilter
      )

    then:
    loaded.size() == expectedResults
    loaded[0].xInterpolator == CurveInterpolators.TIME_SQUARE.name
    loaded[0].yInterpolator == CurveInterpolators.TIME_SQUARE.name

    where:
    tableFilter                                                                       | expectedResults
    emptyTableFilter()                                                                | 2
    new TableFilter([new SimpleFilterClause("name", FilterOperation.EQUAL, "EUR 3M Vols")]) | 1
  }

  def "should get (latest) active surface view"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surfaceVersion1 = new VolatilitySurfaceBuilder()
      .curveGroupId(group.id)
      .recordDate(STATE_DATE.recordDate.minusSeconds(2))
      .build()
    operations.insert(surfaceVersion1)

    def surfaceVersion2 = new VolatilitySurfaceBuilder()
      .entityId(surfaceVersion1.entityId)
      .XInterpolator("PiecewiseCubicHermiteMonotonicity")
      .YInterpolator("TimeSquare")
      .sabr(true)
      .sabrShift(0.0333)
      .sabrBeta(0.0444)
      .skewType(VolatilitySurfaceType.MONEYNESS)
      .recordDate(STATE_DATE.recordDate.minusSeconds(1))
      .build()
    operations.insert(surfaceVersion2)

    def archivedCurve = new CreditCurveBuilder()
      .state(State.ARCHIVED)
      .curveGroupId(group.id)
      .build()
    operations.insert(archivedCurve)

    when:
    def loaded = repository.getActiveSurfaceView(
      surfaceVersion1.curveGroupId,
      surfaceVersion1.entityId,
      STATE_DATE.actualDate
      )

    then:
    loaded.isRight()
    with(loaded.getOrNull()) {
      entityId == surfaceVersion2.entityId
      validFrom == surfaceVersion2.validFrom
      comment == surfaceVersion2.comment
      recordDate != null
      state == surfaceVersion2.state
      modifiedBy == surfaceVersion2.modifiedBy.name
      modifiedAt != null
      name == surfaceVersion2.name
      curveGroupId == surfaceVersion2.curveGroupId
      XInterpolator == surfaceVersion2.XInterpolator
      XExtrapolatorLeft == surfaceVersion2.XExtrapolatorLeft
      XExtrapolatorRight == surfaceVersion2.XExtrapolatorRight
      YInterpolator == surfaceVersion2.YInterpolator
      YExtrapolatorLeft == surfaceVersion2.YExtrapolatorLeft
      YExtrapolatorRight == surfaceVersion2.YExtrapolatorRight
      sabr == surfaceVersion2.sabr
      sabrBeta == surfaceVersion2.sabrBeta
      sabrShift == surfaceVersion2.sabrShift
      skewType == surfaceVersion2.skewType
      capletValuationModel == surfaceVersion2.capletValuationModel
      numberOfSwaptionVolatilityNodes == surfaceVersion2.nodes.size()
      numberOfCapletVolatilityNodes == surfaceVersion2.capletVolatilities.size()
      numberOfSkewNodes == surfaceVersion2.skewNodes.size()
      valuationModel == SwaptionValuationModel.NORMAL.name()
    }
  }

  def "should return error if no active surface view"() {
    when:
    def loaded = repository.getActiveSurfaceView("groupId", "surfaceId", now())

    then:
    loaded.isLeft()
    def error = loaded.left().get() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND
  }

  def "should load version views"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder().curveGroupId(group.id).build()
    operations.insert(surface)

    def surfaceInFuture = new VolatilitySurfaceBuilder()
      .entityId(surface.entityId)
      .state(State.DELETED)
      .validFrom(FUTURE_VALID_FROM)
      .curveGroupId(surface.curveGroupId)
      .build()
    operations.insert(surfaceInFuture)

    when:
    def result = repository.getSurfaceVersionViews(group.id, surface.entityId)

    then:
    result.size() == 2

    // Future DELETED major version
    result[0].entityId == surfaceInFuture.entityId
    result[0].validFrom == surfaceInFuture.validFrom
    result[0].state == State.DELETED

    // Initial ROOT version
    result[1].entityId == surface.entityId
    result[1].validFrom == surface.validFrom
    result[1].state == State.ACTIVE
  }

  def "should load active surface"() {
    setup:
    def surface = operations.insert(surface())

    when:
    def result = repository.getActiveSurface(surface.curveGroupId, surface.entityId, now())

    then:
    result.isRight()

    def loadedSurface = result.right().get() as VolatilitySurface
    loadedSurface.entityId == surface.entityId
    loadedSurface.validFrom == surface.validFrom
    loadedSurface.valueEquals(surface)
  }

  def "should load active surfaces"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def activeSurface = new VolatilitySurfaceBuilder().curveGroupId(group.id).build()
    def archivedSurface = new VolatilitySurfaceBuilder().state(State.ARCHIVED).curveGroupId(group.id).build()
    operations.insertAll([activeSurface, archivedSurface])

    when:
    def result = repository.getActiveSurfaces(group.id, BitemporalDate.newOf(archivedSurface.validFrom))

    then:
    result.size() == 1

    result.get(0).entityId == activeSurface.entityId
    result.get(0).validFrom == activeSurface.validFrom
    result.get(0).valueEquals(activeSurface)
  }


  def "should load surface future versions dates list"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def surface = new VolatilitySurfaceBuilder().curveGroupId(group.id).build()
    def futureSurface = new VolatilitySurfaceBuilder()
      .entityId(surface.entityId)
      .state(State.ARCHIVED)
      .validFrom(FUTURE_VALID_FROM)
      .curveGroupId(surface.curveGroupId)
      .build()
    operations.insertAll([surface, futureSurface])

    when:
    def search = new VolatilitySurfaceSearch(surface.name, surface.validFrom)
    def result = repository.getFutureVersions(group.id, search)

    then:
    result.dates == [futureSurface.validFrom]
  }

  def "should load volatility surface node views"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .nodes([new VolatilitySurfaceNode(tenor: "1Y", expiry: "2Y")])
      .build()
    operations.insert(surface)

    when:
    def versionedResult = repository.getSurfaceNodesViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom
      )

    then:
    versionedResult.versionDate == surface.validFrom

    versionedResult.list.size() == 1
    versionedResult.list[0].tenor == "1Y"
    versionedResult.list[0].expiry == "2Y"
    versionedResult.list[0].key == "2YV1Y_ATM_EUR-EURIBOR-3M"
  }

  def "should load volatility surface node values"() {
    setup:
    def marketDataVolatilities = [
      "2YV1Y_ATM_EUR-EURIBOR-3M": new CalculationMarketValueView(key: "2YV1Y_ATM_EUR-EURIBOR-3M", value: BigDecimal.TEN)
    ]

    def surface = new VolatilitySurfaceBuilder()
      .nodes([new VolatilitySurfaceNode(tenor: "1Y", expiry: "2Y")])
      .build()
    operations.insert(surface)

    when:
    def versionedResult = repository.getSurfaceNodesValuesViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      marketDataVolatilities
      )

    then:
    versionedResult.versionDate == surface.validFrom

    versionedResult.list.size() == 1
    versionedResult.list[0].value == 10d
    versionedResult.list[0].column == "1Y"
    versionedResult.list[0].expiry == "2Y"
  }

  def "should load caplet volatility nodes"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .capletVolatilities([new CapletVolatilityNode(tenor: "1Y")])
      .build()
    operations.insert(surface)

    when:
    def versionedResult = repository.getSurfaceCapletNodesViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom
      )

    then:
    versionedResult.versionDate == surface.validFrom

    versionedResult.list.size() == 1
    versionedResult.list[0].tenor == "1Y"
  }

  def "should load caplet volatility node values"() {
    setup:
    def marketDataVolatilities = [
      "1Y_55.00%_CF_EUR-EURIBOR-3M": new CalculationMarketValueView(key: "1Y_55.00%_CF_EUR-EURIBOR-3M", value: BigDecimal.TEN)
    ]

    def surface = new VolatilitySurfaceBuilder()
      .capletVolatilities([
        new CapletVolatilityNode(tenor: "1Y", strike: 0.55),
        new CapletVolatilityNode(tenor: "2Y", strike: 0.55)
      ])
      .build()
    operations.insert(surface)

    when:
    def versionedResult = repository.getSurfaceCapletNodesValuesViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      marketDataVolatilities
      )

    then:
    versionedResult.versionDate == surface.validFrom

    versionedResult.list.size() == 2

    versionedResult.list[0].strike == 0.55
    versionedResult.list[0].tenor == "1Y"
    versionedResult.list[0].value == 10d

    versionedResult.list[1].strike == 0.55
    versionedResult.list[1].tenor == "2Y"
    versionedResult.list[1].value == null
  }

  def "should load skews"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .skewNodes([VolatilitySurfaceSkew.of(1.0), VolatilitySurfaceSkew.of(2.0)])
      .build()
    operations.insert(surface)

    when:
    def result = repository.getSurfaceSkewsViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom
      )

    then:
    result.size() == 2

    result[0].surfaceSkewId == surface.skewNodes[0].surfaceSkewId
    result[0].skewValue == surface.skewNodes[0].skewValue

    result[1].surfaceSkewId == surface.skewNodes[1].surfaceSkewId
    result[1].skewValue == surface.skewNodes[1].skewValue
  }

  def "should load skew"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .skewNodes([VolatilitySurfaceSkew.of(1.0), VolatilitySurfaceSkew.of(2.0)])
      .build()
    operations.insert(surface)

    when:
    def result = repository.getSurfaceSkewView(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      surface.skewNodes[0].getSurfaceSkewId()
      )

    then:
    result.isRight()

    def skew = result.right().get() as VolatilitySurfaceSkewView
    skew.surfaceSkewId == surface.skewNodes[0].surfaceSkewId
    skew.skewValue == surface.skewNodes[0].skewValue
  }

  def "should load skew nodes views"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .nodes([new VolatilitySurfaceNode(tenor: "1Y", expiry: "2Y")])
      .skewNodes([VolatilitySurfaceSkew.of(0.01)])
      .build()
    operations.insert(surface)

    when:
    def versionedList = repository.getSurfaceSkewNodesViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      surface.skewNodes[0].surfaceSkewId
      )

    then:
    versionedList.versionDate == surface.validFrom

    versionedList.list.size() == 1
    versionedList.list[0].key == "2YV1Y_1%_EUR-EURIBOR-3M"
  }

  def "should load skews node values"() {
    setup:
    def marketDataVolatilities = [
      "2YV1Y_1%_EUR-EURIBOR-3M": new CalculationMarketValueView(key: "2YV1Y_1%_EUR-EURIBOR-3M", value: 0.1)
    ]

    def surface = new VolatilitySurfaceBuilder()
      .nodes([new VolatilitySurfaceNode(tenor: "1Y", expiry: "2Y")])
      .skewNodes([VolatilitySurfaceSkew.of(0.01)])
      .build()
    operations.insert(surface)

    when:
    def versionedList = repository.getSurfaceSkewNodesValueViews(
      surface.curveGroupId,
      surface.entityId,
      surface.validFrom,
      surface.skewNodes[0].surfaceSkewId,
      marketDataVolatilities)

    then:
    versionedList.versionDate == surface.validFrom

    versionedList.list.size() == 1
    versionedList.list[0].value == 0.1
    versionedList.list[0].column == "1Y"
    versionedList.list[0].expiry == "2Y"
  }

  private List<VolatilitySurface> allSortedValidFromDesc(String entityId) {
    operations
      .query(VolatilitySurface).matching(query(where("entityId").is(entityId))
      .with(Sort.by(DESC, "validFrom", "recordDate")))
      .all()
  }

  def static assertUpdateFormFields(VolatilitySurface surface, VolatilitySurfaceUpdateForm form) {
    surface.XInterpolator == form.XInterpolator
    surface.XExtrapolatorLeft == form.XExtrapolatorLeft
    surface.XExtrapolatorRight == form.XExtrapolatorRight
    surface.YInterpolator == form.YInterpolator
    surface.YExtrapolatorLeft == form.YExtrapolatorLeft
    surface.YExtrapolatorRight == form.YExtrapolatorRight
    surface.sabr == form.sabr
    surface.sabrBeta == form.sabrBeta
    surface.sabrShift == form.sabrShift
    surface.skewType.name() == form.skewType
    surface.capletValuationModel.name() == form.capletValuationModel
    surface.nodes.size() == (form.nodes == null ? 0 : form.nodes.size())
    surface.capletVolatilities.size() == (form.capletVolatilities == null ? 0 : form.capletVolatilities.size())
    surface.skewNodes.size() == (form.skewNodes == null ? 0 : form.skewNodes.size())
  }

  def assertAutofilledFields(VolatilitySurface surface) {
    surface.id != null
    surface.recordDate != null
    surface.modifiedAt != null
    surface.modifiedBy != null
    surface.modifiedBy.name == user.name
  }
}
