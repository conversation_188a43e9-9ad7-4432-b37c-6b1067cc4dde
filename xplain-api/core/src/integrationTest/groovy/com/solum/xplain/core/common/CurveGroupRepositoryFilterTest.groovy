package com.solum.xplain.core.common

import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.shared.utils.filter.FilterOperation.BETWEEN
import static com.solum.xplain.shared.utils.filter.FilterOperation.EQUAL

import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationMarketData
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupFilter
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroupBuilder
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupRepositoryFilterTest extends IntegrationSpecification {

  @Resource
  CurveGroupRepository repository
  @Resource
  MongoOperations operations

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), CurveGroup)
  }

  def "should filter curve group by name"() {
    setup:
    def group = curveGroup()
    operations.insert(group)
    def filter = new CurveGroupFilter()
    when:
    def loadedEq = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      new TableFilter([new SimpleFilterClause("name", EQUAL, group.name)]),
      filter)
    def loadedNotEq = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      new TableFilter([new SimpleFilterClause("name", EQUAL, group.name + "2")]), filter)
    then:
    loadedEq.lastRow == 1
    loadedNotEq.lastRow == null
  }

  def "should filter curve group by calibrationMarketDataGroupName"() {
    setup:
    def marketData = new MarketDataGroupBuilder().build()
    operations.insert(marketData)
    def group = new CurveGroupBuilder().calibrationMarketDataGroup(new CalibrationMarketData(marketData, MarketDataSourceType.RAW_PRIMARY, "configId")).build()
    operations.insert(group)
    def filter = new CurveGroupFilter()
    when:
    def loadedEq = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      new TableFilter([new SimpleFilterClause("marketDataGroupName", EQUAL, marketData.name)]),
      filter)
    then:
    loadedEq.lastRow == null
  }

  def "should filter curve group by calibrationDate"() {
    setup:
    def group = new CurveGroupBuilder().calibrationDate(LocalDate.parse("2017-01-01")).build()
    operations.insert(group)
    def filter = new CurveGroupFilter()
    when:
    def loadedEq = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      new TableFilter([new SimpleFilterClause("calibrationDate", EQUAL, "2017-01-01")]),
      filter)
    then:
    loadedEq.lastRow == 1
  }

  def "should filter curve group by calibrationDate with between operation"() {
    setup:
    def group = new CurveGroupBuilder().calibrationDate(LocalDate.parse("2017-01-01")).build()
    operations.insert(group)
    def filter = new CurveGroupFilter()
    when:
    def loadedEq = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      new TableFilter([new SimpleFilterClause("calibrationDate", BETWEEN, ["2016-01-01", "2017-01-02"])]),
      filter)
    then:
    loadedEq.lastRow == 1
  }
}
