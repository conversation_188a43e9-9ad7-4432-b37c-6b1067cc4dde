package com.solum.xplain.core.ipv.data

import static com.solum.xplain.shared.utils.filter.FilterOperation.EQUAL
import static com.solum.xplain.shared.utils.filter.FilterOperation.GREATER_THAN
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.ArchiveForm
import com.solum.xplain.core.common.value.NewMinorVersionForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.datavalue.DataValueUpdateResolver
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.data.entity.IpvDataValue
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm
import com.solum.xplain.core.ipv.data.repository.IpvResolvedDataProviderValueWithEntityNavViewRepository
import com.solum.xplain.core.ipv.data.repository.IpvResolvedDataProviderValueWithTradeNavViewRepository
import com.solum.xplain.core.ipv.data.value.ExceptionManagementSettingsProvider
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup
import com.solum.xplain.core.ipv.nav.entity.CompanyLegalEntityNav
import com.solum.xplain.core.ipv.nav.entity.NavVersion
import com.solum.xplain.core.teams.Team
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class IpvDataRepositoryTest extends IntegrationSpecification {

  static BitemporalDate STATE_DATE = BitemporalDate.newOfNow()

  static final VALUATION_DATE = LocalDate.of(2021, 1, 1)
  static final VALUATION_DATE_ANOTHER = LocalDate.of(2021, 2, 2)

  static final KEY = "EXT_COMPANY_ID_EXT_ENTITY_ID_EXT_PORTFOLIO_ID_EXT_TRADE_ID"
  static final KEY_ANOTHER = "EXT_COMPANY_ID_EXT_ENTITY_ID_EXT_PORTFOLIO_ID_EXT_TRADE_ID_ANOTHER"

  static final ENTITY_LEVEL = "ENTITY_LEVEL"
  static final TRADE_LEVEL = "TRADE_LEVEL"

  static final PROVIDER = "PROVIDER"
  static final PROVIDER_ANOTHER = "PROVIDER_ANOTHER"
  static final PROVIDER_ARCHIVED = "PROVIDER_ARCHIVED"
  static final NAV_PROVIDER = "NAV"

  static final VALUE = new BigDecimal("1.0")
  static final VALUE_ANOTHER = new BigDecimal("10.0")

  static final DELTA = new BigDecimal("2.0")
  static final DELTA_ANOTHER = new BigDecimal("20.0")

  static final VEGA = new BigDecimal("3.0")
  static final VEGA_ANOTHER = new BigDecimal("30.0")

  static final GAMMA = new BigDecimal("4.0")
  static final GAMMA_ANOTHER = new BigDecimal("40.0")

  static final THETA = new BigDecimal("5.0")
  static final THETA_ANOTHER = new BigDecimal("50.0")

  static final RHO = new BigDecimal("6.0")
  static final RHO_ANOTHER = new BigDecimal("60.0")

  static final PAR_RATE = new BigDecimal("7.0")
  static final PAR_RATE_ANOTHER = new BigDecimal("70.0")

  static final SPOT_RATE = new BigDecimal("8.0")
  static final SPOT_RATE_ANOTHER = new BigDecimal("80.0")

  static final IMPLIED_VOL = new BigDecimal("9.0")
  static final IMPLIED_VOL_ANOTHER = new BigDecimal("90.0")

  static final ATM_IMPLIED_VOL = new BigDecimal("10.0")
  static final ATM_IMPLIED_VOL_ANOTHER = new BigDecimal("100.0")

  static final REALISED_VOL = new BigDecimal("11.0")
  static final REALISED_VOL_ANOTHER = new BigDecimal("110.0")

  static final FAIR_VOL = new BigDecimal("12.0")
  static final FAIR_VOL_ANOTHER = new BigDecimal("120.0")

  static final CURRENCY = "USD"
  static final CURRENCY_ANOTHER = "EUR"

  static final COMMENT = "COMMENT"
  static final COMMENT_ANOTHER = "COMMENT ANOTHER"

  @SpringBean
  IpvDataModificationHandler modificationHandler = Mock()

  @SpringBean
  ExceptionManagementSettingsProvider exceptionManagementSettingsProvider = Mock()

  @Resource
  AuthenticationContext userRepository

  @Resource
  MongoOperations operations

  @Resource
  IpvDataRepository repository

  @Resource
  IpvResolvedDataProviderValueWithTradeNavViewRepository resolvedTradeNavViewRepository

  @Resource
  IpvResolvedDataProviderValueWithEntityNavViewRepository resolvedEntityNavViewRepository

  @Resource
  IpvDataViews views

  Team team
  XplainPrincipal ownerUser

  def setup() {
    team = new TeamBuilder().id(new ObjectId()).name("Test team").build()

    ownerUser = UserBuilder.userWithNameAndTeams("ownerId", "Owner Name", [team.getId()])

    def auth = new TestingAuthenticationToken(ownerUser, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth

    views.createViews()
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), IpvDataGroup)
    operations.remove(new Query(), IpvDataValue)
    operations.remove(new Query(), CompanyLegalEntityNav)

    views.dropViews()
  }

  def "should create value"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    when:
    def form = new IpvDataProviderValueForm(
      date: VALUATION_DATE,
      key: KEY,
      provider: PROVIDER,
      value: VALUE,
      delta: DELTA,
      vega: VEGA,
      versionForm: NewMinorVersionForm.of(COMMENT)
      )
    def result = repository.createValue(group.id, form)

    then:
    def loaded = operations.findAll(IpvDataValue)
    loaded.size() == 1

    loaded[0].id == result.id
    loaded[0].groupId == group.id
    loaded[0].date == VALUATION_DATE
    loaded[0].key == KEY
    loaded[0].provider == PROVIDER
    loaded[0].resolved == null
    loaded[0].archivedAt == null
    loaded[0].values.size() == 1
    loaded[0].values[0].recordDate != null
    loaded[0].values[0].modifiedBy.username == ownerUser.name
    loaded[0].values[0].comment == COMMENT
    loaded[0].values[0].value == VALUE
    loaded[0].values[0].delta == DELTA
    loaded[0].values[0].vega == VEGA

    1 * modificationHandler.onIpvDataValueCreation(_)
  }

  def "should get values views"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })
    def pv_archived = operations.insert(archivedProviderValue(group.id).tap {
      it.resolved = true
    })
    def pv_another = operations.insert(anotherProviderValue(group.id).tap {
      it.resolved = true
    })

    def anotherKey_pv = operations.insert(providerValue(group.id).tap {
      it.key = KEY_ANOTHER
      it.resolved = null
    })
    operations.insert(archivedProviderValue(group.id).tap {
      it.key = KEY_ANOTHER
      it.resolved = false
    })
    operations.insert(anotherProviderValue(group.id).tap {
      it.key = KEY_ANOTHER
      it.resolved = false
    })


    def scrollRequest = ScrollRequest.of(0, 1, Sort.by("key", "provider"))
    def filter
    def tableFilter
    def result

    when: "not archived / resolved / not filtered"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)
    tableFilter = emptyTableFilter()
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv)

    when: "not archived / unresolved / not filtered"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, false, false)
    tableFilter = emptyTableFilter()
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result.content.size() == 1

    assertProviderValueView(result.content[0], anotherKey_pv)

    when: "archived / resolved and unresolved / not filtered"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, true)
    tableFilter = emptyTableFilter()
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv_archived)

    when: "not archived / resolved and unresolved / filtered by key"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, false)
    tableFilter = new TableFilter([new SimpleFilterClause("key", EQUAL, KEY)])
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv)

    when: "not archived / resolved and unresolved / filtered by value"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, false)
    tableFilter = new TableFilter([new SimpleFilterClause("value", GREATER_THAN, VALUE.toString())])
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv_another)
  }

  def "should map trade-level NAV values"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })

    operations.insert(providerValue(group.id).tap {
      it.key = KEY
    })
    def nav = operations.insert(providerValue(group.id).tap {
      it.provider = NAV_PROVIDER
      it.key = navKey
    })

    def scrollRequest = ScrollRequest.of(0, 1, Sort.by("key", "provider"))
    def filter
    def tableFilter
    def result

    when: "IPV data has matching NAV"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)
    tableFilter = emptyTableFilter()
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 1
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv, nav.values[-1])

    where:
    navKey << [KEY]
  }

  def "should map entity-level NAV values"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })

    operations.insert(providerValue(group.id).tap {
      it.key = KEY
    })
    def nav = operations.insert(navMappedProviderValue(group.id).tap {
      it.key = navKey
    })

    def scrollRequest = ScrollRequest.of(0, 1, Sort.by("key"))
    def filter
    def tableFilter
    def result

    when: "IPV data has matching NAV"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)
    tableFilter = emptyTableFilter()
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> ENTITY_LEVEL
    result = repository.getValueViews(group.id, filter, tableFilter, scrollRequest)

    then:
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 1
    result.content.size() == 1

    assertProviderValueView(result.content[0], pv)

    where:
    navKey << [KEY]
  }

  def "should retrieve provider values excluding NAV and only NAV values haha"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def providerValue1 = operations.insert(providerValue(group.id))
    def providerValue2 = operations.insert(anotherProviderValue(group.id))

    providerValue1.provider = provider1Type
    providerValue2.provider = provider2Type
    operations.save(providerValue1)
    operations.save(providerValue2)

    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, false)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("key"))

    when: "Fetching values excluding NAV"
    def resultExcludingNav = repository.getValueViews(group.id, filter, emptyTableFilter(), scrollRequest)

    then: "Check non-NAV values are returned correctly"
    resultExcludingNav.content.size() == expectedNonNavSize

    if (expectedNonNavSize > 0) {
      resultExcludingNav.content.every { it.provider != IpvDataType.NAV.toString() }
    }

    when: "Fetching only NAV values"
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> { TRADE_LEVEL }
    def resultNavOnly = repository.getNavValueViews(group.id, filter, emptyTableFilter(), scrollRequest)

    then: "Check NAV values are returned correctly"
    resultNavOnly.content.size() == expectedNavSize

    where:
    provider1Type      | provider2Type       | expectedNonNavSize | expectedNavSize
    PROVIDER           | IpvDataType.NAV     | 1                  | 1       // One of each
    IpvDataType.NAV    | IpvDataType.NAV     | 0                  | 2       // Both NAV
    PROVIDER           | PROVIDER            | 2                  | 0       // None NAV
  }

  def "should handle different nav levels correctly in getNavValueViews"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def navValue1 = operations.insert(navMappedProviderValue(group.id))
    def navValue2 = operations.insert(providerValue(group.id))
    def navValue3 = operations.insert(anotherProviderValue(group.id))

    navValue2.provider = IpvDataType.NAV
    navValue3.provider = IpvDataType.NAV
    operations.save(navValue1)
    operations.save(navValue2)
    operations.save(navValue3)

    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, false)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("key"))

    when:
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> { navLevel }
    def result = repository.getNavValueViews(group.id, filter, emptyTableFilter(), scrollRequest)

    then:
    result.content.size() == expectedSize

    where:
    navLevel       | expectedSize
    ENTITY_LEVEL   | 1
    TRADE_LEVEL    | 2
  }

  def "should get value views stream"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })
    operations.insert(archivedProviderValue(group.id).tap {
      it.resolved = true
    })

    def anotherKey_pv = operations.insert(providerValue(group.id).tap {
      it.key = KEY_ANOTHER
      it.resolved = false
    })
    operations.insert(archivedProviderValue(group.id).tap {
      it.key = KEY_ANOTHER
      it.resolved = false
    })

    def nav_pv = operations.insert(navMappedProviderValue(group.id).tap {
      it.resolved = false
    })

    def filter
    def result

    when: "resolved"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViewsStream(group.id, filter, IpvDataType.ALL).toList()

    then:
    result.size() == 1
    assertProviderValueView(result[0], pv)

    when: "unresolved"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, false, false)
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViewsStream(group.id, filter, IpvDataType.ALL).toList()

    then:
    result.size() == 1
    assertProviderValueView(result[0], anotherKey_pv)

    when: "resolved and unresolved"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, false)
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViewsStream(group.id, filter, IpvDataType.ALL).toList()

    then:
    result.size() == 2
    assertProviderValueView(result[0], pv)
    assertProviderValueView(result[1], anotherKey_pv)

    when: "another date"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE_ANOTHER, null, false)
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViewsStream(group.id, filter, IpvDataType.ALL).toList()

    then:
    result.size() == 0
  }

  def "should fetch from correct collection based on nav level for NAV data"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def navValue1 = operations.insert(navMappedProviderValue(group.id))
    def navValue2 = operations.insert(providerValue(group.id))
    def navValue3 = operations.insert(anotherProviderValue(group.id))

    navValue2.provider = IpvDataType.NAV
    navValue3.provider = IpvDataType.NAV
    operations.save(navValue1)
    operations.save(navValue2)
    operations.save(navValue3)

    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, false, false)

    when:
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> { navLevel }
    def resultStream = repository.getValueViewsStream(group.id, filter, IpvDataType.NAV)
    def resultList = resultStream.toList()

    then:
    resultList.size() == expectedSize

    where:
    navLevel       | expectedSize
    ENTITY_LEVEL   | 1
    TRADE_LEVEL    | 2
  }

  def "should fetch from correct collection based on nav level for NAV data when resolved is true"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })
    operations.insert(providerValue(group.id).tap {
      it.key = KEY
    })
    operations.insert(navMappedProviderValue(group.id).tap {
      it.key = KEY
    })
    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)

    when:
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> { navLevel }
    def resultStream = repository.getValueViewsStream(group.id, filter, IpvDataType.NAV)
    def resultList = resultStream.toList()

    then:
    resultList.size() == expectedSize

    where:
    navLevel       | expectedSize
    ENTITY_LEVEL   | 1
    TRADE_LEVEL    | 1
  }

  def "should fetch Ipv data from correct collection regardless of nav level"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def navValue1 = operations.insert(providerValue(group.id))
    def Value2 = operations.insert(providerValue(group.id))
    def Value3 = operations.insert(anotherProviderValue(group.id))

    navValue1.provider = IpvDataType.NAV
    operations.save(navValue1)
    operations.save(Value2)
    operations.save(Value3)

    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, false, false)

    when:
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> { navLevel }
    def resultStream = repository.getValueViewsStream(group.id, filter, IpvDataType.ALL)
    def resultList = resultStream.toList()

    then:
    resultList.size() == expectedSize

    where:
    navLevel       | expectedSize
    ENTITY_LEVEL   | 3
    TRADE_LEVEL    | 3
  }

  def "should map trade-level and company-level NAV values when streaming"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id).tap {
      it.resolved = true
    })

    operations.insert(providerValue(group.id).tap {
      it.key = KEY
    })
    def nav = operations.insert(providerValue(group.id).tap {
      it.provider = NAV_PROVIDER
      it.key = navKey
    })

    def filter
    def result

    when: "IPV data has matching NAV"
    filter = new IpvDataProviderValueFilter(VALUATION_DATE, true, false)
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> TRADE_LEVEL
    result = repository.getValueViewsStream(group.id, filter, IpvDataType.EXCLUDE_NAV).toList()

    then:
    result.size() == 1

    assertProviderValueView(result[0], pv, nav.values[-1])

    where:
    navKey << [KEY]
  }

  def "should get first available data"() {
    setup:
    def groupId = "groupId"
    def pv = operations.insert(providerValue(groupId))
    operations.insert(providerValue(groupId).tap {
      it.date = it.date.plusDays(1)
    })
    operations.insert(providerValue(groupId).tap {
      it.key = KEY_ANOTHER
    })
    operations.insert(archivedProviderValue(groupId))
    operations.insert(anotherProviderValue(groupId).tap {
      it.date = it.date.minusDays(1)
    })
    operations.insert(providerValue("anotherGroupId").tap {
      it.date = it.date.minusDays(1)
    })

    when:
    def result = repository.getValueAtDate(groupId, KEY, PROVIDER, VALUATION_DATE)

    then:
    result.isRight()
    assertProviderValueView(result.getOrNull(), pv)
  }

  def "should get first available NAV data"() {
    setup:
    def groupId = "groupId"
    def nav = operations.insert(providerValue(groupId).tap {
      it.resolved = true
      it.key = navKey
    })
    operations.insert(navMappedProviderValue(groupId).tap {
      it.date = it.date.plusDays(1)
      it.resolved = true
      it.key = navKey
    })
    operations.insert(navMappedProviderValue(groupId).tap {
      it.key = KEY_ANOTHER
    })
    operations.insert(archivedProviderValue(groupId).tap {
      it.provider = NAV_PROVIDER
      it.key = navKey
    })
    operations.insert(anotherProviderValue(groupId).tap {
      it.date = it.date.minusDays(1)
      it.provider = NAV_PROVIDER
      it.key = navKey
    })
    operations.insert(navMappedProviderValue("anotherGroupId").tap {
      it.date = it.date.minusDays(1)
      it.key = navKey
    })

    when:
    exceptionManagementSettingsProvider.getNavLevel(_ as BitemporalDate) >> ENTITY_LEVEL
    def result = repository.getValueAtDate(groupId, KEY, NAV_PROVIDER, VALUATION_DATE)

    then:
    result.isRight()
    verifyAll(result.getOrNull()) {
      id == nav.id
      groupId == groupId
      date == VALUATION_DATE
      key == KEY
      comment == nav.values[-1].comment
      value == nav.values[-1].value
    }

    where:
    navKey << [KEY]
  }

  def "should get value view"() {
    setup:
    def group = operations.insert(new IpvDataGroup())
    def pv_another = operations.insert(anotherProviderValue(group.id))

    when:
    def result = repository.getValueView(group.id, pv_another.id, STATE_DATE)

    then:
    result.isRight()
    assertProviderValueView(result.getOrNull(), pv_another)
  }

  def "should fail to get archived value view"() {
    setup:
    def group = new IpvDataGroup()
    operations.insert(group)

    def pv_archived = operations.insert(archivedProviderValue(group.id))

    when:
    def result = repository.getValueView(group.id, pv_archived.id, STATE_DATE)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND
    error.description == "VD value not found with id " + pv_archived.id
  }

  def "should get latest ipv date"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    operations.insert(providerValue(group.id))
    operations.insert(archivedProviderValue(group.id).tap {
      date = VALUATION_DATE.plusDays(1)
    })
    operations.insert(providerValue(group.id).tap {
      date = VALUATION_DATE_ANOTHER
    })

    when:
    def result = repository.getLatestIpvDataDate(group.id, VALUATION_DATE.plusDays(1), STATE_DATE)

    then:
    result.isPresent()
    result.get() == VALUATION_DATE
  }

  def "should return values count for date"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    operations.insert(providerValue(group.id))
    operations.insert(archivedProviderValue(group.id))
    operations.insert(providerValue(group.id).tap {
      date = VALUATION_DATE_ANOTHER
    })

    expect:
    repository.getValuesCount(
      group.id,
      BitemporalDate.newOf(VALUATION_DATE, STATE_DATE.recordDate)
      ) == 1L
  }

  def "should update value"() {
    setup:
    def group = new IpvDataGroup()
    operations.insert(group)

    def pv = operations.insert(providerValue(group.id))

    when:
    def form = new IpvDataProviderValueUpdateForm(
      value: VALUE_ANOTHER,
      delta: DELTA_ANOTHER,
      vega: VEGA_ANOTHER,
      versionForm: NewMinorVersionForm.of(COMMENT_ANOTHER)
      )
    def result = repository.updateValue(pv.id, form)

    then:
    result.isRight()
    result.getOrNull().id == pv.id

    def updated = operations.findById(pv.id, IpvDataValue)
    updated.id == result.getOrNull().id
    updated.groupId == group.id
    updated.date == pv.date
    updated.key == pv.key
    updated.provider == pv.provider
    updated.resolved == pv.resolved
    updated.archivedAt == pv.archivedAt
    updated.archivalComment == pv.archivalComment
    updated.archivedBy == pv.archivedBy


    updated.values.size() == 2
    updated.values[0].valueEquals(pv.values[0])
    updated.values[1].recordDate != null
    updated.values[1].modifiedBy.username == ownerUser.name
    updated.values[1].value == VALUE_ANOTHER
    updated.values[1].delta == DELTA_ANOTHER
    updated.values[1].vega == VEGA_ANOTHER
    updated.values[1].comment == COMMENT_ANOTHER

    0 * modificationHandler._
  }

  def "should fail to update archived value"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv_archived = operations.insert(archivedProviderValue(group.id))

    when:
    def form = new IpvDataProviderValueUpdateForm(
      value: VALUE_ANOTHER,
      delta: DELTA_ANOTHER,
      vega: VEGA_ANOTHER,
      versionForm: NewMinorVersionForm.of(COMMENT_ANOTHER)
      )
    def result = repository.updateValue(pv_archived.id, form)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND

    0 * modificationHandler._
  }

  def "should archive value"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv = operations.insert(providerValue(group.id))

    when:
    def result = repository.archiveValue(
      pv.id,
      new ArchiveForm(COMMENT_ANOTHER)
      )

    then:
    result.isRight()
    result.getOrNull().id == pv.id

    def archived = operations.findById(pv.id, IpvDataValue)
    archived.id == result.getOrNull().id
    archived.groupId == group.id
    archived.date == pv.date
    archived.key == pv.key
    archived.provider == pv.provider
    archived.resolved == pv.resolved
    archived.archivedAt != null
    archived.archivalComment == COMMENT_ANOTHER

    archived.values.size() == 1
    archived.values[0].valueEquals(pv.values[0])

    0 * modificationHandler._
  }

  def "should fail to archive already archived value"() {
    setup:
    def group = operations.insert(new IpvDataGroup())

    def pv_archived = operations.insert(archivedProviderValue(group.id))

    when:
    def result = repository.archiveValue(
      pv_archived.id,
      new ArchiveForm(COMMENT_ANOTHER)
      )

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND

    0 * modificationHandler._
  }

  def "should save new entries for IPV Data value"() {
    setup:
    def ipvDataValue = new IpvDataValue()
    ipvDataValue.values = [new IpvDataValueVersion()]
    def entities = DataValueUpdateResolver.<IpvDataValueVersion, IpvDataValue> builder()
      .comment("DUMMY TEST")
      .entitiesToAppend([ipvDataValue])
      .build()

    when:
    var result = repository.updateForImport(entities)

    then:
    verifyAll(result) {
      archivedEntriesCount == 0
      replacedEntriesCount == 0
      newEntriesCount == 1
    }
  }

  def "should get global NAV level settings"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def expectedNavLevel = ENTITY_LEVEL
    exceptionManagementSettingsProvider.getNavLevel(stateDate) >> expectedNavLevel

    when:
    def result = repository.getGlobalNavLevelSettings(stateDate)

    then:
    result == expectedNavLevel
  }

  void assertProviderValueView(IpvDataProviderValueView view, IpvDataValue expectedValue, IpvDataValueVersion expectedNavValue = null) {
    verifyAll(view) {
      id == expectedValue.id
      groupId == expectedValue.groupId
      date == expectedValue.date
      key == expectedValue.key
      provider == expectedValue.provider
      comment == expectedValue.values[-1].comment
      value == expectedValue.values[-1].value
      delta == expectedValue.values[-1].delta
      vega == expectedValue.values[-1].vega
      gamma == expectedValue.values[-1].gamma
      theta == expectedValue.values[-1].theta
      rho == expectedValue.values[-1].rho
      parRate == expectedValue.values[-1].parRate
      spotRate == expectedValue.values[-1].spotRate
      impliedVol == expectedValue.values[-1].impliedVol
      atmImpliedVol == expectedValue.values[-1].atmImpliedVol
      realisedVol == expectedValue.values[-1].realisedVol
      fairVol == expectedValue.values[-1].fairVol
      navValue == expectedNavValue?.value
    }
  }

  static def providerValue(String groupId) {
    def v = new IpvDataValue()
    v.groupId = groupId
    v.date = VALUATION_DATE
    v.key = KEY
    v.provider = PROVIDER
    v.resolved = null
    v.values = [
      value(VALUE, DELTA, VEGA, GAMMA, THETA, RHO, PAR_RATE, SPOT_RATE, IMPLIED_VOL, ATM_IMPLIED_VOL, REALISED_VOL, FAIR_VOL, COMMENT)
    ]
    v
  }

  static def navMappedProviderValue(String groupId) {
    def v = new CompanyLegalEntityNav()
    v.groupId = groupId
    v.date = VALUATION_DATE
    v.key = KEY
    v.resolved = null
    v.values = [value(VALUE, CURRENCY, COMMENT)]
    v
  }

  static def value(BigDecimal value, String currency,
    String comment, LocalDateTime recordDate = STATE_DATE.recordDate.minusSeconds(1)) {
    def v = new NavVersion()
    v.value = value
    v.currency = currency
    v.comment = comment
    v.recordDate = recordDate
    v
  }

  static def anotherProviderValue(String groupId) {
    def v = providerValue(groupId)
    v.provider = PROVIDER_ANOTHER
    v.values.add(value(VALUE_ANOTHER, DELTA_ANOTHER, VEGA_ANOTHER,
      GAMMA_ANOTHER, THETA_ANOTHER, RHO_ANOTHER, PAR_RATE_ANOTHER, SPOT_RATE_ANOTHER, IMPLIED_VOL_ANOTHER, ATM_IMPLIED_VOL_ANOTHER, REALISED_VOL_ANOTHER, FAIR_VOL_ANOTHER,
      COMMENT_ANOTHER))
    v
  }

  static def archivedProviderValue(String groupId) {
    def v = providerValue(groupId)
    v.provider = PROVIDER_ARCHIVED
    v.archivedAt = STATE_DATE.getRecordDate().minusDays(1)
    v
  }

  static def value(BigDecimal value, BigDecimal delta, BigDecimal vega,
    BigDecimal gamma, BigDecimal theta, BigDecimal rho,
    BigDecimal parRate, BigDecimal spotRate,
    BigDecimal impliedVol, BigDecimal atmImpliedVol, BigDecimal realisedVol, BigDecimal fairVol,
    String comment, LocalDateTime recordDate = STATE_DATE.recordDate.minusSeconds(1)) {
    def v = new IpvDataValueVersion()
    v.value = value
    v.delta = delta
    v.vega = vega
    v.gamma = gamma
    v.theta = theta
    v.rho = rho
    v.parRate = parRate
    v.spotRate = spotRate
    v.impliedVol = impliedVol
    v.atmImpliedVol = atmImpliedVol
    v.realisedVol = realisedVol
    v.fairVol = fairVol
    v.comment = comment
    v.recordDate = recordDate
    v
  }
}
