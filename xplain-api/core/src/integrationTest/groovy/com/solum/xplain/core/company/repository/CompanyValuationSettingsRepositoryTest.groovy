package com.solum.xplain.core.company.repository

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.AllowedCompaniesForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.CompanySettingsType
import com.solum.xplain.core.company.csv.CompanyCsvForm
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings
import com.solum.xplain.core.company.entity.CompanyValuationSettings
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup
import com.solum.xplain.core.company.entity.ValuationSettingsNames
import com.solum.xplain.core.company.events.CompanyArchived
import com.solum.xplain.core.company.events.CompanyCreated
import com.solum.xplain.core.company.events.CompanyImported
import com.solum.xplain.core.company.form.ValuationSettingsForm
import com.solum.xplain.core.curveconfiguration.event.CurveConfigurationNameChanged
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.events.MarketDataGroupUpdated
import com.solum.xplain.core.market.value.MarketDataGroupForm
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CompanyValuationSettingsRepositoryTest extends IntegrationSpecification {

  def static VALID_FROM = parse("2020-01-01")
  def static FUTURE_VALID_FROM = parse("2100-01-01")

  def static COMPANY_ID = "companyId"
  def static COMPANY_EXT_ID = "companyExtId"

  def static CURVE_CONFIG_ENTITY = EntityReference.newOf("ccId", "ccName")
  def static MDG_ENTITY = EntityReference.newOf("mdId", "mdName")

  def static STATE_DATE = BitemporalDate.newOf(VALID_FROM)

  @Resource
  MongoOperations operations

  @Resource
  CompanyValuationSettingsRepository repository

  @Resource
  CompanyEntityValuationSettingsResolver companyEntityValuationSettingsResolver

  XplainPrincipal creator

  def setup() {
    creator = user("creatorId")
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), CompanyValuationSettings)
    operations.remove(new Query(), CompanyLegalEntityValuationSettings)
    operations.remove(new Query(), GlobalValuationSettings)
  }

  def "should get valuation settings"() {
    setup:
    def settings = operations.insert(companySettings())

    expect:
    def view = repository.getDefaultValuationSettings(settings.entityId, STATE_DATE)
    view.companyId == "companyId"
    view.settingsType == CompanySettingsType.BESPOKE
    view.marketDataGroupId == "marketDataGroupId"
    view.marketDataGroupName == "marketDataName"
    view.configurationType == "TYPE"
    view.curveConfigurationId == "id"
    view.curveConfigurationName == "name"
    view.nonFxCurveConfigurationId == "id1"
    view.nonFxCurveConfigurationName == "name1"
    view.strippingType == "STRIP"
    view.discountingType == "DISCOUNT"
    view.triangulationCcy == "TRIANGULATION"
    view.reportingCurrency == "REPORT"
    view.priceRequirements == bidRequirements()
    view.validFrom == VALID_FROM.minusYears(10)
  }

  def "should fallback and return global valuation settings"() {
    setup:
    operations.insert(globalSettings())
    operations.insert(CompanyValuationSettings.newOf("companyId"))
    expect:
    def view = repository.getDefaultValuationSettings("companyId", STATE_DATE)
    view.companyId == "companyId"
    view.settingsType == CompanySettingsType.DEFAULT
    view.marketDataGroupId == null
    view.marketDataGroupName == null
    view.configurationType == "type"
    view.curveConfigurationId == "ccId"
    view.curveConfigurationName == "ccName"
    view.nonFxCurveConfigurationId == "id2"
    view.nonFxCurveConfigurationName == "name2"
    view.strippingType == "OIS"
    view.discountingType == "type"
    view.triangulationCcy == "USD"
    view.reportingCurrency == "EUR"
    view.priceRequirements == bidRequirements()
    view.validFrom == LocalDate.ofEpochDay(0)
  }

  def "should get portfolio with default valuation settings"() {
    setup:
    operations.insert(companySettings())
    operations.insert(bespokeValuationSettings({
      c -> c
    }))
    operations.insert(bespokeValuationSettings({
      c -> c.validFrom = VALID_FROM.minusYears(10)
    }))
    operations.insert(defaultEntitySettings())

    expect:
    def list = repository.getCompanyEntitySettingsResolver("companyId", STATE_DATE).settings(["entityId1", "entityId"])
    list.size() == 2
    list[0].entityId == "entityId1"
    list[0].settingsType == CompanySettingsType.DEFAULT
    list[0].marketDataGroupId == "marketDataGroupId"
    list[0].marketDataGroupName == "marketDataName"
    list[0].configurationType == "TYPE"
    list[0].curveConfigurationId == "id"
    list[0].curveConfigurationName == "name"
    list[0].nonFxCurveConfigurationId == "id1"
    list[0].nonFxCurveConfigurationName == "name1"
    list[0].strippingType == "STRIP"
    list[0].discountingType == "DISCOUNT"
    list[0].triangulationCcy == "TRIANGULATION"
    list[0].reportingCurrency == "REPORT"
    list[0].priceRequirements == bidRequirements()
    list[0].validFrom == VALID_FROM.minusYears(5)

    list[1].entityId == "entityId"
    list[1].settingsType == CompanySettingsType.BESPOKE
    list[1].marketDataGroupId == "marketDataGroupId2"
    list[1].marketDataGroupName == "marketDataName2"
    list[1].configurationType == "TYPE2"
    list[1].curveConfigurationId == "id"
    list[1].curveConfigurationName == "name"
    list[1].nonFxCurveConfigurationId == "id1"
    list[1].nonFxCurveConfigurationName == "name1"
    list[1].strippingType == "STRIP2"
    list[1].discountingType == "DISCOUNT2"
    list[1].triangulationCcy == "TRIANGULATION2"
    list[1].reportingCurrency == "REPORT2"
    list[1].priceRequirements == new InstrumentPriceRequirements(ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE)
    list[1].validFrom == VALID_FROM.minusYears(5)
  }

  def "should archive company ipv settings"() {
    setup:
    operations.insert(companySettings {
      c -> c.validFrom = NewVersionFormV2.ROOT_DATE
    })

    when:
    repository.onCompanyArchived(CompanyArchived.newOf(EntityId.entityId("companyId")))
    then:
    def loaded = allSortedValidFromDesc("companyId")
    loaded.size() == 2
    loaded[0].state == State.ARCHIVED
  }

  def "should mark as deleted company ipv settings"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)

    when:
    def result = repository.deleteValuationSettings(settings.entityId, settings.validFrom)
    then:
    result.right
    def loaded = allSortedValidFromDesc("companyId")
    loaded.size() == 2
    loaded[0].state == State.DELETED
  }

  def "should update market data group name"() {
    setup:
    def settings = operations.insert(companySettings())
    when:
    repository.onMarketDataGroupUpdated(
    new MarketDataGroupUpdated(
    "marketDataGroupId",
    new MarketDataGroupForm("New name", null, new AllowedCompaniesForm(true, []), new AllowedTeamsForm(true, []))
    )
    )
    then:
    def loaded = operations.findById(settings.id, CompanyValuationSettings.class)
    loaded.marketDataGroup.marketDataGroupName == "New name"
  }

  def "should update portfolio valuation settings market data group name"() {
    setup:
    def settings = operations.insert(companySettings())
    when:
    repository.onMarketDataGroupUpdated(new MarketDataGroupUpdated("marketDataGroupId", new MarketDataGroupForm("New name", null, new AllowedCompaniesForm(true, []), new AllowedTeamsForm(true, []))))
    then:
    def loaded = operations.findById(settings.id, CompanyValuationSettings.class)
    loaded.marketDataGroup.marketDataGroupName == "New name"
  }

  def "should get company settings entity"() {
    setup:
    operations.insert(companySettings())
    operations.insert(companySettings())
    def companySettings = companySettings()
    companySettings.setEntityId("companyId2")
    operations.insert(companySettings)
    expect:
    def view = repository.companySettingsEntity("companyId", STATE_DATE)
    view != null
  }

  def "should update default to bespoke valuation settings"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)
    def form = new ValuationSettingsForm(settingsType: "BESPOKE",
    marketDataGroupId: "UPDATED",
    configurationType: "UPDATED",
    curveConfigurationId: "UPDATED",
    nonFxCurveConfigurationId: "UPDATED",
    curveDiscountingForm: new CalculationDiscountingForm("UPDATED", "UPDATED", "TRIANGULATION_UPDATED", true),
    reportingCurrency: "UPDATED",
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build())
    when:
    def result = repository.updateValuationSettings(settings.entityId,
    settings.validFrom,
    form,
    new ValuationSettingsNames(marketDataGroupName: "UPDATED",
    curveConfigurationName: "UPDATED",
    nonFxCurveConfigurationName: "UPDATED"))
    then:
    result.isRight()

    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2

    assertAutofilledFields(loaded[0])
    with(loaded[0]) {
      state == State.ACTIVE
      validFrom == form.versionForm.validFrom
      comment == form.versionForm.comment
      entityId == settings.entityId
      marketDataGroup.marketDataGroupId == "UPDATED"
      marketDataGroup.marketDataGroupName == "UPDATED"
      configurationType == "UPDATED"
      curveConfiguration.entityId == "UPDATED"
      nonFxCurveConfiguration.entityId == "UPDATED"
      strippingType == "UPDATED"
      discountingType == "UPDATED"
      triangulationCcy == "TRIANGULATION_UPDATED"
      reportingCurrency == "UPDATED"
      useCsaDiscounting
      priceRequirements == bidRequirements()
    }
  }

  def "should update default valuation settings"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)
    def form = new ValuationSettingsForm(settingsType: "DEFAULT",
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build())
    when:
    def result = repository.updateValuationSettings(settings.entityId,
    settings.validFrom,
    form,
    new ValuationSettingsNames(marketDataGroupName: "UPDATED",
    curveConfigurationName: "UPDATED",
    nonFxCurveConfigurationName: "UPDATED"))
    then:
    result.isRight()

    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2

    assertAutofilledFields(loaded[0])
    with(loaded[0]) {
      state == State.ACTIVE
      validFrom == form.versionForm.validFrom
      comment == form.versionForm.comment
      entityId == settings.entityId
    }
  }

  def "should get company valuation settings versions"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)
    operations.insert(companySettings())
    when:
    def result = repository.getCompanyValuationSettingsVersions(settings.entityId)
    then:
    result.size() == 2
    def view = result.get(0)
    view.marketDataGroupId == "marketDataGroupId"
    view.marketDataGroupName == "marketDataName"
    view.configurationType == "TYPE"
    view.curveConfigurationId == "id"
    view.curveConfigurationName == "name"
    view.nonFxCurveConfigurationId == "id1"
    view.nonFxCurveConfigurationName == "name1"
    view.strippingType == "STRIP"
    view.discountingType == "DISCOUNT"
    view.triangulationCcy == "TRIANGULATION"
    view.reportingCurrency == "REPORT"
    !view.useCsaDiscounting
    view.priceRequirements == bidRequirements()
    view.validFrom == VALID_FROM.minusYears(10)
    view.entityId == settings.entityId
    view.modifiedBy == creator.name
    view.modifiedAt != null
    view.state == State.ACTIVE
  }

  def "should load future versions dates list"() {
    setup:
    def settings = companySettings()
    def futureSettings = companySettings()
    futureSettings.state = State.ARCHIVED
    futureSettings.validFrom = FUTURE_VALID_FROM
    operations.insertAll([settings, futureSettings])

    when:
    def result = repository.getFutureVersions(settings.entityId, settings.validFrom)

    then:
    result.dates == [futureSettings.validFrom]
  }

  def "should update curve configuration names in portfolio"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyValuationSettings)
    result.curveConfiguration.name == "UPDATED"
    result.nonFxCurveConfiguration.name == "name1"
  }

  def "should update non fx curve configuration names in portfolio"() {
    setup:
    def settings = companySettings()
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id1", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyValuationSettings)
    result.curveConfiguration.name == "name"
    result.nonFxCurveConfiguration.name == "UPDATED"
  }

  def "should update curve configuration names in company"() {
    setup:
    def settings = companySettings()
    settings.nonFxCurveConfiguration = new EntityReference(entityId: "id", name: "name")
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyValuationSettings)
    result.curveConfiguration.name == "UPDATED"
    result.nonFxCurveConfiguration.name == "UPDATED"
  }

  private List<CompanyValuationSettings> allSortedValidFromDesc(String entityId) {
    operations.query(CompanyValuationSettings)
    .matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "validFrom", "recordDate")))
    .all()
  }

  def assertAutofilledFields(CompanyValuationSettings settings) {
    settings.id != null
    settings.recordDate != null
    settings.modifiedAt != null
    settings.modifiedBy != null
    settings.modifiedBy.name == creator.name
  }

  def "should create default company valuation settings"() {
    setup:
    repository.onCompanyCreated(new CompanyCreated("companyId"))
    expect:
    def loaded = allSortedValidFromDesc("companyId")
    loaded.size() == 1

    // New major ROOT version
    assertAutofilledFields(loaded[0])
    loaded[0].settingsType == CompanySettingsType.DEFAULT
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == LocalDate.ofEpochDay(0)
    loaded[0].entityId == "companyId"
  }

  def "should create company valuation settings on company created by csv"() {
    when:
    repository.onCompanyCreated(event)

    then:
    def loaded = allSortedValidFromDesc("companyId")
    loaded.size() == 1
    loaded[0].settingsType == settingsType
    Optional.ofNullable(loaded[0].marketDataGroup).map {
      l -> l.marketDataGroupId
    }.orElse(null) == mdgId
    Optional.ofNullable(loaded[0].marketDataGroup).map {
      l -> l.marketDataGroupName
    }.orElse(null) == mdgName
    loaded[0].curveConfiguration == cc

    where:
    event                                           | settingsType                | mdgId               | mdgName         | cc
    companyImportedEvent(null, null)                | CompanySettingsType.DEFAULT | null                | null            | null
    companyImportedEvent()                          | CompanySettingsType.BESPOKE | MDG_ENTITY.entityId | MDG_ENTITY.name | CURVE_CONFIG_ENTITY
    companyImportedEvent(CURVE_CONFIG_ENTITY, null) | CompanySettingsType.BESPOKE | null                | null            | CURVE_CONFIG_ENTITY
  }

  def "should update company valuation settings (BESPOKE) on import"() {
    setup:
    def importOptions = importOptions()
    def altMdg = EntityReference.newOf("mdId2", "mdName2")
    def altCurveConfig = EntityReference.newOf("ccId2", "ccName2")

    def globalSettings = globalSettings()
    operations.insert(globalSettings)

    // same as global
    def bespokeSettings = companySettings({
      c -> {
        c.validFrom = NewVersionFormV2.ROOT_DATE
        c.marketDataGroup = new ValuationSettingsMarketDataGroup(marketDataGroupId: MDG_ENTITY.entityId, marketDataGroupName: MDG_ENTITY.name)
        c.curveConfiguration = CURVE_CONFIG_ENTITY
      }
    } )
    operations.insert(bespokeSettings)

    // different mdg, different curve config
    def form = companyCsvForm( {
      c -> c.marketDataGroup = altMdg; c.curveConfiguration = altCurveConfig
    } )

    when:
    repository.updateValuationSettings(COMPANY_ID, form, importOptions)
    def outputSettings = operations.find(new Query(), CompanyValuationSettings.class)

    then:
    // 2 versions...
    outputSettings.size() == 2
    def v1 = outputSettings[0]
    def v2 = outputSettings[1]
    v1.validFrom.isEqual(v2.validFrom) && v1.validFrom.isEqual(NewVersionFormV2.ROOT_DATE)
    v2.recordDate > v1.recordDate

    // ...due to mdg and curve config changes...
    v1.marketDataGroup.marketDataGroupId == MDG_ENTITY.entityId
    v1.marketDataGroup.marketDataGroupName == MDG_ENTITY.name
    v1.curveConfiguration.entityId == CURVE_CONFIG_ENTITY.entityId
    v1.curveConfiguration.name == CURVE_CONFIG_ENTITY.name

    v2.marketDataGroup.marketDataGroupId == altMdg.entityId
    v2.marketDataGroup.marketDataGroupName == altMdg.name
    v2.curveConfiguration.entityId == altCurveConfig.entityId
    v2.curveConfiguration.name == altCurveConfig.name

    // ...everything else unchanged
    v1.settingsType.equals(v2.settingsType)
    v1.settingsType.equals(CompanySettingsType.BESPOKE)
    v1.configurationType == v2.configurationType
    v1.configurationType == "TYPE"
    v1.nonFxCurveConfiguration == v2.nonFxCurveConfiguration
    v1.nonFxCurveConfiguration == new EntityReference(entityId: "id1", name: "name1")
    v1.strippingType == v2.strippingType
    v1.strippingType == "STRIP"
    v1.discountingType == v2.discountingType
    v1.discountingType == "DISCOUNT"
    v1.triangulationCcy == v2.triangulationCcy
    v1.triangulationCcy == "TRIANGULATION"
    v1.reportingCurrency == v2.reportingCurrency
    v1.reportingCurrency == "REPORT"
    v1.useCsaDiscounting == v2.useCsaDiscounting
    v1.useCsaDiscounting == false
    v1.priceRequirements == v2.priceRequirements
    v1.priceRequirements.curvesPriceType == BID_PRICE
  }

  def "should convert company valuation settings to bespoke settings on import"() {
    setup:
    def globalSettings = globalSettings()
    operations.insert(globalSettings)

    def compSettings = CompanyValuationSettings.newOf(COMPANY_ID)
    compSettings.setMarketDataGroup(ValuationSettingsMarketDataGroup.marketDataGroup(MDG_ENTITY.entityId, MDG_ENTITY.name))
    operations.insert(compSettings)

    def importOptions = importOptions()

    // different curve config, same mdg
    def form = companyCsvForm( {
      c -> c.curveConfiguration = EntityReference.newOf("diffCcId", "diffCcName")
    } )

    when:
    repository.updateValuationSettings(COMPANY_ID, form, importOptions)
    def outputSettings = operations.find(new Query(), CompanyValuationSettings.class)

    then:
    // 2 versions...
    outputSettings.size() == 2
    def v1 = outputSettings[0]
    def v2 = outputSettings[1]
    v1.valueEquals(compSettings)
    !v2.valueEquals(compSettings)
    v1.validFrom.isEqual(v2.validFrom) && v1.validFrom.isEqual(NewVersionFormV2.ROOT_DATE)
    v2.recordDate > v1.recordDate

    // ...due to updated curve config...
    v2.curveConfiguration.entityId == "diffCcId"
    v2.curveConfiguration.name == "diffCcName"
    v1.curveConfiguration == null

    // ...which means settings are now bespoke...
    v2.settingsType == CompanySettingsType.BESPOKE
    v1.settingsType == CompanySettingsType.DEFAULT

    // ...and all other fields in new version equal to global settings...
    v2.configurationType == globalSettings.configurationType
    v2.nonFxCurveConfiguration == globalSettings.nonFxCurveConfiguration
    v2.strippingType == globalSettings.strippingType
    v2.discountingType == globalSettings.discountingType
    v2.triangulationCcy == globalSettings.triangulationCcy
    v2.reportingCurrency == globalSettings.reportingCurrency
    v2.priceRequirements == globalSettings.priceRequirements

    // ...except mdg, which existed before in the default settings and is unchanged...
    v2.marketDataGroup.marketDataGroupId == v1.marketDataGroup.marketDataGroupId
    v2.marketDataGroup.marketDataGroupName == v1.marketDataGroup.marketDataGroupName

    // ...and undefined fields
    v2.useCsaDiscounting == null
  }

  def "should convert bespoke company valuation settings to default settings on import"() {
    setup:
    def globalSettings = globalSettings()
    operations.insert(globalSettings)

    def compSettings = companySettings({
      c -> c.validFrom = NewVersionFormV2.ROOT_DATE
    } )
    compSettings.setMarketDataGroup(ValuationSettingsMarketDataGroup.marketDataGroup(MDG_ENTITY.entityId, MDG_ENTITY.name))
    operations.insert(compSettings)

    def importOptions = importOptions()

    // different curve config, same mdg
    def form = companyCsvForm( {
      c -> c.curveConfiguration = null; c.marketDataGroup = null
    } )

    when:
    repository.updateValuationSettings(COMPANY_ID, form, importOptions)
    def outputSettings = operations.find(new Query(), CompanyValuationSettings.class)

    then:
    // 2 versions...
    outputSettings.size() == 2
    def v1 = outputSettings[0]
    def v2 = outputSettings[1]
    v1.valueEquals(compSettings)
    !v2.valueEquals(compSettings)
    v1.validFrom.isEqual(v2.validFrom) && v1.validFrom.isEqual(NewVersionFormV2.ROOT_DATE)
    v2.recordDate > v1.recordDate

    // ...due to updated curve config and mdg...
    v2.curveConfiguration == null
    v1.curveConfiguration.entityId == "id"
    v1.curveConfiguration.name == "name"
    v2.marketDataGroup == null
    v1.marketDataGroup.marketDataGroupId == MDG_ENTITY.entityId
    v1.marketDataGroup.marketDataGroupName == MDG_ENTITY.name

    // ...which means settings are now default...
    v2.settingsType == CompanySettingsType.DEFAULT
    v1.settingsType == CompanySettingsType.BESPOKE

    // ...and all other fields are now null...
    v2.configurationType == null
    v2.nonFxCurveConfiguration == null
    v2.strippingType == null
    v2.discountingType == null
    v2.triangulationCcy == null
    v2.reportingCurrency == null
    v2.priceRequirements == null
    v2.useCsaDiscounting == null
  }

  def "should not create new version when no changes to company valuation settings (BESPOKE) on import"() {
    setup:
    def importOptions = importOptions()

    def globalSettings = globalSettings()
    operations.insert(globalSettings)

    // same MDG, same curve config
    def form = companyCsvForm( {
      c -> c.curveConfiguration = CURVE_CONFIG_ENTITY
    } )

    // same as global
    def bespokeSettings = companySettings({
      c -> {
        c.validFrom = NewVersionFormV2.ROOT_DATE
        c.marketDataGroup = new ValuationSettingsMarketDataGroup(marketDataGroupId: MDG_ENTITY.entityId, marketDataGroupName: MDG_ENTITY.name)
        c.curveConfiguration = CURVE_CONFIG_ENTITY
      }
    } )
    operations.insert(bespokeSettings)

    when:
    repository.updateValuationSettings(COMPANY_ID, form, importOptions)
    def outputSettings = operations.find(new Query(), CompanyValuationSettings.class)

    then:
    // 1 version...
    outputSettings.size() == 1
    def v1 = outputSettings[0]
    v1.valueEquals(bespokeSettings)
  }

  def "should not create new version when no changes to company valuation settings (DEFAULT) on import"() {
    setup:
    def globalSettings = globalSettings()
    operations.insert(globalSettings)

    def compSettings = CompanyValuationSettings.newOf(COMPANY_ID)
    compSettings.setMarketDataGroup(ValuationSettingsMarketDataGroup.marketDataGroup(MDG_ENTITY.entityId, MDG_ENTITY.name))
    operations.insert(compSettings)

    def importOptions = importOptions()

    when:
    repository.updateValuationSettings(COMPANY_ID, companyCsvForm( {
      c -> c
    } ), importOptions)
    def outputSettings = operations.find(new Query(), CompanyValuationSettings.class)

    then:
    outputSettings.size() == 1
    def v1 = outputSettings[0]
    v1.valueEquals(compSettings)
  }

  def "should apply global defaults to company settings entities"() {
    setup:
    operations.insert(globalSettings())

    def companyIds = ["companyId1", "companyId2"]
    def companyValuationSettings = [
      companySettings {
        entityId = "companyId1"
      },
      companySettings {
        entityId = "companyId2"
      }
    ]

    operations.insertAll(companyValuationSettings)

    def result =  repository.companySettingsEntities(companyIds, STATE_DATE)

    result.each {
      settings ->
      settings.configurationType == "type"
      settings.curveConfiguration.entityId == "id"
      settings.curveConfiguration.name == "name"
      settings.nonFxCurveConfiguration.entityId == "id2"
      settings.nonFxCurveConfiguration.name == "name2"
      settings.strippingType == "OIS"
      settings.discountingType == "type"
      settings.triangulationCcy == "USD"
      settings.reportingCurrency == "EUR"
      settings.priceRequirements == bidRequirements()
    }
  }

  def companySettings(Closure c = {}) {
    new CompanyValuationSettings(
    entityId: COMPANY_ID,
    settingsType: "BESPOKE",
    marketDataGroup: new ValuationSettingsMarketDataGroup(marketDataGroupId: "marketDataGroupId",
    marketDataGroupName: "marketDataName"),
    configurationType: "TYPE",
    curveConfiguration: new EntityReference(entityId: "id", name: "name"),
    nonFxCurveConfiguration: new EntityReference(entityId: "id1", name: "name1"),
    strippingType: "STRIP",
    discountingType: "DISCOUNT",
    triangulationCcy: "TRIANGULATION",
    reportingCurrency: "REPORT",
    useCsaDiscounting: false,
    priceRequirements: bidRequirements(),
    validFrom: VALID_FROM.minusYears(10),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1),
    ).with(true, c)
  }

  def bespokeValuationSettings(Closure c) {
    new CompanyLegalEntityValuationSettings(companyId: "companyId",
    entityId: "entityId",
    settingsType: "BESPOKE",
    marketDataGroup: new ValuationSettingsMarketDataGroup(marketDataGroupId: "marketDataGroupId2",
    marketDataGroupName: "marketDataName2"),
    configurationType: "TYPE2",
    curveConfiguration: new EntityReference(entityId: "id", name: "name"),
    nonFxCurveConfiguration: new EntityReference(entityId: "id1", name: "name1"),
    strippingType: "STRIP2",
    discountingType: "DISCOUNT2",
    triangulationCcy: "TRIANGULATION2",
    reportingCurrency: "REPORT2",
    priceRequirements: new InstrumentPriceRequirements(ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE),
    validFrom: VALID_FROM.minusYears(5),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1),
    ).with(true, c)
  }

  def companyCsvForm(Closure c) {
    new CompanyCsvForm(
    externalCompanyId: COMPANY_EXT_ID,
    slaDeadline: "OTHER",
    valuationDataGroup: null,
    marketDataGroup: MDG_ENTITY,
    curveConfiguration: null
    ).with(true, c)
  }

  def defaultEntitySettings() {
    new CompanyLegalEntityValuationSettings(companyId: "companyId",
    entityId: "entityId1",
    settingsType: "DEFAULT",
    validFrom: VALID_FROM.minusYears(5),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1),
    )
  }

  static def companyImportedEvent(EntityReference curveConfig = CURVE_CONFIG_ENTITY, EntityReference mdg = MDG_ENTITY) {
    new CompanyImported(
    COMPANY_ID,
    mdg,
    EntityReference.newOf("vdId", "vdName"),
    "LDN_1000_T_PLUS",
    curveConfig,
    STATE_DATE
    )
  }

  static def importOptions() {
    new ImportOptions(
    parse("2022-11-29"),
    DuplicateAction.REPLACE,
    STRICT,
    null,
    null,
    null,
    null,
    null
    )
  }

  static def globalSettings() {
    new GlobalValuationSettings(
    configurationType: "type",
    curveConfiguration: CURVE_CONFIG_ENTITY,
    nonFxCurveConfiguration: new EntityReference(entityId: "id2",
    name: "name2"),
    strippingType: "OIS",
    discountingType: "type",
    triangulationCcy: "USD",
    reportingCurrency: "EUR",
    priceRequirements: bidRequirements(),
    validFrom: LocalDate.ofEpochDay(0),
    state: State.ACTIVE,
    recordDate: STATE_DATE.recordDate.minusSeconds(1))
  }
}
