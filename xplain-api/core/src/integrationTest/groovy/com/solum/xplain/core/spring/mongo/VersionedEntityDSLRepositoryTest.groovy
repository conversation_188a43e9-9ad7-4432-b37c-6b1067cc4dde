package com.solum.xplain.core.spring.mongo

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.versions.BitemporalDate.newOfNow

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.UserBuilder
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class VersionedEntityDSLRepositoryTest extends IntegrationSpecification {

  @Resource
  TestVersionedEntityDSLRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), TestVersionedEntity)
  }

  def "should call findAllByNameIs without erroring"() {
    setup:
    def entity = new TestVersionedEntity()

    when:
    def result = repository.findAllByNameIs("Xavier", newOfNow(), active())

    then:
    result.isEmpty()
  }
}
