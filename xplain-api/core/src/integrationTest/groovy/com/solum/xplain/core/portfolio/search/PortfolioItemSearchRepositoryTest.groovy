package com.solum.xplain.core.portfolio.search

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.value.ExternalIdentifierView
import com.solum.xplain.core.search.SearchRequest
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class PortfolioItemSearchRepositoryTest extends IntegrationSpecification {
  @Resource
  PortfolioItemSearchRepository searchRepository
  @Resource
  MongoOperations operations

  static CREATOR = user("userId")
  static PORTFOLIO_ID1 = ObjectId.get().toHexString()
  static PORTFOLIO_ID2 = ObjectId.get().toHexString()

  def setup() {
    def auth = new TestingAuthenticationToken(CREATOR, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), PortfolioItem)
  }

  def "should return matching portfolio items"() {
    setup:
    for (i in 0..<11) {
      def item = PortfolioItemBuilder.irsPortfolioItem(PORTFOLIO_ID1, "prefix-tradeId" + i)
      item.tap(c -> c.setExternalIdentifiers([new ExternalIdentifier("EXT_" + i, "SRC"), new ExternalIdentifier("EXT_2" + i, "SRC2")]))
      operations.insert(item)
    }

    when:
    def results = searchRepository.portfolioItems([PORTFOLIO_ID1], new SearchRequest(query, LocalDate.now()))

    then:
    results.getContent().size() == expectedSize

    where:
    query      | expectedSize
    "prefix"   | 10
    "tradeId8" | 1
    "tradeId1" | 2
    "PAY_ID"   | 10
    "REC_ID"   | 10
    "EXT_2"    | 10
    "EXT_21"   | 2
    "EXT_"     | 10
  }

  def "should correctly map fields"() {
    setup:
    def item = PortfolioItemBuilder.irsPortfolioItem(PORTFOLIO_ID1, "tradeId", LocalDate.of(2023, 01, 01))
    item.externalIdentifiers = [new ExternalIdentifier("ID1", "EXT1"), new ExternalIdentifier("ID2", "EXT2")]
    operations.insert(item)

    when:
    def results = searchRepository.portfolioItems([PORTFOLIO_ID1], new SearchRequest("tradeId", LocalDate.now()))

    then:
    results.getContent().size() == 1
    with(results.getContent()[0]) {
      portfolioId == PORTFOLIO_ID1
      tradeId == item.entityId

      externalTradeId == item.getExternalTradeId()
      productType == "IRS"
      tradeDate == LocalDate.of(2023, 01, 01)

      externalPortfolioId == "extPortfolioId"
      externalCompanyId == "externalCompanyId"
      externalEntityId == "externalEntityId"
      externalIdentifiers == [new ExternalIdentifierView("ID1", "EXT1"), new ExternalIdentifierView("ID2", "EXT2")]
    }
  }

  def "should return only permissible portfolio trades"() {
    setup:
    def item1 = PortfolioItemBuilder.irsPortfolioItem(PORTFOLIO_ID1, "tradeId", LocalDate.of(2023, 01, 01))
    operations.insert(item1)

    def item2 = PortfolioItemBuilder.irsPortfolioItem(PORTFOLIO_ID2, "tradeId", LocalDate.of(2023, 01, 01))
    operations.insert(item2)

    when:
    def results = searchRepository.portfolioItems([PORTFOLIO_ID2], new SearchRequest("tradeId", LocalDate.now()))

    then:
    results.getContent().size() == 1
    with(results.getContent()[0]) {
      portfolioId == PORTFOLIO_ID2
      tradeId == item2.entityId
    }
  }
}
