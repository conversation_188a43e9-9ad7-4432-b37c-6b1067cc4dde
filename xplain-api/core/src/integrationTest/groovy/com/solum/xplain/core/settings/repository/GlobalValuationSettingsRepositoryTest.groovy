package com.solum.xplain.core.settings.repository

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.value.CurveDiscountingForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.entity.ValuationSettingsNames
import com.solum.xplain.core.curveconfiguration.event.CurveConfigurationNameChanged
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import com.solum.xplain.core.settings.form.GlobalValuationSettingsForm
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class GlobalValuationSettingsRepositoryTest extends IntegrationSpecification {

  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.now(), LocalDateTime.now().plusHours(1))

  @Resource
  MongoOperations operations

  @Resource
  GlobalValuationSettingsRepository repository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), GlobalValuationSettings.class)
  }

  def "should get global valuation settings"() {
    setup:
    operations.insert(globalValuationSettings(ofEpochDay(0)))
    when:
    def result = repository.getGlobalValuationSettings(STATE_DATE)
    then:
    result != null
    result.validFrom == ofEpochDay(0)
    result.modifiedBy.name == "Full Name"
    result.modifiedAt != null
    result.state == State.ACTIVE
    result.comment == "comment"
    result.configurationType == "type"
    result.curveConfiguration == new EntityReference(
      entityId: "id1",
      name: "name1"
      )
    result.nonFxCurveConfiguration == new EntityReference(
      entityId: "id2",
      name: "name2"
      )
    result.strippingType == "OIS"
    result.discountingType == "type"
    result.triangulationCcy == "USD"
    result.reportingCurrency == "EUR"
    result.priceRequirements == bidRequirements()
    result.forceIsdaInterpolatorsForCds
  }

  def "should get global settings view"() {
    setup:
    operations.insert(globalValuationSettings(ofEpochDay(0)))
    when:
    def result = repository.entityView(STATE_DATE)
    then:
    result != null
    result.validFrom == ofEpochDay(0)
    result.modifiedBy == "Full Name"
    result.modifiedAt != null
    result.configurationType == "type"
    result.curveConfigurationId == "id1"
    result.curveConfigurationName == "name1"
    result.nonFxCurveConfigurationName == "name2"
    result.nonFxCurveConfigurationId == "id2"
    result.strippingType == "OIS"
    result.discountingType == "type"
    result.triangulationCcy == "USD"
    result.reportingCurrency == "EUR"
    result.priceRequirements == bidRequirements()
    result.forceIsdaInterpolatorsForCds
  }

  def "should get global settings versions"() {
    setup:
    operations.insert(globalValuationSettings(ofEpochDay(0)))
    operations.insert(globalValuationSettings(ofEpochDay(0)))
    when:
    def result = repository.entityVersions()
    then:
    result.size() == 2
    result[0].configurationType == "type"
    result[0].curveConfigurationName == "name1"
    result[0].curveConfigurationId == "id1"
    result[0].nonFxCurveConfigurationName == "name2"
    result[0].nonFxCurveConfigurationId == "id2"
    result[0].strippingType == "OIS"
    result[0].discountingType == "type"
    result[0].triangulationCcy == "USD"
    result[0].reportingCurrency == "EUR"
    result[0].priceRequirements == bidRequirements()
    result[0].comment == "comment"
    result[0].modifiedBy == "Full Name"
    result[0].modifiedAt != null
    result[0].forceIsdaInterpolatorsForCds
  }

  def "should return when non root entry missing"() {
    setup:
    def form = new GlobalValuationSettingsForm(
      "type",
      "id1",
      "id2",
      new CurveDiscountingForm("type", "OIS", "USD"),
      "EUR",
      true,
      true,
      null,
      new NewVersionFormV2("comment", ofEpochDay(1), ofEpochDay(1), FutureVersionsAction.KEEP)
      )

    when:
    def result = repository.save(ofEpochDay(1),
      form, new ValuationSettingsNames(
      curveConfigurationName: "curveConfiguratioName",
      nonFxCurveConfigurationName: "irsInflationName",
      ))

    then:
    result.isLeft()
    def errorItem = (ErrorItem) result.left().get()
    errorItem.reason == Error.OBJECT_NOT_FOUND
    errorItem.description == "Object [GlobalValuationSettings] not found for date 1970-01-02"
  }

  def "should save global valuation settings when root version missing"() {
    setup:
    def form = new GlobalValuationSettingsForm(
      "type",
      "id1",
      "id2",
      new CurveDiscountingForm("type", "OIS", "USD"),
      "EUR",
      true,
      true,
      null,
      new NewVersionFormV2("comment", ofEpochDay(0), ofEpochDay(1), FutureVersionsAction.KEEP)
      )

    when:
    def result = repository.save(ofEpochDay(0),
      form, new ValuationSettingsNames(
      curveConfigurationName: "curveConfiguratioName",
      nonFxCurveConfigurationName: "irsInflationName",
      ))

    then:
    result.isRight()
    def loaded = operations.findAll(GlobalValuationSettings)
    loaded.size() == 1
    loaded[0].validFrom == ofEpochDay(0)
  }

  def "should return error when root version missing and creating non root"() {
    setup:
    def form = new GlobalValuationSettingsForm(
      "type",
      "id1",
      "id2",
      new CurveDiscountingForm("type", "OIS", "USD"),
      "EUR",
      true,
      true,
      null,
      new NewVersionFormV2("comment", ofEpochDay(1), ofEpochDay(1), FutureVersionsAction.KEEP)
      )

    when:
    def result = repository.save(ofEpochDay(0),
      form, new ValuationSettingsNames(
      curveConfigurationName: "curveConfiguratioName",
      nonFxCurveConfigurationName: "irsInflationName",
      ))

    then:
    result.isLeft()
    def error = (ErrorItem) result.left().get()
    error.description == "Root version must be created before modifying other versions"
    error.reason == Error.OPERATION_NOT_ALLOWED
  }

  def "should save global valuation settings"() {
    setup:
    operations.insert(GlobalValuationSettings.empty())
    def form = new GlobalValuationSettingsForm(
      "type",
      "id1",
      "id2",
      new CurveDiscountingForm("type", "OIS", "USD"),
      "EUR",
      true,
      true,
      null,
      new NewVersionFormV2("comment", ofEpochDay(1), ofEpochDay(1), FutureVersionsAction.KEEP)
      )

    when:
    def result = repository.save(ofEpochDay(0),
      form, new ValuationSettingsNames(
      curveConfigurationName: "curveConfiguratioName",
      nonFxCurveConfigurationName: "irsInflationName",
      ))

    then:
    result.isRight()
    def loaded = operations.findAll(GlobalValuationSettings)
    loaded.size() == 2
    loaded[1].comment == "comment"
    loaded[1].validFrom == ofEpochDay(1)
    loaded[1].configurationType == "type"
    loaded[1].curveConfiguration.entityId == "id1"
    loaded[1].curveConfiguration.name == "curveConfiguratioName"
    loaded[1].nonFxCurveConfiguration.entityId == "id2"
    loaded[1].nonFxCurveConfiguration.name == "irsInflationName"
    loaded[1].strippingType == "OIS"
    loaded[1].discountingType == "type"
    loaded[1].triangulationCcy == "USD"
    loaded[1].reportingCurrency == "EUR"
    loaded[1].priceRequirements == bidRequirements()
    loaded[1].forceIsdaInterpolatorsForCds
    loaded[1].excludeFixingsFromValuationDate
  }

  def "should delete global settings version"() {
    setup:
    operations.insert(new GlobalValuationSettings(validFrom: ofEpochDay(1)))

    when:
    def result = repository.deleteSettingsVersion(ofEpochDay(1))

    then:
    result.isRight()
    def loaded = operations.findAll(GlobalValuationSettings)
    loaded.size() == 2
    loaded[1].state == State.DELETED
  }

  def "should fail delete global settings ROOT version"() {
    setup:
    operations.insert(new GlobalValuationSettings(validFrom: ofEpochDay(0)))

    when:
    def result = repository.deleteSettingsVersion(ofEpochDay(0))

    then:
    result.isLeft()
    result.left()[0].reason == Error.OPERATION_NOT_ALLOWED
    result.left()[0].description == "Can not delete first version!"
  }

  def "should get global valuations settings future versions dates list"() {
    setup:
    def v1 = new GlobalValuationSettings(validFrom: ofEpochDay(0))
    def v2 = new GlobalValuationSettings(validFrom: ofEpochDay(2))
    def v3 = new GlobalValuationSettings(validFrom: ofEpochDay(3))
    def v4 = new GlobalValuationSettings(validFrom: ofEpochDay(5))
    def v5 = new GlobalValuationSettings(validFrom: ofEpochDay(6), state: State.DELETED)
    operations.insertAll([v1, v2, v3, v4, v5])

    when:
    def result = repository.futureVersions(ofEpochDay(2))

    then:
    result.dates == [ofEpochDay(3), ofEpochDay(5)]
  }

  def "should update curve configuration names"() {
    setup:
    def settings = new GlobalValuationSettings(
      curveConfiguration: new EntityReference(entityId: "id", name: "name"),
      nonFxCurveConfiguration: new EntityReference(entityId: "id", name: "name1"),
      )
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, GlobalValuationSettings)
    result.curveConfiguration.name == "UPDATED"
    result.nonFxCurveConfiguration.name == "UPDATED"
  }

  def globalValuationSettings(LocalDate version) {
    new GlobalValuationSettings(
      validFrom: version,
      configurationType: "type",
      state: State.ACTIVE,
      curveConfiguration: new EntityReference(
      entityId: "id1",
      name: "name1"
      ),
      nonFxCurveConfiguration: new EntityReference(
      entityId: "id2",
      name: "name2"
      ),
      strippingType: "OIS",
      discountingType: "type",
      triangulationCcy: "USD",
      reportingCurrency: "EUR",
      priceRequirements: bidRequirements(),
      comment: "comment",
      forceIsdaInterpolatorsForCds: true
      )
  }
}
