package com.solum.xplain.core.ipv.tradeleveloverride;

import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.description;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateImportItems;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportActionSummary;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LargeVersionedImportItems;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.csv.VersionedImportItems;
import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.csv.LargeFileImporter;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.tradeleveloverride.csv.TradeLevelOverrideCsvForm;
import com.solum.xplain.core.ipv.tradeleveloverride.csv.TradeLevelOverrideCsvLoaderFactory;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.TradeLevelOverrideRepository;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.TradeLevelOverrideWriteRepository;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class TradeLevelOverrideUploadService {

  private final TradeLevelOverrideWriteRepository writeRepository;
  private final TradeLevelOverrideRepository readRepository;
  private final TradeLevelOverrideCsvLoaderFactory csvLoaderFactory;
  private final AuditEntryService auditEntryService;
  private final AuthenticationContext authenticationContext;

  public Either<List<ErrorItem>, ValidationResponse> validate(
      byte[] bytes, ParsingMode parsingMode, LocalDate stateDate) {
    var user = authenticationContext.currentUser();
    return csvLoaderFactory
        .loader(user)
        .parse(bytes, parsingMode)
        .map(
            forms ->
                validateImportItems(
                    resolveImportItems(stateDate, forms.getParsedLines()), stateDate))
        .flatMap(l -> right(new ValidationResponse(l)));
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadTradeLevelOverrides(
      byte[] bytes, ImportOptions importOptions) {
    var user = authenticationContext.currentUser();
    return csvLoaderFactory
        .loader(user)
        .parse(bytes, importOptions.parsingMode())
        .flatMap(forms -> uploadForms(forms, importOptions).leftMap(ErrorItem.ListOfErrors::from));
  }

  private Either<ErrorItem, List<EntityId>> uploadForms(
      CsvParserResult<TradeLevelOverrideCsvForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();

    if (readRepository.hasChanges(importOptions.getValidationTimestamp())) {
      return left(stateChanged("Deal Exceptions"));
    }

    var importer =
        new LargeFileImporter<TradeLevelOverrideValue, TradeUniqueKey, TradeLevelOverrideEntity>(
            importOptions, writeRepository);
    var importItems = resolveImportItems(importOptions.getStateDate(), forms);

    return Either.<ErrorItem, List<ImportActionSummary>>right(importer.importItems(importItems))
        .map(summaries -> logImport(summaries, parserResult.getWarnings()))
        .map(r -> forms.stream().map(f -> EntityId.entityId(f.toCompositeKey())).toList());
  }

  private List<ImportActionSummary> logImport(
      List<ImportActionSummary> summaries, List<ErrorItem> logs) {
    var changesCount = summaries.stream().mapToInt(ImportActionSummary::getChangesCount).sum();
    var description = description("Deal exception", 0, logs.size(), changesCount);
    auditEntryService.newEntryWithLogs(AuditEntry.of("dealException", description), logs);
    return summaries;
  }

  private VersionedImportItems<TradeLevelOverrideValue, TradeUniqueKey, TradeLevelOverrideEntity>
      resolveImportItems(LocalDate stateDate, List<TradeLevelOverrideCsvForm> forms) {
    var existingItems = writeRepository.entitiesForUpdate(stateDate);
    return LargeVersionedImportItems
        .<TradeLevelOverrideValue, TradeUniqueKey, TradeLevelOverrideEntity>builder()
        .existingItems(existingItems)
        .existingItemToKeyFn(e -> e.getEntity().toUniqueKey())
        .importItems(List.copyOf(forms))
        .importItemToKeyFn(ResolvableEmbeddedVersionValue::getEntityId)
        .newEntityFn(TradeLevelOverrideEntity::new)
        .build();
  }
}
