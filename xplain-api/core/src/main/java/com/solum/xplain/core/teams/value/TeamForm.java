package com.solum.xplain.core.teams.value;

import static com.solum.xplain.core.common.validation.ValidationPatterns.NO_WHITESPACES_AT_THE_BEGINNING_AND_END;

import com.solum.xplain.core.teams.validation.UniqueTeamId;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@UniqueTeamId
@FieldNameConstants
public class TeamForm {

  @NotEmpty private final String name;

  @NotEmpty
  @Pattern(
      regexp = NO_WHITESPACES_AT_THE_BEGINNING_AND_END,
      message = "{com.solum.xplain.core.teams.value.TeamForm.externalId.pattern}")
  private final String externalId;
}
