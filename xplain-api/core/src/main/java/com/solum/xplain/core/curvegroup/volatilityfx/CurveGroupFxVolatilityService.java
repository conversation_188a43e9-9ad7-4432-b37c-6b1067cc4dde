package com.solum.xplain.core.curvegroup.volatilityfx;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveGroupFxVolatilityService {

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupFxVolatilityRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  @Transactional
  public Either<ErrorItem, EntityId> create(String groupId, CurveGroupFxVolatilityForm form) {
    return groupEither(groupId).flatMap(g -> repository.createVolatility(groupId, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> createDefault(String groupId) {
    var form = new CurveGroupFxVolatilityForm();
    form.setVersionForm(NewVersionFormV2.newDefault());
    form.setTimeInterpolator("TimeSquare");
    form.setTimeExtrapolatorLeft("Flat");
    form.setTimeExtrapolatorRight("Flat");
    form.setStrikeInterpolator("Linear");
    form.setStrikeExtrapolatorLeft("Flat");
    form.setStrikeExtrapolatorRight("Flat");
    return create(groupId, form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String groupId, LocalDate version, CurveGroupFxVolatilityForm form) {
    return groupEither(groupId).flatMap(g -> repository.updateVolatility(groupId, version, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> delete(String groupId, LocalDate version) {
    return groupEither(groupId).flatMap(g -> repository.deleteVolatility(groupId, version));
  }

  public Either<ErrorItem, CurveGroupFxVolatilityView> get(String groupId, LocalDate stateDate) {
    return groupEither(groupId)
        .flatMap(g -> repository.getActiveVolatilityView(groupId, stateDate));
  }

  public Either<ErrorItem, List<CurveGroupFxVolatilityView>> getVersions(String groupId) {
    return groupEither(groupId).map(g -> repository.getVolatilityVersionViews(groupId));
  }

  public Either<ErrorItem, DateList> getFutureVersionsDates(String groupId, LocalDate stateDate) {
    return groupEither(groupId).map(g -> repository.getFutureVersions(groupId, stateDate));
  }

  public Either<ErrorItem, FxVolatilityMatrixConfiguration> getMatrixConfiguration(
      String groupId, LocalDate stateDate) {
    return groupEither(groupId)
        .map(
            g ->
                FxVolatilityMatrixConfiguration.configurationFromList(
                    repository.getVolatilityNodesViews(groupId, stateDate)));
  }

  public Either<ErrorItem, FxVolatilityMatrixValues> getMatrixValues(
      String groupId, CurveConfigMarketStateForm stateForm, LocalDate valuationDate) {
    return groupEither(groupId)
        .map(g -> getVolatilityNodesValuesViews(groupId, stateForm, valuationDate))
        .map(FxVolatilityMatrixValues::configurationFromList);
  }

  public Either<ErrorItem, List<FxVolatilitySkewView>> getSkews(
      String groupId, LocalDate stateDate) {
    return groupEither(groupId).map(g -> repository.getVolatilitySkewViews(groupId, stateDate));
  }

  private VersionedList<FxVolatilityNodeValueView> getVolatilityNodesValuesViews(
      String groupId, CurveConfigMarketStateForm stateForm, LocalDate valuationDate) {
    return groupEither(groupId)
        .map(
            g ->
                repository.getVolatilityNodesValuesViews(
                    groupId, stateForm.getStateDate(), valuationDate, volatilities(stateForm)))
        .getOrElse(VersionedList.empty());
  }

  private Map<String, CalculationMarketValueFullView> volatilities(
      CurveConfigMarketStateForm stateForm) {
    return marketDataQuotesSupport.getFullQuotes(stateForm);
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
