package com.solum.xplain.core.common.csv;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import java.util.List;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

@Data
public class ImportOverview {

  private final String identifier;

  private final long insertedCount;
  private final long updatedCount;
  private final long archivedCount;

  private final List<ErrorItem> errors;
  private final List<LogItem> warnings;

  @Builder
  public ImportOverview(
      @lombok.NonNull String identifier,
      long insertedCount,
      long updatedCount,
      long archivedCount,
      List<ErrorItem> errors,
      List<LogItem> warnings) {
    this.identifier = identifier;

    this.insertedCount = insertedCount;
    this.updatedCount = updatedCount;
    this.archivedCount = archivedCount;

    this.errors = Objects.requireNonNullElse(errors, List.of());
    this.warnings = Objects.requireNonNullElse(warnings, List.of());
  }

  public ImportOverview sum(ImportOverview overview) {
    Assert.isTrue(
        StringUtils.equals(overview.getIdentifier(), identifier), "identifiers must be equal");
    var totalWarnings =
        ImmutableList.<LogItem>builder().addAll(overview.getWarnings()).addAll(warnings).build();

    var totalErrors =
        ImmutableList.<ErrorItem>builder().addAll(overview.getErrors()).addAll(errors).build();

    return ImportOverview.builder()
        .identifier(identifier)
        .insertedCount(insertedCount + overview.insertedCount)
        .updatedCount(updatedCount + overview.updatedCount)
        .archivedCount(archivedCount + overview.archivedCount)
        .warnings(totalWarnings)
        .errors(totalErrors)
        .build();
  }
}
