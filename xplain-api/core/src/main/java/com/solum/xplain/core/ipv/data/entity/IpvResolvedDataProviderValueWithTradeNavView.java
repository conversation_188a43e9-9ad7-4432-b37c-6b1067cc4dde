package com.solum.xplain.core.ipv.data.entity;

import static com.solum.xplain.core.ipv.data.entity.IpvResolvedDataProviderValueWithTradeNavView.VIEW_NAME;

import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = VIEW_NAME)
public class IpvResolvedDataProviderValueWithTradeNavView extends IpvDataProviderValueView {
  public static final String VIEW_NAME = "v_ipvResolvedDataProviderValueWithTradeNav";

  @Id
  public String getId() {
    return super.getId();
  }
}
