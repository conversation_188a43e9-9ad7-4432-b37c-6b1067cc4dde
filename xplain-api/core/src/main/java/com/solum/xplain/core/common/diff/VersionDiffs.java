package com.solum.xplain.core.common.diff;

import com.google.common.collect.ImmutableList;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.builder.Diff;
import org.apache.commons.lang3.builder.DiffResult;

@Data
public class VersionDiffs {
  private final List<Diff<?>> diffs;

  public static VersionDiffs of(DiffResult<?> commonsDif) {
    return new VersionDiffs(commonsDif.getDiffs());
  }

  public int numberOfDiffs() {
    return diffs.size();
  }

  public VersionDiffs append(List<Diff<?>> diffs) {
    return new VersionDiffs(
        ImmutableList.<Diff<?>>builder().addAll(this.diffs).addAll(diffs).build());
  }
}
