package com.solum.xplain.core.curvegroup.ratefx.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidFxRateCurrenciesValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidFxRateCurrencies {
  String message() default
      "{com.solum.xplain.api.curvegroup.validation" + ".ValidFxRateCurrencies.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
