package com.solum.xplain.core.company.csv;

import com.solum.xplain.core.company.entity.Company;

public record CompanyUniqueKey(String externalCompanyId) {

  public static CompanyUniqueKey fromForm(CompanyCsvForm form) {
    return new CompanyUniqueKey(form.getExternalCompanyId());
  }

  public static CompanyUniqueKey fromCompany(Company company) {
    return new CompanyUniqueKey(company.getExternalCompanyId());
  }
}
