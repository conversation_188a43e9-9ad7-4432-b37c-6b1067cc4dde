package com.solum.xplain.core.common.validation;

import com.solum.xplain.core.providers.enums.DataProviderType;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidDataProviderCodeValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ValidDataProviderCode {
  String message() default
      "{com.solum.xplain.api.common.validation" + ".ValidDataProviderCode.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  DataProviderType type() default DataProviderType.MARKET;

  String[] excludeProviders() default {};
}
