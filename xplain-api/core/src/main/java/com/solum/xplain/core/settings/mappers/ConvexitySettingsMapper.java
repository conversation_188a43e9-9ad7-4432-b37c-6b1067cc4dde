package com.solum.xplain.core.settings.mappers;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings;
import com.solum.xplain.core.settings.entity.CurveConvexityAdjustment;
import com.solum.xplain.core.settings.form.ConvexityAdjustmentsSettingsForm;
import com.solum.xplain.core.settings.form.CurveConvexityAdjustmentForm;
import com.solum.xplain.core.settings.value.ConvexityAdjustmentsSettingsView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(uses = {AuditUserMapper.class})
public interface ConvexitySettingsMapper
    extends VersionedSettingsMapper<
        ConvexityAdjustmentsSettings, ConvexityAdjustmentsSettingsView> {

  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  ConvexityAdjustmentsSettings from(
      ConvexityAdjustmentsSettingsForm form, @MappingTarget ConvexityAdjustmentsSettings settings);

  CurveConvexityAdjustment from(CurveConvexityAdjustmentForm form);
}
