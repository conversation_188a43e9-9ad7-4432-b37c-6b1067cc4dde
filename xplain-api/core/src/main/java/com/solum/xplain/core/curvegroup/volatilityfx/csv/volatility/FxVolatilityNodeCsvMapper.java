package com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility;

import static com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility.FxVolatilityNodeCsvLoader.CURRENCY_PAIR_FIELD;
import static com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility.FxVolatilityNodeCsvLoader.EXPIRY_FIELD;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView;
import java.util.List;

public class FxVolatilityNodeCsvMapper extends CsvMapper<FxVolatilityNodeValueView> {

  private static final String VALUE_FIELD = "Value";

  private static final String CURRENCY_PAIR = "currencyPair";

  private static final List<CsvColumn<FxVolatilityNodeValueView>> COLUMNS =
      List.of(
          CsvColumn.text(
              FxVolatilityNodeValueView.Fields.expiry,
              EXPIRY_FIELD,
              FxVolatilityNodeValueView::getExpiry),
          CsvColumn.text(CURRENCY_PAIR, CURRENCY_PAIR_FIELD, FxVolatilityNodeValueView::getFxPair),
          CsvColumn.decimal(
                  FxVolatilityNodeValueView.Fields.value,
                  VALUE_FIELD,
                  FxVolatilityNodeValueView::getValue)
              .optional());

  public FxVolatilityNodeCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
