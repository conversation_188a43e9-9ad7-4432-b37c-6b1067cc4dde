package com.solum.xplain.core.curvegroup.volatilityfx;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static java.util.Comparator.comparing;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.reducing;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeView;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class CurveGroupFxVolatilityRepository
    extends GenericUniqueVersionedEntityRepository<CurveGroupFxVolatility> {

  private final CurveGroupFxVolatilityMapper mapper;
  private final CurveGroupEntryCountSupport countSupport;

  public CurveGroupFxVolatilityRepository(
      MongoOperations mongoOperations,
      CurveGroupFxVolatilityMapper mapper,
      CurveGroupEntryCountSupport countSupport) {
    super(mongoOperations, mapper);
    this.mapper = mapper;
    this.countSupport = countSupport;
  }

  public static Criteria uniqueEntityCriteria(String groupId) {
    return where(VersionedEntity.Fields.entityId).is(groupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(CurveGroupFxVolatility entity) {
    return uniqueEntityCriteria(entity.getEntityId());
  }

  public Either<ErrorItem, EntityId> createVolatility(
      String groupId, CurveGroupFxVolatilityForm form) {
    var entity = mapper.fromForm(form, CurveGroupFxVolatility.newOf(groupId));
    updateNodesDeltas(entity.getNodes(), form.getSkews());
    return insert(entity, form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> updateVolatility(
      String groupId, LocalDate version, CurveGroupFxVolatilityForm form) {
    return entityExact(groupId, version)
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> {
                      var updatedEntity = mapper.fromForm(form, copiedEntity);
                      updateNodesDeltas(updatedEntity.getNodes(), form.getSkews());
                      return updatedEntity;
                    }));
  }

  private void updateNodesDeltas(
      Set<CurveGroupFxVolatilityNode> nodes, List<FxVolatilitySkewForm> deltaForms) {
    final var deltasByCurrencyPair =
        CollectionUtils.toMap(deltaForms, FxVolatilitySkewForm::getFxPair);
    nodes.forEach(
        node -> {
          var deltaForm = deltasByCurrencyPair.get(node.getFxPair());
          if (Objects.nonNull(deltaForm)) {
            node.setDelta1(deltaForm.getDelta1());
            node.setDelta2(deltaForm.getDelta2());
          }
        });
  }

  public Either<ErrorItem, EntityId> deleteVolatility(String groupId, LocalDate version) {
    return entityExact(groupId, version).flatMap(this::delete);
  }

  public Either<ErrorItem, CurveGroupFxVolatility> getActiveVolatility(
      String groupId, BitemporalDate stateDate) {
    return entity(groupId, stateDate, active());
  }

  public Either<ErrorItem, CurveGroupFxVolatilityView> getActiveVolatilityView(
      String groupId, LocalDate stateDate) {
    return getActiveVolatility(groupId, new BitemporalDate(stateDate)).map(mapper::toView);
  }

  public List<CurveGroupFxVolatilityView> getVolatilityVersionViews(String groupId) {
    return entityVersions(groupId).stream().map(mapper::toView).toList();
  }

  public DateList getFutureVersions(String groupId, LocalDate stateDate) {
    var searchCriteria = uniqueEntityCriteria(groupId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  public List<CurveGroupEntryCount> getVolatilityNodesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeFxVolsEntriesCount(filter);
  }

  public List<CurveGroupFxVolatilityNode> getVolatilityNodes(
      String groupId, BitemporalDate stateDate) {
    return getActiveVolatility(groupId, stateDate)
        .map(e -> List.copyOf(e.getNodes()))
        .getOrElse(List.of());
  }

  public VersionedList<FxVolatilityNodeView> getVolatilityNodesViews(
      String groupId, LocalDate stateDate) {
    return nodes(groupId, stateDate, mapper::toNodeView);
  }

  public VersionedList<FxVolatilityNodeValueView> getVolatilityNodesValuesViews(
      String groupId,
      LocalDate stateDate,
      LocalDate valuationDate,
      Map<String, CalculationMarketValueFullView> volatilities) {
    return nodes(
        groupId,
        stateDate,
        node ->
            mapper.toFxNodeValueView(
                node,
                marketData(volatilities, node.getKey()),
                marketData(volatilities, node.getDeltaVolatility1StrangleKey()),
                marketData(volatilities, node.getDeltaVolatility2StrangleKey()),
                marketData(volatilities, node.getDeltaVolatility1RiskReversalKey()),
                marketData(volatilities, node.getDeltaVolatility2RiskReversalKey()),
                valuationDate));
  }

  private <T> VersionedList<T> nodes(
      String groupId, LocalDate stateDate, Function<CurveGroupFxVolatilityNode, T> mapperFn) {
    return getActiveVolatility(groupId, new BitemporalDate(stateDate))
        .map(
            v ->
                new VersionedList<>(v.getValidFrom(), v.getNodes().stream().map(mapperFn).toList()))
        .getOrElse(VersionedList.empty());
  }

  private Double marketData(
      Map<String, CalculationMarketValueFullView> volatilities, String valueKey) {
    return ofNullable(valueKey)
        .map(volatilities::get)
        .map(CalculationMarketValueView::getValue)
        .map(BigDecimal::doubleValue)
        .orElse(null);
  }

  public List<FxVolatilitySkewView> getVolatilitySkewViews(String groupId, LocalDate stateDate) {
    return getActiveVolatility(groupId, new BitemporalDate(stateDate))
        .map(this::skewViews)
        .getOrElse(Map.of())
        .values()
        .stream()
        .flatMap(Optional::stream)
        .sorted(comparing(HasFxPair::getFxPair))
        .toList();
  }

  private Map<String, Optional<FxVolatilitySkewView>> skewViews(CurveGroupFxVolatility volatility) {
    return volatility.getNodes().stream()
        .filter(CurveGroupFxVolatilityNode::hasSkew)
        .collect(
            groupingBy(
                CurveGroupFxVolatilityNode::getFxPair,
                mapping(mapper::toSkewView, reducing(mapper::merge))));
  }
}
