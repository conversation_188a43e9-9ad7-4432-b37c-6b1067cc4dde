package com.solum.xplain.core.ccyexposure;

import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.repository.fragment.CcyExposureQueries;
import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CcyExposureRepository
    extends org.springframework.data.repository.Repository<CcyExposure, String>,
        CcyExposureQueries {

  @Query("{}") // avoid using default findAll() which requires converters
  List<CcyExposureView> findAllAsViews();

  List<CcyExposureView> findAllByArchivedIsFalse();

  @Nullable
  CcyExposureView findAllByIdAndArchivedIsFalse(String ccyExposureId);

  @Nullable
  CcyExposure findByIdAndArchivedIsFalse(String ccyExposureId);

  boolean existsByNameAndArchivedFalse(@NotEmpty String name);

  int countByNameAndArchivedFalse(@NotEmpty String name);

  CcyExposure getById(String ccyExposureId);
}
