package com.solum.xplain.core.portfolio.trade;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CALCULATION_RESULTS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/portfolio/{portfolioId}/trades/{tradeEntityId}/valuation-history")
public class TradeValuationHistoryController {
  private final TradeValuationHistoryControllerService service;

  @CommonErrors
  @Operation(summary = "Get trade valuation history")
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_CALCULATION_RESULTS)
  public ResponseEntity<List<TradeValuationHistoryView>> getTradeValuationHistory(
      @PathVariable String portfolioId, @PathVariable String tradeEntityId) {
    return eitherErrorItemResponse(service.getTradeValuationHistory(portfolioId, tradeEntityId));
  }

  @CommonErrors
  @Operation(summary = "Export trade valuation history as csv")
  @GetMapping("/csv")
  @PreAuthorize(AUTHORITY_VIEW_CALCULATION_RESULTS)
  public ResponseEntity<ByteArrayResource> getTradeValuationHistoryCsv(
      @PathVariable String portfolioId, @PathVariable String tradeEntityId) {
    return eitherErrorItemFileResponse(
        service.getTradeValuationHistoryCsv(portfolioId, tradeEntityId));
  }
}
