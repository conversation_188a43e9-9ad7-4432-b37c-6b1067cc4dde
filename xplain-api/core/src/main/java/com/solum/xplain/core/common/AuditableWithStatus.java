package com.solum.xplain.core.common;

import com.solum.xplain.core.common.versions.WithPreviousStatuses;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

@FieldNameConstants
@ToString
@EqualsAndHashCode(exclude = {"modifiedAt"})
public abstract class AuditableWithStatus<S> implements WithPreviousStatuses<HistoricStatus<S>> {

  @Id private String id;

  @LastModifiedBy private AuditUser modifiedBy;

  @LastModifiedDate private LocalDateTime modifiedAt;

  private S status;

  private List<HistoricStatus<S>> previousStatuses = new ArrayList<>();

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public AuditUser getModifiedBy() {
    return modifiedBy;
  }

  public void setModifiedBy(AuditUser modifiedBy) {
    this.modifiedBy = modifiedBy;
  }

  public LocalDateTime getModifiedAt() {
    return modifiedAt;
  }

  public void setModifiedAt(LocalDateTime modifiedAt) {
    this.modifiedAt = modifiedAt;
  }

  public S getStatus() {
    return status;
  }

  public void setStatus(S status) {
    this.status = status;
  }

  @Override
  public List<HistoricStatus<S>> getPreviousStatuses() {
    return previousStatuses;
  }

  @Override
  public void setPreviousStatuses(List<HistoricStatus<S>> states) {
    this.previousStatuses = states;
  }

  @Override
  public HistoricStatus<S> toStatusObject() {
    return HistoricStatus.of(modifiedBy, modifiedAt, status);
  }
}
