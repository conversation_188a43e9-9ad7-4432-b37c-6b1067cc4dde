package com.solum.xplain.core.config;

import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.core.jackson.ModelResolver;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import java.time.Duration;
import java.time.LocalTime;
import java.util.List;
import org.bson.types.ObjectId;
import org.springdoc.core.utils.SpringDocUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;

@Configuration
public class OpenApiConfig {
  private static final String OAUTH2_ACCESS_TOKEN = "oauth2 access token";

  public OpenApiConfig() {
    SpringDocUtils.getConfig()
        .replaceWithClass(LocalTime.class, String.class)
        .replaceWithSchema(
            Duration.class,
            new StringSchema()
                .example("P2DT3H4M")
                .format("duration")
                .description("ISO 8601 duration"))
        .replaceWithSchema(
            ObjectId.class, new StringSchema().format("objectid").pattern("[0-9a-fA-F]{24}"))
        .addRequestWrapperToIgnore(Authentication.class)
        .addRequestWrapperToIgnore(ScrollRequest.class)
        .addRequestWrapperToIgnore(TableFilter.class)
        .addRequestWrapperToIgnore(Sort.class);
  }

  @Bean
  public OpenAPI openApi() {
    ModelResolver.enumsAsRef = true;

    return new OpenAPI()
        .info(apiPublicInfo())
        .components(
            new Components()
                .addSecuritySchemes(
                    OAUTH2_ACCESS_TOKEN,
                    new SecurityScheme()
                        .type(SecurityScheme.Type.APIKEY)
                        .name("Authorization")
                        .in(SecurityScheme.In.HEADER)
                        .bearerFormat("Bearer")
                        .scheme(OAUTH2_ACCESS_TOKEN)))
        .security(List.of(new SecurityRequirement().addList(OAUTH2_ACCESS_TOKEN)));
  }

  private Info apiPublicInfo() {
    return new Info().title("Solum Xplain API").version("0.1.0");
  }
}
