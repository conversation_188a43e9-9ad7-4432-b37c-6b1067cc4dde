package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class PaymentDateForm {

  @NotNull private LocalDate date;

  @NotNull private Double amount;

  @NotEmpty
  @ValidStringSet(BusinessDayConventionsSupplier.class)
  private String convention;
}
