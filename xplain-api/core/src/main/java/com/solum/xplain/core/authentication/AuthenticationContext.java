package com.solum.xplain.core.authentication;

import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AuthenticationContext {

  private final AuthenticationCacheService authenticationCacheService;

  public XplainPrincipal user(Authentication auth) {
    return (XplainPrincipal) auth.getPrincipal();
  }

  public Either<ErrorItem, XplainPrincipal> userEither(Authentication auth) {
    return right(user(auth));
  }

  public XplainPrincipal currentUser() {
    return (XplainPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
  }

  public List<String> activeUserIdsInTeams(List<String> teamIds) {
    return authenticationCacheService.fetchAllCachedPrincipals().stream()
        .filter(e -> !e.isRevoked())
        .filter(
            e ->
                e.getPrincipal().getTeams().stream()
                    .map(ObjectId::toString)
                    .anyMatch(teamIds::contains))
        .map(e -> e.getPrincipal().getId())
        .toList();
  }
}
