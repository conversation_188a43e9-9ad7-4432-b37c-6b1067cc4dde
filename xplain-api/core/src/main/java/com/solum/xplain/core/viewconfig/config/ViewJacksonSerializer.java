package com.solum.xplain.core.viewconfig.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.solum.xplain.core.viewconfig.value.View;
import java.io.IOException;

public class ViewJacksonSerializer extends StdSerializer<View> {

  public ViewJacksonSerializer() {
    super(View.class);
  }

  @Override
  public void serialize(View value, JsonGenerator gen, SerializerProvider provider)
      throws IOException {
    gen.writeString(value.viewClass().getSimpleName());
  }
}
