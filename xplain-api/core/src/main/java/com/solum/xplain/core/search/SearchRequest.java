package com.solum.xplain.core.search;

import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
public class SearchRequest {
  @Size(min = 3, max = 50)
  @Parameter(name = "query", description = "Query to search for", required = true)
  private final String query;

  @NotNull
  @Parameter(name = "stateDate", description = "State date of search", required = true)
  private final LocalDate stateDate;
}
