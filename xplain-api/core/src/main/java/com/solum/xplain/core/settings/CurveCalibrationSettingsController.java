package com.solum.xplain.core.settings;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VALUATION_SETTINGS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_SETTINGS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.VALUATION_SETTINGS_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.settings.form.ConvexityAdjustmentsSettingsForm;
import com.solum.xplain.core.settings.form.InflationSeasonalitySettingsForm;
import com.solum.xplain.core.settings.service.CalibrationSettingsControllerService;
import com.solum.xplain.core.settings.service.ConvexitySettingsUploadService;
import com.solum.xplain.core.settings.service.SeasonalityAdjustmentCalculationService;
import com.solum.xplain.core.settings.service.SeasonalitySettingsUploadService;
import com.solum.xplain.core.settings.value.CalculatedInflationSeasonalitySettingsView;
import com.solum.xplain.core.settings.value.ConvexityAdjustmentsSettingsView;
import com.solum.xplain.core.settings.value.GlobalSettingsSearch;
import com.solum.xplain.core.settings.value.InflationSeasonalitySettingsView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/settings/calibration")
public class CurveCalibrationSettingsController {

  private final CalibrationSettingsControllerService service;
  private final SeasonalitySettingsUploadService uploadService;
  private final SeasonalityAdjustmentCalculationService seasonalityAdjustmentCalculationService;
  private final ConvexitySettingsUploadService convexitySettingsUploadService;

  public CurveCalibrationSettingsController(
      CalibrationSettingsControllerService service,
      SeasonalitySettingsUploadService uploadService,
      SeasonalityAdjustmentCalculationService seasonalityAdjustmentCalculationService,
      ConvexitySettingsUploadService convexitySettingsUploadService) {
    this.service = service;
    this.uploadService = uploadService;
    this.seasonalityAdjustmentCalculationService = seasonalityAdjustmentCalculationService;
    this.convexitySettingsUploadService = convexitySettingsUploadService;
  }

  @Operation(summary = "Get inflation curve seasonality settings")
  @CommonErrors
  @GetMapping("/inflation")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public InflationSeasonalitySettingsView getCalibrationInflationSettings(
      @RequestParam LocalDate stateDate) {
    return service.getCalibrationInflationSettings(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Get calculated inflation curve seasonality settings")
  @CommonErrors
  @GetMapping("/inflation/calculated")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public CalculatedInflationSeasonalitySettingsView getCalculatedInflationCurveSeasonalitySettings(
      @RequestParam LocalDate stateDate, @RequestParam LocalDate valuationDate) {
    return seasonalityAdjustmentCalculationService.calculatedSeasonality(
        BitemporalDate.newOf(stateDate), valuationDate);
  }

  @Operation(summary = "Get inflation curve season. settings in csv format")
  @CommonErrors
  @GetMapping("/inflation/inflation-csv")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public ResponseEntity<ByteArrayResource> getCalibrationInflationSettingsCsv(
      @RequestParam LocalDate stateDate) {
    return service.getCalibrationInflationSettingsCsv(BitemporalDate.newOf(stateDate)).toResponse();
  }

  @Operation(summary = "Get inflation curve season. settings versions")
  @CommonErrors
  @GetMapping("/inflation/versions")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public List<InflationSeasonalitySettingsView> getCalibrationInflationSettingsVersions() {
    return service.getCalibrationInflationSettingsVersions();
  }

  @Operation(summary = "Save inflation curve season. settings")
  @CommonErrors
  @PostMapping("/inflation/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> saveCalibrationInflationSettings(
      @Valid @RequestBody InflationSeasonalitySettingsForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveInflationSesionalitySettings(version, form));
  }

  @Operation(summary = "Upload inflation curve season. settings CSV file")
  @CommonErrors
  @PostMapping(value = "/inflation/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<List<EntityId>> importCalibrationInflationSettings(
      @RequestPart MultipartFile file, @Valid ImportOptions importOptions) throws IOException {
    return eitherErrorItemsResponse(
        uploadService.uploadSeasonalitySettings(file.getBytes(), importOptions));
  }

  @Operation(summary = "Get inflation curve season. settings future versions")
  @GetMapping("/inflation/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public DateList getInflationSettingsFutureVersions(@Valid GlobalSettingsSearch search) {
    return service.getInflationSeasonalityFutureVersions(search);
  }

  @Operation(summary = "Delete inflation curve season. settings")
  @CommonErrors
  @PutMapping("/inflation/{version}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> deleteCalibrationInflationSettings(
      @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteInflationSesionalitySettingsVersion(version));
  }

  @Operation(summary = "Get convexity adjustment settings")
  @CommonErrors
  @GetMapping("/convexity")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public ConvexityAdjustmentsSettingsView getConvexitySettings(@RequestParam LocalDate stateDate) {
    return service.getConvexitySettings(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Get convexity adjustment settings in csv format")
  @CommonErrors
  @GetMapping("/convexity/convexity-csv")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public ResponseEntity<ByteArrayResource> getConvexitySettingsCsv(
      @RequestParam LocalDate stateDate) {
    return service.getConvexitySettingsCsv(BitemporalDate.newOf(stateDate)).toResponse();
  }

  @Operation(summary = "Get convexity adjustment settings versions")
  @CommonErrors
  @GetMapping("/convexity/versions")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public List<ConvexityAdjustmentsSettingsView> getConvexitySettingsVersions() {
    return service.getConvexitySettingsVersions();
  }

  @Operation(summary = "Get convexity settings future versions")
  @GetMapping("/convexity/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public DateList getConvexitySettingsFutureVersions(@Valid GlobalSettingsSearch search) {
    return service.getConvexityFutureVersions(search);
  }

  @Operation(summary = "Save convexity adjustment settings")
  @CommonErrors
  @PostMapping("/convexity/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> saveConvexitySettings(
      @Valid @RequestBody ConvexityAdjustmentsSettingsForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveConvexityAdjustmentsSettings(version, form));
  }

  @Operation(summary = "Upload convexity adjustment settings CSV file")
  @CommonErrors
  @PostMapping(value = "/convexity/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<List<EntityId>> importConvexitySettings(
      @RequestPart MultipartFile file, @Valid ImportOptions importOptions) throws IOException {
    return eitherErrorItemsResponse(
        convexitySettingsUploadService.uploadConvexitySettings(file.getBytes(), importOptions));
  }

  @Operation(summary = "Delete convexity adjustment settings")
  @CommonErrors
  @PutMapping("/convexity/{version}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> deleteConvexitySettings(@PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteConvexitySettingsVersion(version));
  }
}
