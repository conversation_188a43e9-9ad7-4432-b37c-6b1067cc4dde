package com.solum.xplain.core.viewconfig.filter;

import com.solum.xplain.shared.utils.filter.FilterClause;
import com.solum.xplain.shared.utils.filter.FilterOperation;
import com.solum.xplain.shared.utils.filter.NestedCollectionMapping;
import com.solum.xplain.shared.utils.filter.SimpleFilterClause;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.mongodb.core.query.Criteria;

record SubfieldFilterClause(
    String classifierValue,
    String subfieldPropertyPath,
    String fieldPropertyPath,
    FilterOperation filterOperation,
    List<String> values)
    implements FilterClause {
  @Override
  public Criteria criteria(
      String propertyPrefix,
      Class<?> type,
      List<NestedCollectionMapping> mappings,
      ConversionService conversionService) {
    String arrayPropertyPath = StringUtils.substringBeforeLast(subfieldPropertyPath, ".");
    String subfieldPropertyName = subfieldPropertyPath.substring(arrayPropertyPath.length() + 1);
    String fieldPropertyName = fieldPropertyPath.substring(arrayPropertyPath.length() + 1);

    return Criteria.where(arrayPropertyPath)
        .elemMatch(
            new SimpleFilterClause(fieldPropertyName, filterOperation, values)
                .criteria(propertyPrefix, type, mappings, conversionService)
                .and(subfieldPropertyName)
                .is(classifierValue));
  }
}
