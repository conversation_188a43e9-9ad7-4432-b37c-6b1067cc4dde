package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.CriteriaUtils.toOrCriteria;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.portfolio.Portfolio;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.mongodb.core.query.Criteria;

@Data
@Builder
public class PortfolioFilter {

  private Set<String> portfolioIds;

  private Set<String> companyIds;

  private Criteria uniqueExternalPortfolioIdsCriteria;

  public static PortfolioFilter activePortfolios() {
    return PortfolioFilter.builder().build();
  }

  public static PortfolioFilter byPortfolioUniqueExternalIds(
      Set<PortfolioUniqueKey> portfolioUniqueKeys) {
    Criteria criteria =
        portfolioUniqueKeys.stream()
            .map(
                item ->
                    where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                        .is(item.companyId())
                        .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
                        .is(item.entityId())
                        .and(Portfolio.Fields.externalPortfolioId)
                        .is(item.externalPortfolioId()))
            .collect(toOrCriteria());

    return PortfolioFilter.builder().uniqueExternalPortfolioIdsCriteria(criteria).build();
  }

  /**
   * Returns a filter for portfolios by ID.
   *
   * @param portfolioIds list of portfolio IDs
   */
  public static PortfolioFilter byPortfolioIds(Set<String> portfolioIds) {
    return PortfolioFilter.builder().portfolioIds(portfolioIds).build();
  }

  public Criteria criteria() {
    Criteria criteria = new Criteria();
    criteria.and(Portfolio.Fields.archived).is(false);
    if (portfolioIds != null) {
      criteria.and(Portfolio.Fields.id).in(portfolioIds);
    }
    if (uniqueExternalPortfolioIdsCriteria != null) {
      criteria.andOperator(uniqueExternalPortfolioIdsCriteria);
    }
    return criteria;
  }
}
