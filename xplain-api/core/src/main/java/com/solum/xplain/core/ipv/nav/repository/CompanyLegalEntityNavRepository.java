package com.solum.xplain.core.ipv.nav.repository;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static java.lang.String.format;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.ArchiveForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.datavalue.DataValueMongoOperations;
import com.solum.xplain.core.datavalue.DataValueUpdateResolver;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.csv.DataUpdateSummary;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.AbstractIpvRepository;
import com.solum.xplain.core.ipv.data.IpvDataMapper;
import com.solum.xplain.core.ipv.data.IpvDataModificationHandler;
import com.solum.xplain.core.ipv.data.entity.BaseIpvDataValue;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.ipv.nav.entity.CompanyLegalEntityNav;
import com.solum.xplain.core.ipv.nav.entity.NavVersion;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.Objects;
import java.util.stream.Stream;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public class CompanyLegalEntityNavRepository
    extends AbstractIpvRepository<NavVersion, CompanyLegalEntityNav> {
  private final MongoOperations mongoOperations;
  private final DataValueMongoOperations<NavVersion, CompanyLegalEntityNav> dataOperations;
  private final IpvDataMapper ipvDataMapper;
  private final IpvDataModificationHandler ipvDataModificationHandler;

  public CompanyLegalEntityNavRepository(
      MongoOperations mongoOperations,
      AuditorAware<AuditUser> auditorAware,
      IpvDataMapper ipvDataMapper,
      IpvDataModificationHandler ipvDataModificationHandler) {
    this.mongoOperations = mongoOperations;
    this.dataOperations =
        new DataValueMongoOperations<>(CompanyLegalEntityNav.class, auditorAware, mongoOperations);
    this.ipvDataMapper = ipvDataMapper;
    this.ipvDataModificationHandler = ipvDataModificationHandler;
  }

  public EntityId createValue(@NonNull String groupId, @NonNull IpvDataProviderValueForm form) {
    var result = dataOperations.insert(ipvDataMapper.newEntityLevelNavValue(groupId, form));
    ipvDataModificationHandler.onCompanyLegalEntityNavDataValueCreation(groupId);
    return result;
  }

  @Override
  public DataUpdateSummary updateForImport(
      DataValueUpdateResolver<NavVersion, CompanyLegalEntityNav> entities) {
    return dataOperations.updateForImport(entities);
  }

  public Stream<CompanyLegalEntityNav> entityLevelNavValuesStream(
      String groupId, BitemporalDate date) {

    var criteria = activeValuesCriteria(groupId, date);
    return mongoOperations.stream(query(criteria), CompanyLegalEntityNav.class);
  }

  public Either<ErrorItem, IpvDataProviderValueView> getValueView(
      @NonNull String groupId, @NonNull String id, @NonNull BitemporalDate stateDate) {
    var operations = viewAggregations(valueCriteria(groupId, id, stateDate)).build();
    var aggregation = newAggregation(CompanyLegalEntityNav.class, operations);
    var result =
        mongoOperations
            .aggregate(aggregation, IpvDataProviderValueView.class)
            .getUniqueMappedResult();
    return Eithers.cond(
        Objects.nonNull(result),
        OBJECT_NOT_FOUND.entity(format("NAV value not found with id %s", id)),
        result);
  }

  public Either<ErrorItem, EntityId> updateValue(
      @NonNull String id, @NonNull IpvDataProviderValueUpdateForm form) {
    return dataOperations.updateValue(id, ipvDataMapper.navValue(form));
  }

  public Either<ErrorItem, EntityId> archiveValue(@NonNull String id, @NonNull ArchiveForm form) {
    return dataOperations.archiveValue(id, form);
  }

  public boolean valueExists(
      @NonNull String groupId, @NonNull BitemporalDate date, @NonNull String key) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.date)
            .is(date.getActualDate())
            .andOperator(validAtStateDateCriteria(date, false))
            .and(BaseIpvDataValue.Fields.key)
            .is(key);
    return mongoOperations.exists(query(criteria), CompanyLegalEntityNav.class);
  }
}
