package com.solum.xplain.core.curvegroup.curvecredit;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveCreditNodesCsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveFundingNodesCsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveNodeExportViewCsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveNodesCsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCreditNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveFundingNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView;
import com.solum.xplain.core.curvegroup.curvecredit.value.MdkExportForm;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
public class CurveGroupCreditCurveExportService {

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupCreditCurveRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;

  public CurveGroupCreditCurveExportService(
      CurveGroupRepository curveGroupRepository,
      CurveGroupCreditCurveRepository repository,
      MarketDataQuotesSupport marketDataQuotesSupport,
      InstrumentMarketKeyDefinitionExportService definitionExportService) {
    this.curveGroupRepository = curveGroupRepository;
    this.repository = repository;
    this.marketDataQuotesSupport = marketDataQuotesSupport;
    this.definitionExportService = definitionExportService;
  }

  private static FileResponseEntity responseWithName(
      ByteArrayResource bytes, String name, LocalDate stateDate, String prefix) {
    var csvFileName = nameWithTimeStamp(name, prefix, stateDate);
    return FileResponseEntity.csvFile(bytes, csvFileName);
  }

  public Either<ErrorItem, FileResponseEntity> getAllCurvesCsv(
      String groupId,
      BitemporalDate stateDate,
      TableFilter tableFilter,
      List<String> selectedColumns,
      Sort sort) {
    var mapper = new CreditCurveCsvMapper(selectedColumns);
    var csvFileName = nameWithTimeStamp("CreditCurveList", stateDate);
    var curves = repository.getCurveViews(groupId, stateDate, active(), tableFilter, sort);
    return groupEither(groupId)
        .map(
            u -> {
              var rows = curves.stream().map(mapper::toCsvRow).toList();
              var csvFile = new CsvOutputFile(mapper.header(), rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getAllCurvesNodesCsv(
      String groupId, BitemporalDate stateDate, TableFilter tableFilter, Sort sort) {
    var mapper = new CreditCurveNodeExportViewCsvMapper();
    var curves = repository.getCurveViews(groupId, stateDate, active(), tableFilter, sort);
    var prefix = getPrefix("CreditCurveNodes", curves, CreditCurveView::getName);
    var csvFileName = nameWithTimeStamp(prefix, stateDate);
    return getAllNodesCsv(mapper, groupId, curves)
        .map(r -> FileResponseEntity.csvFile(r, csvFileName));
  }

  public Either<ErrorItem, FileResponseEntity> getCurveCdsNodesCsv(
      String groupId,
      String curveId,
      LocalDate version,
      LocalDate valuationDate,
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns) {
    var mapper = new CreditCurveCreditNodesCsvMapper(selectedColumns);
    return repository
        .getActiveCurveView(groupId, curveId, version)
        .flatMap(
            c ->
                getNodesCsv(mapper, cdsNodes(valuationDate, stateForm), groupId, singletonList(c))
                    .map(
                        csv ->
                            responseWithName(
                                csv, c.getName(), stateForm.getStateDate(), "CDSNodes")));
  }

  public Either<ErrorItem, FileResponseEntity> getCurveIndexNodesCsv(
      String groupId,
      String curveId,
      LocalDate version,
      LocalDate valuationDate,
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns) {
    var mapper = new CreditCurveCreditNodesCsvMapper(selectedColumns);
    return repository
        .getActiveCurveView(groupId, curveId, version)
        .flatMap(
            c ->
                getNodesCsv(mapper, indexNodes(valuationDate, stateForm), groupId, singletonList(c))
                    .map(
                        csv ->
                            responseWithName(
                                csv, c.getName(), stateForm.getStateDate(), "IndexNodes")));
  }

  public Either<ErrorItem, FileResponseEntity> getCurveFundingNodesCsv(
      String groupId,
      String curveId,
      LocalDate version,
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns) {
    var mapper = new CreditCurveFundingNodesCsvMapper(selectedColumns);
    var priceRequirements = stateForm.priceRequirements();
    var nodesFn = fundingNodes(marketDataQuotesSupport.getFullQuotes(stateForm), priceRequirements);
    return repository
        .getActiveCurveView(groupId, curveId, version)
        .flatMap(
            c ->
                getNodesCsv(mapper, nodesFn, groupId, singletonList(c))
                    .map(
                        csv ->
                            responseWithName(
                                csv, c.getName(), stateForm.getStateDate(), "FundingNodes")));
  }

  public Either<ErrorItem, FileResponseEntity> getFundingNodesMDKDefinitionsCsv(
      String groupId, MdkExportForm exportForm) {
    return definitionCsv(
        groupId,
        exportForm.getStateDate(),
        exportForm.getCurveIds(),
        exportForm.getConfigurationId(),
        "FundingNodes",
        CreditCurve::fundingNodeInstruments);
  }

  public Either<ErrorItem, FileResponseEntity> getCdsNodesMDKDefinitionCsv(
      String groupId, MdkExportForm exportForm) {
    return definitionCsv(
        groupId,
        exportForm.getStateDate(),
        exportForm.getCurveIds(),
        exportForm.getConfigurationId(),
        "CDSNodes",
        CreditCurve::cdsNodeInstruments);
  }

  public Either<ErrorItem, FileResponseEntity> getIndexNodesMDKDefinitionCsv(
      String groupId, MdkExportForm exportForm) {
    return definitionCsv(
        groupId,
        exportForm.getStateDate(),
        exportForm.getCurveIds(),
        exportForm.getConfigurationId(),
        "IndexNodes",
        CreditCurve::indexNodeInstruments);
  }

  private Either<ErrorItem, FileResponseEntity> definitionCsv(
      String groupId,
      LocalDate stateDate,
      List<String> curveIds,
      String configurationId,
      String prefix,
      Function<CreditCurve, List<InstrumentDefinition>> instrumentExtractor) {
    return groupEither(groupId)
        .map(c -> repository.getActiveCurves(groupId, new BitemporalDate(stateDate)))
        .map(cc -> CreditCurvesUtils.filterCurves(cc, curveIds))
        .flatMap(
            list ->
                definitionExportService.instrumentMdkDefinitions(
                    prefix,
                    list,
                    instrumentExtractor,
                    CreditCurve::getName,
                    stateDate,
                    configurationId));
  }

  private <M extends CsvMapper<T> & CreditCurveNodesCsvMapper<T>, T>
      Either<ErrorItem, ByteArrayResource> getNodesCsv(
          M mapper,
          BiFunction<CurveGroupView, CreditCurveView, List<T>> nodes,
          String groupId,
          List<CreditCurveView> curves) {
    return groupEither(groupId)
        .map(
            cg ->
                curves.stream()
                    .flatMap(
                        c -> nodes.apply(cg, c).stream().map(n -> mapper.toCsvRow(c.getName(), n)))
                    .collect(collectingAndThen(toList(), rr -> csv(mapper, rr)))
                    .writeToByteArray());
  }

  private Either<ErrorItem, ByteArrayResource> getAllNodesCsv(
      CreditCurveNodeExportViewCsvMapper mapper, String groupId, List<CreditCurveView> curves) {
    return groupEither(groupId)
        .map(
            cg ->
                curves.stream()
                    .flatMap(
                        c ->
                            repository
                                .allCurveNodes(cg.getId(), c.getEntityId(), c.getValidFrom())
                                .stream()
                                .map(mapper::toCsvRow))
                    .collect(collectingAndThen(toList(), rr -> csv(mapper, rr)))
                    .writeToByteArray());
  }

  private CsvOutputFile csv(CsvMapper<?> mapper, List<CsvRow> rows) {
    return new CsvOutputFile(mapper.header(), rows);
  }

  private BiFunction<CurveGroupView, CreditCurveView, List<CreditCurveFundingNodeCalculatedView>>
      fundingNodes(
          Map<String, CalculationMarketValueFullView> spreads,
          InstrumentPriceRequirements priceRequirements) {
    return (CurveGroupView cg, CreditCurveView c) ->
        repository.getFundingNodes(
            cg, c.getEntityId(), c.getValidFrom(), spreads, priceRequirements);
  }

  private BiFunction<CurveGroupView, CreditCurveView, List<CreditCurveCreditNodeCalculatedView>>
      cdsNodes(LocalDate valuationDate, CurveConfigMarketStateForm stateForm) {
    var spreads = marketDataQuotesSupport.getFullQuotes(stateForm);
    return (CurveGroupView cg, CreditCurveView c) ->
        repository.curveCdsNodes(
            cg,
            c.getEntityId(),
            c.getValidFrom(),
            new CurveConfigMarketStateQuotes(stateForm, spreads),
            valuationDate,
            cg.getCalibrationCurrency());
  }

  private BiFunction<CurveGroupView, CreditCurveView, List<CreditCurveCreditNodeCalculatedView>>
      indexNodes(LocalDate valuationDate, CurveConfigMarketStateForm stateForm) {
    var spreads = marketDataQuotesSupport.getFullQuotes(stateForm);
    return (CurveGroupView cg, CreditCurveView c) ->
        repository.getIndexNodes(
            cg, c.getEntityId(), c.getValidFrom(), spreads, valuationDate, stateForm);
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
