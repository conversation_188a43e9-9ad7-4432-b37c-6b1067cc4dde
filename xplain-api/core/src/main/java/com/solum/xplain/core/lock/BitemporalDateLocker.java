package com.solum.xplain.core.lock;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.datagrid.ClusterLock;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class BitemporalDateLocker {

  private final LockingSupport lockingSupport;

  public BitemporalDateLocker(LockingSupport lockingSupport) {
    this.lockingSupport = lockingSupport;
  }

  public Either<ErrorItem, BitemporalDate> lock(List<XplainLock> xplainLocks, LocalDate stateDate) {
    return closeLocks(xplainLocks)
        .map(closedLocks -> resolveBitemporalDate(stateDate, closedLocks));
  }

  private BitemporalDate resolveBitemporalDate(LocalDate stateDate, List<ClusterLock> closedLocks) {
    var bitemporalDate = new BitemporalDate(stateDate);
    releaseLocks(closedLocks);
    return bitemporalDate;
  }

  private Either<ErrorItem, List<ClusterLock>> closeLocks(List<XplainLock> locks) {
    return lockingSupport.tryLocks(locks);
  }

  private void releaseLocks(List<ClusterLock> locks) {
    locks.forEach(lockingSupport::releaseLock);
  }
}
