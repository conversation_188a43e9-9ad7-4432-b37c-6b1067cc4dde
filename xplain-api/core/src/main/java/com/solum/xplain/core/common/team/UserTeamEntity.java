package com.solum.xplain.core.common.team;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.AuditContext;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class UserTeamEntity<T extends WithTeamsView> {
  private final XplainPrincipal user;
  private final T view;

  private UserTeamEntity(XplainPrincipal user, T view) {
    this.user = user;
    this.view = view;
  }

  public static <T extends WithTeamsView> UserTeamEntity<T> userEntity(
      XplainPrincipal user, T view) {
    return new UserTeamEntity<>(user, view);
  }

  public Either<ErrorItem, UserTeamEntity<T>> allowTeamsOnly() {
    if (isTrue(view.getAllowAllTeams())) {
      return Either.right(this);
    }
    return user.allowedTeams(view.getTeamIds()).map(u -> this);
  }

  public T getView() {
    return view;
  }

  public XplainPrincipal getUser() {
    return user;
  }

  public AuditContext audit() {
    return new AuditContext(AuditUser.of(user), LocalDateTime.now());
  }
}
