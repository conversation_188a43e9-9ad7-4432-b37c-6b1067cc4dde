package com.solum.xplain.core.settings.form;

import static com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType.AUTO;
import static com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType.MANUAL;

import com.google.common.collect.ImmutableList;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class InflationSeasonalitySettingsGroupProvider
    implements DefaultGroupSequenceProvider<CurveSeasonalityForm> {
  @Override
  public List<Class<?>> getValidationGroups(CurveSeasonalityForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CurveSeasonalityForm.class);
    if (form == null) {
      return builder.build();
    }

    if (MANUAL == form.getSettingsType()) {
      builder.add(ManualGroup.class);
    } else if (AUTO == form.getSettingsType()) {
      builder.add(AutoGroup.class);
    }

    return builder.build();
  }
}
