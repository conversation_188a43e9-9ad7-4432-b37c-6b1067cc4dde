package com.solum.xplain.core.curvegroup.curvecredit.csv.node;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.Y0369M;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.Y369M;
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CDS;
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CREDIT_INDEX;
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CREDIT_INDEX_TRANCHE;
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.FUNDING;
import static com.solum.xplain.core.error.Error.IMPORT_ERROR;
import static io.atlassian.fugue.Either.left;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ItemKey;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.csv.NodesCsvLoader;
import com.solum.xplain.core.common.validation.ValidPeriodValidator;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidFundingNodesValidator;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.instrument.InstrumentType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;

public class CreditCurveNodesCsvLoader extends NodesCsvLoader<CreditCurve, CreditCurveNodeForm> {

  static final String CURVE_ID_FIELD = "Curve Id";
  static final String CURVE_ID_FIELD_NAME = "name";
  static final String NODE_TYPE_FIELD = "Node Type";
  static final String TENOR_FIELD = "Tenor";

  private static final String NODES_NOT_ALLOWED = "Credit Curve %s cannot have %s nodes";

  private final Predicate<CreditCurveNodeType> typeFilter;
  private final Set<String> validInstruments;

  public static CreditCurveNodesCsvLoader newOf(
      @NonNull CreditCurveNodeType type, List<InstrumentType> validInstruments) {
    return new CreditCurveNodesCsvLoader(type::equals, validInstruments);
  }

  public static CreditCurveNodesCsvLoader defaultLoader(List<InstrumentType> validInstruments) {
    return new CreditCurveNodesCsvLoader(r -> true, validInstruments);
  }

  public CreditCurveNodesCsvLoader(
      Predicate<CreditCurveNodeType> typeFilter, List<InstrumentType> validInstruments) {
    this.typeFilter = typeFilter;
    this.validInstruments =
        validInstruments.stream().map(InstrumentType::name).collect(Collectors.toSet());
  }

  @Override
  protected String getCurveNameField() {
    return CURVE_ID_FIELD;
  }

  @Override
  protected String getCurveName(CreditCurve curve) {
    return curve.getName();
  }

  @Override
  protected List<String> getCsvFileHeaders() {
    return List.of(NODE_TYPE_FIELD, TENOR_FIELD);
  }

  @Override
  protected Either<ErrorItem, CreditCurveNodeForm> parse(@NonNull CsvRow row) {
    return parseNodeType(row).flatMap(t -> parseRow(t, row));
  }

  @Override
  protected Either<List<ErrorItem>, NamedList<CreditCurveNodeForm>> validateCurveNodes(
      CreditCurve curve, NamedList<CreditCurveNodeForm> nodes, DuplicateAction action) {
    var fundingAllowed = fundingNodesAllowed(curve);
    var indexNodesAllowed = curve.getCurveType() == CreditCurveType.CREDIT_INDEX;
    var indexTrancheNodesAllowed = curve.getCurveType() == CreditCurveType.CREDIT_INDEX_TRANCHE;
    var cdsNodesAllowed = curve.getCurveType() == CreditCurveType.CDS;

    var errors =
        ImmutableList.<ErrorItem>builder()
            .addAll(validateNodes(curve, nodes, FUNDING, fundingAllowed))
            .addAll(validateNodes(curve, nodes, CDS, cdsNodesAllowed))
            .addAll(validateNodes(curve, nodes, CREDIT_INDEX, indexNodesAllowed))
            .addAll(validateNodes(curve, nodes, CREDIT_INDEX_TRANCHE, indexTrancheNodesAllowed))
            .build();
    return Eithers.cond(errors.isEmpty(), errors, nodes);
  }

  private boolean fundingNodesAllowed(CreditCurve curve) {
    return curve.getCurveType() == CreditCurveType.CDS
        && ValidFundingNodesValidator.fundingNodesAllowed(curve.getSeniority());
  }

  private List<ErrorItem> validateNodes(
      CreditCurve curve,
      NamedList<CreditCurveNodeForm> nodes,
      CreditCurveNodeType type,
      boolean nodesAllowed) {
    var hasNodes = nodes.getItems().stream().anyMatch(n -> n.getType() == type);
    if (!hasNodes || nodesAllowed) {
      return List.of();
    }
    return List.of(nodesNotAllowedErrorItem(curve.getName(), type));
  }

  private ErrorItem nodesNotAllowedErrorItem(String curveName, CreditCurveNodeType type) {
    return IMPORT_ERROR.entity(String.format(NODES_NOT_ALLOWED, curveName, type));
  }

  @Override
  protected ItemKey getNodeKey(CreditCurveNodeForm form) {
    return CreditCurveNodeKey.from(form);
  }

  @Override
  protected Predicate<CsvRow> isRelevantLine() {
    return row -> parseNodeType(row).toOptional().map(typeFilter::test).orElse(true);
  }

  private Either<ErrorItem, CreditCurveNodeType> parseNodeType(@NonNull CsvRow row) {
    return CsvLoaderUtils.getFieldValue(row, NODE_TYPE_FIELD, this::validateNodeType)
        .map(CreditCurveNodeType::valueOf);
  }

  private String validateNodeType(String nodeType) {
    if (StringUtils.isNotEmpty(nodeType)) {
      return validateValue(nodeType.toUpperCase(), validInstruments);
    }
    return nodeType;
  }

  private Either<ErrorItem, CreditCurveNodeForm> parseRow(
      CreditCurveNodeType type, @NonNull CsvRow row) {
    return switch (type) {
      case CDS, FUNDING, CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> parseNodeForm(type, row);
      default -> left(rowParsingError(row, NODE_TYPE_FIELD, "Unsupported node type: " + type));
    };
  }

  private Either<ErrorItem, CreditCurveNodeForm> parseNodeForm(
      CreditCurveNodeType type, @NonNull CsvRow row) {
    return parseTenor(row, type).map(tenor -> nodeForm(type, tenor));
  }

  private Either<ErrorItem, String> parseTenor(@NonNull CsvRow row, CreditCurveNodeType type) {
    return switch (type) {
      case CDS ->
          CsvLoaderUtils.getFieldValue(
              row, TENOR_FIELD, v -> validateValue(v, ValidPeriodValidator.isValid(v, Y0369M)));
      case FUNDING, CREDIT_INDEX_TRANCHE, CREDIT_INDEX ->
          CsvLoaderUtils.getFieldValue(
              row, TENOR_FIELD, v -> validateValue(v, ValidPeriodValidator.isValid(v, Y369M)));
      case ANY -> throw new IllegalArgumentException("ANY nodes are not parsable");
    };
  }

  private CreditCurveNodeForm nodeForm(CreditCurveNodeType type, String tenor) {
    var f = new CreditCurveNodeForm();
    f.setType(type);
    f.setTenor(tenor);
    return f;
  }
}
