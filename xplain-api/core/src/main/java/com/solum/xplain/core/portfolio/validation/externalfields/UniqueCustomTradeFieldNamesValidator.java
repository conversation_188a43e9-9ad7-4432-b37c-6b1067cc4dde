package com.solum.xplain.core.portfolio.validation.externalfields;

import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

public class UniqueCustomTradeFieldNamesValidator
    implements ConstraintValidator<UniqueCustomTradeFieldNames, List<CustomTradeFieldForm>> {

  @Override
  public boolean isValid(List<CustomTradeFieldForm> value, ConstraintValidatorContext context) {
    if (CollectionUtils.isEmpty(value)) {
      return true;
    }
    return value.size()
        == value.stream().map(CustomTradeFieldForm::externalFieldId).distinct().count();
  }
}
