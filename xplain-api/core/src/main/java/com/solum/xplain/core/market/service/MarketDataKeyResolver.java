package com.solum.xplain.core.market.service;

import static com.solum.xplain.core.market.value.MarketDataTicker.fromMd;
import static com.solum.xplain.core.market.value.MarketDataTicker.fromMdk;
import static com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView.fromProvider;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.value.MarketDataKeyView;
import com.solum.xplain.core.market.value.MarketDataProviderTickerView;
import com.solum.xplain.core.market.value.MarketDataTicker;
import com.solum.xplain.core.mdvalue.entity.HasProviderTicker;
import com.solum.xplain.core.mdvalue.entity.MarketDataValueUniqueKey;
import com.solum.xplain.core.mdvalue.value.MarketDataValueCombinedView;
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueCombinedView;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

@ToString
@EqualsAndHashCode
public class MarketDataKeyResolver {

  private final List<MarketDataKeyView> keys;
  private final Set<MarketDataTicker> providerTicker;

  public MarketDataKeyResolver(List<MarketDataKeyView> keys) {
    this.providerTicker =
        keys.stream()
            .map(c -> ofNullable(c.getProviderTickers()))
            .flatMap(Optional::stream)
            .flatMap(Collection::stream)
            .flatMap(
                t ->
                    t.getBidAskType().getSupportedValueBidAsks().stream()
                        .map(bidAsk -> fromMdk(t, bidAsk)))
            .collect(toSet());
    this.keys = keys;
  }

  public Map<String, Boolean> marketDataKeysHasAnyProvider() {
    return keys.stream()
        .collect(
            Collectors.toMap(
                MarketDataKeyView::getKey,
                mdk -> CollectionUtils.isNotEmpty(mdk.getProviderTickers())));
  }

  public List<ResolvedMarketDataValueView> resolvedValues(List<MarketDataValueFlatView> values) {
    return resolvedValuesStream(values).toList();
  }

  public Stream<ResolvedMarketDataValueCombinedView> resolvedCombinedValues(
      List<MarketDataValueFlatView> values) {
    return resolvedValuesStream(values).map(this::toCombinedView);
  }

  private Stream<ResolvedMarketDataValueView> resolvedValuesStream(
      List<MarketDataValueFlatView> values) {
    return resolvedValues(values, false);
  }

  public List<ResolvedMarketDataValueView> allResolvedValues(List<MarketDataValueFlatView> values) {
    return resolvedValues(values, true).toList();
  }

  private Stream<ResolvedMarketDataValueView> resolvedValues(
      List<MarketDataValueFlatView> values, boolean includeEmptyValues) {
    var valuesByTicker = values.stream().collect(groupingBy(MarketDataTicker::fromMd));
    return keys.stream()
        .map(v -> toResolvedView(v, valuesByTicker, includeEmptyValues))
        .flatMap(Optional::stream);
  }

  public List<MarketDataValueFlatView> unresolvedValues(List<MarketDataValueFlatView> values) {
    return filterUnresolvableValues(values);
  }

  public <T extends HasProviderTicker> List<T> filterUnresolvableValues(List<T> values) {
    if (values == null || values.isEmpty()) {
      return List.of();
    }
    return values.stream().filter(this::isUnresolvable).toList();
  }

  private Optional<ResolvedMarketDataValueView> toResolvedView(
      MarketDataKeyView keyView,
      Map<MarketDataTicker, List<MarketDataValueFlatView>> valuesByTicker,
      boolean includeEmptyValues) {
    return Stream.ofNullable(keyView.getProviderTickers())
        .flatMap(Collection::stream)
        .map(t -> marketDataValue(valuesByTicker, t, includeEmptyValues))
        .flatMap(Collection::stream)
        .collect(
            collectingAndThen(
                Collectors.toUnmodifiableList(), values -> toResolvedView(keyView, values)));
  }

  private List<MarketDataValueFlatView> marketDataValue(
      Map<MarketDataTicker, List<MarketDataValueFlatView>> valuesByTicker,
      MarketDataProviderTickerView t,
      boolean includeEmptyValues) {
    var tickerBidAsks = t.getBidAskType().getSupportedValueBidAsks();
    var resolvedMd =
        tickerBidAsks.stream()
            .map(bidAsk -> fromMdk(t, bidAsk))
            .flatMap(key -> ofNullable(valuesByTicker.get(key)).stream())
            .flatMap(vals -> vals.stream().map(v -> v.applyFactor(t.getFactor())))
            .toList();
    if (includeEmptyValues && resolvedMd.size() < tickerBidAsks.size()) {
      return tickerBidAsks.stream()
          .filter(bidAsk -> resolvedMd.stream().noneMatch(r -> r.getBidAsk().equals(bidAsk)))
          .map(bidAsk -> fromProvider(t, null, bidAsk))
          .collect(
              collectingAndThen(
                  toList(),
                  r ->
                      ImmutableList.<MarketDataValueFlatView>builder()
                          .addAll(resolvedMd)
                          .addAll(r)
                          .build()));
    }
    return resolvedMd;
  }

  private Optional<ResolvedMarketDataValueView> toResolvedView(
      MarketDataKeyView key, List<MarketDataValueFlatView> values) {
    if (values.isEmpty()) {
      return Optional.empty();
    } else {
      var v = new ResolvedMarketDataValueView();
      v.setKey(key.getKey());
      v.setInstrumentType(key.getInstrumentType());
      v.setAssetGroup(key.getAssetGroup());
      v.setValues(values);
      return Optional.of(v);
    }
  }

  public Optional<ErrorItem> unresolvableError(MarketDataValueUniqueKey key) {
    if (isUnresolvable(key)) {
      return Optional.of(
          Error.OBJECT_NOT_FOUND.entity(
              String.format(
                  "Mapping for provider %s, ticker %s and bidAsk %s not found",
                  key.getProvider(), key.getTicker(), key.getBidAsk().getLabel())));
    }
    return Optional.empty();
  }

  public boolean isUnresolvable(HasProviderTicker v) {
    return !providerTicker.contains(fromMd(v));
  }

  public ResolvedMarketDataValueCombinedView toCombinedView(ResolvedMarketDataValueView view) {
    var groupedView = new ResolvedMarketDataValueCombinedView();
    groupedView.setKey(view.getKey());
    groupedView.setAssetGroup(view.getAssetGroup());
    groupedView.setInstrumentType(view.getInstrumentType());
    groupedView.setValues(combineValues(view.getValues()));
    return groupedView;
  }

  private List<MarketDataValueCombinedView> combineValues(List<MarketDataValueFlatView> values) {
    var grouped = values.stream().collect(groupingBy(MarketDataGroupByDateAndProvider::new));
    var builder = ImmutableList.<MarketDataValueCombinedView>builder();
    for (var e : grouped.entrySet()) {
      var combined = new MarketDataValueCombinedView();
      combined.setDate(e.getKey().getDate());
      combined.setProvider(e.getKey().getProvider());
      combined.setTicker(e.getKey().getTicker());
      combined.setAskValue(resolveValue(e.getValue(), ASK));
      combined.setMidValue(resolveValue(e.getValue(), MID));
      combined.setBidValue(resolveValue(e.getValue(), BID));
      builder.add(combined);
    }
    return builder.build();
  }

  private BigDecimal resolveValue(List<MarketDataValueFlatView> values, ValueBidAskType type) {
    return values.stream()
        .filter(v -> v.getBidAsk() == type)
        .map(MarketDataValueFlatView::getValue)
        .findFirst()
        .orElse(null);
  }

  @Data
  private static class MarketDataGroupByDateAndProvider {

    private final LocalDate date;
    private final String provider;
    private final String ticker;

    public MarketDataGroupByDateAndProvider(MarketDataValueFlatView view) {
      this.date = view.getDate();
      this.provider = view.getProvider();
      this.ticker = view.getTicker();
    }
  }
}
