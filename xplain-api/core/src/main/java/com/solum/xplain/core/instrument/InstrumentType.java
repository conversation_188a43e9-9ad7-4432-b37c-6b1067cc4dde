package com.solum.xplain.core.instrument;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.solum.xplain.core.curvegroup.curvecredit.xva.CoreXvaInstrumentType;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(anyOf = {CoreInstrumentType.class, CoreXvaInstrumentType.class})
@JsonIgnoreProperties(
    value = {
      "name",
      "label",
      "sortOrder",
      "assetClass",
      "instrumentGroup",
      "priceGroup",
      "curveConfigInstrument"
    })
public interface InstrumentType {

  String name();

  String getLabel();

  AssetClass getAssetClass();

  InstrumentGroup getInstrumentGroup();

  int getSortOrder();

  default AssetGroup assetClassGroup() {
    return getAssetClass().getGroup();
  }

  InstrumentPriceGroup getPriceGroup();

  default boolean isCurveConfigInstrument() {
    return true;
  }
}
