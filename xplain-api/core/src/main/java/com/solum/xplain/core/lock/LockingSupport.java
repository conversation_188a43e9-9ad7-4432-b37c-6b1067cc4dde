package com.solum.xplain.core.lock;

import static com.solum.xplain.core.error.Error.UNEXPECTED_ERROR;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.datagrid.ClusterLock;
import com.solum.xplain.shared.datagrid.DataGrid;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LockingSupport {
  private static final String LOCK_NOT_SUPPORTED = "Hazelcast does not support CP subsystem locks";
  private static final Integer TIME_OUT_MS = 2000;

  private final DataGrid dataGrid;
  private final CachedLockStateRepository cachedLockStateRepository;

  /**
   * Tries to acquire locks for the given list of XplainLocks. If any of the locks cannot be
   * acquired, all locks that were acquired are released and an error is returned.
   *
   * @param xplainLocks the list of XplainLocks to acquire
   * @return an Either containing a list of ClusterLocks if all locks were acquired successfully, or
   *     an error if any of the locks could not be acquired
   */
  public Either<ErrorItem, List<ClusterLock>> tryLocks(List<XplainLock> xplainLocks) {
    return eitherClosedLocks(xplainLocks);
  }

  private Either<ErrorItem, List<ClusterLock>> eitherClosedLocks(List<XplainLock> xplainLocks) {
    var locks = xplainLocks.stream().map(this::tryLock).toList();
    if (!ImmutableList.copyOf(Eithers.filterLeft(locks)).isEmpty()) {
      Eithers.filterRight(locks).forEach(this::releaseLock);
      return Either.left(Error.OPERATION_NOT_ALLOWED.entity("One of required entities are locked"));
    }
    return Eithers.sequenceRight(locks).map(ImmutableList::copyOf);
  }

  public Either<ErrorItem, ClusterLock> tryLock(XplainLock xplLock) {
    log.trace("Thread {}: trying to acquire lock {}", threadId(), xplLock.getId());
    Either<ErrorItem, ClusterLock> result =
        Checked.now(() -> dataGrid.getClusterLock(xplLock.getId()))
            .toEither()
            .leftMap(err -> UNEXPECTED_ERROR.entity(LOCK_NOT_SUPPORTED))
            .flatMap(this::lock);
    if (result.isRight()) {
      cachedLockStateRepository.lockObtained(xplLock);
    }
    return result;
  }

  public void releaseLock(ClusterLock lock) {
    lock.unlock();
    log.trace("Thread {}: {} was unlocked", threadId(), lock.getName());
    cachedLockStateRepository.lockReleased(XplainLock.newOf(lock.getName()));
  }

  public <T> Either<ErrorItem, T> doWithLock(XplainLock xplLock, Supplier<T> protectedCode) {
    return tryLock(xplLock)
        .flatMap(
            lock -> {
              try {
                return Either.right(protectedCode.get());
              } catch (Exception e) {
                log.debug(
                    "Thread {}: {} - error in protected code", threadId(), xplLock.getId(), e);
                return Either.left(UNEXPECTED_ERROR.entity(e.getMessage()));
              } finally {
                releaseLock(lock);
              }
            });
  }

  private Either<ErrorItem, ClusterLock> lock(ClusterLock lock) {
    return Checked.now(() -> lock.tryLock(TIME_OUT_MS, TimeUnit.MILLISECONDS))
        .toEither()
        .leftMap(
            err -> {
              log.error("Error fetching lock: ", err);
              return UNEXPECTED_ERROR.entity("Unable to fetch lock");
            })
        .flatMap(locked -> eitherLock(lock, locked));
  }

  private Either<ErrorItem, ClusterLock> eitherLock(ClusterLock lock, Boolean locked) {
    log.trace("Thread {}: {} status locked = {}", threadId(), lock.getName(), locked);
    return Eithers.cond(
        locked,
        Error.OPERATION_NOT_ALLOWED.entity(
            "Data import or update is in progress. Try again later."),
        lock);
  }

  private long threadId() {
    return Thread.currentThread().getId();
  }
}
