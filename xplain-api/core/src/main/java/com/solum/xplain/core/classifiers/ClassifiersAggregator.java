package com.solum.xplain.core.classifiers;

import static com.solum.xplain.core.utils.proxy.ProxyUtils.lazyProxy;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
public class ClassifiersAggregator {

  private final Map<String, Classifier> aggregatedClassifiers;
  private final Map<Class<?>, Classifier> sourceTypeClassifiers;

  @SuppressWarnings("unchecked")
  public ClassifiersAggregator(List<ClassifiersProvider> classifiersProviders) {
    aggregatedClassifiers =
        lazyProxy(
            Map.class,
            () ->
                classifiersProviders.stream()
                    .sorted(Comparator.comparing(ClassifiersProvider::sortOrder))
                    .flatMap(c -> c.classifiers().stream())
                    .collect(toMap(Classifier::getId, Function.identity(), this::aggregateValues)));

    sourceTypeClassifiers =
        lazyProxy(
            Map.class,
            () ->
                aggregatedClassifiers.values().stream()
                    .filter(c -> c.getSourceType() != null)
                    .collect(
                        toMap(Classifier::getSourceType, Function.identity(), (c1, c2) -> c1)));
  }

  private Classifier aggregateValues(Classifier c1, Classifier c2) {
    var values =
        Stream.of(c1, c2)
            .map(Classifier::getValues)
            .map(CollectionUtils::emptyIfNull)
            .flatMap(Collection::stream)
            .collect(groupingBy(Classifier::getId, LinkedHashMap::new, toList()))
            .values()
            .stream()
            .map(this::joinValues)
            .toList();

    return new Classifier(c1.getId(), c1.getName(), values, c1.getSourceType());
  }

  private Classifier joinValues(List<Classifier> classifiers) {
    if (classifiers.size() == 1) {
      return classifiers.get(0);
    } else {
      var first = classifiers.get(0);
      var second = classifiers.get(1);
      return aggregateValues(first, second);
    }
  }

  public List<Classifier> classifiers() {
    return new ArrayList<>(aggregatedClassifiers.values());
  }

  public Classifier getClassifierBySourceType(Class<?> sourceType) {
    return sourceTypeClassifiers.get(sourceType);
  }

  public List<Classifier> getClassifierValuesById(String classifierId) {
    if (aggregatedClassifiers.containsKey(classifierId)) {
      return aggregatedClassifiers.get(classifierId).getValues();
    } else {
      return Collections.emptyList();
    }
  }
}
