package com.solum.xplain.core.common.validation.identifier;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ExternalIdentifierValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ValidExternalIdentifier {

  String message() default
      "{" + IdentifierValidationUtils.INVALID_EXTERNAL_IDENTIFIER_MESSAGE_KEY + "}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
