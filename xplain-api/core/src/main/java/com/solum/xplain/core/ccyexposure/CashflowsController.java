package com.solum.xplain.core.ccyexposure;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CCY_EXPOSURE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CCY_EXPOSURE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;

import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.ccyexposure.value.CashflowSearchForm;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ccy-exposure")
public class CashflowsController {

  private final CcyExposureControllerService service;

  @Operation(summary = "Get Ccy Exposure Cashflows")
  @CommonErrors
  @GetMapping("/{ccyExposureId}/cashflows")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public List<CashflowView> getCashflows(
      @PathVariable("ccyExposureId") String ccyExposureId, @RequestParam LocalDate stateDate) {
    return service.getCashflows(ccyExposureId, stateDate);
  }

  @Operation(summary = "Get versions for a single cashflow")
  @CommonErrors
  @GetMapping("/cashflows/{entityId}/versions")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public List<CashflowView> getVersions(@PathVariable("entityId") String entityId) {
    return service.getCashflowsVersions(entityId);
  }

  @Operation(summary = "Get future version of a cashflow")
  @GetMapping("/{ccyExposureId}/cashflows/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public DateList getFutureVersions(
      @PathVariable("ccyExposureId") String ccyExposureId, @Valid CashflowSearchForm search) {
    return service.futureCashflowsVersions(ccyExposureId, search);
  }

  @Operation(summary = "Create cashflows for an exposure")
  @CommonErrors
  @PostMapping("/{ccyExposureId}/cashflows")
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> create(
      @PathVariable("ccyExposureId") String ccyExposureId, @RequestBody @Valid CashflowForm form) {
    return eitherErrorItemResponse(service.createCashflow(ccyExposureId, form));
  }

  @Operation(summary = "Update cashflows version")
  @CommonErrors
  @PutMapping("/cashflows/{entityId}/{versionDate}")
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> update(
      @PathVariable("entityId") String entityId,
      @PathVariable LocalDate versionDate,
      @RequestBody @Valid CashflowForm form) {
    return eitherErrorItemResponse(service.updateCashflow(entityId, versionDate, form));
  }

  @Operation(summary = "Delete cashflows version")
  @CommonErrors
  @PutMapping("/cashflows/{entityId}/{versionDate}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> deleteVersion(
      @PathVariable String entityId,
      @PathVariable LocalDate versionDate,
      @Valid @RequestBody ArchiveEntityForm form) {
    return eitherErrorItemResponse(service.deleteCashflowVersion(entityId, versionDate, form));
  }

  @CommonErrors
  @Operation(summary = "Export all cashflows list as CSV")
  @GetMapping("/cashflows/cashflows-csv")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<ByteArrayResource> getCashflowListCsv(
      @RequestParam @NotNull LocalDate stateDate) {
    return eitherErrorItemFileResponse(service.getCashflowListCsv(stateDate));
  }

  @CommonErrors
  @Operation(summary = "Export cashflows as CSV for single exposure")
  @GetMapping("/{ccyExposureId}/cashflows/cashflow-csv")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<ByteArrayResource> getCashflowListCsv(
      @PathVariable("ccyExposureId") String ccyExposureId,
      @RequestParam @NotNull LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        service.getCashflowListCsv(BitemporalDate.newOf(stateDate), ccyExposureId));
  }

  @Operation(summary = "Upload all cashflows CSV file")
  @PostMapping(
      value = "/cashflows/upload-cashflows",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<List<EntityId>> uploadCashflows(
      @Valid ImportOptions importOptions, @RequestPart MultipartFile file) throws IOException {
    return eitherErrorItemsResponse(service.uploadCashflows(importOptions, file.getBytes()));
  }

  @Operation(summary = "Upload cashflow CSV file for single exposure")
  @PostMapping(
      value = "/{ccyExposureId}/cashflows/{versionDate}/upload-cashflow",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<List<EntityId>> uploadCashflow(
      @PathVariable("ccyExposureId") String ccyExposureId,
      @PathVariable("versionDate") LocalDate versionDate,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        service.uploadCashflow(importOptions, file.getBytes(), ccyExposureId, versionDate));
  }
}
