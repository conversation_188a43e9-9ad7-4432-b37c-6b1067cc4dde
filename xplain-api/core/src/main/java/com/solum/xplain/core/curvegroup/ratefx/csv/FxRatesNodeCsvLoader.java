package com.solum.xplain.core.curvegroup.ratefx.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.curvegroup.ratefx.validation.ValidFxRateCurrenciesValidator.validForeignCurrencies;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class FxRatesNodeCsvLoader
    extends GenericCsvLoader<CurveGroupFxRatesNodeForm, FxRatesNodeUniqueKey> {
  public static final String DOMESTIC_CURRENCY_FIELD = "DomesticCurrency";
  public static final String FOREIGN_CURRENCY_FIELD = "ForeignCurrency";

  @Override
  protected CsvParserResultBuilder<CurveGroupFxRatesNodeForm, FxRatesNodeUniqueKey> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        FxRatesNodeUniqueKey::from, FxRatesNodeUniqueKey::getIdentifier, parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(DOMESTIC_CURRENCY_FIELD, FOREIGN_CURRENCY_FIELD);
  }

  @Override
  protected Either<ErrorItem, CurveGroupFxRatesNodeForm> parseLine(CsvRow row) {
    return Steps.begin(parseDomesticCurrency(row))
        .then(domesticCcy -> parseForeignCurrency(row, domesticCcy))
        .yield(this::nodeForm);
  }

  private Either<ErrorItem, Currency> parseDomesticCurrency(CsvRow row) {
    return getFieldValue(
        row,
        DOMESTIC_CURRENCY_FIELD,
        v -> validateValue(Currency.of(v), () -> Constants.EXPLICIT_CURRENCIES));
  }

  private Either<ErrorItem, Currency> parseForeignCurrency(CsvRow row, Currency domesticCcy) {
    return getFieldValue(
        row,
        FOREIGN_CURRENCY_FIELD,
        v -> {
          var foreignCcy = Currency.of(v);
          validateValue(foreignCcy, () -> Constants.EXPLICIT_CURRENCIES);
          validateValue(foreignCcy, () -> validForeignCurrencies(domesticCcy));
          return foreignCcy;
        });
  }

  private CurveGroupFxRatesNodeForm nodeForm(Currency domesticCcy, Currency foreignCcy) {
    CurveGroupFxRatesNodeForm f = new CurveGroupFxRatesNodeForm();
    f.setDomesticCurrency(domesticCcy.getCode());
    f.setForeignCurrency(foreignCcy.getCode());
    return f;
  }
}
