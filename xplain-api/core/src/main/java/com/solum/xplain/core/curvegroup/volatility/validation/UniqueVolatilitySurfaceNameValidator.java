package com.solum.xplain.core.curvegroup.volatility.validation;

import static com.solum.xplain.core.curvegroup.volatility.CurveGroupVolatilityRepository.uniqueEntityCriteria;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.validation.UniqueEntitySupport;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class UniqueVolatilitySurfaceNameValidator
    implements ConstraintValidator<UniqueVolatilitySurfaceName, VolatilitySurfaceForm> {

  private final RequestPathVariablesSupport requestPathSupport;
  private final UniqueEntitySupport uniqueSupport;

  public UniqueVolatilitySurfaceNameValidator(
      RequestPathVariablesSupport requestPathSupport, UniqueEntitySupport uniqueSupport) {
    this.requestPathSupport = requestPathSupport;
    this.uniqueSupport = uniqueSupport;
  }

  @Override
  public boolean isValid(VolatilitySurfaceForm form, ConstraintValidatorContext context) {
    String groupId = requestPathSupport.getPathVariable("groupId");
    if (form != null && groupId != null) {
      return isUnique(groupId, form);
    }
    return true;
  }

  @SuppressWarnings("java:S2589")
  private boolean isUnique(@NonNull String groupId, @NonNull VolatilitySurfaceForm form) {
    if (ObjectUtils.anyNull(form.getName(), form.getVersionForm())
        || form.getVersionForm().getValidFrom() == null) {
      return true;
    }
    return !uniqueSupport.existsByCriteria(
        form.getVersionForm().getValidFrom(),
        uniqueEntityCriteria(groupId, form.getName()),
        VolatilitySurface.class);
  }
}
