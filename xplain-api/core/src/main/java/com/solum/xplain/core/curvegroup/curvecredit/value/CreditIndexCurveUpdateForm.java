package com.solum.xplain.core.curvegroup.curvecredit.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_INDEX_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_INDEX_TRANCHE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DOC_CLAUSE_CLASSIFIER_NAME;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.UniqueValues;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditIndexTrancheLongName;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditNodes;
import com.solum.xplain.core.curvegroup.curvecredit.validation.groups.CreditIndexGroup;
import com.solum.xplain.core.curvegroup.curvecredit.validation.groups.CreditIndexTrancheGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ValidCreditIndexTrancheLongName(groups = CreditIndexTrancheGroup.class)
public class CreditIndexCurveUpdateForm extends CreditCurveCommonForm {

  @ValidStringSet(
      groups = CreditIndexGroup.class,
      value = ClassifierSupplier.class,
      supplierArgument = CREDIT_INDEX_CLASSIFIER_NAME)
  @ValidStringSet(
      groups = CreditIndexTrancheGroup.class,
      value = ClassifierSupplier.class,
      supplierArgument = CREDIT_INDEX_TRANCHE_CLASSIFIER_NAME)
  @Schema(description = "Credit Index")
  private String entityLongName;

  @Schema(description = "Credit Index Series")
  private Integer creditIndexSeries;

  @Schema(description = "Credit Index Version")
  private Integer creditIndexVersion;

  @NotNull
  @Schema(description = "Credit Index Curve Start Date")
  private LocalDate creditIndexStartDate;

  @Min(0)
  @Max(1)
  @NotNull
  @Schema(description = "Credit Index Curve factor")
  private BigDecimal creditIndexFactor;

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = DOC_CLAUSE_CLASSIFIER_NAME)
  @Schema(description = "Credit Index Curve Doc Clause")
  private String docClause;

  @Valid
  @UniqueValues
  @ValidCreditNodes(type = CreditCurveNodeType.CREDIT_INDEX, groups = CreditIndexGroup.class)
  @ValidCreditNodes(
      type = CreditCurveNodeType.CREDIT_INDEX_TRANCHE,
      groups = CreditIndexTrancheGroup.class)
  @Schema(description = "Curve nodes array")
  private List<CreditCurveNodeForm> indexNodes;
}
