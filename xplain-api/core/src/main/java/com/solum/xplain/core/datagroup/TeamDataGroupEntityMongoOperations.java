package com.solum.xplain.core.datagroup;

import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.mongodb.client.result.UpdateResult;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.Auditable;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.NamedObjectForm;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrollableEntrySupport;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.value.HasEntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.users.events.UserUpdated;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;

public class TeamDataGroupEntityMongoOperations<
    E extends TeamMarketDataGroup<E>, V extends HasEntityId, F extends NamedObjectForm> {

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final AuditingHandler auditingHandler;
  private final Class<E> entityClass;
  private final Class<V> viewClass;
  private final TeamDataGroupMapper<E, F> mapper;
  private final ScrollableEntrySupport scrollableSupport;

  public TeamDataGroupEntityMongoOperations(
      MongoOperations mongoOperations,
      ConversionService conversionService,
      AuditingHandler auditingHandler,
      Class<E> entityClass,
      Class<V> viewClass,
      TeamDataGroupMapper<E, F> mapper,
      ScrollableEntrySupport scrollableSupport) {
    this.mongoOperations = mongoOperations;
    this.conversionService = conversionService;
    this.auditingHandler = auditingHandler;
    this.entityClass = entityClass;
    this.viewClass = viewClass;
    this.mapper = mapper;
    this.scrollableSupport = scrollableSupport;
  }

  public ScrollableEntry<V> marketDataGroupList(
      XplainPrincipal user,
      ScrollRequest scrollRequest,
      Criteria filter,
      TableFilter tableFilter,
      ProjectionOperation viewProjection) {
    Criteria criteria = new Criteria().andOperator(accessibleByUser(user), filter);
    List<AggregationOperation> baseOperations =
        List.of(
            match(criteria),
            viewProjection,
            match(tableFilter.criteria(viewClass, conversionService)));
    return scrollableSupport.getScrollableEntry(
        baseOperations, scrollRequest, entityClass, viewClass);
  }

  public List<EntityNameView> marketDataGroupList(XplainPrincipal user, Criteria filter) {
    Criteria criteria = new Criteria().andOperator(accessibleByUser(user), filter);
    List<AggregationOperation> baseOperations = List.of(match(criteria), nameProjection());
    return mongoOperations
        .aggregate(newAggregation(entityClass, baseOperations), EntityNameView.class)
        .getMappedResults();
  }

  public Optional<EntityNameView> dataGroupName(String id) {
    List<AggregationOperation> baseOperations =
        List.of(match(nonArchivedGroup(id)), nameProjection());

    return ofNullable(
        mongoOperations
            .aggregate(newAggregation(entityClass, baseOperations), EntityNameView.class)
            .getUniqueMappedResult());
  }

  public Either<ErrorItem, V> dataGroupView(
      XplainPrincipal user, String marketDataGroupId, ProjectionOperation viewProjection) {
    Criteria criteria =
        new Criteria().andOperator(accessibleByUser(user), nonArchivedGroup(marketDataGroupId));

    var mappedResult =
        mongoOperations
            .aggregate(newAggregation(entityClass, match(criteria), viewProjection), viewClass)
            .getUniqueMappedResult();

    return mappedResult != null ? right(mappedResult) : left(notFound());
  }

  public List<V> dataGroupViewList(XplainPrincipal user, ProjectionOperation viewProjection) {
    Criteria criteria = new Criteria().andOperator(accessibleByUser(user), nonArchivedGroup());

    return mongoOperations
        .aggregate(newAggregation(entityClass, match(criteria), viewProjection), viewClass)
        .getMappedResults();
  }

  private Criteria accessibleByUser(XplainPrincipal user) {
    List<ObjectId> teamIds = user.getTeams();
    return new Criteria()
        .orOperator(
            where(TeamMarketDataGroup.Fields.teamIds).in(teamIds),
            where(TeamMarketDataGroup.Fields.allowAllTeams).is(true));
  }

  public Either<ErrorItem, E> validEntity(String id) {
    return mongoOperations
        .query(entityClass)
        .matching(query(nonArchivedGroup(id)))
        .first()
        .map(Either::<ErrorItem, E>right)
        .orElse(Either.left(notFound()));
  }

  public Either<ErrorItem, EntityId> archive(String marketDataGroupId) {
    return validEntity(marketDataGroupId).map(md -> saveWithDiff(md, mapper.copyArchived(md)));
  }

  public Either<ErrorItem, EntityId> insert(F newForm) {
    E group = mapper.toEntity(newForm);
    mongoOperations.insert(group);
    return right(entityId(group.getId()));
  }

  public Either<ErrorItem, EntityId> update(F form, String marketDataGroupId) {
    return validEntity(marketDataGroupId)
        .map(
            existing -> {
              var newEntity = this.mapper.toEntity(form, mapper.copy(existing));
              var entityId = saveWithDiff(existing, newEntity);
              return EntityId.entityId(entityId.getId());
            });
  }

  protected EntityId saveWithDiff(E existing, E updated) {
    var diff = existing.diff(updated);
    if (diff.numberOfDiffs() > 0) {
      updated.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
    }
    var saved = mongoOperations.save(updated);
    return EntityId.entityId(saved.getId());
  }

  public boolean existsByName(String name, String excludeSelfId) {
    Criteria findDuplicate =
        where(TeamMarketDataGroup.Fields.name)
            .is(name)
            .and(TeamMarketDataGroup.Fields.archived)
            .is(false);
    if (StringUtils.isNotEmpty(excludeSelfId)) {
      findDuplicate = findDuplicate.and(AuditableDiffable.Fields.id).ne(excludeSelfId);
    }
    return mongoOperations.exists(query(findDuplicate), entityClass);
  }

  public Either<ErrorItem, EntityId> groupUpdated(XplainPrincipal user, String marketDataGroupId) {
    Update update =
        new Update()
            .set(Auditable.Fields.modifiedBy, AuditUser.of(user))
            .set(Auditable.Fields.modifiedAt, LocalDateTime.now());

    UpdateResult writeResult =
        mongoOperations.updateFirst(
            query(where(AuditableDiffable.Fields.id).is(marketDataGroupId)), update, entityClass);

    return Eithers.cond(writeResult.getMatchedCount() > 0, notFound(), entityId(marketDataGroupId));
  }

  public ProjectionOperation nameProjection() {
    return project().and(TeamMarketDataGroup.Fields.name).as(EntityNameView.Fields.name);
  }

  private Criteria nonArchivedGroup(String id) {
    return where(AuditableDiffable.Fields.id).is(id).andOperator(nonArchivedGroup());
  }

  private Criteria nonArchivedGroup() {
    return where(TeamMarketDataGroup.Fields.archived).is(false);
  }

  public void onUserUpdated(UserUpdated event) {
    var lastModifiedBy = Auditable.Fields.modifiedBy;
    var form = event.getForm();
    Update update =
        Update.update(joinPaths(lastModifiedBy, AuditUser.Fields.name), form.getName())
            .set(joinPaths(lastModifiedBy, AuditUser.Fields.username), form.getUsername());
    mongoOperations.updateMulti(
        query(where(joinPaths(lastModifiedBy, AuditUser.Fields.userId)).is(event.getEntityId())),
        update,
        entityClass);
  }

  private ErrorItem notFound() {
    return OBJECT_NOT_FOUND.entity("Object not found [" + viewClass.getSimpleName() + "]");
  }

  private ErrorItem noneFound() {
    return OBJECT_NOT_FOUND.entity("None found [" + viewClass.getSimpleName() + "]");
  }
}
