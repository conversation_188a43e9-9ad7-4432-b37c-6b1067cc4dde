package com.solum.xplain.core.classifiers.type;

import static com.opengamma.strata.basics.date.HolidayCalendarIds.BRBD;

import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;

/**
 * Only the day counts used in conventions shall be permissible in Xplain. The following day counts
 * are NOT used in conventions:
 *
 * <ul>
 *   <li>DayCounts.THIRTY_360_PSA - 30/360 PSA
 *   <li>DayCounts.THIRTY_EPLUS_360 - 30E+/360
 *   <li>DayCounts.THIRTY_E_365 - 30E/365
 *   <li>DayCounts.THIRTY_U_360_EOM - 30U/360 EOM
 *   <li>DayCounts.ACT_364 - Act/364
 *   <li>DayCounts.ACT_365_25 - Act/365.25
 *   <li>DayCounts.ACT_ACT_AFB - Act/Act AFB
 *   <li>DayCounts.ACT_ACT_ICMA - Act/Act ICMA
 *   <li>DayCounts.NL_360 - NL/360
 *   <li>DayCounts.NL_365 - NL/365
 * </ul>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum SupportedDayCount {
  ONE_ONE(DayCounts.ONE_ONE, "One/One"),
  THIRTY_360_ISDA(DayCounts.THIRTY_360_ISDA, null),
  THIRTY_E_360(DayCounts.THIRTY_E_360, null),
  THIRTY_E_360_ISDA(DayCounts.THIRTY_E_360_ISDA, null),
  THIRTY_U_360(DayCounts.THIRTY_U_360, null),
  ACT_360(DayCounts.ACT_360, null),
  ACT_365_ACTUAL(DayCounts.ACT_365_ACTUAL, null),
  ACT_365F(DayCounts.ACT_365F, null),
  ACT_365L(DayCounts.ACT_365L, null),
  ACT_ACT_ISDA(DayCounts.ACT_ACT_ISDA, null),
  ACT_ACT_YEAR(DayCounts.ACT_ACT_YEAR, null),
  BUS252_BRBD(DayCount.ofBus252(BRBD), null), // Not Supported for Loan Notes
  ACT_ACT_ICMA(DayCounts.ACT_ACT_ICMA, null); // Only for Loan Notes

  private final DayCount dayCount;
  private final @Nullable String overrideLabel;

  public DayCount getDayCount() {
    return dayCount;
  }

  public String dayCountName() {
    return dayCount.getName();
  }

  public String label() {
    return Optional.ofNullable(overrideLabel).orElse(dayCount.getName());
  }

  public static Optional<SupportedDayCount> ofOGLabel(String ogLabel) {
    return Stream.of(values()).filter(v -> v.dayCountName().equalsIgnoreCase(ogLabel)).findAny();
  }

  public static Optional<SupportedDayCount> of(String label) {
    return Stream.of(values()).filter(v -> v.label().equalsIgnoreCase(label)).findAny();
  }

  public static List<SupportedDayCount> supportedDayCounts() {
    return Stream.of(values())
        .filter(Predicate.not(SupportedDayCount.ACT_ACT_ICMA::equals))
        .toList();
  }

  public static List<SupportedDayCount> supportedLoanDayCounts() {
    return Stream.of(values())
        .filter(Predicate.not(SupportedDayCount.BUS252_BRBD::equals))
        .toList();
  }

  public static List<SupportedDayCount> supportedJpStandardDayCounts() {
    return supportedLoanDayCounts().stream()
        .filter(Predicate.not(SupportedDayCount.ACT_ACT_ICMA::equals))
        .toList();
  }
}
