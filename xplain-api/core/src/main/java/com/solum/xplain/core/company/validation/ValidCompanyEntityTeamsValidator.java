package com.solum.xplain.core.company.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.company.CompanyTeamValidationService;
import com.solum.xplain.core.company.form.CompanyLegalEntityUpdateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidCompanyEntityTeamsValidator
    implements ConstraintValidator<ValidCompanyTeams, CompanyLegalEntityUpdateForm> {

  private final RequestPathVariablesSupport support;
  private final CompanyTeamValidationService companyTeamValidationService;

  public ValidCompanyEntityTeamsValidator(
      RequestPathVariablesSupport support,
      CompanyTeamValidationService companyTeamValidationService) {
    this.support = support;
    this.companyTeamValidationService = companyTeamValidationService;
  }

  @Override
  public boolean isValid(CompanyLegalEntityUpdateForm form, ConstraintValidatorContext context) {

    var companyId = support.getPathVariable("companyId");
    if (form != null && allNotNull(companyId, form.getAllowedTeamsForm())) {
      boolean validTeams =
          companyTeamValidationService.validCompanyTeams(companyId, form.getAllowedTeamsForm());
      if (!validTeams) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate("{ValidCompanyTeams.NotValid}")
            .addPropertyNode("allowAllTeamForm.teamIds")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
