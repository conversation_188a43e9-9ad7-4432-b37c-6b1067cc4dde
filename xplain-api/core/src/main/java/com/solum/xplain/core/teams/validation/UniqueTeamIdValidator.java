package com.solum.xplain.core.teams.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UniqueTeamIdValidator implements ConstraintValidator<UniqueTeamId, TeamForm> {

  private final TeamRepository repository;
  private final RequestPathVariablesSupport variablesSupport;

  public boolean isValid(TeamForm team, ConstraintValidatorContext context) {
    if (team != null && team.getExternalId() != null) {
      final String id = variablesSupport.getPathVariable("id");
      if (repository.existsByExternalIdExcluding(team.getExternalId(), id)) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
            .addPropertyNode(TeamForm.Fields.externalId)
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
