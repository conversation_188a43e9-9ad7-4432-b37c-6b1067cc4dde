package com.solum.xplain.core.company.mapper;

import com.solum.xplain.core.company.entity.CompanyIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings;
import com.solum.xplain.core.company.entity.IpvValuationProviders;
import com.solum.xplain.core.company.entity.IpvValuationSettings;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.RuleBuilder;
import org.mapstruct.Mapper;

@Mapper(uses = {IpvValuationProvidersMapper.class})
public abstract class IpvProvidersRuleMapper extends IpvValuationRulesMapper {
  public static final String PROVIDERS_FACT = "providers";

  /**
   * Return a set of rules triggered by the settings company matching the company of the trade fact.
   * The returned set of rules will set the providers fact with the providers from the settings.
   */
  public Rule toProvidersRule(CompanyIpvSettings companyIpvSettings) {
    List<Rule> providerRules = toProvidersRules(companyIpvSettings);
    String companyId = companyIpvSettings.getEntityId();
    Optional<String> externalCompanyId = toExternalCompanyId(companyId);
    return rulesService.predicated(
        ruleBuilder
            .name(companyId)
            .description("Company provider rule")
            .when(facts -> tradeCompanyMatches(externalCompanyId, facts))
            .build(),
        Set.of(rulesService.oneOf(providerRules)),
        COMPANY_RULE_PRIORITY);
  }

  /**
   * Return a set of rules triggered by the settings company & entity matching those of the trade
   * fact. The returned set of rules will set the providers fact with the providers from the
   * settings.
   */
  public Rule toProvidersRule(CompanyLegalEntityIpvSettings companyLegalEntityIpvSettings) {
    List<Rule> providerRules = toProvidersRules(companyLegalEntityIpvSettings);
    String companyId = companyLegalEntityIpvSettings.getCompanyId();
    String entityId = companyLegalEntityIpvSettings.getEntityId();
    Optional<String> externalCompanyId = toExternalCompanyId(companyId);
    Optional<String> externalEntityId = toExternalEntityId(companyId, entityId);
    return rulesService.predicated(
        ruleBuilder
            .name(entityId)
            .description("Entity provider rule")
            .when(facts -> tradeCompanyAndEntityMatches(externalCompanyId, externalEntityId, facts))
            .build(),
        Set.of(rulesService.oneOf(providerRules)),
        ENTITY_RULE_PRIORITY);
  }

  protected List<Rule> toProvidersRules(IpvValuationSettings ipvValuationSettings) {
    return toProductProvidersRules(ipvValuationSettings.getProducts());
  }

  protected abstract List<Rule> toProductProvidersRules(List<IpvValuationProviders> providers);

  protected Rule toProductProvidersRule(IpvValuationProviders providers) {
    ProductType productType = providers.getProductType();
    return new RuleBuilder()
        .name(productType.name())
        .description(productType.name() + " provider rule")
        .when(facts -> facts.<TradeFact>get(TRADE_FACT).getProductType() == productType)
        .then(
            facts ->
                facts.put(
                    PROVIDERS_FACT, ipvValuationProvidersMapper.toProvidersValueObject(providers)))
        .build();
  }
}
