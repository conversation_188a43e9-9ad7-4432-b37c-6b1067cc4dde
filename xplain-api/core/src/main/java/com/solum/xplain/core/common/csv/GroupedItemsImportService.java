package com.solum.xplain.core.common.csv;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static com.solum.xplain.core.common.EitherUtils.errorsOrRight;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.futureVersionExists;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.newVersionViable;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.orErrors;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.unexpectedDuplicateAction;
import static com.solum.xplain.core.error.Error.MISSING_ENTRY;
import static java.util.stream.Collectors.joining;

import com.google.common.collect.Sets;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;

/**
 * Base class for importing rows where each row maps into a list within the target versioned entity.
 *
 * @param <E> The versioned entity into which multiple items will be imported
 * @param <F> The form of each item to be imported
 * @param <K> A key to indentify each item
 */
public abstract class GroupedItemsImportService<E extends VersionedEntity, F, K extends ItemKey>
    extends LoggingImportService {

  protected GroupedItemsImportService(AuditEntryService auditEntryService) {
    super(auditEntryService);
  }

  protected abstract String entityIdentifier(E entity);

  protected abstract Either<ErrorItem, EntityId> update(
      E entity, List<F> forms, NewVersionFormV2 versionForm);

  protected abstract boolean hasFutureVersions(E entity, LocalDate stateDate);

  protected ImportResult maybeImport(
      ParsingMode parsingMode,
      ItemsGroupCsvResult<F> parsedResult,
      Function<List<NamedList<F>>, List<LogItem>> importFn) {
    var warnings = parsedResult.getWarnings();
    var errors = parsedResult.getErrors();
    return parsedResult
        .toEither(parsingMode.failOnError())
        .flatMap(r -> errorsOrRight(r.getErrors(), parsedResult.getNamedLists()))
        .map(importFn)
        .fold(
            l -> new ImportResult(joinLogItems(l, warnings), List.of()),
            r -> new ImportResult(joinLogItems(r, warnings), errors));
  }

  protected final List<LogItem> importForEntity(
      E entity, ImportItems<F, K, F> importItems, ImportOptions importOptions) {
    var duplicateAction = importOptions.getDuplicateAction();
    return switch (duplicateAction) {
      case ERROR -> onError(entity, importItems, importOptions);
      case APPEND_DELETE -> onAppendDelete(entity, importItems, importOptions);
      case APPEND -> onAppend(entity, importItems, importOptions);
      default -> List.of(unexpectedDuplicateAction(duplicateAction));
    };
  }

  private List<LogItem> onError(
      E entity, ImportItems<F, K, F> importItems, ImportOptions importOptions) {
    var errors = validate(entity, importItems, importOptions);
    return asLogItems(errors);
  }

  private List<LogItem> onAppendDelete(
      E entity, ImportItems<F, K, F> importItems, ImportOptions importOptions) {
    if (!importItems.getNewKeys().isEmpty() || !importItems.getSpareKeys().isEmpty()) {
      return updateItems(entity, importItems, importOptions, true);
    } else {
      return List.of();
    }
  }

  private List<LogItem> onAppend(
      E entity, ImportItems<F, K, F> importItems, ImportOptions importOptions) {
    if (!importItems.getNewKeys().isEmpty()) {
      return updateItems(entity, importItems, importOptions, false);
    } else {
      return List.of();
    }
  }

  private List<ErrorItem> validate(
      E entity, ImportItems<F, K, F> importItems, ImportOptions importOptions) {
    var missingItemsErrors = validateMissingItems(entity, importItems);
    var futureVersionsErrors = validateFutureVersions(entity, importOptions.getStateDate());
    var viableNewVersionsErrors = validateViableNewVersion(entity, importOptions.getStateDate());
    return join(missingItemsErrors, futureVersionsErrors, viableNewVersionsErrors);
  }

  private List<ErrorItem> validateMissingItems(E entity, ImportItems<F, K, F> importItems) {
    return importItems.getSpareKeys().stream()
        .map(k -> missingItemError(k, entityIdentifier(entity)))
        .toList();
  }

  private List<ErrorItem> validateViableNewVersion(E entity, LocalDate stateDate) {
    var entityValidFrom = entity.getValidFrom();
    return orErrors(
        Objects.isNull(entityValidFrom) || stateDate.isAfter(entityValidFrom),
        newVersionViable(entityIdentifier(entity)));
  }

  private List<ErrorItem> validateFutureVersions(E entity, LocalDate stateDate) {
    boolean hasFutureVersions = hasFutureVersions(entity, stateDate);
    return orErrors(hasFutureVersions, futureVersionExists(entityIdentifier(entity)));
  }

  private List<LogItem> updateItems(
      E entity,
      ImportItems<F, K, F> importItems,
      ImportOptions importOptions,
      boolean removeSpareItems) {
    var vf =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions, entity.getValidFrom(), importOptions::getFutureVersionsAction);

    var itemForms = itemForms(importItems, removeSpareItems);

    var eitherId = update(entity, itemForms, vf);
    var updateDescription = description(entity, importItems, removeSpareItems);
    return List.of(createLogItem(updateDescription, eitherId));
  }

  private List<F> itemForms(ImportItems<F, K, F> importItems, boolean removeSpare) {
    var itemsToRemove = removeSpare ? importItems.getSpareKeys() : Set.<K>of();
    var preservedItemForms =
        Sets.difference(importItems.getExistingKeys(), itemsToRemove).stream()
            .map(importItems::existingItem)
            .toList();
    var newItemForms = importItems.getNewKeys().stream().map(importItems::importItem).toList();
    return join(preservedItemForms, newItemForms);
  }

  private String description(E entity, ImportItems<F, K, F> importItems, boolean removeSpareItems) {
    Set<K> newKeys = importItems.getNewKeys();
    Set<K> removeKeys = removeSpareItems ? importItems.getSpareKeys() : Set.of();
    return String.format(
        "Updated %s. Added %d %s and removed %d %s",
        entityIdentifier(entity),
        newKeys.size(),
        keysString(newKeys),
        removeKeys.size(),
        keysString(removeKeys));
  }

  private String keysString(Set<K> keys) {
    if (keys.isEmpty()) {
      return StringUtils.EMPTY;
    } else {
      return keys.stream().map(ItemKey::getIdentifier).collect(joining(", ", "[", "]"));
    }
  }

  private ErrorItem missingItemError(ItemKey itemKey, String entityName) {
    return new ErrorItem(
        MISSING_ENTRY,
        String.format(
            "%s %s in %s %s is missing",
            StringUtils.capitalize(itemKey.getEntityTypeName()),
            itemKey.getIdentifier(),
            StringUtils.lowerCase(itemKey.getParentEntityTypeName()),
            entityName));
  }
}
