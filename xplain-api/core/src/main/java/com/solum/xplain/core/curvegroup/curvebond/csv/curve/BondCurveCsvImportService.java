package com.solum.xplain.core.curvegroup.curvebond.csv.curve;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.BaseVersionedImportService;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvebond.BondCurveRepository;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveSearch;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BondCurveCsvImportService
    extends BaseVersionedImportService<BondCurveForm, String, BondCurve> {

  private final BondCurveRepository curveRepository;
  private final BondCurveCsvLoader csvLoader;

  public BondCurveCsvImportService(
      AuditEntryService auditEntryService,
      BondCurveRepository curveRepository,
      BondCurveCsvLoader csvLoader) {
    super(auditEntryService);
    this.curveRepository = curveRepository;
    this.csvLoader = csvLoader;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importCurves(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(parsed -> importCurves(groupId, parsed, importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private ImportResult importCurves(
      String groupId, CsvParserResult<BondCurveForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    var existingItems =
        curveRepository.getActiveCurves(
            groupId, BitemporalDate.newOf(importOptions.getStateDate()));
    var importItems =
        ImportItems.<BondCurveForm, String, BondCurve>builder()
            .existingActiveItems(existingItems)
            .existingItemToKeyFn(BondCurve::getName)
            .importItems(forms)
            .importItemToKeyFn(BondCurveForm::getName)
            .build();
    var importLogs = importItems(groupId, importOptions, importItems);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  @Override
  public String getCollection() {
    return BondCurve.BOND_CURVE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "Bond curves";
  }

  @Override
  protected String getIdentifier(String key) {
    return key;
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(String groupId, BondCurveForm f) {
    return curveRepository.createCurve(groupId, f);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(BondCurve e, BondCurveForm f) {
    return curveRepository.updateCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(BondCurve e, ArchiveEntityForm f) {
    return curveRepository.archiveCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected boolean hasFutureVersions(String groupId, String key, LocalDate stateDate) {
    var f = new BondCurveSearch(key, stateDate);
    return curveRepository.getFutureVersions(groupId, f).notEmpty();
  }
}
