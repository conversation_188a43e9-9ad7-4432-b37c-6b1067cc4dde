package com.solum.xplain.core.ccyexposure.csv;

import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_CURRENCY_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_DESCRIPTION_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_NAME_FIELD;

import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;

public class CcyExposureCsvMapper extends CsvMapper<CcyExposureView> {

  private static final List<CsvColumn<CcyExposureView>> COLUMNS =
      List.of(
          CsvColumn.text(
              CcyExposureView.Fields.name, CCY_EXPOSURE_NAME_FIELD, CcyExposureView::getName),
          CsvColumn.text(
              CcyExposureView.Fields.currency,
              CCY_EXPOSURE_CURRENCY_FIELD,
              CcyExposureView::getCurrency),
          CsvColumn.text(
              CcyExposureView.Fields.description,
              CCY_EXPOSURE_DESCRIPTION_FIELD,
              CcyExposureView::getDescription));

  public CcyExposureCsvMapper() {
    super(COLUMNS, null);
  }
}
