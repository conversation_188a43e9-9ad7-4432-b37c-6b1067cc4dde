package com.solum.xplain.core.classifiers;

import static com.opengamma.strata.basics.currency.Currency.AUD;
import static com.opengamma.strata.basics.currency.Currency.CAD;
import static com.opengamma.strata.basics.currency.Currency.CHF;
import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.NZD;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_1M;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_3M;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_6M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.NZD_BKBM_1M;
import static com.opengamma.strata.basics.index.IborIndices.NZD_BKBM_3M;
import static com.opengamma.strata.basics.index.IborIndices.NZD_BKBM_6M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_6M;
import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.getAllowedIndicesByType;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.ADDITIONAL_CURRENCIES;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_CURRENCIES_ORDER;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_NAME;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_SD_NAME;
import static com.solum.xplain.extensions.product.ExtendedIborContractSpecs.NZD_BKBM_3M_QUARTERLY_FUTURE_NAME;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.IborIndices;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.collect.tuple.Pair;
import com.opengamma.strata.market.ShiftType;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNodeClashAction;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators;
import com.opengamma.strata.product.common.SettlementType;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.extensions.calendar.XplainHolidayCalendars;
import com.solum.xplain.extensions.curve.ExtendedCurveInterpolators;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@SuppressWarnings("deprecation")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {

  public static final List<String> FIRST_TIME_ZONES =
      List.of("Europe/London", "America/New_York", "Europe/Paris");

  public static final String ACT_360_DAY_COUNT = "Act/360";

  public static final Set<Frequency> SWAP_LEG_FREQUENCIES =
      ImmutableSet.<Frequency>builder()
          .add(Frequency.P1W)
          .add(Frequency.P4W)
          .add(Frequency.P13W)
          .add(Frequency.P26W)
          .add(Frequency.P1M)
          .add(Frequency.P3M)
          .add(Frequency.P6M)
          .add(Frequency.P12M)
          .add(Frequency.ofYears(1))
          .add(Frequency.TERM)
          .build();

  public static final Set<Frequency> CREDIT_FREQUENCIES =
      ImmutableSet.<Frequency>builder()
          .add(Frequency.P1W)
          .add(Frequency.P2W)
          .add(Frequency.P4W)
          .add(Frequency.P1M)
          .add(Frequency.P2M)
          .add(Frequency.ofWeeks(7))
          .add(Frequency.P3M)
          .add(Frequency.P13W)
          .add(Frequency.P4M)
          .add(Frequency.ofMonths(5))
          .add(Frequency.P6M)
          .add(Frequency.P26W)
          .add(Frequency.ofMonths(7))
          .add(Frequency.ofMonths(8))
          .add(Frequency.ofMonths(9))
          .add(Frequency.ofMonths(10))
          .add(Frequency.ofMonths(11))
          .add(Frequency.P12M)
          .add(Frequency.TERM)
          .build();

  public static final Set<Frequency> MIN_NODE_GAPS =
      ImmutableSet.<Frequency>builder()
          .add(Frequency.ofDays(1))
          .add(Frequency.ofDays(2))
          .add(Frequency.ofDays(3))
          .add(Frequency.ofDays(4))
          .add(Frequency.ofDays(5))
          .add(Frequency.ofDays(6))
          .add(Frequency.ofWeeks(1))
          .build();

  public static final Set<Tenor> FRA_SETTLEMENT_VALUES =
      Collections.unmodifiableSet(fraSettlementValues());
  public static final List<String> SERIAL_FUTURE_VALUES =
      ImmutableList.<String>builder()
          .add("2D+1")
          .add("2D+2")
          .add("2D+3")
          .add("2D+4")
          .add("2D+5")
          .add("2D+6")
          .add("2D+7")
          .add("2D+8")
          .add("2D+9")
          .add("2D+10")
          .add("2D+11")
          .add("2D+12")
          .add("1D+1")
          .add("1D+2")
          .add("1D+3")
          .add("1D+4")
          .add("1D+5")
          .add("1D+6")
          .add("1D+7")
          .add("1D+8")
          .add("1D+9")
          .add("1D+10")
          .add("1D+11")
          .add("1D+12")
          .add("0D+1")
          .add("0D+2")
          .add("0D+3")
          .add("0D+4")
          .add("0D+5")
          .add("0D+6")
          .add("0D+7")
          .add("0D+8")
          .add("0D+9")
          .add("0D+10")
          .add("0D+11")
          .add("0D+12")
          .build();
  public static final List<Double> CREDIT_CURVE_FIXED_RATE_BPS =
      List.of(0.0025, 0.005, 0.01, 0.05, 0.1);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_EUR =
      List.of(EUR_EURIBOR_1M, EUR_EURIBOR_3M, EUR_EURIBOR_6M, EUR_EURIBOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_USD =
      List.of(USD_LIBOR_1M, USD_LIBOR_3M, USD_LIBOR_6M, USD_LIBOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_GBP =
      List.of(GBP_LIBOR_1M, GBP_LIBOR_3M, GBP_LIBOR_6M, GBP_LIBOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_AUD =
      List.of(AUD_BBSW_1M, AUD_BBSW_3M, AUD_BBSW_6M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_CAD =
      List.of(CAD_CDOR_1M, CAD_CDOR_3M, CAD_CDOR_6M, CAD_CDOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_CHF =
      List.of(CHF_LIBOR_1M, CHF_LIBOR_3M, CHF_LIBOR_6M, CHF_LIBOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_JPY =
      List.of(JPY_LIBOR_1M, JPY_LIBOR_3M, JPY_LIBOR_6M, JPY_LIBOR_12M);
  public static final List<IborIndex> IBOR_INDICES_DISCOUNT_NZD =
      List.of(NZD_BKBM_1M, NZD_BKBM_3M, NZD_BKBM_6M);

  public static final List<Currency> ALLOWED_DISCOUNTING_CURRENCIES =
      List.of(USD, EUR, GBP, AUD, CAD, CHF, JPY, NZD);
  public static final List<String> ALLOWED_DISCOUNTING_CCY_CODES =
      Collections.unmodifiableList(allowedDiscountingCcyCodes());
  public static final List<Currency> XCCY_FALLBACK_CURRENCIES = List.of(USD, EUR);
  // This should be kept in sync with VolatilitySurfaceConfigurations.VOLATILITY_SURFACES (for
  // non-OIS)
  public static final List<IborIndex> SWAPTION_INDICES =
      List.of(
          IborIndex.of("AED-EIBOR-3M"),
          IborIndices.AUD_BBSW_3M,
          IborIndices.AUD_BBSW_6M,
          IborIndices.CAD_CDOR_3M,
          IborIndices.CHF_LIBOR_1M,
          IborIndices.CHF_LIBOR_3M,
          IborIndices.CHF_LIBOR_6M,
          IborIndex.of("CNY-REPO-1W"),
          IborIndices.CZK_PRIBOR_6M,
          IborIndices.DKK_CIBOR_6M,
          IborIndices.EUR_EURIBOR_1M,
          IborIndices.EUR_EURIBOR_3M,
          IborIndices.EUR_EURIBOR_6M,
          IborIndices.GBP_LIBOR_1M,
          IborIndices.GBP_LIBOR_3M,
          IborIndices.GBP_LIBOR_6M,
          IborIndex.of("HKD-HIBOR-3M"),
          IborIndices.HUF_BUBOR_6M,
          IborIndex.of("ILS-TLBOR-3M"),
          IborIndices.JPY_LIBOR_6M,
          IborIndex.of("KRW-CD-3M"),
          IborIndices.MXN_TIIE_4W,
          IborIndices.MXN_TIIE_13W,
          IborIndices.MXN_TIIE_26W,
          IborIndex.of("MYR-KLIBOR-3M"),
          IborIndices.NOK_NIBOR_6M,
          IborIndices.NZD_BKBM_3M,
          IborIndices.PLN_WIBOR_3M,
          IborIndices.PLN_WIBOR_6M,
          IborIndex.of("SAR-SAIBOR-3M"),
          IborIndices.SEK_STIBOR_3M,
          IborIndex.of("SGD-SOR-1M"),
          IborIndex.of("SGD-SOR-3M"),
          IborIndex.of("SGD-SOR-6M"),
          IborIndex.of("THB-THBFIX-6M"),
          IborIndex.of("TRY-TRLIBOR-3M"),
          IborIndex.of("TWD-TAIBOR-3M"),
          IborIndices.USD_LIBOR_3M,
          IborIndices.ZAR_JIBAR_3M);
  public static final List<IborIndex> CAPFLOOR_INDICES =
      new ImmutableList.Builder<IborIndex>()
          .addAll(SWAPTION_INDICES)
          .add(IborIndices.JPY_LIBOR_1M)
          .add(IborIndices.JPY_LIBOR_3M)
          .build();
  public static final List<IborIndex> IBOR_INDICES = getAllowedIndicesByType(IborIndex.class);
  public static final List<OvernightIndex> OVERNIGHT_INDICES =
      getAllowedIndicesByType(OvernightIndex.class);
  public static final List<PriceIndex> INFLATION_INDICES =
      getAllowedIndicesByType(PriceIndex.class);

  public static final List<Currency> EXPLICIT_CURRENCIES =
      new ImmutableList.Builder<Currency>()
          .addAll(FX_SWAP_CURRENCIES_ORDER)
          .addAll(ADDITIONAL_CURRENCIES)
          .build();

  public static final List<CurveInterpolator> CURVE_INTERPOLATOR =
      new ImmutableList.Builder<CurveInterpolator>()
          .add(CurveInterpolators.DOUBLE_QUADRATIC)
          .add(CurveInterpolators.LINEAR)
          .add(CurveInterpolators.LOG_LINEAR)
          .add(ExtendedCurveInterpolators.LOG_LINEAR_DISCOUNT_FACTOR)
          .add(CurveInterpolators.LOG_NATURAL_SPLINE_DISCOUNT_FACTOR)
          .add(CurveInterpolators.LOG_NATURAL_SPLINE_MONOTONE_CUBIC)
          .add(CurveInterpolators.NATURAL_CUBIC_SPLINE)
          .add(CurveInterpolators.NATURAL_SPLINE)
          .add(CurveInterpolators.NATURAL_SPLINE_NONNEGATIVITY_CUBIC)
          .add(CurveInterpolators.PCHIP)
          .add(CurveInterpolators.PRODUCT_LINEAR)
          .add(CurveInterpolators.PRODUCT_NATURAL_SPLINE)
          .add(CurveInterpolators.PRODUCT_NATURAL_SPLINE_MONOTONE_CUBIC)
          .add(CurveInterpolators.SQUARE_LINEAR)
          .add(ExtendedCurveInterpolators.STEP_LOWER)
          .add(CurveInterpolators.STEP_UPPER)
          .add(CurveInterpolators.TIME_SQUARE)
          .build();

  public static final List<CurveExtrapolator> CURVE_EXTRAPOLATORS =
      new ImmutableList.Builder<CurveExtrapolator>()
          .add(CurveExtrapolators.DISCOUNT_FACTOR_LINEAR_RIGHT_ZERO_RATE)
          .add(CurveExtrapolators.DISCOUNT_FACTOR_QUADRATIC_LEFT_ZERO_RATE)
          .add(CurveExtrapolators.EXCEPTION)
          .add(CurveExtrapolators.EXPONENTIAL)
          .add(CurveExtrapolators.FLAT)
          .add(CurveExtrapolators.INTERPOLATOR)
          .add(CurveExtrapolators.LINEAR)
          .add(CurveExtrapolators.LOG_LINEAR)
          .add(CurveExtrapolators.PRODUCT_LINEAR)
          .add(CurveExtrapolators.QUADRATIC_LEFT)
          .build();

  public static final List<CurveExtrapolator> SURFACE_EXTRAPOLATORS =
      new ImmutableList.Builder<CurveExtrapolator>()
          .add(CurveExtrapolators.EXCEPTION)
          .add(CurveExtrapolators.EXPONENTIAL)
          .add(CurveExtrapolators.FLAT)
          .add(CurveExtrapolators.INTERPOLATOR)
          .add(CurveExtrapolators.LINEAR)
          .add(CurveExtrapolators.LOG_LINEAR)
          .add(CurveExtrapolators.PRODUCT_LINEAR)
          .add(CurveExtrapolators.QUADRATIC_LEFT)
          .build();

  public static final List<Classifier> CURVE_NODE_CLASH_ACTIONS =
      List.of(
          new Classifier(CurveNodeClashAction.DROP_OTHER.toString(), "Futures"),
          new Classifier(CurveNodeClashAction.DROP_THIS.toString(), "Nodes Other Than Futures"));

  public static final List<Classifier> SWAPTION_SETTLEMENT_TYPES =
      List.of(
          new Classifier(SettlementType.CASH.name(), "Cash (PAR_YIELD)"),
          new Classifier(SettlementType.PHYSICAL.name(), "Physical"));

  public static final List<Classifier> DEFAULT_CURVE_VALUE_TYPES =
      List.of(
          new Classifier(ValueType.ZERO_RATE.getName(), "Zero Rate"),
          new Classifier(ValueType.FORWARD_RATE.getName(), "Forward Rate"),
          new Classifier(ValueType.DISCOUNT_FACTOR.getName(), "Discount Factor"));

  public static final List<Classifier> INFLATION_CURVE_VALUE_TYPES =
      List.of(new Classifier(ValueType.PRICE_INDEX.getName(), "Price Index"));

  public static final List<HolidayCalendarId> SUPPORTED_CALENDARS =
      new ImmutableList.Builder<HolidayCalendarId>()
          .addAll(
              XplainHolidayCalendars.CCY_CALENDARS.values().stream().map(Pair::getFirst).toList())
          .add(HolidayCalendarIds.USGS)
          .build();

  public static final List<String> BANK_BILL_90_DAY_FUTURES =
      List.of(
          AUD_BBSW_3M_QUARTERLY_FUTURE_NAME,
          AUD_BBSW_3M_QUARTERLY_FUTURE_SD_NAME,
          NZD_BKBM_3M_QUARTERLY_FUTURE_NAME);

  public static final Set<ShiftType> EXPOSURE_SHIFT_TYPES =
      ImmutableSet.of(ShiftType.ABSOLUTE, ShiftType.RELATIVE);

  private static Set<Tenor> fraSettlementValues() {
    return IntStream.range(1, 25).mapToObj(Tenor::ofMonths).collect(Collectors.toUnmodifiableSet());
  }

  private static List<String> allowedDiscountingCcyCodes() {
    return CollectionUtils.convertCollectionTo(ALLOWED_DISCOUNTING_CURRENCIES, Currency::getCode);
  }
}
