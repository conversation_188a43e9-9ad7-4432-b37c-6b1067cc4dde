package com.solum.xplain.core.providers;

import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.providers.DataProvider.INTERNAL_PROVIDER_CODES;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static io.atlassian.fugue.Either.right;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.providers.value.DataProviderCreateForm;
import com.solum.xplain.core.providers.value.DataProviderUpdateForm;
import com.solum.xplain.core.providers.value.DataProviderView;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.Comparator;
import java.util.List;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class DataProviderRepository {
  private final MongoOperations mongoOperations;
  private final DataProviderMapper mapper;
  private final AuditingHandler auditingHandler;
  private final ConversionService conversionService;

  public DataProviderRepository(
      MongoTemplate mongoOperations,
      DataProviderMapper mapper,
      AuditingHandler auditingHandler,
      ConversionService conversionService) {
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.auditingHandler = auditingHandler;
    this.conversionService = conversionService;
  }

  public Either<ErrorItem, EntityId> save(DataProvider source) {
    var insert = mongoOperations.insert(source);
    return Either.right(EntityId.entityId(insert.getId()));
  }

  public Either<ErrorItem, EntityId> insert(DataProviderCreateForm form) {
    DataProvider source = mongoOperations.insert(mapper.toEntity(form));
    return right(entityId(source.getId()));
  }

  public Either<ErrorItem, EntityId> update(String id, DataProviderUpdateForm form) {
    return update(id, f -> mapper.toEntity(form, f));
  }

  public Either<ErrorItem, EntityId> update(String id, DataProvider form) {
    return update(id, f -> mapper.copy(form, f));
  }

  private Either<ErrorItem, EntityId> update(String id, UnaryOperator<DataProvider> f) {
    return entity(id)
        .flatMap(DataProvider::allowModify)
        .map(
            existing -> {
              var newProvider = f.apply(mapper.copy(existing));
              var diff = existing.diff(newProvider);
              if (diff.numberOfDiffs() > 0) {
                newProvider.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newProvider);
              return entityId(newProvider.getId());
            });
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return entity(id).flatMap(DataProvider::allowModify).map(this::archive);
  }

  public List<EntityId> archiveAll(TableFilter tableFilter) {
    return filteredDataProviders(tableFilter)
        .filter(p -> !INTERNAL_PROVIDER_CODES.contains(p.getExternalId()))
        .map(this::archive)
        .toList();
  }

  private EntityId archive(DataProvider provider) {
    var archived = mapper.copyArchived(provider);
    mongoOperations.save(archived);
    return entityId(archived.getId());
  }

  public Either<ErrorItem, DataProvider> entity(String id) {
    return mongoOperations
        .query(DataProvider.class)
        .matching(notArchivedProvider(id))
        .one()
        .map(Either::<ErrorItem, DataProvider>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Data provider not found")));
  }

  public List<DataProvider> dataProvidersList() {
    return mongoOperations.query(DataProvider.class).matching(query(notArchived())).all();
  }

  private Stream<DataProvider> filteredDataProviders(TableFilter tableFilter) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(notArchived()))
            .add(match(tableFilter.criteria(DataProvider.class, conversionService)))
            .build();
    return mongoOperations.aggregateStream(
        newAggregation(DataProvider.class, operations), DataProvider.class);
  }

  public List<DataProviderView> dataProvidersViewList(
      TableFilter tableFilter, Sort sort, boolean archived) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(where(DataProvider.Fields.archived).is(archived)))
            .add(projectView())
            .add(match(tableFilter.criteria(DataProviderView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    } else {
      operations.add(sort(defaultSort()));
    }
    return dataProviderViews(operations.build());
  }

  public List<DataProviderView> dataProvidersViewListForType(DataProviderType dataProviderType) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(notArchived().and(DataProvider.Fields.types).all(List.of(dataProviderType))))
            .add(projectView())
            .add(sort(defaultSort()));

    return dataProviderViews(operations.build());
  }

  private List<DataProviderView> dataProviderViews(List<AggregationOperation> operations) {
    return mongoOperations
        .aggregate(newAggregation(DataProvider.class, operations), DataProviderView.class)
        .getMappedResults()
        .stream()
        .sorted(providersComparator())
        .toList();
  }

  private Comparator<DataProviderView> providersComparator() {
    return Comparator.comparing(DataProviderView::isInternalProvider).reversed();
  }

  public boolean existsByExternalId(String externalId, String excludeSelfId) {
    Criteria findDuplicate = where(DataProvider.Fields.externalId).is(externalId);
    if (StringUtils.isNotEmpty(excludeSelfId)) {
      findDuplicate = findDuplicate.and(DataProvider.Fields.id).ne(excludeSelfId);
    }
    findDuplicate.and(DataProvider.Fields.archived).is(false);
    return mongoOperations.exists(query(findDuplicate), DataProvider.class);
  }

  public boolean existsByType(String code, DataProviderType type) {
    Criteria findDuplicate = where(DataProvider.Fields.externalId).is(code);
    if (type != null) {
      findDuplicate.and(DataProvider.Fields.types).all(List.of(type));
    }
    findDuplicate.and(DataProvider.Fields.archived).is(false);
    return mongoOperations.exists(query(findDuplicate), DataProvider.class);
  }

  private ProjectionOperation projectView() {
    return project()
        .and(DataProvider.Fields.id)
        .as(DataProviderView.Fields.id)
        .and(DataProvider.Fields.name)
        .as(DataProviderView.Fields.name)
        .and(DataProvider.Fields.externalId)
        .as(DataProviderView.Fields.externalId)
        .and(DataProvider.Fields.createdAt)
        .as(DataProviderView.Fields.createdAt)
        .and(DataProvider.Fields.auditLogs)
        .as(DataProviderView.Fields.auditLogs)
        .and(propertyName(DataProvider.Fields.createdBy, AuditUser.Fields.name))
        .as(DataProviderView.Fields.creatorName)
        .and(propertyName(DataProvider.Fields.createdBy, AuditUser.Fields.userId))
        .as(DataProviderView.Fields.creatorId)
        .and(propertyName(DataProvider.Fields.lastModifiedBy, AuditUser.Fields.name))
        .as(DataProviderView.Fields.modifiedByName)
        .and(DataProvider.Fields.lastModifiedAt)
        .as(DataProviderView.Fields.lastModifiedAt)
        .and(DataProvider.Fields.types)
        .as(DataProviderView.Fields.types);
  }

  private Sort defaultSort() {
    return Sort.by(DataProvider.Fields.externalId);
  }

  private Criteria notArchivedProvider(String id) {
    return where(DataProvider.Fields.id).is(id).andOperator(notArchived());
  }

  private Criteria notArchived() {
    return where(DataProvider.Fields.archived).is(false);
  }
}
