package com.solum.xplain.core.common.validation;

import com.solum.xplain.core.providers.DataProviderRepository;
import com.solum.xplain.core.providers.enums.DataProviderType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class ValidDataProviderCodeValidator
    implements ConstraintValidator<ValidDataProviderCode, String> {

  private final DataProviderRepository providerRepository;
  private DataProviderType type;
  private List<String> excludeProviders;

  public ValidDataProviderCodeValidator(DataProviderRepository providerRepository) {
    this.providerRepository = providerRepository;
  }

  @Override
  public void initialize(ValidDataProviderCode constraintAnnotation) {
    type = constraintAnnotation.type();
    excludeProviders = Arrays.stream(constraintAnnotation.excludeProviders()).toList();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (!StringUtils.isEmpty(value)) {
      if (excludeProviders.contains(value)) {
        return false;
      }
      return providerRepository.existsByType(value, type);
    }
    return true;
  }
}
