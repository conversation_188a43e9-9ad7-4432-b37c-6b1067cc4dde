package com.solum.xplain.core.curvegroup.volatility.csv.surface;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.BaseVersionedImportService;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.volatility.CurveGroupVolatilityRepository;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VolatilitySurfaceCsvImportService
    extends BaseVersionedImportService<VolatilitySurfaceForm, String, VolatilitySurface> {

  private final CurveGroupVolatilityRepository repository;
  private final VolatilitySurfaceCsvLoader csvLoader;

  public VolatilitySurfaceCsvImportService(
      AuditEntryService auditEntryService,
      CurveGroupVolatilityRepository repository,
      VolatilitySurfaceCsvLoader csvLoader) {
    super(auditEntryService);
    this.repository = repository;
    this.csvLoader = csvLoader;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importSurfaces(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(parsed -> importSurfaces(groupId, parsed, importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private ImportResult importSurfaces(
      String groupId,
      CsvParserResult<VolatilitySurfaceForm> parserResult,
      ImportOptions importOptions) {
    var importItems =
        buildImportItems(groupId, importOptions.getStateDate(), parserResult.getParsedLines());
    var importLogs = importItems(groupId, importOptions, importItems);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private ImportItems<VolatilitySurfaceForm, String, VolatilitySurface> buildImportItems(
      String groupId, LocalDate stateDate, List<VolatilitySurfaceForm> forms) {
    var existingItems = fetchSurfaces(groupId, stateDate);
    return ImportItems.<VolatilitySurfaceForm, String, VolatilitySurface>builder()
        .existingActiveItems(existingItems)
        .existingItemToKeyFn(VolatilitySurface::getName)
        .importItems(forms)
        .importItemToKeyFn(VolatilitySurfaceForm::getName)
        .build();
  }

  private List<VolatilitySurface> fetchSurfaces(String groupId, LocalDate stateDate) {
    return repository.getActiveSurfaces(groupId, BitemporalDate.newOf(stateDate));
  }

  @Override
  public String getCollection() {
    return VolatilitySurface.VOLATILITY_SURFACE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "IR Volatility Surfaces";
  }

  @Override
  protected String getIdentifier(String key) {
    return key;
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(String groupId, VolatilitySurfaceForm f) {
    return repository.createSurface(groupId, f);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(VolatilitySurface e, VolatilitySurfaceForm f) {
    return repository.updateSurface(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(VolatilitySurface e, ArchiveEntityForm f) {
    return repository.archiveSurface(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected boolean hasFutureVersions(String groupId, String key, LocalDate stateDate) {
    var s = new VolatilitySurfaceSearch(key, stateDate);
    return repository.getFutureVersions(groupId, s).notEmpty();
  }
}
