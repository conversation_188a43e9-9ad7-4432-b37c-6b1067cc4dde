package com.solum.xplain.core.company.csv;

import com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonCompanyCsvFields extends CommonCompanyEntityCsvFields {
  public static final String COMPANY_NAME = "Company Name";
  public static final String COMPANY_DESCRIPTION = "Company Description";
}
