package com.solum.xplain.core.common;

import com.solum.xplain.core.error.ErrorItem;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@ApiResponse(
    responseCode = "401",
    description = "Unauthorized",
    content = @Content(schema = @Schema(implementation = ErrorItem.class)))
@ApiResponse(
    responseCode = "404",
    description = "Entity not found",
    content = @Content(schema = @Schema(implementation = ErrorItem.class)))
@ApiResponse(
    responseCode = "422",
    content = @Content(schema = @Schema(implementation = ErrorItem.ListOfErrors.class)),
    description = "Error while processing " + "request")
@ApiResponse(responseCode = "200")
public @interface FileUploadErrors {}
