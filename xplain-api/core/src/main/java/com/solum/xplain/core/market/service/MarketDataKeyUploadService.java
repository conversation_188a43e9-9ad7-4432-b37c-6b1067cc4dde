package com.solum.xplain.core.market.service;

import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.description;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateImportItems;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportActionSummary;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LargeVersionedImportItems;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.csv.VersionedImportItems;
import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.csv.LargeFileImporter;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.MarketDataKeyEntity;
import com.solum.xplain.core.market.MarketDataKeyValue;
import com.solum.xplain.core.market.csv.MarketDataKeyCsvLoader;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.market.repository.MarketDataKeyWriteRepository;
import com.solum.xplain.core.market.value.MarketDataKeyCsvForm;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class MarketDataKeyUploadService {

  private final MarketDataKeyWriteRepository writeRepository;
  private final MarketDataKeyRepository readRepository;
  private final MarketDataKeyCsvLoader loader;
  private final AuditEntryService auditEntryService;

  public Either<List<ErrorItem>, ValidationResponse> validate(
      byte[] bytes, ParsingMode parsingMode, LocalDate stateDate) {
    return loader
        .parse(bytes, parsingMode)
        .map(
            forms ->
                validateImportItems(
                    resolveImportItems(stateDate, forms.getParsedLines()), stateDate))
        .flatMap(l -> right(new ValidationResponse(l)));
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadMarketDataKeys(
      byte[] bytes, ImportOptions importOptions) {
    return loader
        .parse(bytes, importOptions.parsingMode())
        .flatMap(forms -> uploadForms(forms, importOptions).leftMap(ErrorItem.ListOfErrors::from));
  }

  private Either<ErrorItem, List<EntityId>> uploadForms(
      CsvParserResult<MarketDataKeyCsvForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    if (readRepository.hasChanges(importOptions.getValidationTimestamp())) {
      return left(stateChanged("Market Data Keys"));
    }
    var importer =
        new LargeFileImporter<MarketDataKeyValue, String, MarketDataKeyEntity>(
            importOptions, writeRepository);
    var importItems = resolveImportItems(importOptions.getStateDate(), forms);

    return Either.<ErrorItem, List<ImportActionSummary>>right(importer.importItems(importItems))
        .map(summaries -> logImport(summaries, parserResult.getWarnings()))
        .map(r -> forms.stream().map(f -> EntityId.entityId(f.getEntityId())).toList());
  }

  private List<ImportActionSummary> logImport(
      List<ImportActionSummary> summaries, List<ErrorItem> logs) {
    var changesCount = summaries.stream().mapToInt(ImportActionSummary::getChangesCount).sum();
    var description = description("Market data key", 0, logs.size(), changesCount);
    auditEntryService.newEntryWithLogs(AuditEntry.of("marketDataKey", description), logs);
    return summaries;
  }

  private VersionedImportItems<MarketDataKeyValue, String, MarketDataKeyEntity> resolveImportItems(
      LocalDate stateDate, List<MarketDataKeyCsvForm> forms) {
    var existingItems = writeRepository.entitiesForUpdate(stateDate);
    return LargeVersionedImportItems.<MarketDataKeyValue, String, MarketDataKeyEntity>builder()
        .existingItems(existingItems)
        .existingItemToKeyFn(e -> e.getEntity().getSemanticId())
        .importItems(List.copyOf(forms))
        .importItemToKeyFn(ResolvableEmbeddedVersionValue::getEntityId)
        .newEntityFn(MarketDataKeyEntity::new)
        .build();
  }
}
