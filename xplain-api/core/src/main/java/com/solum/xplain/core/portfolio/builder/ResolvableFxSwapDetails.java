package com.solum.xplain.core.portfolio.builder;

import static com.solum.xplain.core.portfolio.builder.validation.TradeBuilderValidatorUtils.validateSameBaseCurrencyNotionals;
import static com.solum.xplain.core.portfolio.builder.validation.TradeBuilderValidatorUtils.validateTradeCurrency;
import static com.solum.xplain.core.portfolio.calendars.TradeCalendarUtils.getFxTradeCalendar;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.portfolio.csv.ResolvableFxTradeDetailsBuilder;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
public class ResolvableFxSwapDetails implements ResolvableTradeDetails {

  @NonNull private final LocalDate settlementDate;
  @NonNull private final LocalDate maturityDate;

  // currency for which we pay in the near leg, and receive in the far leg
  @NonNull private final Currency payCurrency;

  // currency for which we receive in the near leg, and pay in the far leg
  @NonNull private final Currency receiveCurrency;

  private final Double nearLegPayCurrencyAmount;
  private final Double nearLegReceiveCurrencyAmount;
  private final Double farLegPayCurrencyAmount;
  private final Double farLegReceiveCurrencyAmount;

  @NonNull private final String businessDayConvention;

  private final Double fxRate;
  private final Double nearDateFxRate;
  private final String payLegExtIdentifier;
  private final String receiveLegExtIdentifier;

  public static class ResolvableFxSwapDetailsBuilder
      implements ResolvableFxTradeDetailsBuilder<ResolvableFxSwapDetails> {}

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    validateTradeCurrency(
        tradeInfo.getTradeCurrency(), payCurrency.getCode(), receiveCurrency.getCode());
    if (fxRate == null) {
      // bespoke trade
      if (payCurrency.getCode().equals(tradeInfo.getTradeCurrency())) {
        validateSameBaseCurrencyNotionals(nearLegPayCurrencyAmount, farLegPayCurrencyAmount);
      } else {
        validateSameBaseCurrencyNotionals(
            nearLegReceiveCurrencyAmount, farLegReceiveCurrencyAmount);
      }
    }
    var calendar = getFxTradeCalendar(receiveCurrency.getCode(), payCurrency.getCode());
    TradeDetails tradeDetails = TradeDetails.newOf(tradeInfo);
    tradeDetails.setBusinessDayConvention(businessDayConvention);
    tradeDetails.setEndDate(maturityDate);
    tradeDetails.setStartDate(settlementDate);
    tradeDetails.setReceiveLeg(
        tradeLegDetails(
            PayReceive.RECEIVE,
            receiveCurrency,
            nearLegReceiveCurrencyAmount,
            receiveLegExtIdentifier,
            farLegReceiveCurrencyAmount));
    tradeDetails.setPayLeg(
        tradeLegDetails(
            PayReceive.PAY,
            payCurrency,
            nearLegPayCurrencyAmount,
            payLegExtIdentifier,
            farLegPayCurrencyAmount));
    tradeDetails.setCalendar(calendar.getName());
    tradeDetails.setFxRate(fxRate);
    tradeDetails.setNearDateFxRate(nearDateFxRate);
    return tradeDetails;
  }

  private TradeLegDetails tradeLegDetails(
      PayReceive payReceive,
      Currency currency,
      Double nearLegAmount,
      String legIdentifier,
      Double farLegAmount) {
    TradeLegDetails legDetails = new TradeLegDetails();
    legDetails.setExtLegIdentifier(legIdentifier);
    legDetails.setPayReceive(payReceive);
    legDetails.setCurrency(currency.getCode());
    legDetails.setNearNotional(nearLegAmount);
    legDetails.setNotional(farLegAmount);
    return legDetails;
  }
}
