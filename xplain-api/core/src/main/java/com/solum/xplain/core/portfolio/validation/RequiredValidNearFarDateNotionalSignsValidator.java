package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxSwapNotionalsForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Performs validation on the combinations of near/far date base/counter notionals in {@link
 * FxSwapNotionalsForm}. This validator validates that both notionals on one date cannot be
 * negative, and notionals of the same type (base/counter) must have opposite signs on near/far
 * dates.
 */
public class RequiredValidNearFarDateNotionalSignsValidator
    implements ConstraintValidator<RequiredValidNearFarDateNotionalSigns, FxSwapNotionalsForm> {

  public boolean isValid(FxSwapNotionalsForm form, ConstraintValidatorContext context) {
    if (form == null
        || ObjectUtils.anyNull(
            form.getNearDateBaseNotional(),
            form.getNearDateCounterNotional(),
            form.getFarDateBaseNotional(),
            form.getFarDateCounterNotional())) {
      return true;
    }

    var baseNear = form.getNearDateBaseNotional();
    var baseNearDatePositive = baseNear > 0;
    var counterNear = form.getNearDateCounterNotional();
    var counterNearDatePositive = counterNear > 0;

    var baseFar = form.getFarDateBaseNotional();
    var baseFarDatePositive = baseFar > 0;
    var counterFar = form.getFarDateCounterNotional();
    var counterFarDatePositive = counterFar > 0;

    // can't have same signs on one date (near or far)
    if ((baseNearDatePositive == counterNearDatePositive)
        || (baseFarDatePositive == counterFarDatePositive)) {
      return false;
    }

    // currency (base or counter) cannot have same sign across the two dates (near and far)
    if ((baseNearDatePositive && baseFarDatePositive)
        || (counterNearDatePositive && counterFarDatePositive)) {
      return false;
    }

    return true;
  }
}
