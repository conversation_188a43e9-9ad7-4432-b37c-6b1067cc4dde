package com.solum.xplain.core.events;

import com.solum.xplain.core.sockets.events.SocketEvent;
import com.solum.xplain.shared.datagrid.ClusterEventPublisher;
import com.solum.xplain.shared.utils.ratelimit.RateLimitedOperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for publishing events with rate limiting. This service can be used to prevent event
 * flooding in the system.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RateLimitedEventsService {

  private final ClusterEventPublisher clusterEventPublisher;
  private final RateLimitedOperationService rateLimitedOperationService;

  public void publishClusterEvent(String rateLimitKey, SocketEvent event) {
    rateLimitedOperationService.progressOperation(
        rateLimitKey, 0, () -> clusterEventPublisher.publishEvent(event));
  }

  public void publishNotificationEvent(String rateLimitKey, SocketEvent event) {
    log.trace("Maybe publishing notification event: {} {}", rateLimitKey, event);
    rateLimitedOperationService.notificationOperation(
        rateLimit<PERSON>ey, false, () -> clusterEventPublisher.publishEvent(event));
  }
}
