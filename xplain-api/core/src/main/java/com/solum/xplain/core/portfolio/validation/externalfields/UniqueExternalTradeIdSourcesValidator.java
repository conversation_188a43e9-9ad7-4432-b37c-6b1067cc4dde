package com.solum.xplain.core.portfolio.validation.externalfields;

import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

public class UniqueExternalTradeIdSourcesValidator
    implements ConstraintValidator<UniqueExternalTradeIdSources, List<ExternalIdentifierForm>> {

  @Override
  public boolean isValid(List<ExternalIdentifierForm> value, ConstraintValidatorContext context) {
    if (CollectionUtils.isEmpty(value)) {
      return true;
    }
    return value.size()
        == value.stream().map(ExternalIdentifierForm::getExternalSourceId).distinct().count();
  }
}
