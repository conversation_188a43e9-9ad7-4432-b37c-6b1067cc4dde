package com.solum.xplain.core.teams;

import static com.google.common.cache.CacheLoader.from;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.CountResult;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.teams.events.TeamUpdated;
import com.solum.xplain.core.teams.value.TeamForm;
import com.solum.xplain.core.teams.value.TeamListView;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.core.teams.value.TeamView;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
@NullMarked
public class TeamRepository implements CacheInvalidationListener {
  private static final String TEAM_NOT_FOUND = "Team not found";
  private static final Duration CACHE_FRESHNESS_LIMIT = Duration.of(10, ChronoUnit.MINUTES);
  private static final String BY_ID_CACHE_KEY = "byId";
  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final TeamMapper teamMapper;
  private final ApplicationEventPublisher eventPublisher;
  private final AuditingHandler auditingHandler;

  private final LoadingCache<String, Map<String, TeamListView>> teamsCache =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_FRESHNESS_LIMIT)
          .build(from(this::loadCache));

  public Either<ErrorItem, EntityId> insert(TeamForm form) {
    Team team = teamMapper.from(form);
    mongoOperations.insert(team);
    invalidateAll();
    return Either.right(EntityId.entityId(team.getId().toString()));
  }

  /**
   * Retrieves a list of team name views for the specified external IDs. Any non-existent external
   * IDs are ignored.
   */
  public List<TeamNameView> teamNamesListByExternalIds(List<String> externalIds) {
    return mongoOperations
        .aggregate(
            newAggregation(
                Team.class,
                match(where(Team.Fields.externalId).in(externalIds)),
                teamNameProjection()),
            TeamNameView.class)
        .getMappedResults();
  }

  public ScrollableEntry<TeamListView> list(ScrollRequest scrollRequest, TableFilter tableFilter) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                teamListViewProjection(),
                match(tableFilter.criteria(TeamListView.class, conversionService)))
            .build();
    TypedAggregation<Team> aggregation =
        newAggregation(
            Team.class,
            ImmutableList.<AggregationOperation>builder()
                .addAll(baseOperations)
                .add(count().as(CountResult.Fields.count))
                .build());
    AggregationResults<CountResult> totalAggr =
        mongoOperations.aggregate(aggregation, CountResult.class);
    return ofNullable(totalAggr.getUniqueMappedResult())
        .map(CountResult::getCount)
        .map(
            t ->
                ScrollableEntry.of(
                    teamList(scrollRequest, baseOperations), scrollRequest, t.longValue()))
        .orElse(ScrollableEntry.empty());
  }

  public List<TeamNameView> teamNamesList() {
    return getTeamsById().values().stream().map(this::toTeamNameView).collect(toList());
  }

  private List<TeamListView> teamList(
      ScrollRequest scrollRequest, List<AggregationOperation> baseOperations) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder().addAll(baseOperations);
    operations.addAll(new ScrollSortOperations(scrollRequest, TeamNameView.Fields.id).build());
    TypedAggregation<Team> aggregation = newAggregation(Team.class, operations.build());
    return mongoOperations.aggregate(aggregation, TeamListView.class).getMappedResults();
  }

  /**
   * Retrieves a list of team name views for the specified team IDs. Any non-existent team IDs are
   * ignored.
   */
  public List<TeamNameView> teamNamesList(Collection<String> teamIds) {
    Map<String, TeamListView> cachedTeams = getTeamsById();
    return teamIds.stream()
        .map(cachedTeams::get)
        .filter(Objects::nonNull)
        .map(this::toTeamNameView)
        .collect(toList());
  }

  private static ProjectionOperation teamNameProjection() {
    return project()
        .and(Team.Fields.id)
        .as(TeamNameView.Fields.id)
        .and(Team.Fields.name)
        .as(TeamNameView.Fields.name);
  }

  private ProjectionOperation teamListViewProjection() {
    return project()
        .and(Team.Fields.id)
        .as(TeamNameView.Fields.id)
        .and(Team.Fields.name)
        .as(TeamNameView.Fields.name)
        .and(Team.Fields.externalId)
        .as(TeamListView.Fields.externalId)
        .and(Team.Fields.createdAt)
        .as(TeamListView.Fields.createdAt)
        .and(joinPaths(Team.Fields.createdBy, AuditUser.Fields.name))
        .as(TeamListView.Fields.creator)
        .and(joinPaths(Team.Fields.lastModifiedBy, AuditUser.Fields.name))
        .as(TeamListView.Fields.updatedBy)
        .and(Team.Fields.lastModifiedAt)
        .as(TeamListView.Fields.updatedAt);
  }

  private Either<ErrorItem, Team> get(String id) {
    Team team = mongoOperations.findById(id, Team.class);
    if (team == null) {
      return Either.left(Error.OBJECT_NOT_FOUND.entity(TEAM_NOT_FOUND));
    } else {
      return Either.right(team);
    }
  }

  public Either<ErrorItem, TeamView> teamView(String id) {
    TeamView view =
        mongoOperations
            .aggregate(
                newAggregation(
                    Team.class, match(where(Team.Fields.id).is(id)), teamViewProjection()),
                TeamView.class)
            .getUniqueMappedResult();
    if (view == null) {
      return Either.left(Error.OBJECT_NOT_FOUND.entity(TEAM_NOT_FOUND));
    } else {
      return Either.right(view);
    }
  }

  public Either<ErrorItem, TeamListView> teamListView(String teamId) {
    var team = getTeamsById().get(teamId);
    if (team == null) {
      return Either.left(Error.OBJECT_NOT_FOUND.entity(TEAM_NOT_FOUND));
    } else {
      return Either.right(team);
    }
  }

  public Either<ErrorItem, EntityId> update(String id, TeamForm edit) {
    return get(id)
        .map(
            team -> {
              var newTeam = teamMapper.update(edit, mongoOperations.findById(id, Team.class));
              var diff = team.diff(newTeam);
              if (diff.numberOfDiffs() > 0) {
                newTeam.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newTeam);
              eventPublisher.publishEvent(new TeamUpdated(id, edit));
              return EntityId.entityId(newTeam.getId());
            });
  }

  public Either<ErrorItem, EntityId> remove(String teamId) {
    var result = mongoOperations.remove(query(where(Team.Fields.id).is(teamId)), Team.class);
    if (result.getDeletedCount() > 0) {
      invalidateAll();
      return right(entityId(teamId));
    } else {
      return left(OBJECT_NOT_FOUND.entity(TEAM_NOT_FOUND));
    }
  }

  private ProjectionOperation teamViewProjection() {
    return project()
        .and(Team.Fields.id)
        .as(TeamNameView.Fields.id)
        .and(Team.Fields.name)
        .as(TeamNameView.Fields.name)
        .and(Team.Fields.externalId)
        .as(TeamView.Fields.externalId)
        .and(Team.Fields.createdAt)
        .as(TeamView.Fields.createdAt)
        .and(joinPaths(Team.Fields.createdBy, AuditUser.Fields.name))
        .as(TeamView.Fields.creator)
        .and(joinPaths(Team.Fields.lastModifiedBy, AuditUser.Fields.name))
        .as(TeamView.Fields.updatedBy)
        .and(Team.Fields.auditLogs)
        .as(TeamView.Fields.auditLogs)
        .and(Team.Fields.lastModifiedAt)
        .as(TeamView.Fields.updatedAt);
  }

  public boolean existsByExternalIdExcluding(String externalId, String excludeId) {
    Criteria criteria = where(Team.Fields.externalId).is(externalId);

    if (isNotEmpty(excludeId)) {
      criteria.and(Team.Fields.id).ne(excludeId);
    }

    return mongoOperations.exists(query(criteria), Team.class);
  }

  @EventListener
  public void onTeamUpdated(@SuppressWarnings("unused") TeamUpdated event) {
    invalidateAll();
  }

  @Override
  public void invalidateAll() {
    teamsCache.invalidateAll();
  }

  private Map<String, TeamListView> loadTeamsById() {
    return mongoOperations
        .aggregate(newAggregation(Team.class, teamListViewProjection()), TeamListView.class)
        .getMappedResults()
        .stream()
        .collect(
            toMap(
                TeamListView::getId,
                identity(),
                (existing, replacement) -> existing,
                LinkedHashMap::new));
  }

  private TeamNameView toTeamNameView(TeamListView teamListView) {
    TeamNameView teamNameView = new TeamNameView();
    teamNameView.setId(teamListView.getId());
    teamNameView.setName(teamListView.getName());
    return teamNameView;
  }

  public Map<String, TeamListView> getTeamsById() {
    try {
      return teamsCache.get(BY_ID_CACHE_KEY);
    } catch (ExecutionException e) {
      throw new IllegalStateException("Failed to load teams from cache", e);
    }
  }

  private Map<String, TeamListView> loadCache(String key) {
    return switch (key) {
      case BY_ID_CACHE_KEY -> loadTeamsById();
      default -> throw new IllegalArgumentException("Invalid cache key: " + key);
    };
  }
}
