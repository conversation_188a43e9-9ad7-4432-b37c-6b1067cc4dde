package com.solum.xplain.core.curvemarket.marketvalue;

import static java.util.Optional.ofNullable;

import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;

@Data
public class CalculationMarketValueView implements CalculationMarketValue {
  private String key;
  private BigDecimal value;
  private BigDecimal bidValue;
  private BigDecimal midValue;
  private BigDecimal askValue;
  private InstrumentPriceType priceType;

  @Override
  public void withOverlayValue(Map<ValueBidAskType, BigDecimal> overlayValues) {
    ofNullable(overlayValues.get(ValueBidAskType.ASK)).ifPresent(this::setAskValue);
    ofNullable(overlayValues.get(ValueBidAskType.MID)).ifPresent(this::setMidValue);
    ofNullable(overlayValues.get(ValueBidAskType.BID)).ifPresent(this::setBidValue);
    priceType.calculateRequiredPrice(askValue, midValue, bidValue).ifPresent(this::setValue);
  }

  public CalculationMarketValueView copyWithOverlay(
      Map<ValueBidAskType, BigDecimal> overlayValues) {
    CalculationMarketValueView v = new CalculationMarketValueView();
    v.setKey(key);
    v.setPriceType(priceType);
    v.withOverlayValue(overlayValues);
    return v;
  }
}
