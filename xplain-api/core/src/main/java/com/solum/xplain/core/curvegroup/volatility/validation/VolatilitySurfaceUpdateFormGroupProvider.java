package com.solum.xplain.core.curvegroup.volatility.validation;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceATMGroup;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceNonSabrFormGroup;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceSabrFormGroup;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import java.util.List;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class VolatilitySurfaceUpdateFormGroupProvider
    implements DefaultGroupSequenceProvider<VolatilitySurfaceUpdateForm> {
  @Override
  public List<Class<?>> getValidationGroups(VolatilitySurfaceUpdateForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(VolatilitySurfaceUpdateForm.class);
    if (form != null) {
      if (BooleanUtils.isTrue(form.getSabr())) {
        builder.add(VolatilitySurfaceSabrFormGroup.class);
      } else {
        builder.add(VolatilitySurfaceNonSabrFormGroup.class);
      }
      if (StringUtils.equals(form.getSkewType(), VolatilitySurfaceType.ATM_ONLY.name())) {
        builder.add(VolatilitySurfaceATMGroup.class);
      }
    }
    return builder.build();
  }
}
