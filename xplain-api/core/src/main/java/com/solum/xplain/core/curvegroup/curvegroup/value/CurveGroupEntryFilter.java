package com.solum.xplain.core.curvegroup.curvegroup.value;

import java.time.LocalDate;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveGroupEntryFilter {
  private final LocalDate stateDate;
  private final List<String> curveGroupIds;

  public static CurveGroupEntryFilter singleGroup(LocalDate stateDate, String curveGroupId) {
    return new CurveGroupEntryFilter(stateDate, List.of(curveGroupId));
  }

  public static CurveGroupEntryFilter listOfGroups(
      LocalDate stateDate, List<String> curveGroupIds) {
    return new CurveGroupEntryFilter(stateDate, curveGroupIds);
  }
}
