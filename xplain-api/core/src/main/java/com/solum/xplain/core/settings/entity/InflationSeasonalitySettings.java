package com.solum.xplain.core.settings.entity;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.settings.VersionedSettings;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
@FieldNameConstants
public class InflationSeasonalitySettings extends VersionedSettings {
  public static final String INFLATION_SEASONALITY_SETTINGS = "inflationSeasonalitySettings";
  private List<CurveSeasonality> curveSeasonalities = List.of();

  public static InflationSeasonalitySettings empty() {
    var settings = new InflationSeasonalitySettings();
    settings.setValidFrom(NewVersionFormV2.ROOT_DATE);
    settings.setState(State.ACTIVE);
    return settings;
  }

  @Override
  public boolean valueEquals(Object entity) {
    if (entity instanceof InflationSeasonalitySettings item) {
      return super.valueEquals(entity)
          && Objects.equals(this.curveSeasonalities, item.curveSeasonalities);
    }
    return false;
  }
}
