package com.solum.xplain.core.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;

public class ValidStringMapKeysValidator
    implements ConstraintValidator<ValidStringMapKeys, Map<String, ?>> {
  private ValidStringSetValidator keyStringSetValidator;

  @Override
  public void initialize(ValidStringMapKeys constraint) {
    this.keyStringSetValidator = new ValidStringSetValidator();
    keyStringSetValidator.initialize(
        constraint.valueForKeys(), constraint.supplierArgumentForKeys());
  }

  @Override
  public boolean isValid(Map<String, ?> obj, ConstraintValidatorContext context) {
    if (obj == null) {
      return true;
    } else {
      return obj.keySet().stream().allMatch(v -> keyStringSetValidator.isValid(v, context));
    }
  }
}
