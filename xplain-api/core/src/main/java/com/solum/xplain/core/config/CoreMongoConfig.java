package com.solum.xplain.core.config;

import com.solum.xplain.shared.spring.mongo.XplainMongoRepositoryFactoryBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories(
    basePackages = {"com.solum.xplain.core"},
    repositoryFactoryBeanClass = XplainMongoRepositoryFactoryBean.class)
public class CoreMongoConfig {}
