package com.solum.xplain.core.portfolio.validation;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.portfolio.form.IrsTradeForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.form.SwaptionTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.IrsTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.SwaptionTradeGroup;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class SwapTradeFormGroupProvider implements DefaultGroupSequenceProvider<SwapTradeForm> {
  @Override
  public List<Class<?>> getValidationGroups(SwapTradeForm object) {
    var builder = ImmutableList.<Class<?>>builder();
    builder.add(SwapTradeForm.class);

    if (object instanceof IrsTradeForm) {
      builder.add(IrsTradeGroup.class);
    }
    if (object instanceof SwaptionTradeForm) {
      builder.add(SwaptionTradeGroup.class);
    }
    return builder.build();
  }
}
