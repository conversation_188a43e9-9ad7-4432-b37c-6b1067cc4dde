package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import com.solum.xplain.core.portfolio.value.FxSwapTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/fxswap")
@AllArgsConstructor
public class FxSwapController
    implements BespokeTradeTypedController<FxSwapTradeForm, FxSwapTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, FxSwapTradeView> toViewFunction(PortfolioItem e) {
    return FxSwapTradeView.of(e);
  }
}
