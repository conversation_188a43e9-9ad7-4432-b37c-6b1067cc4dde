package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;
import static com.solum.xplain.extensions.enums.CallPutType.CALL;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class FxDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(
        CoreProductType.FXFWD,
        CoreProductType.FXOPT,
        CoreProductType.FXSWAP,
        CoreProductType.FXCOLLAR);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    var baseCcy = baseCcy(productType, tradeDetails);
    return Optional.ofNullable(tradeDetails.currencyPair())
        .map(p -> p.getBase().equals(baseCcy) ? p : p.inverse())
        .map(CurrencyPair::toString)
        .orElseThrow(() -> new IllegalArgumentException("FX trade must have currency pair"));
  }

  private Currency baseCcy(ProductType productType, TradeDetails tradeDetails) {
    if (productType == CoreProductType.FXOPT || productType == CoreProductType.FXCOLLAR) {
      return tradeDetails.getOptionTradeDetails().getCallPutType() == CALL
          ? tradeDetails.getReceiveLeg().currency()
          : tradeDetails.getPayLeg().currency();
    }
    return tradeDetails.tradeCurrency();
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
