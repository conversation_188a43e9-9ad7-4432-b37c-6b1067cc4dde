package com.solum.xplain.core.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = NotZeroValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface NotZero {

  String message() default "{com.solum.xplain.api.common.validation.NotZero.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
