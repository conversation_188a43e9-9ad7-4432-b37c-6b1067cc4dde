package com.solum.xplain.core.ipv.tradeleveloverride.csv;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedCompanyView;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.providers.DataProviderRepository;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.providers.value.DataProviderView;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TradeLevelOverrideCsvLoaderFactory {

  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final DataProviderRepository dataProviderRepository;

  public TradeLevelOverrideCsvLoader loader(XplainPrincipal user) {
    var existingIpvDataGroups = activeIpvDataGroups(user);
    var existingValuationDataProviders = dataProviders();
    return new TradeLevelOverrideCsvLoader(existingIpvDataGroups, existingValuationDataProviders);
  }

  private List<String> activeIpvDataGroups(XplainPrincipal user) {
    return ipvDataGroupRepository.activeUserIpvDataGroups(EntityTeamFilter.filter(user)).stream()
        .map(IpvDataGroupCondensedCompanyView::getName)
        .toList();
  }

  private List<String> dataProviders() {
    return dataProviderRepository.dataProvidersViewListForType(DataProviderType.VALUATION).stream()
        .map(DataProviderView::getExternalId)
        .filter(externalId -> !externalId.equals(DataProvider.NAV_PROVIDER_CODE))
        .toList();
  }
}
