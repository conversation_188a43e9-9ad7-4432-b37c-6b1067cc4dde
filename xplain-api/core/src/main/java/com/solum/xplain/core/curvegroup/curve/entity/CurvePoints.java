package com.solum.xplain.core.curvegroup.curve.entity;

import static java.util.stream.Collectors.toList;

import com.opengamma.strata.market.ValueType;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.value.CombinedCurvePoint;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@AllArgsConstructor(staticName = "newOf")
@NoArgsConstructor
public class CurvePoints {
  private static final String BAD_DISCOUNT_FACTORS_VALUE_TYPE =
      "Only Zero Coupon rates can be converted to discount factors";
  private static final String BAD_MARKET_RATE_VALUE_TYPE =
      "Only Price Index values can be converted to market rates";

  private String curveName;
  @Nullable private String discountingKey;
  private ValueType yValueType;
  private List<ChartPoint> points;

  public static CurvePoints empty(String curveName, ValueType valueType) {
    return new CurvePoints(curveName, null, valueType, List.of());
  }

  public boolean isEmpty() {
    return points.isEmpty();
  }

  public Either<ErrorItem, List<ChartPoint>> discountPoints() {
    if (!yValueType.equals(ValueType.ZERO_RATE)) {
      return Either.left(Error.OPERATION_NOT_ALLOWED.entity(BAD_DISCOUNT_FACTORS_VALUE_TYPE));
    }
    return points.stream()
        .map(this::convertToDiscountFactorPoint)
        .collect(Collectors.collectingAndThen(toList(), Either::right));
  }

  public Either<ErrorItem, List<ChartPoint>> marketRatePoints() {
    var firstPoint = points.get(0);
    if (!yValueType.equals(ValueType.PRICE_INDEX)) {
      return Either.left(Error.OPERATION_NOT_ALLOWED.entity(BAD_MARKET_RATE_VALUE_TYPE));
    }
    return points.stream()
        .map(
            cp -> convertToMarketRatePoint(cp, firstPoint.getCalculatedValue(), points.indexOf(cp)))
        .collect(Collectors.collectingAndThen(toList(), Either::right));
  }

  public List<CombinedCurvePoint> combinedPoints() {
    var firstPoint = points.isEmpty() ? new ChartPoint(null, null, null) : points.get(0);

    return points.stream()
        .map(
            p ->
                new CombinedCurvePoint(
                    p.getDate(),
                    p.getMaturity(),
                    getValueIfType(ValueType.ZERO_RATE, p),
                    getValueIfType(ValueType.PRICE_INDEX, p),
                    getValueIfType(ValueType.FORWARD_RATE, p),
                    discountFactorValue(p),
                    marketValue(p, firstPoint.getCalculatedValue(), points.indexOf(p))))
        .toList();
  }

  private ChartPoint convertToDiscountFactorPoint(ChartPoint cp) {
    var value = discountFactorValue(cp);
    return new ChartPoint(cp.getDate(), value, cp.getMaturity());
  }

  private ChartPoint convertToMarketRatePoint(ChartPoint cp, Double firstPointValue, double index) {
    var value = marketValue(cp, firstPointValue, index);
    return new ChartPoint(cp.getDate(), value, cp.getMaturity());
  }

  private Double discountFactorValue(ChartPoint cp) {
    if (yValueType.equals(ValueType.ZERO_RATE)) {
      return Math.exp(-cp.getMaturity() * cp.getCalculatedValue());
    }
    return null;
  }

  private Double marketValue(ChartPoint cp, Double firstPointValue, double index) {
    if (index == 0 || firstPointValue == null) {
      return null;
    }
    if (yValueType.equals(ValueType.PRICE_INDEX)) {
      return Math.pow(cp.getCalculatedValue() / firstPointValue, 12 / index) - 1;
    }
    return null;
  }

  private Double getValueIfType(ValueType valueType, ChartPoint cp) {
    if (yValueType.equals(valueType)) {
      return cp.getCalculatedValue();
    }
    return null;
  }
}
