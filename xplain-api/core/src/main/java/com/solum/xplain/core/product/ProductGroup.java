package com.solum.xplain.core.product;

import com.solum.xplain.core.portfolio.CoreProductTypeGroup;
import com.solum.xplain.core.portfolio.ProductTypeGroupDiscounting;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

@Schema(
    anyOf = {CoreProductTypeGroup.class}, // also GenericProductType.class
    type = "enum",
    $id = "ProductGroup",
    description = "Product group")
public interface ProductGroup {
  String name();

  String label();

  ProductTypeGroupDiscounting getGroupDiscounting();

  List<ProductType> getProductTypes();

  boolean isRates();

  boolean isCredit();

  boolean isFx();
}
