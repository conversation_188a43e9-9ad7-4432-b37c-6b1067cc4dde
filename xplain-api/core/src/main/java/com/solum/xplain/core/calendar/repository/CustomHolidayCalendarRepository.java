package com.solum.xplain.core.calendar.repository;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar;
import com.solum.xplain.core.calendar.event.CustomHolidayCalendarUpdatedEvent;
import com.solum.xplain.core.calendar.form.CustomHolidayCalendarForm;
import com.solum.xplain.core.calendar.mapper.CustomHolidayCalendarMapper;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.shared.datagrid.ClusterEventPublisher;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class CustomHolidayCalendarRepository
    extends GenericUniqueVersionedEntityRepository<CustomHolidayCalendar> {

  private final CustomHolidayCalendarMapper mapper;
  private final ClusterEventPublisher eventPublisher;

  public CustomHolidayCalendarRepository(
      MongoOperations mongoOperations,
      CustomHolidayCalendarMapper mapper,
      ClusterEventPublisher eventPublisher) {
    super(mongoOperations, mapper);
    this.mapper = mapper;
    this.eventPublisher = eventPublisher;
  }

  @Override
  protected Criteria uniqueEntityCriteria(CustomHolidayCalendar entity) {
    return where(VersionedEntity.Fields.entityId).is(entity.getEntityId());
  }

  public Optional<CustomHolidayCalendar> findActiveByCalendarId(
      HolidayCalendarId calendarId, BitemporalDate vd) {
    return findOneByEntityId(calendarId.getName(), vd, active());
  }

  public List<CustomHolidayCalendar> findAllActive(BitemporalDate vd) {
    return entities(vd, active(), new Criteria(), Sort.unsorted());
  }

  public EntityId createCalendar(HolidayCalendarId id, CustomHolidayCalendarForm form) {
    EntityId entityId =
        saveNew(mapper.fromForm(form, CustomHolidayCalendar.newOf(id)), form.getVersionForm());
    fireUpdatedEvent(id);
    return entityId;
  }

  public EntityId updateCalendar(CustomHolidayCalendar existing, CustomHolidayCalendarForm form) {
    EntityId entityId =
        update(
            existing, form.getVersionForm(), copiedEntity -> mapper.fromForm(form, copiedEntity));
    fireUpdatedEvent(existing.getHolidayCalendarId());
    return entityId;
  }

  private void fireUpdatedEvent(HolidayCalendarId id) {
    eventPublisher.publishEvent(new CustomHolidayCalendarUpdatedEvent(id));
  }
}
