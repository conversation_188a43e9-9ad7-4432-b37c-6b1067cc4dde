package com.solum.xplain.core.ccyexposure.entity;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * This is most like a {@link com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration}
 * where there are multiple curve configurations per curve group. Here we have multiple cashflows
 * per ccy exposure. Each cashflow has a key which is the date - do we make that the name? Yes,
 * lets!
 */
@Document(collation = "en", collection = Cashflow.CASHFLOW_COLLECTION)
@ToString(callSuper = true)
@Data
@NoArgsConstructor
@FieldNameConstants
public class Cashflow extends VersionedNamedEntity {

  public static final String CASHFLOW_COLLECTION = "cashflow";

  @Nonnull private String ccyExposureId;
  @Nonnull private LocalDate date;
  @Nonnull private Double amount;

  public static Cashflow newOf() {
    Cashflow c = new Cashflow();
    c.setEntityId(ObjectId.get().toString());
    c.setState(State.ACTIVE);
    c.setRecordDate(LocalDateTime.now());
    return c;
  }

  @Override
  public boolean valueEquals(Object object) {
    if (object instanceof Cashflow cashflow) {
      return super.valueEquals(cashflow)
          && Objects.equals(this.ccyExposureId, cashflow.ccyExposureId)
          && Objects.equals(this.date, cashflow.date)
          && Objects.equals(this.amount, cashflow.amount);
    }
    return false;
  }
}
