package com.solum.xplain.core.portfolio.value.item;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import lombok.Data;

@Data
public class CustomTradeDetailsView {

  @ConfigurableViewQuery(sortable = true)
  private String assetType;

  @ConfigurableViewQuery(sortable = true)
  private String subAssetType;

  @ConfigurableViewQuery(sortable = true)
  private String additionalInfo;

  @ConfigurableViewQuery(sortable = true)
  private Double notional;

  @ConfigurableViewQuery(sortable = true)
  private String underlying;

  @ConfigurableViewQuery(sortable = true)
  private String optionPosition;
}
