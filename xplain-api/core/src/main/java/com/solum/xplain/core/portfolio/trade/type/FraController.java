package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.FraTradeForm;
import com.solum.xplain.core.portfolio.value.FraTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/fra")
@AllArgsConstructor
public class FraController implements BespokeTradeTypedController<FraTradeForm, FraTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, FraTradeView> toViewFunction(PortfolioItem e) {
    return FraTradeView.of(e);
  }
}
