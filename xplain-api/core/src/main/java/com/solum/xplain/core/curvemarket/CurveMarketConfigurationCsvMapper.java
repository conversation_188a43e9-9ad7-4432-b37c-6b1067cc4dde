package com.solum.xplain.core.curvemarket;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.market.csv.MarketDataKeyCsvMapper;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveMarketConfigurationCsvMapper {

  private static final List<String> CSV_HEADER =
      List.of(
          MarketDataKeyCsvMapper.KEY_FIELD,
          MarketDataKeyCsvMapper.PROVIDER_FIELD,
          MarketDataKeyCsvMapper.TICKER_FIELD,
          MarketDataKeyCsvMapper.FACTOR_FIELD,
          MarketDataKeyCsvMapper.BIDASKTYPE_FIELD);

  public static List<CsvRow> toCsvRows(String key, MarketDataProviders provider) {
    ImmutableList.Builder<CsvRow> builder = ImmutableList.builder();
    toProviderCsvRow(key, provider, MarketDataProviders::getPrimary).ifPresent(builder::add);
    toProviderCsvRow(key, provider, MarketDataProviders::getSecondary).ifPresent(builder::add);

    return builder.build();
  }

  private static Optional<CsvRow> toProviderCsvRow(
      String key, MarketDataProviders provider, Function<MarketDataProviders, String> fn) {
    return Optional.ofNullable(provider)
        .map(fn)
        .filter(StringUtils::isNotEmpty)
        .map(p -> toCsvRow(key, p));
  }

  public static CsvOutputFile csvOutputFile(List<CsvRow> rows) {
    return new CsvOutputFile(CSV_HEADER, rows);
  }

  private static CsvRow toCsvRow(String key, String provider) {
    return new CsvRow(
        List.of(
            new CsvField(MarketDataKeyCsvMapper.KEY_FIELD, key),
            new CsvField(MarketDataKeyCsvMapper.PROVIDER_FIELD, provider)));
  }
}
