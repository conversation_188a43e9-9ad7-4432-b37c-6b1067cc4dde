package com.solum.xplain.core.config.converters.product;

import static com.solum.xplain.core.config.converters.JacksonEnumDeserializationUtils.errorMessage;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.KeyDeserializer;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.ProductTypeResolver;
import jakarta.inject.Provider;
import java.io.IOException;
import java.util.Optional;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ProductTypeKeyDeserializer extends KeyDeserializer {

  private final Provider<ProductTypeResolver> typeResolverProvider;

  @Override
  public Object deserializeKey(String key, DeserializationContext ctxt) throws IOException {
    var resolver = typeResolverProvider.get();
    return Optional.ofNullable(key)
        .map(resolver::of)
        .orElseThrow(
            () ->
                ctxt.weirdStringException(key, ProductType.class, errorMessage(resolver.values())));
  }
}
