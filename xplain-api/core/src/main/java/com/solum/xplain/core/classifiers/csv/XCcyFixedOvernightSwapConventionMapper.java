package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapConvention;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class XCcyFixedOvernightSwapConventionMapper
    extends CsvMapper<XCcyFixedOvernightSwapConvention> {

  private static final List<CsvColumn<XCcyFixedOvernightSwapConvention>> COLUMNS =
      List.of(
          textObject("name", XCcyFixedOvernightSwapConvention::getName),
          textObject("spotDateOffset", XCcyFixedOvernightSwapConvention::getSpotDateOffset),
          textObject("fixedLeg.currency", x -> x.getFixedLeg().getCurrency()),
          textObject("fixedLeg.dayCount", x -> x.getFixedLeg().getDayCount()),
          textObject("fixedLeg.accrualFreq", x -> x.getFixedLeg().getAccrualFrequency()),
          textObject("fixedLeg.accrual", x -> x.getFixedLeg().getAccrualMethod()),
          textObject("fixedLeg.accrualBDA", x -> x.getFixedLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "fixedLeg.startDateBDA", x -> x.getFixedLeg().getStartDateBusinessDayAdjustment()),
          textObject("fixedLeg.endDateBDA", x -> x.getFixedLeg().getEndDateBusinessDayAdjustment()),
          textObject("fixedLeg.stubConv", x -> x.getFixedLeg().getStubConvention()),
          textObject("fixedLeg.rollConv", x -> x.getFixedLeg().getRollConvention()),
          textObject("fixedLeg.payFreq", x -> x.getFixedLeg().getPaymentFrequency()),
          textObject("fixedLeg.payDateOffset", x -> x.getFixedLeg().getPaymentDateOffset()),
          textObject("fixedLeg.compounding", x -> x.getFixedLeg().getCompoundingMethod()),
          textObject("floatingLeg.index", x -> x.getFloatingLeg().getIndex()),
          textObject("floatingLeg.currency", x -> x.getFloatingLeg().getCurrency()),
          textObject("floatingLeg.dayCount", x -> x.getFloatingLeg().getDayCount()),
          textObject("floatingLeg.accrualFreq", x -> x.getFloatingLeg().getAccrualFrequency()),
          textObject("floatingLeg.accrual", x -> x.getFloatingLeg().getAccrualMethod()),
          textObject(
              "floatingLeg.accrualBDA", x -> x.getFloatingLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "floatingLeg.endDateBDA", x -> x.getFloatingLeg().getEndDateBusinessDayAdjustment()),
          textObject(
              "floatingLeg.startDateBDA",
              x -> x.getFloatingLeg().getStartDateBusinessDayAdjustment()),
          textObject("floatingLeg.stubConv", x -> x.getFloatingLeg().getStubConvention()),
          textObject("floatingLeg.rollConv", x -> x.getFloatingLeg().getRollConvention()),
          textObject("floatingLeg.rateCutOffDays", x -> x.getFloatingLeg().getRateCutOffDays()),
          textObject("floatingLeg.payFreq", x -> x.getFloatingLeg().getPaymentFrequency()),
          textObject("floatingLeg.payDateOffset", x -> x.getFloatingLeg().getPaymentDateOffset()),
          textObject("floatingLeg.compounding", x -> x.getFloatingLeg().getCompoundingMethod()));

  private XCcyFixedOvernightSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource xccyFixedOvernightSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new XCcyFixedOvernightSwapConventionMapper(), XCcyFixedOvernightSwapConvention.class)
        .export();
  }
}
