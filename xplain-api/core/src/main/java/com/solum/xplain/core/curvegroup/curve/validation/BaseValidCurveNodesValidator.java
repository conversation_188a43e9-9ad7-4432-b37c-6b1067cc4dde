package com.solum.xplain.core.curvegroup.curve.validation;

import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

public abstract class BaseValidCurveNodesValidator {

  private final RequestPathVariablesSupport requestPathVariablesSupport;
  private final CurveGroupCurveRepository repository;

  protected BaseValidCurveNodesValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      CurveGroupCurveRepository repository) {
    this.requestPathVariablesSupport = requestPathVariablesSupport;
    this.repository = repository;
  }

  protected abstract boolean isNodeFormValid(CurveView curve, CurveNodeForm form);

  protected abstract void addViolation(ConstraintValidatorContext context);

  protected boolean isCurveNodesValid(CurveUpdateForm form, ConstraintValidatorContext context) {
    String groupId = requestPathVariablesSupport.getPathVariable("groupId");
    String curveId = requestPathVariablesSupport.getPathVariable("curveId");
    if (isEmpty(curveId) || isEmpty(groupId)) {
      return true;
    }

    LocalDate stateDate = form.getVersionForm().getValidFrom();
    return filterInvalid(form.getNodes(), groupId, curveId, stateDate)
        .map(f -> addError(context, f))
        .findAny()
        .isEmpty();
  }

  private Stream<CurveNodeForm> filterInvalid(
      List<CurveNodeForm> nodes, String groupId, String curveId, LocalDate stateDate) {
    return repository
        .getActiveCurveView(groupId, curveId, BitemporalDate.newOf(stateDate))
        .map(c -> emptyIfNull(nodes).stream().filter(n -> !isNodeFormValid(c, n)))
        .fold(error -> Stream.empty(), Function.identity());
  }

  private CurveNodeForm addError(ConstraintValidatorContext context, CurveNodeForm invalidForm) {
    addViolation(context);
    return invalidForm;
  }
}
