package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.value.PortfolioCalculationType;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class PortfolioCalculationTypeSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.of(PortfolioCalculationType.values()).map(Enum::name).toList();
  }
}
