package com.solum.xplain.core.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidAllowedTeamsFormValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidAllowedTeamsForm {

  String message() default "{com.solum.xplain.api.common.validation.ValidAllowedTeamsForm.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
