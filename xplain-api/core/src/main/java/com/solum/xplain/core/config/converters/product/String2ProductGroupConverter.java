package com.solum.xplain.core.config.converters.product;

import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductGroupResolver;
import jakarta.inject.Provider;
import lombok.AllArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class String2ProductGroupConverter implements Converter<String, ProductGroup> {

  private final Provider<ProductGroupResolver> productGroupResolver;

  @Override
  public ProductGroup convert(@NonNull String source) {
    return productGroupResolver.get().of(source);
  }
}
