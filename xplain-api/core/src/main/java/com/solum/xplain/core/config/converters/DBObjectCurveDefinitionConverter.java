package com.solum.xplain.core.config.converters;

import static com.solum.xplain.core.config.converters.CustomJodaBeanSerializer.JODA_SERIALIZER;

import com.opengamma.strata.market.curve.CurveGroupDefinition;
import org.bson.Document;
import org.springframework.core.convert.converter.Converter;

public class DBObjectCurveDefinitionConverter implements Converter<Document, CurveGroupDefinition> {
  @Override
  public CurveGroupDefinition convert(Document source) {
    return (CurveGroupDefinition) JODA_SERIALIZER.jsonReader().read(source.toJson());
  }
}
