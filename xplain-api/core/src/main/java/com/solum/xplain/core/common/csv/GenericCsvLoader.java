package com.solum.xplain.core.common.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.readCsv;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateEmptyContent;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateHeaders;

import com.google.common.collect.Iterables;
import com.opengamma.strata.collect.io.CsvFile;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public abstract class GenericCsvLoader<T, K> implements CsvLoader<T> {

  protected void validateFile(CsvFile csvFile) {
    validateHeaders(csvFile, Iterables.toArray(getFileHeaders(), String.class));
    validateEmptyContent(csvFile);
  }

  @Override
  public Either<List<ErrorItem>, CsvParserResult<T>> parse(
      byte[] csvContent, ParsingMode parsingMode) {
    try {
      CsvFile csvFile = readCsv(csvContent);
      validateFile(csvFile);

      CsvParserResultBuilder<T, K> result = createResult(parsingMode);
      for (CsvRow csvRow : csvFile.rows()) {
        Either<ErrorItem, T> parsedRow = parseLine(csvRow);
        result.addLine(csvRow.lineNumber(), parsedRow);
      }

      return result.toEither();
    } catch (Exception e) {
      return Either.<ErrorItem, CsvParserResult<T>>left(Error.PARSING_ERROR.entity(e.getMessage()))
          .leftMap(ErrorItem.ListOfErrors::from);
    }
  }

  protected abstract CsvParserResultBuilder<T, K> createResult(ParsingMode mode);

  protected abstract List<String> getFileHeaders();

  protected abstract Either<ErrorItem, T> parseLine(CsvRow csvRow);
}
