package com.solum.xplain.core.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.bson.types.ObjectId;

public class ValidObjectIdValidator implements ConstraintValidator<ValidObjectId, String> {

  public boolean isValid(String obj, ConstraintValidatorContext context) {
    if (obj == null) {
      return true;
    } else {
      return ObjectId.isValid(obj);
    }
  }
}
