package com.solum.xplain.core.config.converters;

import static com.solum.xplain.core.config.converters.CustomJodaBeanSerializer.JODA_SERIALIZER;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;
import org.joda.beans.Bean;

public class BeanJacksonSerializer extends StdSerializer<Bean> {

  public BeanJacksonSerializer() {
    super(Bean.class);
  }

  @Override
  public void serialize(Bean value, JsonGenerator gen, SerializerProvider serializers)
      throws IOException {
    gen.writeRawValue(JODA_SERIALIZER.jsonWriter().write(value));
  }
}
