package com.solum.xplain.core.company.form;

import jakarta.validation.constraints.NotEmpty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IpvValuationProvidersForm extends ValuationDataProvidersForm {

  @NotEmpty private final String ipvDataGroupId;

  public IpvValuationProvidersForm(
      String ipvDataGroupId, String primary, String secondary, String tertiary, String quaternary) {
    super(primary, secondary, tertiary, quaternary);
    this.ipvDataGroupId = ipvDataGroupId;
  }
}
