package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.company.mapper.TradeFact;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * An item in a portfolio, which is currently always a trade, but could conceivably be something
 * that isn't, such as cash.
 */
@Document(collation = "en", collection = PortfolioItem.PORTFOLIO_ITEM_COLLECTION)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
public class PortfolioItem extends VersionedTradeEntity implements TradeFact {

  public static final String PORTFOLIO_ITEM_COLLECTION = "portfolioItem";
  private ObjectId portfolioId;
  private String externalPortfolioId;
  private String externalCompanyId;
  private String externalEntityId;
  private LocalDateTime portfolioArchivedAt;
  private String valuationDataKey;

  @Override
  public String getPortfolioExternalId() {
    return externalPortfolioId;
  }
}
