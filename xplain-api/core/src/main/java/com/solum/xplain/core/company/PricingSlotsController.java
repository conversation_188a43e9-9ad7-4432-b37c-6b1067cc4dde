package com.solum.xplain.core.company;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_PORTFOLIO;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.PricingSlotPortfolioView;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pricing-slots/portfolios")
@RequiredArgsConstructor
public class PricingSlotsController {
  private final PricingSlotsControllerService service;

  @Operation(summary = "Get pricing slot portfolios")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_PORTFOLIO)
  public List<PricingSlotPortfolioView> getAllPricingSlotPortfolios(
      @RequestParam LocalDate stateDate) {
    var bitemporalDate = BitemporalDate.newOf(stateDate);
    return service.getAllPricingSlotPortfolios(bitemporalDate);
  }
}
