package com.solum.xplain.core.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.DateTimeException;
import java.time.ZoneId;

public class ValidZoneValidator implements ConstraintValidator<ValidZone, String> {

  public boolean isValid(String obj, ConstraintValidatorContext context) {
    try {
      if (obj != null) {
        ZoneId.of(obj);
      }
      return true;
    } catch (DateTimeException ex) {
      return false;
    }
  }
}
