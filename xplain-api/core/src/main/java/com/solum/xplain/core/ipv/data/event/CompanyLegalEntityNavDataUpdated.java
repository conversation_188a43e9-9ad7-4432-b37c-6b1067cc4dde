package com.solum.xplain.core.ipv.data.event;

import com.solum.xplain.core.common.EntityEvent;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CompanyLegalEntityNavDataUpdated extends EntityEvent {

  private CompanyLegalEntityNavDataUpdated(String entityId) {
    super(entityId);
  }

  public static CompanyLegalEntityNavDataUpdated newOf(String groupId) {
    return new CompanyLegalEntityNavDataUpdated(groupId);
  }
}
