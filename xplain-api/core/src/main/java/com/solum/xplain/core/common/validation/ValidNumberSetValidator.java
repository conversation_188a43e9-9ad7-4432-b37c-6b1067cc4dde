package com.solum.xplain.core.common.validation;

import com.google.common.collect.ImmutableSet;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class ValidNumberSetValidator implements ConstraintValidator<ValidNumberSet, Number> {
  private Set<Number> valueList;

  @Override
  public void initialize(ValidNumberSet constraint) {
    valueList =
        Arrays.stream(constraint.value())
            .map(this::resolveValues)
            .flatMap(Collection::stream)
            .collect(Collectors.toUnmodifiableSet());
  }

  private Set<? extends Number> resolveValues(
      Class<? extends Supplier<Collection<? extends Number>>> supplier) {
    try {
      return ImmutableSet.copyOf(supplier.getConstructor().newInstance().get());
    } catch (NoSuchMethodException
        | InvocationTargetException
        | InstantiationException
        | IllegalAccessException e) {
      throw new IllegalStateException(e.getMessage(), e);
    }
  }

  @Override
  public boolean isValid(Number value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    } else {
      return valueList.contains(value);
    }
  }
}
