package com.solum.xplain.core.portfolio.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REFERENCE_TRADE_ID;
import static java.lang.String.format;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.solum.xplain.core.portfolio.value.PortfolioNamesUniqueKey;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public final class TradeValueRowFilterUtils {
  private static final String PORTFOLIO_FILTER_MESSAGE = "Company %s Entity %s Portfolio %s";
  private static final String ALLOCATION_FILTER_MESSAGE = "Ref Security %s";

  public static RowFilter portfolioRowsFilter(PortfolioView view) {
    var namesUniqueKey = PortfolioNamesUniqueKey.fromView(view);
    var filterLabel =
        format(
            PORTFOLIO_FILTER_MESSAGE,
            view.getExternalCompanyId(),
            view.getExternalEntityId(),
            view.getExternalPortfolioId());
    return new RowFilter(
        row -> TradeCsvLoader.parseUniqueKey(row).map(namesUniqueKey::equals).getOr(() -> false),
        filterLabel);
  }

  public static RowFilter referenceRowsFilter(
      boolean onlyAllocationTrades, String referenceTradeId) {
    if (!onlyAllocationTrades || isEmpty(referenceTradeId)) {
      return RowFilter.EMPTY_FILTER;
    }
    return new RowFilter(
        row ->
            getFieldValue(row, TRADE_REFERENCE_TRADE_ID, String::toUpperCase)
                .map(referenceTradeId::equalsIgnoreCase)
                .getOr(() -> false),
        format(ALLOCATION_FILTER_MESSAGE, referenceTradeId));
  }
}
