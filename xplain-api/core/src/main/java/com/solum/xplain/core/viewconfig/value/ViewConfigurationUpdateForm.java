package com.solum.xplain.core.viewconfig.value;

import com.solum.xplain.core.viewconfig.validation.ValidViewConfiguration;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@SuperBuilder
@ValidViewConfiguration
public class ViewConfigurationUpdateForm {

  @NotEmpty
  @Schema(description = "Name of this view configuration")
  private final String name;

  @NotNull
  @Schema(description = "True if the first column definition group should be frozen (not scroll)")
  private final Boolean freezeFirstGroup;

  @Valid
  @NotEmpty
  @Schema(description = "Groups of column definitions")
  private final List<ColumnDefinitionGroupForm> columnDefinitionGroups;

  @NotNull
  @Schema(description = "Should this view be shared with other users")
  private final Boolean shared;
}
