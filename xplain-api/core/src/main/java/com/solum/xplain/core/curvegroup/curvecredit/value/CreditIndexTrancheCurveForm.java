package com.solum.xplain.core.curvegroup.curvecredit.value;

import com.solum.xplain.core.common.creditindex.HasCreditTranche;
import com.solum.xplain.core.common.creditindex.ValidCreditIndexTranche;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils;
import com.solum.xplain.core.curvegroup.curvecredit.validation.CreditCurveCreateForm;
import com.solum.xplain.core.curvegroup.curvecredit.validation.UniqueCreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditCurrency;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@UniqueCreditCurve
@FieldNameConstants
@ValidCreditIndexTranche
public class CreditIndexTrancheCurveForm extends CreditIndexCurveUpdateForm
    implements CreditCurveCreateForm, HasCreditTranche {

  @NotEmpty
  @ValidIdentifier
  @Schema(description = "Credit Curve Reference")
  private String reference;

  @NotEmpty
  @ValidCreditCurrency
  @Schema(description = "Credit Curve Currency")
  private String currency;

  @NotEmpty
  @Schema(description = "Credit Index Tranche")
  private String creditIndexTranche;

  @Override
  public String creditCurveName() {
    return CreditCurveNameUtils.resolveCreditIndexTrancheCurveName(
        reference, creditIndexTranche, currency);
  }
}
