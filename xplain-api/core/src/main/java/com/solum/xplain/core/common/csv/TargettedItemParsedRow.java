package com.solum.xplain.core.common.csv;

import static java.util.Objects.requireNonNull;

import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.function.BiFunction;

public class TargettedItemParsedRow<T> {
  private final String targetName;
  private final Either<ErrorItem, T> itemEither;

  /**
   * @param targetName - name by which the target is identified in import file e.g. the name of
   *     curve
   * @param itemEither - such as a curve node
   */
  public TargettedItemParsedRow(String targetName, Either<ErrorItem, T> itemEither) {
    requireNonNull(targetName);
    this.targetName = targetName;
    this.itemEither = requireNonNull(itemEither);
  }

  public static <T> TargettedItemParsedRow<T> fromEither(
      String targetName, Either<ErrorItem, T> parsedRow) {
    return new TargettedItemParsedRow<>(targetName, parsedRow);
  }

  public String getTargetName() {
    return targetName;
  }

  public Either<ErrorItem, T> getItemEither() {
    return itemEither;
  }

  public TargettedItemParsedRow<T> validateNode(
      BiFunction<TargettedItemParsedRow<T>, T, Either<ErrorItem, T>> f) {
    return new TargettedItemParsedRow<>(targetName, itemEither.flatMap(n -> f.apply(this, n)));
  }
}
