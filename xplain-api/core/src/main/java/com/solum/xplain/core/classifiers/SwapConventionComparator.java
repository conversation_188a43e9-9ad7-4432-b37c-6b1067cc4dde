package com.solum.xplain.core.classifiers;

import java.util.Comparator;
import java.util.List;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

public class SwapConventionComparator implements Comparator<String> {
  public static final SwapConventionComparator INSTANCE = new SwapConventionComparator();
  private static final Pattern FREQ_PATTERN = Pattern.compile("\\d+[WMY]");
  private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d+");

  @Override
  public int compare(String c1, String c2) {
    if (!ObjectUtils.allNotNull(c1, c2)) {
      return StringUtils.compare(c1, c2);
    }

    // make sure we are not comparing totally different conventions (different Ibor's etc.)
    if (!c1.replaceAll(FREQ_PATTERN.pattern(), "")
        .equals(c2.replaceAll(FREQ_PATTERN.pattern(), ""))) {
      return StringUtils.compare(c1, c2);
    }

    List<String> freq1 = conventionFrequencies(c1);
    List<String> freq2 = conventionFrequencies(c2);

    // if no match or match count differ, return
    if (freq1.isEmpty() || freq1.size() != freq2.size()) {
      return StringUtils.compare(c1, c2);
    }

    // compare first match
    int compare1 = Integer.compare(weekCount(freq1.get(0)), weekCount(freq2.get(0)));
    // if first compare worked or we do not have second match, return
    if (compare1 != 0 || freq1.size() == 1) {
      return compare1;
    }

    // compare second match
    return Integer.compare(weekCount(freq1.get(1)), weekCount(freq2.get(1)));
  }

  private List<String> conventionFrequencies(String convention) {
    return FREQ_PATTERN.matcher(convention).results().map(MatchResult::group).toList();
  }

  private int weekCount(String freq) {
    // calculate aprox. weeks count
    int n =
        NUMBER_PATTERN
            .matcher(freq)
            .results()
            .map(MatchResult::group)
            .findFirst()
            .map(Integer::parseInt)
            .orElse(0);
    if (freq.contains("M")) {
      n *= 4;
    }
    if (freq.contains("Y")) {
      n *= 4 * 12;
    }
    return n;
  }
}
