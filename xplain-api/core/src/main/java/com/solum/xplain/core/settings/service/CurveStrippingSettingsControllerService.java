package com.solum.xplain.core.settings.service;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.form.CurveStrippingProductForm;
import com.solum.xplain.core.settings.repository.CurveStrippingProductSettingsRepository;
import com.solum.xplain.core.settings.value.CurveStrippingProductSettingsView;
import com.solum.xplain.core.settings.value.GlobalSettingsSearch;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveStrippingSettingsControllerService {
  private final CurveStrippingProductSettingsRepository productSettingsRepository;

  public CurveStrippingProductSettingsView getCurveStrippingProductSettings(
      BitemporalDate stateDate) {
    return productSettingsRepository.entityView(stateDate);
  }

  @Transactional
  public Either<ErrorItem, EntityId> saveCurveStrippingProductSettings(
      LocalDate stateDate, CurveStrippingProductForm form) {
    return productSettingsRepository.saveProductSettings(stateDate, form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteCurveStrippingProductSettings(LocalDate stateDate) {
    return productSettingsRepository.deleteProductSettingsVersion(stateDate);
  }

  public List<CurveStrippingProductSettingsView> productSettingsVersions() {
    return productSettingsRepository.entityVersions();
  }

  public DateList getCurveStrippingProductSettingsFutureVersions(GlobalSettingsSearch search) {
    return productSettingsRepository.futureVersions(search.getStateDate());
  }
}
