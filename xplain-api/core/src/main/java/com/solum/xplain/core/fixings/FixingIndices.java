package com.solum.xplain.core.fixings;

import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES;
import static com.solum.xplain.core.classifiers.Constants.INFLATION_INDICES;
import static com.solum.xplain.core.classifiers.Constants.OVERNIGHT_INDICES;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.index.Index;
import com.solum.xplain.core.common.CollectionUtils;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FixingIndices {

  public static final List<Index> FIXING_INDICES =
      new ImmutableList.Builder<Index>()
          .addAll(IBOR_INDICES)
          .addAll(OVERNIGHT_INDICES)
          .addAll(INFLATION_INDICES)
          .build();

  private static final Map<String, Index> INDEX_LOOKUP =
      CollectionUtils.toMap(FIXING_INDICES, Index::getName);

  public static Index lookupFixingIndex(String indexName) {
    return INDEX_LOOKUP.get(indexName);
  }
}
