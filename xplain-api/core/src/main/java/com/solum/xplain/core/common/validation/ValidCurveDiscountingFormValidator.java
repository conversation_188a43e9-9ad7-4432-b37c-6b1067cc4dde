package com.solum.xplain.core.common.validation;

import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY;
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS;

import com.solum.xplain.core.common.value.CurveDiscountingForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidCurveDiscountingFormValidator
    implements ConstraintValidator<ValidCurveDiscountingForm, CurveDiscountingForm> {

  public boolean isValid(CurveDiscountingForm form, ConstraintValidatorContext context) {
    if (form == null || form.getDiscountingType() == null || form.getStrippingType() == null) {
      return true;
    }

    return LOCAL_CURRENCY.name().equals(form.getDiscountingType())
        || OIS.name().equals(form.getStrippingType());
  }
}
