package com.solum.xplain.core.company.entity;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class ValuationSettings extends VersionedEntity {
  private CompanySettingsType settingsType;
  private ValuationSettingsMarketDataGroup marketDataGroup;
  private String configurationType;
  private EntityReference curveConfiguration;
  private EntityReference nonFxCurveConfiguration;
  private String strippingType;
  private String discountingType;
  private String triangulationCcy;
  private String reportingCurrency;
  private Boolean useCsaDiscounting;
  private InstrumentPriceRequirements priceRequirements;

  @Override
  public boolean valueEquals(Object object) {
    ValuationSettings other = (ValuationSettings) object;
    return super.valueEquals(object)
        && Objects.equals(this.marketDataGroup, other.marketDataGroup)
        && Objects.equals(configurationType, other.configurationType)
        && Objects.equals(curveConfiguration, other.curveConfiguration)
        && Objects.equals(nonFxCurveConfiguration, other.nonFxCurveConfiguration)
        && Objects.equals(strippingType, other.strippingType)
        && Objects.equals(discountingType, other.discountingType)
        && Objects.equals(triangulationCcy, other.triangulationCcy)
        && Objects.equals(reportingCurrency, other.reportingCurrency)
        && Objects.equals(useCsaDiscounting, other.useCsaDiscounting)
        && Objects.equals(priceRequirements, other.priceRequirements);
  }
}
