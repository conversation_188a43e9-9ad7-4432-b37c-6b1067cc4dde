package com.solum.xplain.core.curvegroup.conventions;

import static com.google.common.collect.ImmutableList.copyOf;
import static com.solum.xplain.core.curvegroup.conventions.fx.XccyCurveConventions.XCCY_CURVES;
import static com.solum.xplain.core.curvegroup.conventions.index.IndexBasisCurveConventions.INDEX_BASIS_CURVES;
import static com.solum.xplain.core.curvegroup.conventions.index.InflationCurveConventions.INFLATION_CURVES;
import static com.solum.xplain.core.curvegroup.conventions.index.IrIndexCurveConventions.INDEX_CURVES;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.extensions.index.OffshoreIndices;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ConventionalCurveConfigurations {

  public static final List<ConventionalCurveConvention> ALL_CONVENTIONAL_CURVES =
      ImmutableList.<ConventionalCurveConvention>builder()
          .addAll(INDEX_BASIS_CURVES)
          .addAll(INDEX_CURVES)
          .addAll(XCCY_CURVES)
          .addAll(INFLATION_CURVES)
          .build();

  private static final Map<String, String> ALL_CURVES_NAME_KEY_MAP =
      ALL_CONVENTIONAL_CURVES.stream()
          .collect(toMap(CurveConvention::getName, CurveConvention::getKey, (a, b) -> a));

  public static Optional<ConventionalCurveConvention> lookupByName(String name, CurveType type) {
    return configurationsByType(type).stream()
        .filter(c -> StringUtils.equals(c.getName(), name))
        .findFirst();
  }

  public static List<ConventionalCurveConvention> lookupByName(String name) {
    return ALL_CONVENTIONAL_CURVES.stream()
        .filter(c -> StringUtils.equals(c.getName(), name))
        .toList();
  }

  public static Optional<String> lookupKeyByName(String name) {
    return Optional.ofNullable(ALL_CURVES_NAME_KEY_MAP.get(name));
  }

  public static List<CurveConvention> lookupAllByKey(String key) {
    return ALL_CONVENTIONAL_CURVES.stream()
        .filter(c -> StringUtils.equals(c.getKey(), key))
        .map(CurveConvention.class::cast)
        .toList();
  }

  public static Optional<ConventionalCurveConvention> lookupByIndex(String index) {
    return ALL_CONVENTIONAL_CURVES.stream()
        .filter(IndexCurveConvention.class::isInstance)
        .filter(c -> StringUtils.equals(((IndexCurveConvention) c).getIndex().getName(), index))
        .findFirst();
  }

  public static IndexCurveConvention lookupByOvernightIndex(OvernightIndex index) {
    return lookupByIndex(index.getName()).map(IndexCurveConvention.class::cast).orElse(null);
  }

  public static <T extends FloatingRateIndex> List<T> getAllowedIndicesByType(Class<T> type) {
    return ALL_CONVENTIONAL_CURVES.stream()
        .filter(IndexCurveConvention.class::isInstance)
        .map(IndexCurveConvention.class::cast)
        .map(IndexCurveConvention::getIndex)
        .filter(type::isInstance)
        .map(type::cast)
        .filter(not(OffshoreIndices::isOffshore))
        .distinct()
        .sorted(Comparator.comparing(FloatingRateIndex::getName))
        .toList();
  }

  public static List<ConventionalCurveConvention> configurationsByType(CurveType curveType) {
    return switch (curveType) {
      case IR_INDEX -> copyOf(INDEX_CURVES);
      case XCCY -> copyOf(XCCY_CURVES);
      case INDEX_BASIS -> copyOf(INDEX_BASIS_CURVES);
      case INFLATION_INDEX -> copyOf(INFLATION_CURVES);
    };
  }
}
