package com.solum.xplain.core.company;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import com.solum.xplain.core.company.value.CompanyView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class UserCompanyService {
  private final AuthenticationContext authenticationContext;
  private final CompanyRepository companyRepository;
  private final CompanyLegalEntityRepository legalEntityRepository;

  public UserCompanyService(
      AuthenticationContext authenticationContext,
      CompanyRepository companyRepository,
      CompanyLegalEntityRepository legalEntityRepository) {
    this.authenticationContext = authenticationContext;
    this.companyRepository = companyRepository;
    this.legalEntityRepository = legalEntityRepository;
  }

  public Either<ErrorItem, UserTeamEntity<CompanyView>> userCompany(
      Authentication user, String companyId) {
    return authenticationContext
        .userEither(user)
        .flatMap(u -> companyRepository.userCompanyView(u, companyId));
  }

  public Either<ErrorItem, UserTeamEntity<CompanyLegalEntityView>> userLegalEntity(
      Authentication authentication, String companyId, String entityId) {
    return authenticationContext
        .userEither(authentication)
        .flatMap(u -> legalEntityRepository.userCompanyLegalEntityView(u, companyId, entityId));
  }
}
