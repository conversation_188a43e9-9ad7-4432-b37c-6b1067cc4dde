package com.solum.xplain.core.curvegroup.instrument;

import static com.solum.xplain.core.classifiers.BondCurveNodeTypes.BOND_YIELD_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.*;
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.INFLATION_RATE;
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.ALL_CREDIT;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.ALL_FX;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.INFLATION;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.IR;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.VOL;
import static com.solum.xplain.core.instrument.InstrumentPriceGroup.CURVE_PRICE;
import static com.solum.xplain.core.instrument.InstrumentPriceGroup.FX_RATE_PRICE;
import static com.solum.xplain.core.instrument.InstrumentPriceGroup.VOL_PRICE;
import static com.solum.xplain.core.instrument.InstrumentPriceGroup.VOL_SKEW_PRICE;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.InstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentPriceGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import java.util.Arrays;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CoreInstrumentType implements InstrumentType {
  IBOR_FIXING_DEPOSIT(IBOR_FIXING_DEPOSIT_NODE, IR_RATE, IR, true, 1, CURVE_PRICE),
  TERM_DEPOSIT(TERM_DEPOSIT_NODE, IR_RATE, IR, true, 1, CURVE_PRICE),
  FIXED_IBOR_SWAP(FIXED_IBOR_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  FIXED_OVERNIGHT_SWAP(FIXED_OVERNIGHT_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  FRA(FRA_NODE, IR_RATE, IR, true, 3, CURVE_PRICE),
  IMM_FRA(IMM_FRA_NODE, IR_RATE, IR, true, 3, CURVE_PRICE),
  IBOR_FUTURE(IBOR_FUTURE_NODE, IR_RATE, IR, true, 2, CURVE_PRICE),
  OVERNIGHT_IBOR_BASIS_SWAP(OVERNIGHT_IBOR_BASIS_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  IBOR_IBOR_SWAP(IBOR_IBOR_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  XCCY_IBOR_IBOR_SWAP(XCCY_IBOR_IBOR_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  XCCY_OIS_OIS_SWAP(XCCY_OIS_OIS_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  XCCY_IBOR_OIS_SWAP(XCCY_IBOR_OIS_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  XCCY_FIXED_OVERNIGHT_SWAP(XCCY_FIXED_OVERNIGHT_SWAP_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  FIXED_INFLATION_SWAP(FIXED_INFLATION_SWAP_NODE, INFLATION_RATE, INFLATION, true, 5, CURVE_PRICE),
  BOND_YIELD(BOND_YIELD_NODE, IR_RATE, IR, true, 5, CURVE_PRICE),
  SWAPTION_ATM("Swaption ATM", CoreAssetClass.SWAPTION_VOLS, VOL, false, 6, VOL_PRICE),
  SWAPTION_SKEW("Swaption Skews", CoreAssetClass.SWAPTION_SKEW, VOL, false, 6, VOL_SKEW_PRICE),
  CAP_FLOOR_VOL("Cap / Floors", CoreAssetClass.CAPFLOOR_VOLS, VOL, false, 6, VOL_PRICE),
  FX_RATE("FX", CoreAssetClass.FX_RATES, ALL_FX, false, 4, FX_RATE_PRICE),
  FX_SWAP("FxSwap", CoreAssetClass.FX_SWAP, ALL_FX, false, 6, CURVE_PRICE),
  FX_VOL("FX Vol", CoreAssetClass.FX_VOLS, ALL_FX, false, 6, VOL_PRICE),
  FX_VOL_SKEW("FX Vol Skew", CoreAssetClass.FX_VOL_SKEW, ALL_FX, false, 6, VOL_SKEW_PRICE),
  CDS("CDS", CoreAssetClass.CDS, ALL_CREDIT, false, 6, CURVE_PRICE),
  CREDIT_INDEX("Credit Index", CoreAssetClass.CREDIT_INDEX, ALL_CREDIT, false, 6, CURVE_PRICE),
  CREDIT_INDEX_TRANCHE(
      "Credit Index Tranche",
      CoreAssetClass.CREDIT_INDEX_TRANCHE,
      ALL_CREDIT,
      false,
      7,
      CURVE_PRICE),
  ;
  private final String label;
  private final AssetClass assetClass;
  private final InstrumentGroup instrumentGroup;
  private final boolean isNodeType;
  private final int sortOrder;
  private final InstrumentPriceGroup priceGroup;

  public boolean isNodeType() {
    return isNodeType;
  }

  @Override
  public int getSortOrder() {
    return sortOrder;
  }

  @Override
  public InstrumentPriceGroup getPriceGroup() {
    return priceGroup;
  }

  public static InstrumentType ofLabel(String label) {
    return Arrays.stream(values())
        .filter(v -> StringUtils.equals(v.getLabel(), label))
        .findFirst()
        .orElse(null);
  }
}
