package com.solum.xplain.core.curvegroup.curvegroup.value;

import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.market.MarketDataGroup;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CalibrationMarketData {
  private String marketDataGroupId;
  private String marketDataGroupName;
  private String curveConfigurationId;
  private MarketDataSourceType sourceType;

  public CalibrationMarketData() {}

  public CalibrationMarketData(
      EntityNameView m, MarketDataSourceType sourceType, String curveConfigurationId) {
    this.marketDataGroupId = m.getId();
    this.marketDataGroupName = m.getName();
    this.curveConfigurationId = curveConfigurationId;
    this.sourceType = sourceType;
  }

  public CalibrationMarketData(
      MarketDataGroup marketDataGroup,
      MarketDataSourceType sourceType,
      String curveConfigurationId) {
    this.marketDataGroupId = marketDataGroup.getId();
    this.marketDataGroupName = marketDataGroup.getName();
    this.curveConfigurationId = curveConfigurationId;
    this.sourceType = sourceType;
  }
}
