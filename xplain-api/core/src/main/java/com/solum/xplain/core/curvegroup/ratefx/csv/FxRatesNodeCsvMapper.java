package com.solum.xplain.core.curvegroup.ratefx.csv;

import static com.solum.xplain.core.curvegroup.ratefx.csv.FxRatesNodeCsvLoader.DOMESTIC_CURRENCY_FIELD;
import static com.solum.xplain.core.curvegroup.ratefx.csv.FxRatesNodeCsvLoader.FOREIGN_CURRENCY_FIELD;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeView;
import java.util.List;

public class FxRatesNodeCsvMapper extends CsvMapper<CurveGroupFxRatesNodeValueView> {

  private static final List<CsvColumn<CurveGroupFxRatesNodeValueView>> COLUMNS =
      List.of(
          CsvColumn.text(
              CurveGroupFxRatesNodeView.Fields.domesticCurrency,
              DOMESTIC_CURRENCY_FIELD,
              CurveGroupFxRatesNodeValueView::getDomesticCurrency),
          CsvColumn.text(
              CurveGroupFxRatesNodeView.Fields.foreignCurrency,
              FOREIGN_CURRENCY_FIELD,
              CurveGroupFxRatesNodeValueView::getForeignCurrency));

  public FxRatesNodeCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
