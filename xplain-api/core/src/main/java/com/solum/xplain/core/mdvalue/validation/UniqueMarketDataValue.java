package com.solum.xplain.core.mdvalue.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueMarketDataValueValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface UniqueMarketDataValue {
  String MESSAGE_KEY = "com.solum.xplain.api.md.validation.UniqueMarketData.message";

  String message() default "{" + MESSAGE_KEY + "}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
