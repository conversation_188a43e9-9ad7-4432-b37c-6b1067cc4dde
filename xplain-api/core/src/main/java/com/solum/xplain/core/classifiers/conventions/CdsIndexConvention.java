package com.solum.xplain.core.classifiers.conventions;

import com.solum.xplain.core.classifiers.CdsIndex;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CdsIndexConvention {
  private final String name;
  private final String currency;
  private final String sector;
  private final String docClause;

  public static CdsIndexConvention newOf(CdsIndex index) {
    return new CdsIndexConvention(
        index.name(),
        index.getCurrency().getCode(),
        index.getSector().toString(),
        index.getDocClause().toString());
  }
}
