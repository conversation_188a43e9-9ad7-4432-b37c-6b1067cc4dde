package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.csv.TradeValueCsvLoaderFactory;
import com.solum.xplain.core.portfolio.event.CompanyPortfoliosModified;
import com.solum.xplain.core.portfolio.event.PortfolioDetailsUpdated;
import com.solum.xplain.core.portfolio.event.PortfolioUpdated;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.form.PortfolioItemSearchForm;
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm;
import com.solum.xplain.core.portfolio.form.TradeImportOptions;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioCountedView;
import com.solum.xplain.core.portfolio.value.PortfolioFilter;
import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.shared.utils.cache.CacheUnpaged;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Unit;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@AllArgsConstructor
public class PortfolioControllerService {

  private final PortfolioRepository portfolioRepository;
  private final PortfolioItemRepository portfolioItemRepository;
  private final PortfolioItemWriteRepository portfolioItemWriteRepository;
  private final AuthenticationContext authenticationContext;
  private final PortfolioItemsUploadService portfolioItemsUploadService;
  private final PortfolioTeamFilterProvider teamFilterProvider;
  private final PortfolioMapper portfolioMapper;
  private final TradeValueCsvLoaderFactory loaderFactory;
  private final ViewQueryTranslatorFactory viewQueryTranslatorFactory;
  private final TeamRepository teamRepository;

  @CacheUnpaged(
      invalidateOnEvent = {
        CompanyPortfoliosModified.class,
        PortfolioUpdated.class,
        PortfolioDetailsUpdated.class
      })
  public ScrollableEntry<PortfolioCountedView> getAll(
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      PortfolioFilter portfolioFilter,
      BitemporalDate stateDate) {
    var filter = teamFilterProvider.provideFilter();
    ScrollableEntry<PortfolioView> uncountedViews =
        portfolioRepository.portfolioList(scrollRequest, tableFilter, portfolioFilter, filter);
    List<PortfolioCountedView> countedViews =
        toCountedViews(uncountedViews.getContent(), stateDate);
    Long lastRow = uncountedViews.getLastRow();
    return lastRow == null
        ? ScrollableEntry.of(countedViews, scrollRequest)
        : ScrollableEntry.of(countedViews, scrollRequest, lastRow);
  }

  public List<PortfolioCondensedView> getPortfoliosForCompanyLegalEntity(
      String entityId, PortfolioFilter filter) {
    var teamFilter = EntityTeamFilter.filter(authenticationContext.currentUser());
    return portfolioRepository.portfolioListForEntityId(entityId, filter, teamFilter);
  }

  public List<PortfolioCondensedView> getAllActivePortfolios() {
    var f = teamFilterProvider.provideFilter();
    return portfolioRepository.portfolioCondensedViewList(
        activePortfolios(),
        f,
        Sort.by(
                PortfolioCondensedView.Fields.externalCompanyId,
                PortfolioCondensedView.Fields.externalEntityId,
                PortfolioCondensedView.Fields.externalPortfolioId)
            .ascending());
  }

  @Transactional
  public Either<ErrorItem, EntityId> create(PortfolioCreateForm newForm) {
    return Either.<ErrorItem, XplainPrincipal>right(authenticationContext.currentUser())
        .flatMap(
            u ->
                isTrue(newForm.getAllowedTeamsForm().getAllowAll())
                    ? Either.right(u)
                    : u.allowedTeams(newForm.getAllowedTeamsForm().getTeamIds()))
        .flatMap(u -> portfolioRepository.insert(newForm));
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(String id, PortfolioUpdateForm edit) {
    return getUserPortfolio(id).flatMap(p -> portfolioRepository.update(p.getView().getId(), edit));
  }

  public Either<ErrorItem, PortfolioCountedView> get(String id, BitemporalDate stateDate) {
    return getUserPortfolio(id).map(UserTeamEntity::getView).map(p -> toCountedView(p, stateDate));
  }

  public DateList futureVersions(String portfolioId, PortfolioItemSearchForm form) {
    return portfolioItemRepository.futureVersions(portfolioId, form);
  }

  private Either<ErrorItem, UserTeamEntity<PortfolioView>> getUserPortfolio(String id) {
    return portfolioRepository.getUserPortfolioView(authenticationContext.currentUser(), id);
  }

  private Either<ErrorItem, Unit> canAccessPortfolio(String id) {
    return portfolioRepository
        .getUserPortfolioView(authenticationContext.currentUser(), id)
        .map(__ -> Unit.VALUE);
  }

  public Either<ErrorItem, ScrollableEntry<PortfolioItemWithKeyView>> getItems(
      String id,
      BitemporalDate stateDate,
      boolean withArchived,
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(PortfolioItemWithKeyView.class);

    return getUserPortfolio(id)
        .map(UserTeamEntity::getView)
        .map(
            p ->
                portfolioItemRepository.portfolioItemsWithKeyView(
                    id,
                    stateDate,
                    viewQueryTranslator.translate(tableFilter),
                    VersionedEntityFilter.of(withArchived),
                    viewQueryTranslator.translate(scrollRequest),
                    // TODO: SXSD-9967 translate the group request too
                    groupRequest));
  }

  public Either<ErrorItem, PortfolioItemWithKeyView> getItem(
      String id, String tradeEntityId, BitemporalDate stateDate) {
    return canAccessPortfolio(id)
        .flatMap(
            __ ->
                portfolioItemRepository
                    .portfolioItemLatest(id, tradeEntityId, stateDate)
                    .map(portfolioMapper::toKeyView));
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadTrades(
      MultipartFile file, TradeImportOptions importOptions) throws IOException {
    byte[] bytes = file.getBytes();
    var loader =
        loaderFactory.standard(
            isTrue(importOptions.getOnlyAllocationTrades()),
            importOptions.getReferenceTradeId(),
            importOptions.getStateDate());
    return portfolioItemsUploadService.importTrades(loader, bytes, importOptions);
  }

  public Either<List<ErrorItem>, ValidationResponse> validateTradesImport(
      MultipartFile file, TradeImportOptions importOptions) throws IOException {
    byte[] bytes = file.getBytes();
    var loader =
        loaderFactory.standard(
            isTrue(importOptions.getOnlyAllocationTrades()),
            importOptions.getReferenceTradeId(),
            importOptions.getStateDate());
    return portfolioItemsUploadService.validate(loader, bytes, importOptions);
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadTradesForPortfolio(
      String portfolioId, MultipartFile file, TradeImportOptions importOptions) throws IOException {
    byte[] bytes = file.getBytes();
    return getUserPortfolio(portfolioId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(UserTeamEntity::getView)
        .map(view -> loaderFactory.forPortfolio(view, importOptions.getStateDate()))
        .flatMap(l -> portfolioItemsUploadService.importTrades(l, bytes, importOptions));
  }

  public Either<List<ErrorItem>, ValidationResponse> validateUploadForPortfolio(
      String portfolioId, MultipartFile file, TradeImportOptions importOptions) throws IOException {
    byte[] bytes = file.getBytes();
    return getUserPortfolio(portfolioId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(UserTeamEntity::getView)
        .map(view -> loaderFactory.forPortfolio(view, importOptions.getStateDate()))
        .flatMap(l -> portfolioItemsUploadService.validate(l, bytes, importOptions));
  }

  public Either<ErrorItem, EntityId> archivePortfolio(String portfolioId) {
    return getUserPortfolio(portfolioId).flatMap(u -> portfolioRepository.archive(portfolioId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archivePortfolioItem(
      String portfolioId, String tradeEntityId, LocalDate version, ArchiveEntityForm form) {
    return getUserPortfolio(portfolioId)
        .flatMap(
            p ->
                portfolioItemWriteRepository.archiveItem(
                    p.getView().getId(), tradeEntityId, version, form));
  }

  public Either<ErrorItem, EntityId> deletePortfolioItem(
      String portfolioId, String tradeEntityId, LocalDate version) {
    return getUserPortfolio(portfolioId)
        .flatMap(
            p ->
                portfolioItemWriteRepository.deleteItem(
                    p.getView().getId(), tradeEntityId, version));
  }

  public Either<ErrorItem, List<PortfolioItemWithKeyView>> getVersions(
      String id, String tradeEntityId) {
    return canAccessPortfolio(id)
        .map(
            __ ->
                portfolioItemRepository.getVersions(id, tradeEntityId).stream()
                    .map(portfolioMapper::toKeyView)
                    .toList());
  }

  private PortfolioCountedView toCountedView(PortfolioView view, BitemporalDate stateDate) {
    var tradesCount = portfolioItemRepository.portfolioTradesCount(view.getId(), stateDate);
    return portfolioMapper.toCountedView(view, tradesCount, teamRepository.getTeamsById());
  }

  private List<PortfolioCountedView> toCountedViews(
      Collection<PortfolioView> views, BitemporalDate stateDate) {
    Map<String, Integer> tradesCountMap =
        portfolioItemRepository.portfoliosTradesCount(
            views.stream().map(PortfolioView::getId).toList(), stateDate);
    Map<String, ? extends TeamNameView> teamNamesMap = teamRepository.getTeamsById();
    return views.stream()
        .map(
            p ->
                portfolioMapper.toCountedView(
                    p, tradesCountMap.getOrDefault(p.getId(), 0), teamNamesMap))
        .toList();
  }
}
