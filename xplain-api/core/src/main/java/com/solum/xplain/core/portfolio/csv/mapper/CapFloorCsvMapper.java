package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.ACRUAL_SCHEDULE_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_FIXING_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_SPREAD_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_IDENTIFIER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.OFFSHORE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_COMPOUNDING_METHOD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_DATE_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CAPFLOOR_STRIKE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CAPFLOOR_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE_ADJ_CONVENTION;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.csv.DayCountCsvUtils;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CapFloorCsvMapper implements ProductCsvMapper {

  public static final Integer CAPFLOOR_PAYMENT_OFFSET_DAYS = 0;
  public static final StubConvention CAPFLOOR_STUB_CONVENTION = StubConvention.NONE;

  public static final BusinessDayAdjustmentType CAPFLOOR_BUSINESS_DAY_ADJUSTMENT_TYPE =
      BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.CAP_FLOOR);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails trade) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    var optionDetails = trade.getOptionTradeDetails();
    builder.addAll(OptionDetailsCsvMapper.toCsvFields(optionDetails));
    builder.add(new CsvField(TRADE_CAPFLOOR_TYPE, optionDetails.getCapFloorType()));
    builder.add(new CsvField(TRADE_CAPFLOOR_STRIKE, optionDetails.getStrike()));
    builder.add(
        new CsvField(TRADE_PREMIUM_DATE_ADJ_CONVENTION, optionDetails.getPremiumDateConvention()));
    builder.add(new CsvField(TRADE_POSITION, trade.getPositionType()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_START_DATE, trade.getStartDate()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_END_DATE, trade.getEndDate()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION, CAPFLOOR_STUB_CONVENTION));
    builder.add(new CsvField(TRADE_BUSINESS_DAY_CONVENTION, trade.getBusinessDayConvention()));
    builder.add(
        new CsvField(
            TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE, CAPFLOOR_BUSINESS_DAY_ADJUSTMENT_TYPE.name()));

    trade
        .tradePositionLeg()
        .ifPresent(
            leg -> {
              builder.add(new CsvField(addField(LEG_1, OFFSHORE), leg.getIsOffshore()));
              builder.add(new CsvField(addField(LEG_1, LEG_CURRENCY), leg.getCurrency()));
              builder.add(new CsvField(addField(LEG_1, LEG_TYPE), CalculationType.IBOR.getLabel()));
              builder.add(new CsvField(addField(LEG_1, LEG_NOTIONAL), leg.getNotional()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, CALCULATION_IBOR_DAY_COUNT),
                      DayCountCsvUtils.toExportLabel(leg.getDayCount())));
              builder.add(new CsvField(addField(LEG_1, CALCULATION_IBOR_INDEX), leg.getIndex()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, CALCULATION_IBOR_FIXING_OFFSET),
                      leg.getFixingDateOffsetDays()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, PAYMENT_SCHEDULE_DATE_OFFSET), leg.getPaymentOffsetDays()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, CALCULATION_IBOR_SPREAD_VALUE), leg.getInitialValue()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, PAYMENT_SCHEDULE_DATE_OFFSET), CAPFLOOR_PAYMENT_OFFSET_DAYS));
              builder.add(
                  new CsvField(
                      addField(LEG_1, PAYMENT_SCHEDULE_FREQUENCY),
                      IborIndex.of(leg.getIndex()).getTenor().toString()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, ACRUAL_SCHEDULE_FREQUENCY),
                      IborIndex.of(leg.getIndex()).getTenor().toString()));
              builder.add(
                  new CsvField(
                      addField(LEG_1, PAYMENT_SCHEDULE_COMPOUNDING_METHOD),
                      leg.getPaymentCompounding()));
              builder.add(new CsvField(addField(LEG_1, LEG_IDENTIFIER), leg.getExtLegIdentifier()));
            });

    return builder.build();
  }
}
