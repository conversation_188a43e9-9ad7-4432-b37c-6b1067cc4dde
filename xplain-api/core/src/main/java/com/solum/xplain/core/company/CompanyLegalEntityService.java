package com.solum.xplain.core.company;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.team.EntityTeamUtils.filterAllowedEntities;
import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.company.entity.CompanyLegalEntityCsvMapper;
import com.solum.xplain.core.company.form.CompanyLegalEntityCreateForm;
import com.solum.xplain.core.company.form.CompanyLegalEntityUpdateForm;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@AllArgsConstructor
public class CompanyLegalEntityService {

  private final CompanyLegalEntityRepository repository;
  private final AuthenticationContext authenticationContext;
  private final CompanyLegalEntityUploadService uploadService;
  private final CompanyControllerService companyControllerService;

  @Transactional
  public Either<ErrorItem, EntityId> create(
      Authentication auth, String companyId, CompanyLegalEntityCreateForm form) {
    return companyControllerService
        .getUserCompany(auth, companyId)
        .flatMap(u -> repository.createEntity(companyId, form));
  }

  public Either<List<ErrorItem>, List<EntityId>> upload(
      MultipartFile file, ImportOptions importOptions) throws IOException {
    return uploadService.importEntities(file, importOptions);
  }

  public Either<ErrorItem, FileResponseEntity> getEntityListCsv(
      Authentication auth, LocalDate stateDate) {
    var user = authenticationContext.user(auth);

    var csvFileName = nameWithTimeStamp("EntityList", stateDate);
    var mapper = new CompanyLegalEntityCsvMapper();
    var entityImportViewStream =
        repository
            .entityImportAggregatedViews(user, newOf(stateDate, LocalDateTime.now()))
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);

    var file = new CsvOutputFile(mapper.header(), entityImportViewStream);
    return Either.right(FileResponseEntity.csvFile(file.writeToByteArray(), csvFileName));
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      Authentication auth, String companyId, String entityId, CompanyLegalEntityUpdateForm form) {

    return userLegalEntity(auth, companyId, entityId)
        .flatMap(u -> repository.updateEntity(companyId, entityId, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archive(
      Authentication auth, String companyId, String entityId) {

    return userLegalEntity(auth, companyId, entityId)
        .flatMap(id -> repository.archiveEntity(companyId, entityId));
  }

  public Either<ErrorItem, List<CompanyLegalEntityView>> list(
      Authentication authentication, String companyId, CompanyLegalEntityFilter filter, Sort sort) {
    return Steps.begin(companyControllerService.getUserCompany(authentication, companyId))
        .then(() -> right(repository.companyLegalEntitiesViews(companyId, filter, sort)))
        .yield((u, list) -> filterAllowedEntities(list, u.getUser()));
  }

  public Either<ErrorItem, List<CompanyLegalEntityView>> list(
      Authentication authentication, String companyId) {
    return Steps.begin(companyControllerService.getUserCompany(authentication, companyId))
        .then(
            () ->
                right(
                    repository.companyLegalEntitiesViews(
                        companyId, CompanyLegalEntityFilter.notArchived())))
        .yield((u, list) -> filterAllowedEntities(list, u.getUser()));
  }

  public Either<ErrorItem, CompanyLegalEntityView> legalEntity(
      Authentication auth, String companyId, String entityId) {
    return userLegalEntity(auth, companyId, entityId).map(UserTeamEntity::getView);
  }

  public Either<ErrorItem, UserTeamEntity<CompanyLegalEntityView>> userLegalEntity(
      Authentication authentication, String companyId, String entityId) {
    return authenticationContext
        .userEither(authentication)
        .flatMap(u -> repository.userCompanyLegalEntityView(u, companyId, entityId));
  }
}
