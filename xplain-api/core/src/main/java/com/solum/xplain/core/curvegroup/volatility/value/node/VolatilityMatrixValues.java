package com.solum.xplain.core.curvegroup.volatility.value.node;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityValueMatrix;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class VolatilityMatrixValues extends VolatilityValueMatrix<VolatilityNodeValueView> {

  private VolatilityMatrixValues(List<VolatilityNodeValueView> content, LocalDate versionDate) {
    super(content, versionDate);
  }

  public static VolatilityMatrixValues configurationFromList(
      List<VolatilityNodeValueView> nodes, LocalDate versionDate) {
    return new VolatilityMatrixValues(nodes, versionDate);
  }

  public static VolatilityMatrixValues configurationFromList(
      VersionedList<VolatilityNodeValueView> nodes) {
    return new VolatilityMatrixValues(nodes.getList(), nodes.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(Tenor::parse);
  }
}
