package com.solum.xplain.core.csv;

import com.solum.xplain.core.common.csv.ImportActionSummary;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.VersionedImportItems;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;

@FunctionalInterface
public interface ImportFunction<V, K, E extends EmbeddedVersionEntity<V>> {
  ImportActionSummary modify(VersionedImportItems<V, K, E> importItems, ImportOptions options);
}
