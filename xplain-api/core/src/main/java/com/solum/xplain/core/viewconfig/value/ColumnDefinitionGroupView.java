package com.solum.xplain.core.viewconfig.value;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/** a list of Column Definitions, part of a View Configuration */
public record ColumnDefinitionGroupView(
    @Schema(description = "Literal name of column group") @NotNull String name,
    @Schema(description = "List of columns in the group") @NotNull
        List<ColumnDefinitionView> columnDefinitions,
    @Schema(
            description =
                "Optional RGB colour (as a hex string #aabbcc) to associate with the group")
        String hexColour) {

  public ColumnDefinitionGroupView(String name, List<ColumnDefinitionView> columnDefinitions) {
    this(name, columnDefinitions, null);
  }
}
