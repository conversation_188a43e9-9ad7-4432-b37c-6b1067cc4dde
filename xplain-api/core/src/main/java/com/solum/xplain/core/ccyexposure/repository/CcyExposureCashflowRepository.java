package com.solum.xplain.core.ccyexposure.repository;

import static org.springframework.data.domain.Sort.Order.asc;
import static org.springframework.data.domain.Sort.by;

import com.solum.xplain.core.ccyexposure.entity.Cashflow;
import com.solum.xplain.core.ccyexposure.repository.fragment.CcyExposureCashflowQueries;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.spring.mongo.XplainDefaultAggregations;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

@Repository
@XplainDefaultAggregations
public interface CcyExposureCashflowRepository
    extends org.springframework.data.repository.Repository<Cashflow, String>,
        CcyExposureCashflowQueries {

  List<Cashflow> findAllByCcyExposureId(
      String ccyExposureId,
      BitemporalDate stateDate,
      VersionedEntityFilter filter,
      Pageable pageable);

  default List<Cashflow> getActiveItemsByCcyExposureId(String ccyExposureId, LocalDate stateDate) {
    return findAllByCcyExposureId(
        ccyExposureId,
        BitemporalDate.newOf(stateDate),
        VersionedEntityFilter.active(),
        by(asc(Cashflow.Fields.date)));
  }

  default List<Cashflow> findAllByCcyExposureId(
      String ccyExposureId, BitemporalDate stateDate, VersionedEntityFilter filter, Sort sort) {
    return findAllByCcyExposureId(ccyExposureId, stateDate, filter, Pageable.unpaged(sort));
  }

  Optional<Cashflow> findOneByEntityId(@NotNull String id);
}
