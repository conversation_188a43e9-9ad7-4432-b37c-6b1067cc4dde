package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;

public class ValidIborLegAccrualFrequencyValidator
    implements ConstraintValidator<ValidIborLegAccrualFrequency, SwapLegForm> {

  @Override
  public boolean isValid(SwapLegForm form, ConstraintValidatorContext context) {
    if (form.getCalculationType() != CalculationType.IBOR
        || ObjectUtils.anyNull(form.getAccrualFrequency(), form.getCalculationIborIndex())) {
      return true;
    }
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("accrualFrequency")
        .addConstraintViolation();
    return Steps.begin(parseFrequency(form.getAccrualFrequency()))
        .then(() -> parseIndexTenor(form.getCalculationIborIndex()))
        .yield(Objects::equals)
        .orElse(true);
  }

  private Optional<Frequency> parseFrequency(String frequencyStr) {
    return Checked.now(() -> Frequency.parse(frequencyStr)).toOptional();
  }

  private Optional<Frequency> parseIndexTenor(String iborIndex) {
    return Checked.now(() -> IborIndex.of(iborIndex))
        .map(IborIndexAccrualFrequencies::indexAccrualFrequency)
        .toOptional();
  }
}
