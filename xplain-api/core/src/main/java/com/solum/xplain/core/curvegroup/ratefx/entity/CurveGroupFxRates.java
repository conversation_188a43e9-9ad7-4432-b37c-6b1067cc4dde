package com.solum.xplain.core.curvegroup.ratefx.entity;

import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static java.util.Collections.emptyList;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupNodesEntry;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@FieldNameConstants
@Document(collection = CurveGroupFxRates.CURVE_GROUP_FX_RATES_COLLECTION)
public class CurveGroupFxRates extends VersionedEntity implements CurveGroupNodesEntry {
  public static final String CURVE_GROUP_FX_RATES_COLLECTION = "curveGroupFxRates";
  private Set<CurveGroupFxRatesNode> nodes = new HashSet<>();

  public static CurveGroupFxRates newOf(String groupId) {
    CurveGroupFxRates c = new CurveGroupFxRates();
    c.setEntityId(groupId);
    c.setRecordDate(LocalDateTime.now());
    c.setState(State.ACTIVE);
    return c;
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return Stream.ofNullable(nodes)
        .flatMap(Collection::stream)
        .flatMap(n -> n.allInstruments().stream())
        .toList();
  }

  @Override
  public boolean valueEquals(Object object) {
    CurveGroupFxRates entity = (CurveGroupFxRates) object;
    return super.valueEquals(entity) && nullSafeIsEqualCollection(this.nodes, entity.nodes);
  }

  public List<InstrumentDefinition> instrumentsOfCurrencyPairs(Set<CurrencyPair> currencyPairs) {
    if (nodes == null) {
      return emptyList();
    }
    return nodes.stream()
        .filter(
            fxRate -> currencyPairs.contains(CurrencyPair.parse(fxRate.getKey()).toConventional()))
        .flatMap(fxRate -> fxRate.allInstruments().stream())
        .collect(Collectors.toList());
  }
}
