package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_INDEX_CLASSIFIER_NAME;
import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX;
import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.builder.ResolvableCreditIndexDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import java.util.Optional;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;

@Data
@NoArgsConstructor
public class CreditIndexTradeForm extends CommonCreditTradeForm implements ParsableToTradeValue {

  private static final Logger LOG = getLogger(CreditIndexTradeForm.class);

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = CREDIT_INDEX_CLASSIFIER_NAME)
  private String entityLongName;

  private Integer creditIndexVersion;

  private Integer creditIndexSeries;

  public CreditIndexTradeForm(VersionedTradeEntity item) {
    this.withTradeInfo(item.getTradeDetails());
    setPosition(item.getTradeDetails().getPositionType().name());
    item.getTradeDetails()
        .tradePositionLeg()
        .ifPresent(
            leg -> {
              setDayCount(leg.getDayCount());
              setNotionalValue(leg.getNotional());
              setCurrency(leg.getCurrency());
              setFixedRate(leg.getInitialValue());
              setFrequency(leg.getPaymentFrequency());
            });
    var creditDetails = item.getTradeDetails().getCreditTradeDetails();
    setStartDate(item.getTradeDetails().getStartDate());
    setEndDate(item.getTradeDetails().getEndDate());
    setEntityLongName(creditDetails.getEntityLongName());
    setCreditIndexVersion(creditDetails.getCreditIndexVersion());
    setCreditIndexSeries(creditDetails.getCreditIndexSeries());
    setReference(creditDetails.getReference());
    setSector(creditDetails.getSector().name());
    Optional.ofNullable(creditDetails.getDocClause()).map(Enum::name).ifPresent(this::setDocClause);
    Optional.ofNullable(creditDetails.getUpfront())
        .map(
            value ->
                PaymentDateForm.of(
                    creditDetails.getUpfrontDate(), value, creditDetails.getUpfrontConvention()))
        .ifPresent(this::setUpfrontFee);
  }

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return toTrade(toTradeInfo()).map(details -> defaultTradeValue(CREDIT_INDEX, details));
  }

  protected Either<ErrorItem, TradeDetails> toTrade(TradeInfoDetails tradeInfo) {
    try {
      var details = builder().build().toTradeDetails(tradeInfo);
      return Either.right(details);
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }

  protected ResolvableCreditIndexDetails.ResolvableCreditIndexDetailsBuilder builder() {
    return ResolvableCreditIndexDetails.builder()
        .entityLongName(entityLongName)
        .creditIndexSeries(creditIndexSeries)
        .creditIndexVersion(creditIndexVersion)
        .commonCreditTradeDetails(commonDetails());
  }
}
