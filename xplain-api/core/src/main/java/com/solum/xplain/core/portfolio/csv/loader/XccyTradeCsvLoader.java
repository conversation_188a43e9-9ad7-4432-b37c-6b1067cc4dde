package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.portfolio.CoreProductType.XCCY;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvLoader;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class XccyTradeCsvLoader implements ProductCsvLoader {

  private final FullSwapTradeCsvLoader commonLoader;

  @Override
  public List<ProductType> productTypes() {
    return List.of(XCCY);
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    return commonLoader.parse(row, XCCY, refSecTrade).map(ResolvableTradeDetails.class::cast);
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    return CsvLoaderUtils.parseTradeCurrency(row)
        .orElseThrow(
            () -> new IllegalArgumentException("Trade currency is mandatory for XCCY trades"));
  }
}
