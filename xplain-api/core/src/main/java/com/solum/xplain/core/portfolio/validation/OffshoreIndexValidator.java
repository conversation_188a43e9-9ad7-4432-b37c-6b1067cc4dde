package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.index.OffshoreIndices;
import io.atlassian.fugue.Checked;
import java.util.Optional;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OffshoreIndexValidator {
  private static final String OFFSHORE_INDEX_NOT_FOUND_ERROR =
      "Offshore index not found for index %s.";

  public static Optional<ErrorItem> validateIborOffshoreIndex(String index) {
    return Checked.now(() -> IborIndex.of(index))
        .toOptional()
        .flatMap(i -> validate(i, OffshoreIndices::lookupOffshoreIbor));
  }

  public static Optional<ErrorItem> validateOvernightOffshoreIndex(String index) {
    return Checked.now(() -> OvernightIndex.of(index))
        .toOptional()
        .flatMap(i -> validate(i, OffshoreIndices::lookupOffshoreOvernight));
  }

  private static <T extends Index> Optional<ErrorItem> validate(
      T index, Function<T, Optional<T>> offshoreFn) {
    if (offshoreFn.apply(index).isPresent()) {
      return Optional.empty();
    }
    return Optional.of(formatError(index.getName()));
  }

  private static ErrorItem formatError(String index) {
    return Error.VALIDATION_ERROR.entity(OFFSHORE_INDEX_NOT_FOUND_ERROR.formatted(index));
  }
}
