package com.solum.xplain.core.common.validation;

import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.dayCount;

import com.solum.xplain.core.classifiers.Classifier;
import java.util.Collection;
import java.util.function.Supplier;

public class DayCountsSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return dayCount().getValues().stream().map(Classifier::getId).toList();
  }
}
