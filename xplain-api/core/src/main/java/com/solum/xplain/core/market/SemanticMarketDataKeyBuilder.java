package com.solum.xplain.core.market;

import static lombok.AccessLevel.PRIVATE;

import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNodeInstrument;
import lombok.NoArgsConstructor;

/**
 * This builder is used to build the semantic market data key.
 *
 * <p>TODO(SXSD-9959): This is where the semantic market data key should be built for dated
 * instruments. This might become an actual component in the next refactor iteration, since there is
 * 2 way of building the semantic market data key based on the configuration.
 */
@NoArgsConstructor(access = PRIVATE)
public final class SemanticMarketDataKeyBuilder {

  /**
   * Builds the semantic market data key for a given CurveNodeInstrument and ClearingHouse.
   *
   * @param curveNodeInstrument the CurveNodeInstrument for which the key is built
   * @param clearingHouse the ClearingHouse for which the key is built
   */
  public static String semanticMarketDataKey(
      CurveNodeInstrument curveNodeInstrument, ClearingHouse clearingHouse) {
    return String.format(
        "%S_%S%S",
        curveNodeInstrument.getInstrument(),
        curveNodeInstrument.getConvention(),
        clearingHouse.getSuffix());
  }

  /**
   * Builds the semantic market data key for a given CurveNodeInstrument and alternate instrument.
   *
   * @param curveNodeInstrument the CurveNodeInstrument for which the key is built
   * @param alternateInstrument the instrument name for which the key is built
   */
  public static String semanticMarketDataKey(
      CurveNodeInstrument curveNodeInstrument, String alternateInstrument) {
    return String.format("%S_%S", alternateInstrument, curveNodeInstrument.getConvention());
  }
}
