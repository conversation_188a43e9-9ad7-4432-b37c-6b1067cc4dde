package com.solum.xplain.core.refdata;

import com.opengamma.strata.basics.ImmutableReferenceData;
import com.opengamma.strata.basics.ReferenceDataId;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.ImmutableHolidayCalendar;
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar;
import com.solum.xplain.core.calendar.event.CustomHolidayCalendarUpdatedEvent;
import com.solum.xplain.core.calendar.mapper.CustomHolidayCalendarMapper;
import com.solum.xplain.core.calendar.repository.CustomHolidayCalendarRepository;
import com.solum.xplain.core.common.versions.BitemporalDate;
import jakarta.annotation.PostConstruct;
import java.time.Clock;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAmount;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Implementation of reference data which can be refreshed, because its data is held externally and
 * can be reloaded. This would respond to Spring events to reload reference data from the
 * repository. It also implements {@link VersionedReferenceData} so can be used to obtain reference
 * data as at a particular timestamp. To save unnecessary repository queries, if the timestamp is
 * close (within 200ms) of the current time then the current data is returned instead.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RefreshableReferenceData implements VersionedReferenceData {
  private static final TemporalAmount FRESHNESS_LIMIT = Duration.of(200, ChronoUnit.MILLIS);
  private final CustomHolidayCalendarRepository customHolidayCalendarRepository;
  private final CustomHolidayCalendarMapper customHolidayCalendarMapper;
  private Clock clock = Clock.systemDefaultZone();
  private AtomicReference<ImmutableReferenceData> cachedCurrentData = new AtomicReference<>();

  /** For unit testing. */
  void configureClock(Clock clock) {
    this.clock = clock;
  }

  @PostConstruct
  @EventListener(classes = CustomHolidayCalendarUpdatedEvent.class)
  public void refresh() {
    log.info("Refreshing cached custom holiday calendars");
    ImmutableReferenceData referenceData = loadCalendars(BitemporalDate.newOfNow(clock));
    cachedCurrentData.set(referenceData);
  }

  private ImmutableReferenceData loadCalendars(BitemporalDate vd) {
    log.debug("Loading holiday calendars for {}", vd);
    List<CustomHolidayCalendar> allActive = customHolidayCalendarRepository.findAllActive(vd);
    Map<HolidayCalendarId, ImmutableHolidayCalendar> calendars =
        allActive.stream()
            .map(customHolidayCalendarMapper::toImmutableHolidayCalendar)
            .collect(Collectors.toMap(HolidayCalendar::getId, Function.identity()));
    if (log.isDebugEnabled()) {
      log.debug("Loaded: {}", calendars.keySet());
    }
    return ImmutableReferenceData.of(calendars);
  }

  @Override
  public ImmutableReferenceData asAt(LocalDateTime recordDate) {
    if (LocalDateTime.now(clock).minus(FRESHNESS_LIMIT).isBefore(recordDate)) {
      log.trace("Record date is current: returning current cached data.");
      return cachedCurrentData.get();
    } else {
      BitemporalDate vd = BitemporalDate.newOf(LocalDate.now(clock), recordDate);
      log.trace("Record date is historic: loading custom holiday calendars as at {}.", recordDate);
      return loadCalendars(vd);
    }
  }

  @Override
  public <T> T queryValueOrNull(ReferenceDataId<T> id) {
    return cachedCurrentData.get().queryValueOrNull(id);
  }
}
