package com.solum.xplain.core.company;

import static com.solum.xplain.core.company.mapper.IpvValuationRulesMapper.TRADE_FACT;
import static com.solum.xplain.core.company.mapper.IpvValuationRulesMapper.TRADE_RULE_PRIORITY;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.mapper.IpvDataGroupRuleMapper;
import com.solum.xplain.core.company.mapper.TradeFact;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.tradeleveloverride.TradeLevelOverride;
import com.solum.xplain.core.ipv.tradeleveloverride.TradeUniqueKey;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.CachingTradeLevelOverrideService;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;

@Slf4j
class MatchingTradeLevelOverrideDataGroupRule extends BasicRule {
  // Use a short lived static cache to avoid repeated lookups when processing batches of trades
  public static final Duration FRESHNESS_LIMIT = Duration.of(1, ChronoUnit.MINUTES);
  private static final Cache<String, IpvDataGroupVo> vdgCache =
      CacheBuilder.newBuilder().expireAfterAccess(FRESHNESS_LIMIT).build();

  private final CachingTradeLevelOverrideService tradeLevelOverrideService;
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final BitemporalDate stateDate;

  /** We can cache the current override as we synchonize on the ruleset */
  private Optional<IpvDataGroupVo> currentOverride;

  public MatchingTradeLevelOverrideDataGroupRule(
      CachingTradeLevelOverrideService tradeLevelOverrideService,
      IpvDataGroupRepository ipvDataGroupRepository,
      BitemporalDate stateDate) {
    super(
        "Trade level override VDG rule",
        "If a trade level override exists, use it",
        TRADE_RULE_PRIORITY);
    this.tradeLevelOverrideService = tradeLevelOverrideService;
    this.ipvDataGroupRepository = ipvDataGroupRepository;
    this.stateDate = stateDate;
  }

  /** This rule is active if we have a matching trade level override for the trade */
  @Override
  public boolean evaluate(Facts facts) {
    var fact = facts.<TradeFact>get(TRADE_FACT);
    currentOverride =
        tradeLevelOverrideService
            .activeTradeLevelOverride(
                stateDate,
                new TradeUniqueKey(
                    fact.getExternalCompanyId(),
                    fact.getExternalEntityId(),
                    fact.getPortfolioExternalId(),
                    fact.getExternalTradeId()))
            .filter(TradeLevelOverride::isDataGroupOverride)
            .map(this::getIpvDataGroup);
    return currentOverride.isPresent();
  }

  private IpvDataGroupVo getIpvDataGroup(TradeLevelOverride tradeLevelOverride) {
    try {
      return vdgCache.get(
          tradeLevelOverride.getValuationDataGroupName(),
          () -> loadIpvDataGroup(tradeLevelOverride.getValuationDataGroupName()));
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    } catch (CacheLoader.InvalidCacheLoadException e) {
      log.warn(
          "Failed to load IpvDataGroup from cache for deal exception {} due to: {}",
          tradeLevelOverride,
          e.getMessage());
      return null;
    }
  }

  private IpvDataGroupVo loadIpvDataGroup(String valuationDataGroupName) {
    log.debug("Loading IpvDataGroup from repository for name {}", valuationDataGroupName);
    return ipvDataGroupRepository
        .findIpvDataGroupCondensedByName(valuationDataGroupName)
        .map(v -> new IpvDataGroupVo(v.getId(), v.getName()))
        .orElse(null);
  }

  @Override
  public void execute(Facts facts) {
    facts.put(IpvDataGroupRuleMapper.IPV_DATA_GROUP_FACT, currentOverride.orElseThrow());
  }
}
