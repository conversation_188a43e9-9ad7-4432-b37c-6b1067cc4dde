package com.solum.xplain.core.curvegroup.curvebond.validation;

import static java.util.stream.Collectors.groupingBy;

import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class UniqueBondCurveNodesValidator
    implements ConstraintValidator<UniqueBondCurveNodes, BondCurveUpdateForm> {

  private static final String DUPLICATE_ERROR_TEMPLATE = "Duplicate node maturity dates";

  public boolean isValid(BondCurveUpdateForm form, ConstraintValidatorContext context) {
    if (form.getNodes() == null
        || form.getNodes().stream().anyMatch(n -> n.getMaturityDate() == null)) {
      return true;
    }
    var hasDuplicates =
        form.getNodes().stream()
            .collect(groupingBy(BondCurveNodeForm::getMaturityDate))
            .entrySet()
            .stream()
            .anyMatch(e -> e.getValue().size() > 1);

    if (hasDuplicates) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(DUPLICATE_ERROR_TEMPLATE)
          .addPropertyNode("nodes")
          .addConstraintViolation();
      return false;
    }

    return true;
  }
}
