package com.solum.xplain.core.curvegroup.volatility;

import static com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixConfiguration.configurationFromList;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveGroupVolatilityService {

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupVolatilityRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  @Transactional
  public Either<ErrorItem, EntityId> createSurface(String groupId, VolatilitySurfaceForm form) {
    return groupEither(groupId).flatMap(g -> repository.createSurface(groupId, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateSurface(
      String groupId, String surfaceId, LocalDate version, VolatilitySurfaceUpdateForm form) {
    return groupEither(groupId)
        .flatMap(g -> repository.updateSurface(groupId, surfaceId, version, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveSurface(
      String groupId, String surfaceId, LocalDate version, ArchiveEntityForm form) {
    return groupEither(groupId)
        .flatMap(g -> repository.archiveSurface(groupId, surfaceId, version, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteSurface(
      String groupId, String surfaceId, LocalDate version) {
    return groupEither(groupId).flatMap(g -> repository.deleteSurface(groupId, surfaceId, version));
  }

  public Either<ErrorItem, List<VolatilitySurfaceView>> getSurfaces(
      String groupId, BitemporalDate stateDate, boolean withArchived, Sort sort) {
    return groupEither(groupId)
        .map(
            g ->
                repository.getSurfaceViews(
                    groupId,
                    stateDate,
                    VersionedEntityFilter.of(withArchived),
                    TableFilter.emptyTableFilter(),
                    sort));
  }

  public Either<ErrorItem, VolatilitySurfaceView> getSurface(
      String groupId, String surfaceId, LocalDate stateDate) {
    return groupEither(groupId)
        .flatMap(g -> repository.getActiveSurfaceView(groupId, surfaceId, stateDate));
  }

  public Either<ErrorItem, List<VolatilitySurfaceView>> getSurfaceVersions(
      String groupId, String surfaceId) {
    return groupEither(groupId).map(g -> repository.getSurfaceVersionViews(groupId, surfaceId));
  }

  public Either<ErrorItem, DateList> getSurfaceFutureVersions(
      String groupId, VolatilitySurfaceSearch search) {
    return groupEither(groupId).map(g -> repository.getFutureVersions(groupId, search));
  }

  public Either<ErrorItem, VolatilityMatrixConfiguration> getSurfaceAtmMatrixConfiguration(
      String groupId, String surfaceId, LocalDate version) {
    return groupEither(groupId)
        .map(
            g ->
                VolatilityMatrixConfiguration.configurationFromList(
                    repository.getSurfaceNodesViews(groupId, surfaceId, version)));
  }

  public Either<ErrorItem, VolatilityMatrixValues> getSurfaceAtmMatrixValues(
      String groupId, String surfaceId, LocalDate version, CurveConfigMarketStateForm stateForm) {
    return groupEither(groupId)
        .map(
            g ->
                repository.getSurfaceNodesValuesViews(
                    groupId, surfaceId, version, volatilities(stateForm)))
        .map(VolatilityMatrixValues::configurationFromList);
  }

  public Either<ErrorItem, CapletVolatilityMatrixConfiguration> getSurfaceCapletMatrixConfiguration(
      String groupId, String surfaceId, LocalDate version) {
    return groupEither(groupId)
        .map(
            g ->
                configurationFromList(
                    repository.getSurfaceCapletNodesViews(groupId, surfaceId, version)));
  }

  public Either<ErrorItem, CapletVolatilityMatrixValues> getSurfaceCapletMatrixValues(
      String groupId, String surfaceId, LocalDate version, CurveConfigMarketStateForm stateForm) {
    return groupEither(groupId)
        .map(
            g ->
                repository.getSurfaceCapletNodesValuesViews(
                    groupId, surfaceId, version, volatilities(stateForm)))
        .map(CapletVolatilityMatrixValues::configurationFromList);
  }

  public Either<ErrorItem, List<VolatilitySurfaceSkewView>> getSurfaceSkews(
      String groupId, String surfaceId, LocalDate version) {
    return groupEither(groupId)
        .map(g -> repository.getSurfaceSkewsViews(groupId, surfaceId, version));
  }

  public Either<ErrorItem, VolatilitySurfaceSkewMatrixConfiguration>
      getSurfaceSkewMatrixConfiguration(
          String groupId, String surfaceId, LocalDate version, String surfaceSkewId) {
    return groupEither(groupId)
        .flatMap(g -> repository.getSurfaceSkewView(groupId, surfaceId, version, surfaceSkewId))
        .map(skew -> skewMatrixConfiguration(skew, surfaceId, version, groupId));
  }

  private VolatilitySurfaceSkewMatrixConfiguration skewMatrixConfiguration(
      VolatilitySurfaceSkewView skew, String surfaceId, LocalDate version, String groupId) {
    return VolatilitySurfaceSkewMatrixConfiguration.configurationFromList(
        skew.getSkewValue(),
        repository.getSurfaceSkewNodesViews(groupId, surfaceId, version, skew.getSurfaceSkewId()));
  }

  public Either<ErrorItem, VolatilitySurfaceSkewMatrixValues> getSurfaceSkewMatrixValues(
      String groupId,
      String surfaceId,
      LocalDate version,
      String surfaceSkewId,
      CurveConfigMarketStateForm stateForm) {
    return groupEither(groupId)
        .flatMap(g -> repository.getSurfaceSkewView(groupId, surfaceId, version, surfaceSkewId))
        .map(skew -> skewMatrixValues(skew, groupId, surfaceId, version, stateForm));
  }

  private VolatilitySurfaceSkewMatrixValues skewMatrixValues(
      VolatilitySurfaceSkewView skew,
      String groupId,
      String surfaceId,
      LocalDate version,
      CurveConfigMarketStateForm stateForm) {
    return VolatilitySurfaceSkewMatrixValues.configurationFromList(
        skew.getSkewValue(),
        repository.getSurfaceSkewNodesValueViews(
            groupId, surfaceId, version, skew.getSurfaceSkewId(), volatilities(stateForm)));
  }

  private Map<String, CalculationMarketValueView> volatilities(
      CurveConfigMarketStateForm stateForm) {
    return marketDataQuotesSupport.getQuotes(stateForm);
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String id) {
    return curveGroupRepository.getEither(id);
  }
}
