package com.solum.xplain.core.ipv.data.entity;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = IpvDataValue.COLLECTION_NAME)
@FieldNameConstants
public class IpvDataValue extends BaseIpvDataValue<IpvDataValueVersion> {
  public static final String COLLECTION_NAME = "ipvDataValue";

  private String provider;
}
