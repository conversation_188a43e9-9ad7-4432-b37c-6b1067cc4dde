package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_PAIRS;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.extensions.enums.CallPutType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class PortfolioItemFlatView {

  private String tradeId;
  private String portfolioId;
  private LocalDate validFrom;
  private LocalDateTime recordDate;
  private State state;
  private String comment;

  private String tradeInfoExternalTradeId;
  private ProductType tradeInfoTradeType;
  private String tradeInfoCounterparty;
  private String tradeInfoCounterpartyType;
  private LocalDate tradeInfoTradeDate;
  private String tradeInfoTradeCurrency;
  private String tradeInfoPosition;
  private String tradeInfoUnderlyingSwapType;
  private LocalDate tradeInfoExpiryDate;
  private LocalDate tradeInfoPremiumDate;
  private LocalDate tradeInfoSettlementDate;
  private String tradeInfoCsaDiscountingGroup;
  private String tradeInfoPremiumValueCurrency;
  private Double tradeInfoPremiumValueAmount;
  private CallPutType tradeInfoCallPutType;
  private String tradeInfoType;
  private Double tradeInfoStrike;
  private Double tradeInfoOtherOptionStrike;
  private Double tradeInfoOtherOptionCounterNotional;
  private Double tradeInfoUpfrontFee;
  private String tradeInfoCreditReference;
  private String tradeInfoSeniority;
  private String tradeInfoDocClause;
  private String tradeInfoSector;
  private String tradeInfoCreditEntityLongName;
  private Double tradeInfoClientMetricsPresentValue;
  private Double notional;
  private String underlying;

  private LocalDate tradeInfoStartDate;
  private LocalDate tradeInfoEndDate;

  private String payLegCurrency;
  private Double payLegNotionalValue;
  private Double payLegNearNotionalValue;
  private Double payLegRateMargin;
  private String payLegIndex;
  private String payLegFrequency;
  private String payLegDayCount;
  private String payLegExtIdentifier;

  private String receiveLegCurrency;
  private Double receiveLegNotionalValue;
  private Double receiveLegNearNotionalValue;
  private Double receiveLegRateMargin;
  private String receiveLegIndex;
  private String receiveLegFrequency;
  private String receiveLegDayCount;
  private String receiveLegExtIdentifier;

  private Double tradeLoanFixedRate;
  private Double tradeLoanCreditSpread;

  private String isPotentialHedge;

  private String currencies;

  private LocalDateTime modifiedAt;
  private String modifiedBy;
  private String description;

  public String currencyPair() {
    if (isEmpty(payLegCurrency)
        || isEmpty(receiveLegCurrency)
        || payLegCurrency.equals(receiveLegCurrency)) {
      return null;
    }
    return FX_SWAP_PAIRS.stream()
        .filter(
            cp ->
                (cp.getBase().getCode().equals(payLegCurrency)
                        && cp.getCounter().getCode().equals(receiveLegCurrency))
                    || (cp.getBase().getCode().equals(receiveLegCurrency)
                        && cp.getCounter().getCode().equals(payLegCurrency)))
        .findAny()
        .map(CurrencyPair::toString)
        .orElse(null);
  }
}
