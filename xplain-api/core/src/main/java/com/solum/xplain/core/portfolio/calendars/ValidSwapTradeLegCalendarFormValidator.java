package com.solum.xplain.core.portfolio.calendars;

import static org.apache.commons.lang3.StringUtils.isEmpty;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidSwapTradeLegCalendarFormValidator
    implements ConstraintValidator<ValidSwapTradeLegCalendarForm, SwapTradeLegCalendarForm> {

  @Override
  public boolean isValid(SwapTradeLegCalendarForm form, ConstraintValidatorContext context) {
    return !isEmpty(form.getIndex()) || !isEmpty(form.getCurrency());
  }
}
