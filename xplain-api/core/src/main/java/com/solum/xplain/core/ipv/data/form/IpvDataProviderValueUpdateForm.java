package com.solum.xplain.core.ipv.data.form;

import com.solum.xplain.core.common.value.NewMinorVersionForm;
import com.solum.xplain.core.ipv.data.validation.NavProviderOnlyValueSupported;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
@NavProviderOnlyValueSupported
public class IpvDataProviderValueUpdateForm {

  @NotNull private BigDecimal value;

  @Nullable private BigDecimal delta;
  @Nullable private BigDecimal vega;
  @Nullable private BigDecimal gamma;
  @Nullable private BigDecimal theta;
  @Nullable private BigDecimal rho;

  @Nullable private BigDecimal parRate;
  @Nullable private BigDecimal spotRate;
  @Nullable private BigDecimal impliedVol;
  @Nullable private BigDecimal atmImpliedVol;
  @Nullable private BigDecimal realisedVol;
  @Nullable private BigDecimal fairVol;

  @Nullable private String currency;

  @NotNull private NewMinorVersionForm versionForm;
}
