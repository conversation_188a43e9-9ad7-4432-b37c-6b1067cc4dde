package com.solum.xplain.core.ccyexposure;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CCY_EXPOSURE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CCY_EXPOSURE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureUpdateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import com.solum.xplain.core.ccyexposure.value.CombinedCcyExposureView;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.DateList;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@AllArgsConstructor
@RestController
@RequestMapping("/ccy-exposure")
public class CcyExposureController {

  private final CcyExposureControllerService service;

  @Operation(summary = "Create new Ccy Exposure")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> create(@Valid @RequestBody CcyExposureCreateForm form) {
    return eitherErrorItemResponse(service.create(form));
  }

  @Operation(summary = "Update Ccy Exposure")
  @CommonErrors
  @PutMapping("/{ccyExposureId}")
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> create(
      @PathVariable("ccyExposureId") String ccyExposureId,
      @Valid @RequestBody CcyExposureUpdateForm form) {
    return eitherErrorItemResponse(service.update(ccyExposureId, form));
  }

  @Operation(summary = "Archive Ccy Exposure")
  @CommonErrors
  @PutMapping("/{ccyExposureId}/archive")
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<EntityId> archive(@PathVariable("ccyExposureId") String ccyExposureId) {
    return eitherErrorItemResponse(service.archive(ccyExposureId));
  }

  @Operation(summary = "Get Ccy Exposures")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<List<CcyExposureView>> getCcyExposures(
      @RequestParam(required = false) boolean withArchived) {
    return eitherErrorItemResponse(right(service.getCcyExposures(withArchived)));
  }

  @Operation(summary = "Get Ccy Exposures Combined View")
  @CommonErrors
  @GetMapping("/{ccyExposureId}/combined")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public CombinedCcyExposureView getCcyExposuresWithCashflows(
      @PathVariable("ccyExposureId") String ccyExposureId, @RequestParam LocalDate stateDate) {
    return service.getCcyExposureCombinedViewById(ccyExposureId, stateDate);
  }

  @Operation(summary = "Get Ccy Exposure by ID")
  @CommonErrors
  @GetMapping("/{ccyExposureId}")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<CcyExposureView> getCcyExposure(
      @PathVariable("ccyExposureId") String ccyExposureId) {
    return eitherErrorItemResponse(service.getCcyExposureById(ccyExposureId));
  }

  @Operation(summary = "Gets Ccy Exposure future versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<DateList> getCcyExposureFutureVersions(
      @RequestParam String ccyExposureId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(right(service.getFutureVersions(ccyExposureId, stateDate)));
  }

  @CommonErrors
  @Operation(summary = "Export ccy exposure list as csv")
  @GetMapping("/exposure-csv")
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE)
  public ResponseEntity<ByteArrayResource> getCcyExposureListCsv(@NotNull LocalDate stateDate) {
    return eitherErrorItemFileResponse(service.getCcyExposureListCsv(stateDate));
  }

  @Operation(summary = "Upload ccy exposure CSV file")
  @PostMapping(value = "/upload-exposure", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CCY_EXPOSURE)
  public ResponseEntity<List<EntityId>> uploadCcyExposures(
      @Valid ImportOptions importOptions, @RequestPart MultipartFile file) throws IOException {
    return eitherErrorItemsResponse(service.uploadCcyExposures(importOptions, file.getBytes()));
  }
}
