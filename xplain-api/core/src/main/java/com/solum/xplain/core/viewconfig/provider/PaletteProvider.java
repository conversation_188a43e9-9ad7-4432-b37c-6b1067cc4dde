package com.solum.xplain.core.viewconfig.provider;

import com.solum.xplain.core.viewconfig.value.PaletteView;
import java.util.Collection;

/**
 * Can be implemented in other modules to allow them to provide a definition of fields in their view
 * types.
 *
 * <p>An ideal implementation of this would be to introspect view types and use the existing
 * annotations (and possible a few new ones) to create field definitions.
 */
@FunctionalInterface
public interface PaletteProvider {
  Collection<PaletteView<?>> providePalettes();
}
