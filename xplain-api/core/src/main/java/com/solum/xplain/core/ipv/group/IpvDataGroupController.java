package com.solum.xplain.core.ipv.group;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VALUATION_DATA_GROUP;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_DATA_GROUP;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static org.springframework.data.domain.Sort.Direction.ASC;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.ipv.group.filter.IpvDataGroupFilter;
import com.solum.xplain.core.ipv.group.form.IpvDataGroupForm;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedView;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCountedView;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ipv-data-groups")
public class IpvDataGroupController {

  private final IpvDataGroupService groupService;

  public IpvDataGroupController(IpvDataGroupService groupService) {
    this.groupService = groupService;
  }

  @Operation(summary = "Get all ipv data groups list")
  @ScrolledFiltered
  @CommonErrors
  @GetMapping("/all")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA_GROUP)
  public List<IpvDataGroupCondensedView> list(
      Authentication auth, @RequestParam(required = false) String companyId) {
    return groupService.list(auth, companyId);
  }

  @Operation(summary = "Get ipv data group")
  @GetMapping("/{groupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA_GROUP)
  public ResponseEntity<IpvDataGroupView> get(
      Authentication authentication,
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate valuationDate) {
    return eitherErrorItemResponse(
        groupService.get(authentication, groupId, BitemporalDate.newOf(valuationDate)));
  }

  @Operation(summary = "Create new ipv group")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA_GROUP)
  public ResponseEntity<EntityId> newIpvDataGroup(
      @Valid @RequestBody IpvDataGroupForm newForm, Authentication authentication) {
    return eitherErrorItemResponse(groupService.create(authentication, newForm));
  }

  @Operation(summary = "Archive ipv data group")
  @PutMapping("/{groupId}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA_GROUP)
  public ResponseEntity<EntityId> archive(
      @PathVariable("groupId") String groupId, Authentication authentication) {
    return eitherErrorItemResponse(groupService.archiveIpvDataGroup(authentication, groupId));
  }

  @Operation(summary = "Update ipv data group")
  @PutMapping("/{groupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA_GROUP)
  public ResponseEntity<EntityId> update(
      @PathVariable("groupId") String groupId,
      @Valid @RequestBody IpvDataGroupForm updateForm,
      Authentication authentication) {
    return eitherErrorItemResponse(
        groupService.updateIpvDataGroup(authentication, groupId, updateForm));
  }

  @Operation(summary = "Get ipv data groups list")
  @ScrolledFiltered
  @CommonErrors
  @Sorted
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA_GROUP)
  public ScrollableEntry<IpvDataGroupCountedView> getAll(
      @SortDefault(sort = IpvDataGroupView.Fields.name, direction = ASC)
          ScrollRequest scrollRequest,
      Authentication auth,
      IpvDataGroupFilter filter,
      TableFilter tableFilter,
      @RequestParam LocalDate stateDate) {
    return groupService.getAll(
        scrollRequest, auth, filter, tableFilter, BitemporalDate.newOf(stateDate));
  }
}
