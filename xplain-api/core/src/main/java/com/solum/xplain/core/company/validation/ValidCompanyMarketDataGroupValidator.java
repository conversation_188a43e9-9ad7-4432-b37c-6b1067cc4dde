package com.solum.xplain.core.company.validation;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ValidCompanyMarketDataGroupValidator
    implements ConstraintValidator<ValidCompanyMarketDataGroup, String> {
  private final MarketDataGroupRepository marketDataGroupRepository;
  private final RequestPathVariablesSupport support;

  public ValidCompanyMarketDataGroupValidator(
      MarketDataGroupRepository marketDataGroupRepository, RequestPathVariablesSupport support) {
    this.marketDataGroupRepository = marketDataGroupRepository;
    this.support = support;
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {

    String companyId = support.getPathVariable("companyId");
    if (StringUtils.isNotEmpty(value) && StringUtils.isNotEmpty(companyId)) {
      return marketDataGroupRepository
          .validEntity(value)
          .filter(
              v ->
                  v.getAllowAllCompanies()
                      || v.getCompanies().stream()
                          .map(EntityReference::getEntityId)
                          .toList()
                          .contains(companyId))
          .toOptional()
          .isPresent();
    }
    return true;
  }
}
