package com.solum.xplain.core.ipv.data.value;

import com.solum.xplain.core.common.value.HasEntityId;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableView;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@ConfigurableView
@Data
@FieldNameConstants
public class IpvDataNavValueView implements HasEntityId {

  @ConfigurableViewIgnore(comment = "Internal ID not exposed to user")
  private String id;

  @ConfigurableViewQuery(sortable = true)
  private String key; // VDK

  private LocalDate date;

  @ConfigurableViewQuery(sortable = true)
  private BigDecimal value;

  @ConfigurableViewQuery(sortable = true)
  private String currency;

  @ConfigurableViewQuery(sortable = true)
  private Boolean resolved;

  @ConfigurableViewIgnore(comment = "Internal use only")
  private Boolean reportingCurrencyMismatch;
}
