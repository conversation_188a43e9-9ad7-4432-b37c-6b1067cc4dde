package com.solum.xplain.core.market.service;

import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.market.MarketDataKeyEntity;
import com.solum.xplain.core.market.MarketDataKeyValue;
import com.solum.xplain.core.market.MarketDataProviderTicker;
import com.solum.xplain.core.market.value.MarketDataKeyProviderCsvForm;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;

@Builder
public class MarketDataKeyProvidersImportItems {

  private final List<ImportEntry> importEntries;
  private final List<EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity>> marketDataKeys;

  public List<ImportEntry> getImportEntries() {
    return importEntries;
  }

  public List<EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity>> getMarketDataKeys() {
    return marketDataKeys;
  }

  @AllArgsConstructor(staticName = "of")
  static class ImportEntry {
    private final EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity> marketDataKey;
    private final ImportItems<MarketDataKeyProviderCsvForm, String, MarketDataProviderTicker>
        importItems;

    public EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity> getMarketDataKey() {
      return marketDataKey;
    }

    public MarketDataKeyValue getMarketDataKeyDataValue() {
      return marketDataKey.getVersion().getValue();
    }

    public ImportItems<MarketDataKeyProviderCsvForm, String, MarketDataProviderTicker>
        getImportItems() {
      return importItems;
    }
  }
}
