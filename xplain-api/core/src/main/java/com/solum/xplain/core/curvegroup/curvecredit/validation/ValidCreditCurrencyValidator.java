package com.solum.xplain.core.curvegroup.curvecredit.validation;

import static com.solum.xplain.core.classifiers.PermissibleConventions.CREDIT_CURRENCIES;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class ValidCreditCurrencyValidator
    implements ConstraintValidator<ValidCreditCurrency, String> {

  public static boolean isValid(String currency) {
    return CREDIT_CURRENCIES.contains(currency);
  }

  public boolean isValid(String currency, ConstraintValidatorContext context) {
    return isEmpty(currency) || isValid(currency);
  }
}
