package com.solum.xplain.core.company.validation;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideForm;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

public class ValidCompanyEntityPortfolioTradeValidator
    implements ConstraintValidator<ValidCompanyEntityPortfolioTrade, TradeLevelOverrideForm> {

  private final PortfolioItemRepository portfolioItemRepository;

  public ValidCompanyEntityPortfolioTradeValidator(
      PortfolioItemRepository portfolioItemRepository) {
    this.portfolioItemRepository = portfolioItemRepository;
  }

  @Override
  public boolean isValid(TradeLevelOverrideForm value, ConstraintValidatorContext context) {
    var externalCompanyId = value.getExternalCompanyId();
    var externalEntityId = value.getExternalEntityId();
    var externalPortfolioId = value.getExternalPortfolioId();
    var externalTradeId = value.getExternalPortfolioItemId();

    if (ObjectUtils.anyNull(
        externalCompanyId, externalEntityId, externalPortfolioId, externalTradeId)) {
      // to allow validation to be handled by @NotEmpty validators in TradeLevelOverrideForm
      return true;
    }

    var stateDate = value.getVersionForm().getStateDate();

    return portfolioItemRepository.matchingActiveAndCompanyTreeExternalIds(
        BitemporalDate.newOf(stateDate),
        externalCompanyId,
        externalEntityId,
        externalPortfolioId,
        externalTradeId);
  }
}
