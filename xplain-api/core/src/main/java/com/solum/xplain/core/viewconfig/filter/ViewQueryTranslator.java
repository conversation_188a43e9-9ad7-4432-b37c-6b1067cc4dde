package com.solum.xplain.core.viewconfig.filter;

import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.shared.utils.filter.TableFilter;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Sort;

/**
 * Translate {@link TableFilter}, {@link Sort}, and {@link ScrollRequest} where property paths need
 * to be mapped between layers.
 */
@NullMarked
public interface ViewQueryTranslator {
  /** Default implementation which passes through the parameters unchanged. */
  ViewQueryTranslator NOOP_TRANSLATOR =
      new ViewQueryTranslator() {
        @Override
        public TableFilter translate(TableFilter filter) {
          return filter;
        }

        @Override
        public Sort translate(Sort sort) {
          return sort;
        }

        @Override
        public ScrollRequest translate(ScrollRequest scrollRequest) {
          return scrollRequest;
        }
      };

  TableFilter translate(TableFilter filter);

  Sort translate(Sort sort);

  default ScrollRequest translate(ScrollRequest scrollRequest) {
    return ScrollRequest.of(
        scrollRequest.getStartRow(), scrollRequest.getEndRow(), translate(scrollRequest.getSort()));
  }
}
