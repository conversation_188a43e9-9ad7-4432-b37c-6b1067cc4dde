package com.solum.xplain.core.curvemarket.node;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNode;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.curve.node.ZeroMonthCdsNodeTemplate;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Stream;

public class NonExpiredCreditNodeFilter {

  private static final String EXPIRED_CDS_NODE_WARNING_MSG =
      "Market tenor node with key %s was dropped since it has expired";

  public static ValidNodesFilter ofNonExpiredZeroMonthCdsNodes(
      LocalDate valuationDate,
      ReferenceData referenceData,
      CreditCurve curve,
      Consumer<List<ErrorItem>> warningsConsumer) {

    return new ValidNodesFilter() {
      @Override
      public <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> wrappedNodes) {
        var eithers =
            Stream.ofNullable(wrappedNodes)
                .flatMap(Collection::stream)
                .map(v -> validateNode(v, curve, valuationDate, referenceData))
                .toList();
        warningsConsumer.accept(ImmutableList.copyOf(Eithers.filterLeft(eithers).iterator()));

        return ImmutableList.copyOf(Eithers.filterRight(eithers).iterator());
      }
    };
  }

  private static <T> Either<ErrorItem, T> validateNode(
      NodeInstrumentWrapper<T> wrapper,
      CreditCurve curve,
      LocalDate valuationDate,
      ReferenceData referenceData) {

    var node = wrapper.getNode();
    if (wrapper.getNode() instanceof CreditCurveCdsNode creditCurveCdsNode) {
      var cdsNodeEndDate = creditCurveCdsNode.date(curve, valuationDate, referenceData);
      // if 0M cds node and end date <= valuation date, it's expired so drop it
      if (creditCurveCdsNode.getTenor().equals(ZeroMonthCdsNodeTemplate.label())) {
        if (cdsNodeEndDate != null && !cdsNodeEndDate.isAfter(valuationDate)) {
          return Either.left(
              Error.CALIBRATION_WARNING.entity(
                  String.format(EXPIRED_CDS_NODE_WARNING_MSG, wrapper.getDefinition().getKey())));
        }
      }
    }
    return Either.right(node);
  }
}
