package com.solum.xplain.core.classifiers;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;

/** Created by da<PERSON><PERSON> on 20/06/17. */
@Data
@RequiredArgsConstructor
public class Classifier {

  private final String id;
  private final String name;
  private final List<Classifier> values;
  @JsonIgnore @Nullable private final Class<?> sourceType;

  public Classifier(String id, String name, List<Classifier> values) {
    this(id, name, values, null);
  }

  public Classifier(String id, Classifier value) {
    this(id, null, List.of(value));
  }

  public Classifier(String id, List<Classifier> values) {
    this(id, null, values);
  }

  public Classifier(String id) {
    this(id, null, null);
  }

  public Classifier(String id, String name) {
    this(id, name, null);
  }
}
