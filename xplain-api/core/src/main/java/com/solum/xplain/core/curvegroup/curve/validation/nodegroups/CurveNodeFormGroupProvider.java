package com.solum.xplain.core.curvegroup.curve.validation.nodegroups;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.solum.xplain.core.classifiers.CurveNodeTypes;
import com.solum.xplain.core.curvegroup.curve.validation.CurveNodeValidationConstants;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class CurveNodeFormGroupProvider implements DefaultGroupSequenceProvider<CurveNodeForm> {

  @Override
  public List<Class<?>> getValidationGroups(CurveNodeForm form) {
    Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CurveNodeForm.class);
    if (form != null && !StringUtils.isEmpty(form.getType())) {
      switch (form.getType()) {
        case CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE ->
            builder.add(CurveNodeFormIborFixingDepositGroup.class);
        case CurveNodeTypes.FRA_NODE -> builder.add(CurveNodeFormFraGroup.class);
        case CurveNodeTypes.IMM_FRA_NODE -> builder.add(CurveNodeFormImmFraGroup.class);
        case CurveNodeTypes.IBOR_FUTURE_NODE -> builder.add(CurveNodeFormIborFutureGroup.class);
        case CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE -> {
          if (CurveNodeValidationConstants.isBrlConvention(form.getConvention())) {
            builder.add(CurveNodeFormBrlFixedOvernightSwapGroup.class);
          } else {
            builder.add(CurveNodeFormFixedOvernightSwapGroup.class);
          }
        }
        case CurveNodeTypes.FIXED_IBOR_SWAP_NODE ->
            builder.add(CurveNodeFormFixedIborSwapGroup.class);
        case CurveNodeTypes.IBOR_IBOR_SWAP_NODE ->
            builder.add(CurveNodeFormIborIborSwapGroup.class);
        case CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE ->
            builder.add(CurveNodeFormOvernightIborBasisSwapGroup.class);
        case CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE ->
            builder.add(CurveNodeFormXCcyIborIborSwapGroup.class);
        case CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE ->
            builder.add(CurveNodeFormXCcyOOSwapGroup.class);
        case CurveNodeTypes.XCCY_FIXED_OVERNIGHT_SWAP_NODE ->
            builder.add(CurveNodeFormXCcyFixedOvernightSwapGroup.class);
        case CurveNodeTypes.FX_SWAP_NODE -> builder.add(CurveNodeFormFxSwapGroup.class);
        case CurveNodeTypes.FIXED_INFLATION_SWAP_NODE ->
            builder.add(CurveNodeFormFixedInflationSwapGroup.class);
        case CurveNodeTypes.TERM_DEPOSIT_NODE -> builder.add(CurveNodeFormTermDepositGroup.class);
        default -> {}
      }
    }
    return builder.build();
  }
}
