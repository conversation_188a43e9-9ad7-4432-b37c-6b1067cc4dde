package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.OnboardingDetails;
import com.solum.xplain.core.portfolio.value.item.TradeDetailsView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableView;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;

@Data
@FieldNameConstants
@ConfigurableView
public class PortfolioItemWithKeyView {
  @ConfigurableViewIgnore private String key;

  @ConfigurableViewIgnore private String entityId;

  @ConfigurableViewQuery(sortable = true)
  private String externalTradeId;

  @ConfigurableViewIgnore private LocalDate version;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate validFrom;

  @ConfigurableViewQuery(sortable = true)
  private String comment;

  @ConfigurableViewIgnore private LocalDateTime recordFrom;

  @ConfigurableViewQuery(sortable = true)
  private State state;

  @ConfigurableViewQuery(sortable = true)
  private String modifiedBy;

  @ConfigurableViewQuery(sortable = true)
  private LocalDateTime modifiedAt;

  @ConfigurableViewIgnore private ObjectId portfolioId;

  @ConfigurableViewQuery(sortable = true)
  private String description;

  @ConfigurableViewQuery(sortable = true)
  private ProductType productType;

  @ConfigurableViewField(prefix = "trade.")
  private TradeDetailsView tradeDetails;

  private List<ExternalIdentifier> tradeInfoExternalIdentifiers;

  private List<CustomTradeField> tradeInfoCustomFields;

  @ConfigurableViewField(prefix = "allocationDetails.")
  private AllocationTradeDetails allocationTradeDetails;

  @ConfigurableViewField(prefix = "clientMetrics.")
  private ClientMetrics clientMetrics;

  @ConfigurableViewField(prefix = "onboardingDetails.")
  private OnboardingDetails onboardingDetails;
}
