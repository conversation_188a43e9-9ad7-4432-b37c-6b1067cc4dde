package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX_TRANCHE;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.CreditIndexTrancheTradeForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CreditIndexTrancheTradeView extends CreditIndexTrancheTradeForm implements TradeView {
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public CreditIndexTrancheTradeView(VersionedTradeEntity item) {
    super(item);
    setCalendar(item.getTradeDetails().getCalendar());
    this.updateCommonView(item);
  }

  public static Either<ErrorItem, CreditIndexTrancheTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CREDIT_INDEX_TRANCHE) {
      return Either.right(new CreditIndexTrancheTradeView(item));
    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not expected " + item.getProductType()));
    }
  }
}
