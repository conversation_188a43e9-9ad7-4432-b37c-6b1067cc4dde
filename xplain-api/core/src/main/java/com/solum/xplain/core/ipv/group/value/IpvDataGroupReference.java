package com.solum.xplain.core.ipv.group.value;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.common.EntityReference;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class IpvDataGroupReference extends EntityReference {
  private PricingSlot pricingSlot;

  public static IpvDataGroupReference newOf(String entityId, String name, PricingSlot pricingSlot) {
    var reference = new IpvDataGroupReference();
    reference.setEntityId(entityId);
    reference.setName(name);
    reference.setPricingSlot(pricingSlot);
    return reference;
  }
}
