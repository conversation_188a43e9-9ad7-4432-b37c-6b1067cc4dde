package com.solum.xplain.core.ipv.data.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.ipv.data.IpvDataRepository;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm;
import com.solum.xplain.core.ipv.nav.repository.CompanyLegalEntityNavRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component
public class UniqueIpvDataProviderValueValidator
    implements ConstraintValidator<UniqueIpvDataProviderValue, IpvDataProviderValueForm> {

  private final RequestPathVariablesSupport requestPathSupport;
  private final IpvDataRepository repository;
  private final CompanyLegalEntityNavRepository companyLegalEntityNavRepository;

  public UniqueIpvDataProviderValueValidator(
      RequestPathVariablesSupport requestPathSupport,
      IpvDataRepository repository,
      CompanyLegalEntityNavRepository companyLegalEntityNavRepository) {
    this.requestPathSupport = requestPathSupport;
    this.repository = repository;
    this.companyLegalEntityNavRepository = companyLegalEntityNavRepository;
  }

  @Override
  public boolean isValid(IpvDataProviderValueForm form, ConstraintValidatorContext context) {
    String navLevel = repository.getGlobalNavLevelSettings(BitemporalDate.newOf(form.getDate()));
    boolean isEntityLevelNav = "ENTITY_LEVEL".equals(navLevel) && "NAV".equals(form.getProvider());

    var groupId = requestPathSupport.getPathVariable("groupId");
    var date = form.getDate();
    var key = form.getKey();

    if (isEntityLevelNav) {
      return validateEntityLevelNav(groupId, date, key);
    } else {
      var provider = form.getProvider();
      return validateOtherNav(groupId, date, key, provider);
    }
  }

  private boolean validateEntityLevelNav(String groupId, LocalDate date, String key) {
    if (ObjectUtils.allNotNull(groupId, date, key)) {
      return !companyLegalEntityNavRepository.valueExists(groupId, BitemporalDate.newOf(date), key);
    }
    return true;
  }

  private boolean validateOtherNav(String groupId, LocalDate date, String key, String provider) {
    if (ObjectUtils.allNotNull(groupId, date, key, provider)) {
      return !repository.valueExists(groupId, BitemporalDate.newOf(date), key, provider);
    }
    return true;
  }
}
