package com.solum.xplain.core.common.csv;

import static com.solum.xplain.core.error.Error.DUPLICATE_ENTRY;
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS;
import static com.solum.xplain.core.error.Error.IMPORT_ERROR;
import static com.solum.xplain.core.error.Error.MISSING_ENTRY;
import static com.solum.xplain.core.error.Error.MISSING_ENTRY_FUTURE_VERSION_EXISTS;
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE;
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;

import com.solum.xplain.core.error.ErrorItem;
import java.time.LocalDate;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ImportErrorUtils {

  public static ErrorItem missingItem() {
    return new ErrorItem(MISSING_ENTRY, "At least one entry is missing");
  }

  public static ErrorItem missingItem(String identifier) {
    return new ErrorItem(MISSING_ENTRY, String.format("%s is missing", identifier));
  }

  public static ErrorItem missingItem(LocalDate date) {
    return new ErrorItem(
        MISSING_ENTRY, String.format("At least one entry for %s is missing", date));
  }

  public static ErrorItem duplicateItem() {
    return new ErrorItem(DUPLICATE_ENTRY, "At least one entry already exists");
  }

  public static ErrorItem duplicateItem(String identifier) {
    return new ErrorItem(DUPLICATE_ENTRY, String.format("%s already exists", identifier));
  }

  public static ErrorItem duplicateItem(LocalDate date) {
    return new ErrorItem(
        DUPLICATE_ENTRY, String.format("At least one entry for %s already exists", date));
  }

  public static ErrorItem newVersionViable() {
    return new ErrorItem(NEW_VERSION_VIABLE, "New version is viable for at least one entry");
  }

  public static ErrorItem newVersionViable(String identifier) {
    return new ErrorItem(
        NEW_VERSION_VIABLE, String.format("New version is viable for %s", identifier));
  }

  public static ErrorItem futureVersionExists() {
    return new ErrorItem(FUTURE_VERSION_EXISTS, "At least one entry has future versions(s)");
  }

  public static ErrorItem futureVersionExists(String identifier) {
    return new ErrorItem(
        FUTURE_VERSION_EXISTS, String.format("%s has future version(s)", identifier));
  }

  public static ErrorItem missingEntryFutureVersionExists() {
    return new ErrorItem(
        MISSING_ENTRY_FUTURE_VERSION_EXISTS, "At least one entry has future versions(s)");
  }

  public static ErrorItem missingEntryFutureVersionExists(String identifier) {
    return new ErrorItem(
        MISSING_ENTRY_FUTURE_VERSION_EXISTS, String.format("%s has future version(s)", identifier));
  }

  public static ErrorItem stateChanged(String identifier) {
    return new ErrorItem(OPERATION_NOT_ALLOWED, String.format("%s have been modified", identifier));
  }

  public static ErrorItem unexpectedDuplicateAction(DuplicateAction action) {
    return new ErrorItem(IMPORT_ERROR, String.format("Unexpected duplicate action: %s", action));
  }

  public static List<ErrorItem> orErrors(boolean errCond, ErrorItem err) {
    return errCond ? List.of(err) : List.of();
  }
}
