package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.AuditUserProvider;
import com.solum.xplain.core.portfolio.event.PortfolioUpdated;
import java.time.LocalDateTime;
import java.util.Collection;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class PortfolioAuditSupport {
  private final AuditUserProvider provider;
  private final MongoOperations mongoOperations;

  public PortfolioAuditSupport(AuditUserProvider provider, MongoOperations mongoOperations) {
    this.provider = provider;
    this.mongoOperations = mongoOperations;
  }

  @EventListener
  public void onPortfolioUpdate(PortfolioUpdated event) {
    chunked(event.portfolioIds().stream())
        .forEach(c -> updatePortfoliosLastModified(c, event.modifiedAt()));
  }

  private void updatePortfoliosLastModified(
      Collection<String> portfolioIds, LocalDateTime modifiedAt) {
    var user = provider.get();
    mongoOperations
        .update(Portfolio.class)
        .matching(query(where(Portfolio.Fields.id).in(portfolioIds)))
        .apply(
            Update.update(Portfolio.Fields.lastModifiedAt, modifiedAt)
                .set(Portfolio.Fields.lastModifiedBy, user))
        .first();
  }
}
