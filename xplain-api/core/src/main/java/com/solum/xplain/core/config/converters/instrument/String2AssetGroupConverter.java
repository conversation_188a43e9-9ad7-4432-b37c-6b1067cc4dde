package com.solum.xplain.core.config.converters.instrument;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetClassResolver;
import com.solum.xplain.core.instrument.AssetGroup;
import jakarta.inject.Provider;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class String2AssetGroupConverter implements Converter<String, AssetGroup> {

  private final Provider<AssetClassResolver> instrumentTypeResolver;

  @Override
  public AssetGroup convert(@NonNull String source) {
    return instrumentTypeResolver.get().assetClasses().stream()
        .map(AssetClass::getGroup)
        .filter(v -> StringUtils.equals(v.name(), source))
        .findFirst()
        .orElseThrow(() -> new IllegalArgumentException("Invalid asset group: " + source));
  }
}
