package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.portfolio.ProductTypeGroupDiscounting.FX_DSC;
import static com.solum.xplain.core.portfolio.ProductTypeGroupDiscounting.NONFX_DSC;

import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CoreProductTypeGroup implements ProductGroup {
  RATES("Rates", NONFX_DSC, true, false, false),
  CREDIT("Credit", NONFX_DSC, false, true, false),
  FX("FX", FX_DSC, false, false, true);

  private final String label;
  private final ProductTypeGroupDiscounting groupDiscounting;
  private final boolean rates;
  private final boolean credit;
  private final boolean fx;

  public List<ProductType> getProductTypes() {
    return Stream.of(CoreProductType.values())
        .filter(productType -> productType.getGroup() == this)
        .map(ProductType.class::cast)
        .toList();
  }

  @Override
  public String label() {
    return label;
  }

  public ProductTypeGroupDiscounting getGroupDiscounting() {
    return groupDiscounting;
  }
}
