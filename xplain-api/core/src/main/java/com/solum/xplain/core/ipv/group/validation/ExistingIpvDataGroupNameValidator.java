package com.solum.xplain.core.ipv.group.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ExistingIpvDataGroupNameValidator
    implements ConstraintValidator<ExistingIpvDataGroupName, String> {

  private final IpvDataGroupRepository repository;

  public ExistingIpvDataGroupNameValidator(IpvDataGroupRepository repository) {
    this.repository = repository;
  }

  @Override
  public boolean isValid(String name, ConstraintValidatorContext context) {
    if (isNotEmpty(name)) {
      return repository.existsByName(name, null);
    }
    return true;
  }
}
