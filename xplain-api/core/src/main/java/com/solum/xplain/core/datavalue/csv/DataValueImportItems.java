package com.solum.xplain.core.datavalue.csv;

import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.VersionedValue;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Builder
@ToString
@EqualsAndHashCode
public class DataValueImportItems<V extends VersionedValue, K, E extends DataValuesHolder<V>> {

  private final String importComment;
  private final Map<K, E> importItemsByKeys;
  private final Map<K, E> existingItemsByKey;

  public List<E> newItems() {
    var duplicateKeys = existingItemsByKey.keySet();
    return importItemsByKeys.entrySet().stream()
        .filter(v -> !duplicateKeys.contains(v.getKey()))
        .map(Entry::getValue)
        .toList();
  }

  public List<DataValueForUpdate<V, E>> replaceItems() {
    return existingItemsByKey.entrySet().stream()
        .filter(i -> importItemsByKeys.containsKey(i.getKey()))
        .map(e -> new DataValueForUpdate<>(e.getValue(), newValue(e.getKey())))
        .toList();
  }

  public boolean duplicateExists() {
    return existingItemsByKey.entrySet().stream()
        .anyMatch(i -> importItemsByKeys.containsKey(i.getKey()));
  }

  public List<E> spareItems() {
    return existingItemsByKey.entrySet().stream()
        .filter(v -> !importItemsByKeys.containsKey(v.getKey()))
        .map(Entry::getValue)
        .toList();
  }

  public boolean spareItemsExist() {
    return existingItemsByKey.entrySet().stream()
        .anyMatch(i -> !importItemsByKeys.containsKey(i.getKey()));
  }

  public String getImportComment() {
    return importComment;
  }

  private V newValue(K key) {
    return importItemsByKeys.get(key).latestValue();
  }
}
