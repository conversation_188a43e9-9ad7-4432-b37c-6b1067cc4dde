package com.solum.xplain.core.portfolio.calendars;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@ParameterObject
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class FxTradeCalendarForm {

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private final String domesticCcy;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private final String foreignCcy;

  public String getDomesticCcy() {
    return domesticCcy;
  }

  public String getForeignCcy() {
    return foreignCcy;
  }
}
