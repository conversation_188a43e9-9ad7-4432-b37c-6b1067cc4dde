package com.solum.xplain.core.portfolio.repository;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE_BATCH_1000;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.portfolio.PortfolioTeamFilter.emptyFilter;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static io.atlassian.fugue.Either.left;
import static java.lang.Boolean.FALSE;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.AuditUserProvider;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.CompanyLegalEntityFilter;
import com.solum.xplain.core.company.CompanyLegalEntityFilter.CompanyIdWithLegalEntityId;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference;
import com.solum.xplain.core.company.entity.CompanyReference;
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityUpdated;
import com.solum.xplain.core.company.events.CompanyUpdated;
import com.solum.xplain.core.company.mapper.CompanyReferenceMapper;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.Portfolio;
import com.solum.xplain.core.portfolio.PortfolioMapper;
import com.solum.xplain.core.portfolio.PortfolioTeamFilter;
import com.solum.xplain.core.portfolio.event.PortfolioAdded;
import com.solum.xplain.core.portfolio.event.PortfolioArchived;
import com.solum.xplain.core.portfolio.event.PortfolioDetailsUpdated;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm;
import com.solum.xplain.core.portfolio.value.ImportPortfolio;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioFilter;
import com.solum.xplain.core.portfolio.value.PortfolioUniqueKey;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.users.events.UserUpdated;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations.BulkMode;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@NullMarked
@RequiredArgsConstructor
@Repository
public class PortfolioRepository {

  private static final String PORTFOLIO_NOT_FOUND = "Portfolio not found";
  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final CompanyReferenceMapper companyReferenceMapper;
  private final CompanyLegalEntityRepository legalEntityRepository;
  private final CompanyRepository companyRepository;
  private final AuditingHandler auditingHandler;
  private final ApplicationEventPublisher publisher;
  private final PortfolioMapper portfolioMapper;
  private final AuditUserProvider auditUserProvider;

  public Either<ErrorItem, Portfolio> portfolioEither(String id) {
    var result = mongoOperations.findById(id, Portfolio.class);
    return result == null
        ? left(OBJECT_NOT_FOUND.entity(PORTFOLIO_NOT_FOUND))
        : Either.right(result);
  }

  public List<PortfolioView> activeCompanyPortfolios(String companyId, BitemporalDate stateDate) {
    List<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(where(Portfolio.Fields.createdAt).lte(stateDate.getRecordDate())),
                match(
                    where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                        .is(companyId)),
                match(where(Portfolio.Fields.archived).is(FALSE)),
                portfolioViewProjection())
            .build();

    return mongoOperations
        .aggregate(newAggregation(Portfolio.class, operations), PortfolioView.class)
        .getMappedResults();
  }

  public List<PortfolioView> activePortfolios(
      Collection<String> portfolioIds, BitemporalDate stateDate) {
    List<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(where(Portfolio.Fields.createdAt).lte(stateDate.getRecordDate())),
                match(where(Portfolio.Fields.id).in(portfolioIds)),
                match(where(Portfolio.Fields.archived).is(FALSE)),
                portfolioViewProjection())
            .build();

    return mongoOperations
        .aggregate(newAggregation(Portfolio.class, operations), PortfolioView.class)
        .getMappedResults();
  }

  public Either<ErrorItem, PortfolioView> getEither(String id) {
    return portfolio(id)
        .map(Either::<ErrorItem, PortfolioView>right)
        .orElse(left(OBJECT_NOT_FOUND.entity(PORTFOLIO_NOT_FOUND)));
  }

  public Either<ErrorItem, UserTeamEntity<PortfolioView>> getUserPortfolioView(
      XplainPrincipal user, String id) {
    return getEither(id)
        .flatMap(
            p ->
                legalEntityRepository
                    .userCompanyLegalEntityView(user, p.getCompanyId(), p.getEntityId())
                    .map(l -> UserTeamEntity.userEntity(user, p)))
        .flatMap(UserTeamEntity::allowTeamsOnly);
  }

  public List<PortfolioCondensedView> portfolioViews(
      PortfolioFilter filter, PortfolioTeamFilter portfolioTeamFilter) {
    return portfolioCondensedViews(filter, portfolioTeamFilter).toList();
  }

  public Map<String, PortfolioCondensedView> portfolioCondensedViewsByPortfolioId(
      PortfolioFilter filter, PortfolioTeamFilter portfolioTeamFilter) {
    return this.portfolioCondensedViews(filter, portfolioTeamFilter)
        .collect(toMap(PortfolioCondensedView::getId, identity()));
  }

  public List<ImportPortfolio> activeImportPortfoliosList(PortfolioFilter filter) {
    var operations = importPortfolioAggregations(filter, emptyFilter());
    return mongoOperations
        .aggregate(newAggregation(Portfolio.class, operations), ImportPortfolio.class)
        .getMappedResults();
  }

  public boolean hasChanges(Set<String> portfolioIds, LocalDateTime validationTs) {
    return mongoOperations
        .query(Portfolio.class)
        .matching(
            where(Portfolio.Fields.id)
                .in(portfolioIds)
                .and(Portfolio.Fields.lastModifiedAt)
                .gt(validationTs))
        .exists();
  }

  public int duplicatePortfoliosCount(
      Set<PortfolioUniqueKey> uniqueKeys, PortfolioTeamFilter teamFilter) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(importPortfolioAggregations(PortfolioFilter.activePortfolios(), teamFilter))
            .add(match(where(ImportPortfolio.Fields.key).in(uniqueKeys)))
            .add(count().as(COUNT_FIELD))
            .build();

    var result =
        mongoOperations
            .aggregate(newAggregation(Portfolio.class, operations), Document.class)
            .getUniqueMappedResult();
    return Optional.ofNullable(result).map(r -> r.getInteger(COUNT_FIELD)).orElse(0);
  }

  private Stream<PortfolioCondensedView> portfolioCondensedViews(
      PortfolioFilter filter, PortfolioTeamFilter portfolioTeamFilter) {
    return mongoOperations.aggregateStream(
        portfolioCondensedViewAggregation(filter, portfolioTeamFilter, unsorted()),
        PortfolioCondensedView.class);
  }

  private List<AggregationOperation> importPortfolioAggregations(
      PortfolioFilter filter, PortfolioTeamFilter teamFilter) {
    return List.of(
        match(filter.criteria()),
        match(where(Portfolio.Fields.archived).is(false)),
        match(teamFilter.criteria()),
        project()
            .and(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
            .as(propertyName(ImportPortfolio.Fields.key, PortfolioUniqueKey.Fields.companyId))
            .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
            .as(propertyName(ImportPortfolio.Fields.key, PortfolioUniqueKey.Fields.entityId))
            .and(Portfolio.Fields.externalPortfolioId)
            .as(
                propertyName(
                    ImportPortfolio.Fields.key, PortfolioUniqueKey.Fields.externalPortfolioId)));
  }

  public List<PortfolioView> portfolioViewList(
      PortfolioFilter filter, PortfolioTeamFilter teamFilter) {
    return mongoOperations
        .aggregate(portfolioAggregation(filter, teamFilter, Sort.unsorted()), PortfolioView.class)
        .getMappedResults();
  }

  public List<PortfolioCondensedView> portfolioCondensedViewList(
      PortfolioFilter filter, PortfolioTeamFilter teamFilter, Sort sorting) {
    return mongoOperations
        .aggregate(
            portfolioCondensedViewAggregation(filter, teamFilter, sorting),
            PortfolioCondensedView.class)
        .getMappedResults();
  }

  public List<PortfolioCondensedView> portfolioCondensedViewList(
      PortfolioFilter filter, PortfolioTeamFilter teamFilter) {
    return this.portfolioCondensedViewList(filter, teamFilter, Sort.unsorted());
  }

  private TypedAggregation<Portfolio> portfolioAggregation(
      PortfolioFilter filter, PortfolioTeamFilter teamFilter, Sort sorting) {
    ImmutableList.Builder<AggregationOperation> operationsBuilder =
        ImmutableList.<AggregationOperation>builder()
            .add(match(filter.criteria()), match(teamFilter.criteria()), portfolioViewProjection());

    // Required to prevent unsorted being used within the aggregation for Mongo
    if (!sorting.isEmpty()) {
      operationsBuilder.add(sort(sorting));
    }

    List<AggregationOperation> operations = operationsBuilder.build();

    return newAggregation(Portfolio.class, operations);
  }

  public ScrollableEntry<PortfolioView> portfolioList(
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      PortfolioFilter filter,
      PortfolioTeamFilter teamFilter) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(filter.criteria()),
                match(teamFilter.criteria()),
                portfolioViewProjection(),
                match(tableFilter.criteria(PortfolioView.class, conversionService)))
            .build();
    TypedAggregation<Portfolio> aggregation =
        newAggregation(
            Portfolio.class,
            ImmutableList.<AggregationOperation>builder()
                .addAll(baseOperations)
                .add(count().as(COUNT_FIELD))
                .build());
    var totalAggr = mongoOperations.aggregate(aggregation, Map.class);
    return ofNullable(totalAggr.getUniqueMappedResult())
        .map(r -> r.get(COUNT_FIELD))
        .map(Number.class::cast)
        .map(
            t ->
                ScrollableEntry.of(
                    portfolioList(scrollRequest, baseOperations), scrollRequest, t.longValue()))
        .orElse(ScrollableEntry.empty());
  }

  public Stream<PortfolioView> portfolioViewStream(
      TableFilter tableFilter, Sort sort, PortfolioFilter filter, PortfolioTeamFilter teamFilter) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(filter.criteria()),
                match(teamFilter.criteria()),
                portfolioViewProjection(),
                match(tableFilter.criteria(PortfolioView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    }

    return mongoOperations.aggregateStream(
        Aggregation.newAggregation(Portfolio.class, operations.build())
            .withOptions(ALLOW_DISK_USE_BATCH_1000),
        PortfolioView.class);
  }

  public List<PortfolioCondensedView> portfolioListForEntityId(
      String entityId, PortfolioFilter filter, EntityTeamFilter teamFilter) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();
    operations.add(
        match(
            where(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
                .is(entityId)));

    var baseAggregation = portfolioCondensedViewAggregation(filter, teamFilter, unsorted());
    operations.addAll(baseAggregation.getPipeline().getOperations());

    return mongoOperations
        .aggregate(
            newAggregation(Portfolio.class, operations.build()), PortfolioCondensedView.class)
        .getMappedResults();
  }

  /**
   * Creates an aggregation pipeline for retrieving a condensed view of portfolios.
   *
   * @param filter the filter to apply to the portfolios
   * @param teamFilter additional filtering for team visibility
   * @param sorting the sorting criteria to apply to the results
   * @return aggregation pipeline for retrieving PortfolioCondensedView
   */
  private TypedAggregation<Portfolio> portfolioCondensedViewAggregation(
      PortfolioFilter filter, EntityTeamFilter teamFilter, Sort sorting) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(filter.criteria()), match(teamFilter.criteria()), projectCondensedView());

    // Required to prevent unsorted being used within the aggregation for Mongo
    if (sorting.isSorted()) {
      operations.add(sort(sorting));
    }

    return newAggregation(Portfolio.class, operations.build());
  }

  /**
   * Aggregation stage to project the fields of a Portfolio as a PortfolioCondensedView.
   *
   * @return projection operation that maps Portfolio fields to PortfolioCondensedView fields.
   */
  private ProjectionOperation projectCondensedView() {
    return project()
        .and(Portfolio.Fields.externalPortfolioId)
        .as(PortfolioCondensedView.Fields.externalPortfolioId)
        .and(Portfolio.Fields.id)
        .as(PortfolioCondensedView.Fields.id)
        .and(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
        .as(PortfolioCondensedView.Fields.companyId)
        .and(propertyName(Portfolio.Fields.company, CompanyReference.Fields.externalCompanyId))
        .as(PortfolioCondensedView.Fields.externalCompanyId)
        .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
        .as(PortfolioCondensedView.Fields.entityId)
        .and(
            propertyName(
                Portfolio.Fields.entity, CompanyLegalEntityReference.Fields.externalEntityId))
        .as(PortfolioCondensedView.Fields.externalEntityId);
  }

  private List<PortfolioView> portfolioList(
      ScrollRequest scrollRequest, List<AggregationOperation> baseOperations) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder().addAll(baseOperations);
    operations.addAll(
        new ScrollSortOperations(scrollRequest, PortfolioCondensedView.Fields.id).build());

    TypedAggregation<Portfolio> aggregation = newAggregation(Portfolio.class, operations.build());
    return mongoOperations.aggregate(aggregation, PortfolioView.class).getMappedResults();
  }

  public Optional<PortfolioView> portfolio(String id) {
    return ofNullable(
        mongoOperations
            .aggregate(
                newAggregation(
                    Portfolio.class,
                    match(PortfolioFilter.activePortfolios().criteria()),
                    match(where(Portfolio.Fields.id).is(id)),
                    portfolioViewProjection()),
                PortfolioView.class)
            .getUniqueMappedResult());
  }

  private ProjectionOperation portfolioViewProjection() {
    return project()
        .and(Portfolio.Fields.id)
        .as(PortfolioCondensedView.Fields.id)
        .and(Portfolio.Fields.createdAt)
        .as(PortfolioView.Fields.createdAt)
        .and(propertyName(Portfolio.Fields.createdBy, AuditUser.Fields.userId))
        .as(PortfolioView.Fields.creatorId)
        .and(propertyName(Portfolio.Fields.createdBy, AuditUser.Fields.name))
        .as(PortfolioView.Fields.creatorName)
        .and(propertyName(Portfolio.Fields.lastModifiedBy, AuditUser.Fields.name))
        .as(PortfolioView.Fields.modifiedBy)
        .and(Portfolio.Fields.lastModifiedAt)
        .as(PortfolioView.Fields.updatedAt)
        .and(Portfolio.Fields.teamIds)
        .as(PortfolioView.Fields.teamIds)
        .and(Portfolio.Fields.allowAllTeams)
        .as(PortfolioView.Fields.allowAllTeams)
        .and(Portfolio.Fields.calculatedAt)
        .as(PortfolioView.Fields.calculatedAt)
        .and(Portfolio.Fields.auditLogs)
        .as(PortfolioView.Fields.auditLogs)
        .and(Portfolio.Fields.valuationDate)
        .as(PortfolioView.Fields.valuationDate)
        .and(propertyName(Portfolio.Fields.calculatedBy, AuditUser.Fields.name))
        .as(PortfolioView.Fields.calculatedBy)
        .and(Portfolio.Fields.description)
        .as(PortfolioView.Fields.description)
        .and(Portfolio.Fields.archived)
        .as(PortfolioView.Fields.archived)
        .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.name))
        .as(PortfolioView.Fields.entityName)
        .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
        .as(PortfolioCondensedView.Fields.entityId)
        .and(
            propertyName(
                Portfolio.Fields.entity, CompanyLegalEntityReference.Fields.externalEntityId))
        .as(PortfolioCondensedView.Fields.externalEntityId)
        .and(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
        .as(PortfolioCondensedView.Fields.companyId)
        .and(Portfolio.Fields.externalPortfolioId)
        .as(PortfolioCondensedView.Fields.externalPortfolioId)
        .and(Portfolio.Fields.name)
        .as(PortfolioView.Fields.name)
        .and(propertyName(Portfolio.Fields.company, EntityReference.Fields.name))
        .as(PortfolioView.Fields.companyName)
        .and(propertyName(Portfolio.Fields.company, CompanyReference.Fields.externalCompanyId))
        .as(PortfolioCondensedView.Fields.externalCompanyId);
  }

  public Either<ErrorItem, EntityId> insert(PortfolioCreateForm newForm) {
    return legalEntityRepository
        .companyLegalEntityView(newForm.getCompanyId(), newForm.getEntityId())
        .map(CompanyLegalEntityReference::of)
        .map(
            legalEntityReference -> {
              var companyReference =
                  companyReferenceMapper.toCompanyReference(newForm.getCompanyId());
              var portfolio =
                  portfolioMapper.createPortfolio(newForm, companyReference, legalEntityReference);
              mongoOperations.insert(portfolio);
              publisher.publishEvent(PortfolioAdded.newOf(portfolio.getCompany()));
              return entityId(portfolio.getId());
            });
  }

  public Map<PortfolioUniqueKey, Either<ErrorItem, EntityId>> bulkInsert(
      Stream<PortfolioCreateForm> newForms) {
    var errorPortfolios = new ArrayList<PortfolioCreateForm>();
    var validPortfolios = new ArrayList<Portfolio>();

    fromBulkInsertForm(newForms)
        .forEach(
            it -> {
              if (it.isRight()) {
                var portfolio = it.right().get();
                // bulk inserts do not set the document id back.
                portfolio.setId(ObjectId.get().toHexString());
                // Manually set audit fields since bulk operations bypass Spring Data auditing
                auditingHandler.markCreated(portfolio);
                validPortfolios.add(portfolio);
              } else {
                errorPortfolios.add(it.left().get());
              }
            });

    var updatedCompanies =
        validPortfolios.stream()
            .map(Portfolio::getCompany)
            .map(CompanyReference::getEntityId)
            .collect(toSet());
    var bulkOps = mongoOperations.bulkOps(BulkMode.UNORDERED, Portfolio.class);
    if (!validPortfolios.isEmpty()) {
      bulkOps.insert(validPortfolios).execute();
    }
    publisher.publishEvent(new PortfolioAdded(updatedCompanies));

    var errors =
        errorPortfolios.stream()
            .collect(
                toMap(
                    PortfolioUniqueKey::fromForm,
                    item ->
                        Either.<ErrorItem, EntityId>left(
                            OBJECT_NOT_FOUND.entity(
                                MessageFormatter.format(
                                        "Company Entity not found for {}/{}",
                                        item.getCompanyId(),
                                        item.getEntityId())
                                    .getMessage()))));
    var success =
        validPortfolios.stream()
            .collect(
                toMap(
                    it ->
                        new PortfolioUniqueKey(
                            it.getCompany().getEntityId(),
                            it.getEntity().getEntityId(),
                            it.getExternalPortfolioId()),
                    it -> Either.<ErrorItem, EntityId>right(entityId(it.getId()))));

    var result = new HashMap<PortfolioUniqueKey, Either<ErrorItem, EntityId>>();
    result.putAll(errors);
    result.putAll(success);

    return result;
  }

  public Set<EntityId> bulkUpdate(Map<String, ? extends PortfolioUpdateForm> edits) {
    var bulkOps = mongoOperations.bulkOps(BulkMode.UNORDERED, Portfolio.class);
    var bulkOpsEmpty = true;
    var updatedPortfolios = new HashSet<EntityId>();
    var currentUser = auditUserProvider.get();
    var modifiedAt = LocalDateTime.now();

    for (var portfolio :
        mongoOperations.find(
            query(where(Portfolio.Fields.id).in(edits.keySet())), Portfolio.class)) {
      var newEntity = portfolioMapper.updatedPortfolio(portfolio, edits.get(portfolio.getId()));
      var diff = portfolio.diff(newEntity);
      var update = fromVersionDiff(diff);
      if (diff.numberOfDiffs() > 0) {
        var newAuditLog = auditingHandler.markCreated(AuditLog.of(diff));
        update.push(Portfolio.Fields.auditLogs).each(newAuditLog);
        // Manually set audit fields since bulk operations bypass Spring Data auditing
        update
            .set(Portfolio.Fields.lastModifiedBy, currentUser)
            .set(Portfolio.Fields.lastModifiedAt, modifiedAt);
        bulkOps.updateOne(query(where(Portfolio.Fields.id).is(portfolio.getId())), update);
        bulkOpsEmpty = false;
      }
      updatedPortfolios.add(entityId(portfolio.getId()));
    }
    if (!bulkOpsEmpty) {
      bulkOps.execute();
    }
    publisher.publishEvent(new PortfolioDetailsUpdated(edits.keySet()));
    return updatedPortfolios;
  }

  private Stream<Either<PortfolioCreateForm, Portfolio>> fromBulkInsertForm(
      Stream<PortfolioCreateForm> formStream) {
    var forms = formStream.toList();
    var legalEntityFilterBuilder = CompanyLegalEntityFilter.builder();
    for (PortfolioCreateForm form : forms) {
      legalEntityFilterBuilder.companyIdWithLegalEntityId(
          new CompanyIdWithLegalEntityId(form.getCompanyId(), form.getEntityId()));
    }
    var legalEntityFilter = legalEntityFilterBuilder.archived(false).build();
    var foundLegalEntities =
        legalEntityRepository
            .companyLegalEntityView(legalEntityFilter)
            .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

    var allCompanyIds = forms.stream().map(PortfolioCreateForm::getCompanyId).collect(toSet());

    var companyReferences =
        companyRepository
            .companyReferencesStream(allCompanyIds)
            .collect(toMap(CompanyReference::getEntityId, identity()));

    return forms.stream()
        .map(
            form -> {
              if (foundLegalEntities.containsKey(
                      new CompanyIdWithLegalEntityId(form.getCompanyId(), form.getEntityId()))
                  && companyReferences.containsKey(form.getCompanyId())) {

                return Either.right(
                    portfolioMapper.createPortfolio(
                        form,
                        companyReferences.get(form.getCompanyId()),
                        CompanyLegalEntityReference.of(
                            foundLegalEntities.get(
                                new CompanyIdWithLegalEntityId(
                                    form.getCompanyId(), form.getEntityId())))));
              } else {
                return Either.left(form);
              }
            });
  }

  public Either<ErrorItem, EntityId> update(String id, PortfolioUpdateForm edit) {
    return portfolioEither(id)
        .map(
            p -> {
              var newEntity = portfolioMapper.updatedPortfolio(p, edit);
              CompanyReference newCompanyReference =
                  companyReferenceMapper.toCompanyReference(edit.getCompanyId());
              CompanyLegalEntityReference newCompanyLegalEntityReference =
                  legalEntityRepository
                      .companyLegalEntityView(edit.getCompanyId(), edit.getEntityId())
                      .toOptional()
                      .map(CompanyLegalEntityReference::of)
                      .orElse(null);

              if (newCompanyReference != null && newCompanyLegalEntityReference != null) {
                newEntity.setCompany(newCompanyReference);
                newEntity.setEntity(newCompanyLegalEntityReference);
              }

              var diff = p.diff(newEntity);
              if (diff.numberOfDiffs() > 0) {
                newEntity.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newEntity);
              return newEntity;
            })
        .map(this::publishUpdatedEvent);
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return portfolioEither(id)
        .map(
            p -> {
              var newEntity = portfolioMapper.shallowClone(p);
              newEntity.setArchived(true);
              newEntity.addAuditLog(auditingHandler.markCreated(AuditLog.of(p.diff(newEntity))));
              mongoOperations.save(newEntity);
              return newEntity;
            })
        .map(this::publishArchiveEvent);
  }

  /**
   * Archives a set of portfolios by their IDs.
   *
   * @param ids the set of portfolio IDs to archive
   * @return a set of Either objects indicating success or failure for each ID
   */
  public Set<Either<EntityId, EntityId>> bulkArchive(Set<String> ids) {

    // All Archived Diff are the same, so we can create a single instance and reuse it.
    VersionDiffs archivedDiff =
        VersionDiffs.of(
            new DiffBuilder<>(Boolean.FALSE, Boolean.TRUE, ToStringStyle.DEFAULT_STYLE)
                .append(Portfolio.Fields.archived, false, true)
                .build());
    var archivedDate = LocalDateTime.now();
    var currentUser = auditUserProvider.get();
    mongoOperations
        .bulkOps(BulkMode.UNORDERED, Portfolio.class)
        .updateMulti(
            query(where(Portfolio.Fields.id).in(ids)),
            Update.update(Portfolio.Fields.archived, true)
                .push(Portfolio.Fields.auditLogs)
                .each(auditingHandler.markCreated(AuditLog.of(archivedDiff)))
                .set(Portfolio.Fields.lastModifiedAt, archivedDate)
                .set(Portfolio.Fields.lastModifiedBy, currentUser))
        .execute();
    var updatedCompanies = new HashSet<String>();
    var actualArchivedEntities =
        mongoOperations.stream(query(where(Portfolio.Fields.id).in(ids)), Portfolio.class)
            .peek(portfolio -> updatedCompanies.add(portfolio.getCompany().getEntityId()))
            .map(portfolio -> entityId(portfolio.getId()))
            .map(EntityId::getId)
            .collect(toSet());
    publisher.publishEvent(new PortfolioArchived(updatedCompanies, ids));

    return ids.stream()
        .map(
            id -> {
              if (actualArchivedEntities.contains(id)) {
                return Either.<EntityId, EntityId>right(entityId(id));
              } else {
                return Either.<EntityId, EntityId>left(entityId(id));
              }
            })
        .collect(toSet());
  }

  private Update fromVersionDiff(VersionDiffs diffs) {
    Update update = new Update();
    for (var diff : diffs.getDiffs()) {
      update.set(diff.getFieldName(), diff.getRight());
    }
    return update;
  }

  private EntityId publishArchiveEvent(Portfolio portfolio) {
    publisher.publishEvent(PortfolioArchived.newOf(portfolio.getCompany(), portfolio.getId()));
    return entityId(portfolio.getId());
  }

  private EntityId publishUpdatedEvent(Portfolio portfolio) {
    publisher.publishEvent(PortfolioDetailsUpdated.newOf(portfolio.getId()));
    return entityId(portfolio.getId());
  }

  public boolean existsByExternalIdExcludingSelf(
      String companyId, String legalEntityId, String externalId, @Nullable String id) {
    var criteria =
        where(Portfolio.Fields.externalPortfolioId)
            .is(externalId)
            .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
            .is(legalEntityId)
            .and(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
            .is(companyId);
    if (id != null) {
      criteria.and(Portfolio.Fields.id).ne(id);
    }
    criteria.and(Portfolio.Fields.archived).is(false);
    return mongoOperations.exists(new Query(criteria), Portfolio.class);
  }

  @EventListener
  public void onEvent(UserUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(propertyName(Portfolio.Fields.createdBy, AuditUser.Fields.userId))
                .is(event.getEntityId())),
        Update.update(
                propertyName(Portfolio.Fields.createdBy, AuditUser.Fields.name),
                event.getForm().getName())
            .set(
                propertyName(Portfolio.Fields.createdBy, AuditUser.Fields.username),
                event.getForm().getUsername()),
        Portfolio.class);
    mongoOperations.updateMulti(
        query(
            where(propertyName(Portfolio.Fields.lastModifiedBy, AuditUser.Fields.userId))
                .is(event.getEntityId())),
        Update.update(
                propertyName(Portfolio.Fields.lastModifiedBy, AuditUser.Fields.name),
                event.getForm().getName())
            .set(
                propertyName(Portfolio.Fields.lastModifiedBy, AuditUser.Fields.username),
                event.getForm().getUsername()),
        Portfolio.class);
  }

  public void updateAsStartedCalculation(String id, AuditUser user, LocalDate valuationDate) {
    mongoOperations.updateFirst(
        query(where(Portfolio.Fields.id).is(id)),
        Update.update(Portfolio.Fields.calculatedBy, user)
            .set(Portfolio.Fields.calculatedAt, LocalDateTime.now())
            .set(Portfolio.Fields.valuationDate, valuationDate),
        Portfolio.class);
  }

  @EventListener
  public void onEvent(CompanyUpdated event) {
    var companyRef = companyReferenceMapper.toCompanyReference(event.getEntityId());
    mongoOperations
        .update(Portfolio.class)
        .matching(
            query(
                where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                    .is(event.getEntityId())))
        .apply(Update.update(Portfolio.Fields.company, companyRef))
        .all();
  }

  @EventListener
  public void onLegalEntityUpdate(CompanyLegalEntityUpdated event) {
    var entityRef =
        companyReferenceMapper.toLegalEntityReference(event.getCompanyId(), event.getEntityId());
    mongoOperations
        .update(Portfolio.class)
        .matching(
            query(
                where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                    .is(event.getCompanyId())
                    .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
                    .is(event.getEntityId())))
        .apply(Update.update(Portfolio.Fields.entity, entityRef))
        .all();
  }

  @EventListener
  public void onLegalEntityArchived(CompanyLegalEntityArchived event) {
    var query =
        query(
            where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                .is(event.getCompanyId())
                .and(propertyName(Portfolio.Fields.entity, EntityReference.Fields.entityId))
                .is(event.getEntityId())
                .and(Portfolio.Fields.archived)
                .is(false));
    mongoOperations.find(query, Portfolio.class).stream()
        .map(Portfolio::getId)
        .forEach(this::archive);
  }

  public Map<String, Integer> portfoliosCountAll(List<String> companyIds) {
    return mongoOperations
        .aggregateStream(
            newAggregation(
                Portfolio.class,
                match(
                    where(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                        .in(companyIds)
                        .and(Portfolio.Fields.archived)
                        .is(false)),
                group(propertyName(Portfolio.Fields.company, EntityReference.Fields.entityId))
                    .count()
                    .as(Company.Fields.numberOfPortfolios)),
            Document.class)
        .collect(
            toMap(
                d -> d.getString(UNDERSCORE_ID),
                d -> d.getInteger(Company.Fields.numberOfPortfolios)));
  }
}
