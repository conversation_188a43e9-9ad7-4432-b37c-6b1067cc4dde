package com.solum.xplain.core.curvegroup.curve.dto;

import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import lombok.Data;
import lombok.NonNull;

@Data
public class CalibratedCurvesOptions {
  @NonNull private final LocalDate valuationDate;
  @Nullable private final CalculationDiscountingType calibrationCurrency;
  @Nullable private final CalculationStrippingType calibrationStrippingType;
}
