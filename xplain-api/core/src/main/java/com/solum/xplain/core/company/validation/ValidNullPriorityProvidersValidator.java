package com.solum.xplain.core.company.validation;

import static com.solum.xplain.core.company.validation.ValuationDataProvidersValidationUtils.validNullPriorityProviders;

import com.solum.xplain.core.company.form.ValuationDataProvidersForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

public class ValidNullPriorityProvidersValidator
    implements ConstraintValidator<ValidNullPriorityProviders, ValuationDataProvidersForm> {

  @Override
  public boolean isValid(ValuationDataProvidersForm value, ConstraintValidatorContext context) {
    var p1 = value.getPrimary();
    var p2 = value.getSecondary();
    var p3 = value.getTertiary();
    var p4 = value.getQuaternary();

    if (ObjectUtils.allNull(p1, p2, p3, p4)) {
      return true;
    }

    return validNullPriorityProviders(p1, p2, p3, p4);
  }
}
