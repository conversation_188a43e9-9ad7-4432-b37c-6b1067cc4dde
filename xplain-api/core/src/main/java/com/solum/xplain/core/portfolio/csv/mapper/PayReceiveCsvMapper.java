package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_IDENTIFIER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_MARGIN;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_IDENTIFIER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_MARGIN;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.RECEIVE_NOTIONAL;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView;
import java.util.List;

public class PayReceiveCsvMapper extends CsvMapper<PortfolioItemFlatView> {

  private static final List<CsvColumn<PortfolioItemFlatView>> COLUMNS =
      List.of(
          CsvColumn.text(
              PortfolioItemFlatView.Fields.payLegCurrency,
              PAY_CURRENCY,
              PortfolioItemFlatView::getPayLegCurrency),
          CsvColumn.decimal(
              PortfolioItemFlatView.Fields.payLegNotionalValue,
              PAY_NOTIONAL,
              PortfolioItemFlatView::getPayLegNotionalValue),
          CsvColumn.decimal(
              PortfolioItemFlatView.Fields.payLegRateMargin,
              PAY_MARGIN,
              PortfolioItemFlatView::getPayLegRateMargin),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.payLegIndex,
              PAY_INDEX,
              PortfolioItemFlatView::getPayLegIndex),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.payLegFrequency,
              PAY_FREQUENCY,
              PortfolioItemFlatView::getPayLegFrequency),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.payLegDayCount,
              PAY_DAY_COUNT,
              PortfolioItemFlatView::getPayLegDayCount),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.payLegExtIdentifier,
              PAY_IDENTIFIER,
              PortfolioItemFlatView::getPayLegExtIdentifier),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.receiveLegCurrency,
              RECEIVE_CURRENCY,
              PortfolioItemFlatView::getReceiveLegCurrency),
          CsvColumn.decimal(
              PortfolioItemFlatView.Fields.receiveLegNotionalValue,
              RECEIVE_NOTIONAL,
              PortfolioItemFlatView::getReceiveLegNotionalValue),
          CsvColumn.decimal(
              PortfolioItemFlatView.Fields.receiveLegRateMargin,
              RECEIVE_MARGIN,
              PortfolioItemFlatView::getReceiveLegRateMargin),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.receiveLegIndex,
              RECEIVE_INDEX,
              PortfolioItemFlatView::getReceiveLegIndex),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.receiveLegFrequency,
              RECEIVE_FREQUENCY,
              PortfolioItemFlatView::getReceiveLegFrequency),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.receiveLegDayCount,
              RECEIVE_DAY_COUNT,
              PortfolioItemFlatView::getReceiveLegDayCount),
          CsvColumn.text(
              PortfolioItemFlatView.Fields.receiveLegExtIdentifier,
              RECEIVE_IDENTIFIER,
              PortfolioItemFlatView::getReceiveLegExtIdentifier));

  public PayReceiveCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
