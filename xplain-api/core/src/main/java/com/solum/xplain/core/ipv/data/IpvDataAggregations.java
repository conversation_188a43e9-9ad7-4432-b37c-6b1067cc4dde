package com.solum.xplain.core.ipv.data;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.VersionedValue;
import com.solum.xplain.core.ipv.data.entity.BaseIpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion;
import com.solum.xplain.core.ipv.data.value.IpvDataNavValueView;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.ipv.nav.entity.NavVersion;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IpvDataAggregations {

  private static final String VALUE_FIELD = "value";

  public static List<AggregationOperation> providerValueViewAggregations() {
    return ImmutableList.<AggregationOperation>builder()
        .add(latestProviderValueProjection())
        .add(ipvDataProviderValueViewProjection())
        .build();
  }

  private static ProjectionOperation latestProviderValueProjection() {
    return project(
            DataValuesHolder.Fields.id,
            BaseIpvDataValue.Fields.groupId,
            DataValuesHolder.Fields.date,
            BaseIpvDataValue.Fields.key,
            IpvDataValue.Fields.provider,
            BaseIpvDataValue.Fields.resolved)
        .and(ArrayOperators.arrayOf(DataValuesHolder.Fields.values).elementAt(-1))
        .as(VALUE_FIELD);
  }

  private static ProjectionOperation ipvDataProviderValueViewProjection() {
    return project(
            DataValuesHolder.Fields.id,
            BaseIpvDataValue.Fields.groupId,
            DataValuesHolder.Fields.date,
            BaseIpvDataValue.Fields.key,
            IpvDataValue.Fields.provider,
            BaseIpvDataValue.Fields.resolved)
        .and(joinPaths(VALUE_FIELD, VersionedValue.Fields.value))
        .as(IpvDataProviderValueView.Fields.value)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.delta))
        .as(IpvDataProviderValueView.Fields.delta)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.vega))
        .as(IpvDataProviderValueView.Fields.vega)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.gamma))
        .as(IpvDataProviderValueView.Fields.gamma)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.theta))
        .as(IpvDataProviderValueView.Fields.theta)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.rho))
        .as(IpvDataProviderValueView.Fields.rho)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.parRate))
        .as(IpvDataProviderValueView.Fields.parRate)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.spotRate))
        .as(IpvDataProviderValueView.Fields.spotRate)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.impliedVol))
        .as(IpvDataProviderValueView.Fields.impliedVol)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.atmImpliedVol))
        .as(IpvDataProviderValueView.Fields.atmImpliedVol)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.realisedVol))
        .as(IpvDataProviderValueView.Fields.realisedVol)
        .and(joinPaths(VALUE_FIELD, IpvDataValueVersion.Fields.fairVol))
        .as(IpvDataProviderValueView.Fields.fairVol)
        .and(joinPaths(VALUE_FIELD, NavVersion.Fields.currency))
        .as(IpvDataNavValueView.Fields.currency)
        .and(joinPaths(VALUE_FIELD, VersionedValue.Fields.comment))
        .as(IpvDataProviderValueView.Fields.comment);
  }
}
