package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.validation.OffshoreIndexValidator.validateIborOffshoreIndex;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidOffshoreCapFloorValidator
    implements ConstraintValidator<ValidOffshoreCapFloor, CapFloorTradeForm> {

  @Override
  public boolean isValid(CapFloorTradeForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(value.getIsOffshore(), value.getCalculationIborIndex())
        && isTrue(value.getIsOffshore())) {
      var error = validateIborOffshoreIndex(value.getCalculationIborIndex());
      error.ifPresent(
          errorItem ->
              context
                  .buildConstraintViolationWithTemplate(errorItem.getDescription())
                  .addPropertyNode(CapFloorTradeForm.Fields.isOffshore)
                  .addConstraintViolation());
      return error.isEmpty();
    }
    return true;
  }
}
