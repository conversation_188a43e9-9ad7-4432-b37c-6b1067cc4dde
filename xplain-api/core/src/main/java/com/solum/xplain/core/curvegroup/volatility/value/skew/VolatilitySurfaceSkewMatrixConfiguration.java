package com.solum.xplain.core.curvegroup.volatility.value.skew;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityMatrix;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeView;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class VolatilitySurfaceSkewMatrixConfiguration extends VolatilityMatrix<VolatilityNodeView> {

  private final BigDecimal skewValue;

  private VolatilitySurfaceSkewMatrixConfiguration(
      BigDecimal skewValue, List<VolatilityNodeView> nodes, LocalDate versionDate) {
    super(nodes, versionDate);
    this.skewValue = skewValue;
  }

  public static VolatilitySurfaceSkewMatrixConfiguration configurationFromList(
      BigDecimal skewValue, VersionedList<VolatilityNodeView> nodes) {
    return new VolatilitySurfaceSkewMatrixConfiguration(
        skewValue, nodes.getList(), nodes.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  public BigDecimal getSkewValue() {
    return skewValue;
  }
}
