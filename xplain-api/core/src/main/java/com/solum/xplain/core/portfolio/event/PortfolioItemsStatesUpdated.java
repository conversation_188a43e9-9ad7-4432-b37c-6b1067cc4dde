package com.solum.xplain.core.portfolio.event;

import static java.util.Objects.requireNonNull;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity;
import com.solum.xplain.core.portfolio.PortfolioItem;
import java.time.LocalDate;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Stream;
import lombok.Data;
import lombok.NonNull;

/** This event is used to notify about changes in the states of portfolio items. */
@Data
public class PortfolioItemsStatesUpdated {

  private final Set<VDKUpdate> changes = new HashSet<>();

  public static PortfolioItemsStatesUpdated newOf(@NonNull Collection<PortfolioItem> items) {
    return new PortfolioItemsStatesUpdated(items);
  }

  private PortfolioItemsStatesUpdated(@NonNull Collection<PortfolioItem> items) {
    for (var item : items) {
      VDKUpdate.newOf(item).forEach(changes::add);
    }
  }

  public record VDKUpdate(String valuationDataKey, LocalDate fromDate, LocalDate toDate) {

    private static Stream<VDKUpdate> newOf(PortfolioItem item) {
      requireNonNull(item.getValuationDataKey());
      requireNonNull(item.getValidFrom());
      for (DateRangeVersionValidity validity : item.getValidities()) {
        requireNonNull(validity.getValidTo());
      }
      return item.getValidities().stream()
          .map(v -> new VDKUpdate(item.getValuationDataKey(), item.getValidFrom(), v.getValidTo()));
    }
  }
}
