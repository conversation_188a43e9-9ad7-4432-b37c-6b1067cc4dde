package com.solum.xplain.core.curvegroup.curvecredit.value;

import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCdsCurveName;
import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCreditIndexCurveName;
import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCreditIndexTrancheCurveName;

import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType;
import com.solum.xplain.core.curvegroup.curvecredit.validation.CreditCurveCreateForm;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import org.springframework.util.Assert;

@Data
public class CreditCurveCsvForm implements CreditCurveCreateForm, HasVersionForm {
  private final CreditCurveType curveType;

  // Common fields
  private String sector;
  private BigDecimal recoveryRate;
  private String quoteConvention;
  private Double fixedCoupon;
  private String entityLongName; // CDS legal entity name or Credit Index
  private String reference;
  private String currency;
  private String seniority;
  private String docClause;
  // Cds Fields
  private String corpTicker;
  // Index fields
  private Integer creditIndexSeries;
  private Integer creditIndexVersion;
  private LocalDate creditIndexStartDate;
  private BigDecimal creditIndexFactor;
  // Credit Index Tranche fields
  private String creditIndexTranche;

  private NewVersionFormV2 versionForm;

  public CdsCurveForm cdsCurveForm() {
    assert curveType == CreditCurveType.CDS;
    var cdsCurveForm = new CdsCurveForm();
    cdsCurveForm.setReference(reference);
    cdsCurveForm.setCurrency(currency);
    cdsCurveForm.setSeniority(seniority);
    cdsCurveForm.setDocClause(docClause);
    cdsCurveForm.setEntityLongName(entityLongName);
    cdsCurveForm.setCorpTicker(corpTicker);
    cdsCurveForm.setSector(sector);
    cdsCurveForm.setRecoveryRate(recoveryRate);
    cdsCurveForm.setQuoteConvention(quoteConvention);
    cdsCurveForm.setFixedCoupon(fixedCoupon);
    cdsCurveForm.setVersionForm(versionForm);
    return cdsCurveForm;
  }

  public CreditIndexCurveForm creditIndexCurveForm() {
    Assert.isTrue(
        curveType == CreditCurveType.CREDIT_INDEX
            || curveType == CreditCurveType.CREDIT_INDEX_TRANCHE,
        "Curve must be Credit Index or Credit Index Tranche");
    var indexForm = new CreditIndexCurveForm();
    indexForm.setReference(reference);
    indexForm.setCurrency(currency);
    return fillIndexForm(indexForm);
  }

  public CreditIndexTrancheCurveForm creditIndexTrancheCurveForm() {
    Assert.isTrue(
        curveType == CreditCurveType.CREDIT_INDEX_TRANCHE, "Curve must be Credit Index Tranche");
    var trancheCurveForm = new CreditIndexTrancheCurveForm();
    trancheCurveForm.setReference(reference);
    trancheCurveForm.setCurrency(currency);
    trancheCurveForm.setCreditIndexTranche(creditIndexTranche);
    return fillIndexForm(trancheCurveForm);
  }

  private <T extends CreditIndexCurveUpdateForm> T fillIndexForm(T form) {
    form.setEntityLongName(entityLongName);
    form.setCreditIndexSeries(creditIndexSeries);
    form.setCreditIndexVersion(creditIndexVersion);
    form.setCreditIndexStartDate(creditIndexStartDate);
    form.setCreditIndexFactor(creditIndexFactor);
    form.setDocClause(docClause);
    form.setSector(sector);
    form.setRecoveryRate(recoveryRate);
    form.setQuoteConvention(quoteConvention);
    form.setFixedCoupon(fixedCoupon);
    form.setVersionForm(versionForm);
    return form;
  }

  @Override
  public String creditCurveName() {
    return switch (curveType) {
      case CDS -> resolveCdsCurveName(reference, currency, seniority, docClause);
      case CREDIT_INDEX -> resolveCreditIndexCurveName(reference, currency);
      case CREDIT_INDEX_TRANCHE ->
          resolveCreditIndexTrancheCurveName(reference, creditIndexTranche, currency);
    };
  }
}
