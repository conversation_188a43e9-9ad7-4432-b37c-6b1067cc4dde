package com.solum.xplain.core.portfolio.validation;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention;
import com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions;
import com.solum.xplain.core.portfolio.form.LoanNoteTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.JpLoanNoteTradeGroup;
import java.util.List;
import java.util.Optional;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class LoanNoteGroupProvider implements DefaultGroupSequenceProvider<LoanNoteTradeForm> {
  @Override
  public List<Class<?>> getValidationGroups(LoanNoteTradeForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(LoanNoteTradeForm.class);
    if (form == null) {
      return builder.build();
    }
    Optional.ofNullable(form.getReference())
        .flatMap(BondCurveConventions::findBondCurveYieldConventionByName)
        .filter(FixedCouponBondYieldConvention.JP_SIMPLE::equals)
        .ifPresent(c -> builder.add(JpLoanNoteTradeGroup.class));

    return builder.build();
  }
}
