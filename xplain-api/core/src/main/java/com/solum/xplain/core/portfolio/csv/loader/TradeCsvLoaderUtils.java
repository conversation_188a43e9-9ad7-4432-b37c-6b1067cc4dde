package com.solum.xplain.core.portfolio.csv.loader;

import static com.opengamma.strata.collect.ArgChecker.inOrderOrEqual;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.ROLL_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.STUB_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ROLL_CONVETIONT;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.portfolio.value.AccrualDates;
import java.time.LocalDate;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeCsvLoaderUtils {
  public static final String NO_VALUE_FOUND = "No value was found for '%s'";
  private static final String MISSING_DATE =
      "Trade must define field '%s' if stub convention is 'Both'";
  private static final String INVALID_DATE =
      "Trade must not define field '%s' if stub convention is not 'Both'";
  public static final ClassifierSupplier BUSINESS_DAY_ADJUSTMENT_TYPE_SUPPLIER =
      new ClassifierSupplier(BUSINESS_DAY_ADJUSTMENT_TYPE);
  private static final ClassifierSupplier ROLL_CONVENTION_SUPPLIER =
      new ClassifierSupplier(ROLL_CONVENTION_CLASSIFIER);
  private static final ClassifierSupplier STUB_CONVENTION_SUPPLIER =
      new ClassifierSupplier(STUB_CONVENTION_CLASSIFIER);
  private static final ClassifierSupplier FX_TRADE_CCY_SUPPLIER =
      new ClassifierSupplier("fxTradeCurrency");

  static BusinessDayConvention parseBusinessDayConvention(CsvRow row, String fieldName) {
    return row.findValue(fieldName)
        .map(v -> CsvLoaderUtils.parseBusinessDayConvention(v, fieldName))
        .orElse(BusinessDayConventions.FOLLOWING);
  }

  static Supplier<IllegalArgumentException> illegalArgument(String fieldName) {
    return () -> new IllegalArgumentException(String.format(NO_VALUE_FOUND, fieldName));
  }

  public static LocalDate parseEndDate(CsvRow row, LocalDate startDate) {
    var endDate = parseDate(row, TRADE_ACCRUAL_SCHEDULE_END_DATE);

    return validateValue(
        endDate,
        TRADE_ACCRUAL_SCHEDULE_END_DATE,
        "Must be after Start Date",
        endDate.isAfter(startDate));
  }

  public static LocalDate parseStartDate(CsvRow row) {
    return parseDate(row, TRADE_ACCRUAL_SCHEDULE_START_DATE);
  }

  public static Currency parseFxCurrency(CsvRow row, String field) {
    var ccyStr = row.getValue(field);
    var ccy = validateValue(ccyStr, FX_TRADE_CCY_SUPPLIER);
    return Currency.of(ccy);
  }

  @Nullable
  static String rollConvention(CsvRow row) {
    return row.findValue(TRADE_ROLL_CONVETIONT)
        .map(c -> validateValue(c, ROLL_CONVENTION_SUPPLIER))
        .orElse(null);
  }

  @Nullable
  static String stubConvention(CsvRow row) {
    return row.findValue(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION)
        .map(c -> validateValue(c, STUB_CONVENTION_SUPPLIER))
        .orElse(null);
  }

  static AccrualDates parseAccrualDates(CsvRow row) {
    var startDate = parseStartDate(row);
    var endDate = parseEndDate(row, startDate);
    var stubConvention = TradeCsvLoaderUtils.stubConvention(row);

    if (StubConvention.BOTH.getName().equals(stubConvention)) {
      if (row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE).isEmpty()) {
        throw new IllegalArgumentException(
            String.format(MISSING_DATE, TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE));
      }
      if (row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE).isEmpty()) {
        throw new IllegalArgumentException(
            String.format(MISSING_DATE, TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE));
      }
    } else {
      if (row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE).isPresent()) {
        throw new IllegalArgumentException(
            String.format(INVALID_DATE, TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE));
      }
      if (row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE).isPresent()) {
        throw new IllegalArgumentException(
            String.format(INVALID_DATE, TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE));
      }
    }
    var regularStartDate =
        row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE)
            .map(CsvLoaderUtils::parseDate)
            .orElse(null);
    var regularEndDate =
        row.findValue(TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE)
            .map(CsvLoaderUtils::parseDate)
            .orElse(null);

    Optional.ofNullable(regularStartDate)
        .ifPresent(
            date ->
                inOrderOrEqual(
                    startDate,
                    date,
                    TRADE_ACCRUAL_SCHEDULE_START_DATE,
                    TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE));
    Optional.ofNullable(regularEndDate)
        .ifPresent(
            date ->
                inOrderOrEqual(
                    date,
                    endDate,
                    TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE,
                    TRADE_ACCRUAL_SCHEDULE_END_DATE));

    return new AccrualDates(stubConvention, startDate, endDate, regularStartDate, regularEndDate);
  }

  private static LocalDate parseDate(CsvRow row, String fieldName) {
    return row.findValue(fieldName)
        .map(CsvLoaderUtils::parseDate)
        .orElseThrow(() -> new IllegalArgumentException(String.format(NO_VALUE_FOUND, fieldName)));
  }
}
