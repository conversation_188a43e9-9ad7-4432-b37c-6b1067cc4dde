package com.solum.xplain.core.utils;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataFxRateProvider;
import com.opengamma.strata.market.curve.Curve;
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider;
import com.opengamma.strata.pricer.rate.ImmutableRatesProviderBuilder;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RatesUtils {

  public static ImmutableRatesProvider combine(
      MarketData marketData, List<ImmutableRatesProvider> rates) {
    ImmutableRatesProviderBuilder merged =
        ImmutableRatesProvider.builder(marketData.getValuationDate());
    merged.fxRateProvider(MarketDataFxRateProvider.of(marketData));
    for (ImmutableRatesProvider r : rates) {
      ImmutableMap<Currency, Curve> discounts = r.getDiscountCurves();
      for (Map.Entry<Currency, Curve> entry : discounts.entrySet()) {
        merged.discountCurve(entry.getKey(), entry.getValue());
      }
      ImmutableMap<Index, Curve> indices = r.getIndexCurves();
      for (Map.Entry<Index, Curve> entry : indices.entrySet()) {
        merged.indexCurve(entry.getKey(), entry.getValue());
      }
      Map<Index, LocalDateDoubleTimeSeries> timeSeries = r.getTimeSeries();
      for (Map.Entry<Index, LocalDateDoubleTimeSeries> entry : timeSeries.entrySet()) {
        merged.timeSeries(entry.getKey(), entry.getValue());
      }
    }
    return merged.build();
  }
}
