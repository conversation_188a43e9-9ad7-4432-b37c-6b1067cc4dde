package com.solum.xplain.core.common;

import static com.solum.xplain.core.common.GroupRequest.emptyGroupRequest;

import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

public class GroupRequestArgumentResolver implements HandlerMethodArgumentResolver {

  public static final String ROW_GROUP_COLS = "rowGroupCols";
  public static final String GROUP_KEYS = "groupKeys";

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return GroupRequest.class.isAssignableFrom(parameter.getParameterType());
  }

  @Override
  public GroupRequest resolveArgument(
      MethodParameter parameter,
      ModelAndViewContainer mavContainer,
      NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) {

    String[] rowGroupCosl = webRequest.getParameterValues(ROW_GROUP_COLS);
    String[] groupKeys = webRequest.getParameterValues(GROUP_KEYS);
    if (rowGroupCosl == null || rowGroupCosl.length == 0) {
      return emptyGroupRequest();
    } else {
      if (groupKeys == null) {
        groupKeys = new String[0];
      }
      return GroupRequest.of(rowGroupCosl, groupKeys);
    }
  }
}
