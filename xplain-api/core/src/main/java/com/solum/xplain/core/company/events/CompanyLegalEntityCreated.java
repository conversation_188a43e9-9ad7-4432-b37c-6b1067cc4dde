package com.solum.xplain.core.company.events;

import com.solum.xplain.core.common.EntityEvent;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyLegalEntityCreated extends EntityEvent {
  private final String companyId;

  public CompanyLegalEntityCreated(String companyId, String entityId) {
    super(entityId);
    this.companyId = companyId;
  }

  public static CompanyLegalEntityCreated newOf(String companyId, String entityId) {
    return new CompanyLegalEntityCreated(companyId, entityId);
  }
}
