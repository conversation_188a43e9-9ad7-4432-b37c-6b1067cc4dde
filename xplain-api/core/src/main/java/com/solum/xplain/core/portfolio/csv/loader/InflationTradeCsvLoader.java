package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.portfolio.CoreProductType.INFLATION;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvLoader;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class InflationTradeCsvLoader implements ProductCsvLoader {
  private final FullSwapTradeCsvLoader commonLoader;

  @Override
  public List<ProductType> productTypes() {
    return List.of(INFLATION);
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    return commonLoader.parse(row, INFLATION, refSecTrade).map(ResolvableTradeDetails.class::cast);
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    return commonLoader.tradeCurrency(row);
  }
}
