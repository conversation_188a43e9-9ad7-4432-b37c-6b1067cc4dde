package com.solum.xplain.core.common.csv;

import static java.util.stream.Collectors.toUnmodifiableSet;

import com.google.common.collect.Sets;
import com.solum.xplain.core.common.CollectionUtils;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class ImportItems<F, K, D> {

  private final Map<K, D> existingItemsByKeys;
  private final Map<K, F> importItemsByKeys;

  private final Set<K> newItemsKeys;
  private final Set<K> duplicateItemsKeys;
  private final Set<K> spareItemsKeys;

  private final Predicate<K> relevantKeyFilter;

  /**
   * Allows consistent access and comparision between import items and existing items, such that we
   * can identify new, duplicate and spare (those not replaced) items.
   *
   * @param relevantKeyFilter filter to apply to existing and import keys
   */
  @Builder
  public ImportItems(
      List<D> existingActiveItems,
      Function<D, K> existingItemToKeyFn,
      List<F> importItems,
      Function<F, K> importItemToKeyFn,
      @Nullable Predicate<K> relevantKeyFilter) {
    this.relevantKeyFilter = relevantKeyFilter != null ? relevantKeyFilter : k -> true;

    this.existingItemsByKeys = CollectionUtils.toMap(existingActiveItems, existingItemToKeyFn);
    this.importItemsByKeys = CollectionUtils.toMap(importItems, importItemToKeyFn);

    Set<K> activeItemsKeys = filterRelevantKeys(existingItemsByKeys.keySet());
    Set<K> importItemsKeys = filterRelevantKeys(importItemsByKeys.keySet());
    this.newItemsKeys = Sets.difference(importItemsKeys, activeItemsKeys);
    this.duplicateItemsKeys = Sets.intersection(importItemsKeys, activeItemsKeys);
    this.spareItemsKeys = Sets.difference(activeItemsKeys, importItemsKeys);
  }

  public D existingItem(K key) {
    return existingItemsByKeys.get(key);
  }

  public F importItem(K key) {
    return importItemsByKeys.get(key);
  }

  public Set<K> getExistingKeys() {
    return existingItemsByKeys.keySet();
  }

  public Set<K> getImportKeys() {
    return importItemsByKeys.keySet();
  }

  public Set<Entry<K, F>> getImportEntries() {
    return importItemsByKeys.entrySet();
  }

  /**
   * @return the keys of import items that are new compared to existing items
   */
  public Set<K> getNewKeys() {
    return newItemsKeys;
  }

  /**
   * @return the keys of import items that are duplicates of existing items
   */
  public Set<K> getDuplicateKeys() {
    return duplicateItemsKeys;
  }

  /**
   * @return the keys of existing items that are not replaced by import items
   */
  public Set<K> getSpareKeys() {
    return spareItemsKeys;
  }

  private Set<K> filterRelevantKeys(Set<K> keys) {
    return keys.stream().filter(relevantKeyFilter).collect(toUnmodifiableSet());
  }
}
