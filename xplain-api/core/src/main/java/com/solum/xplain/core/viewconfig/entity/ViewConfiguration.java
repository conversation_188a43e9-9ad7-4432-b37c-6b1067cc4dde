package com.solum.xplain.core.viewconfig.entity;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.viewconfig.value.View;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class ViewConfiguration {
  @Id private ObjectId id;
  private View<?> scope;
  private String name;
  private Boolean freezeFirstGroup;
  private List<ColumnDefinitionGroup> columnDefinitionGroups;
  private Boolean shared;

  @CreatedBy private AuditUser createdBy;
  @LastModifiedDate private LocalDateTime lastModifiedAt;

  public boolean ownedBy(XplainPrincipal xplainUser) {
    return getCreatedBy().getUserId().equals(xplainUser.getId());
  }
}
