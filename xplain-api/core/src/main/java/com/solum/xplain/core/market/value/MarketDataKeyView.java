package com.solum.xplain.core.market.value;

import com.solum.xplain.core.common.versions.State;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@EqualsAndHashCode
@FieldNameConstants
public class MarketDataKeyView {
  private String entityId;
  private String key;
  private String name;
  private String assetGroup;
  private String instrumentType;
  private List<MarketDataProviderTickerView> providerTickers;

  private LocalDate validFrom;
  private LocalDateTime recordFrom;
  private State state;
  private String comment;
  private String modifiedBy;
  private LocalDateTime modifiedAt;
}
