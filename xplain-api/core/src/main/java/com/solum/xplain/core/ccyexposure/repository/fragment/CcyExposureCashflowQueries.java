package com.solum.xplain.core.ccyexposure.repository.fragment;

import com.solum.xplain.core.ccyexposure.entity.Cashflow;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.ccyexposure.value.CashflowSearchForm;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.VersionedEntityQueries;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;

public interface CcyExposureCashflowQueries extends VersionedEntityQueries<Cashflow> {
  Either<ErrorItem, Cashflow> getActiveItemByEntityId(String entityId, LocalDate stateDate);

  Either<ErrorItem, EntityId> create(CashflowForm f);

  Either<ErrorItem, EntityId> update(String entityId, LocalDate version, CashflowForm form);

  List<Cashflow> getActiveItemsByCcyExposureIdIn(List<String> entityIds, LocalDate stateDate);

  List<CashflowView> findAllVersions(String entityId);

  DateList futureVersions(String ccyExposureId, CashflowSearchForm form);
}
