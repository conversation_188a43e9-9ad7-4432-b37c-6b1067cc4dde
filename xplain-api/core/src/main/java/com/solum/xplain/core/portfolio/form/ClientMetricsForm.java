package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.portfolio.ClientMetrics;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ClientMetricsForm {
  @Schema(description = "Client PV")
  private Double presentValue;

  public ClientMetricsForm(ClientMetrics clientMetrics) {
    if (clientMetrics != null) {
      this.presentValue = clientMetrics.getPresentValue();
    }
  }

  public ClientMetrics clientMetrics() {
    return new ClientMetrics(presentValue);
  }
}
