package com.solum.xplain.core.common.validation;

import static com.opengamma.strata.basics.date.Tenor.TENOR_12M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_1M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_3M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_6M;

import com.opengamma.strata.basics.date.Tenor;
import java.util.Collection;
import java.util.List;
import java.util.function.Supplier;

public class CurveStrippingIndexTenorSupplier implements Supplier<Collection<String>> {

  private static final List<Tenor> TENORS = List.of(TENOR_1M, TENOR_3M, TENOR_6M, TENOR_12M);

  @Override
  public Collection<String> get() {
    return TENORS.stream().map(Tenor::toString).toList();
  }
}
