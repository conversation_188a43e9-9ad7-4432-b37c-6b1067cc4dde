package com.solum.xplain.core.market.validation;

import static com.solum.xplain.core.datagroup.DataGroupNameValidatorUtils.isUniqueGroupName;

import com.solum.xplain.core.common.NamedObjectForm;
import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class UniqueMarketDataGroupNameValidator
    implements ConstraintValidator<UniqueMarketDataGroupName, NamedObjectForm> {

  private final MarketDataGroupRepository repository;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  public UniqueMarketDataGroupNameValidator(
      MarketDataGroupRepository repository,
      RequestPathVariablesSupport requestPathVariablesSupport) {
    this.repository = repository;
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  public boolean isValid(NamedObjectForm form, ConstraintValidatorContext context) {
    String groupId = requestPathVariablesSupport.getPathVariable("groupId");
    return isUniqueGroupName(form, context, name -> repository.existsByName(name, groupId));
  }
}
