package com.solum.xplain.core.curvemarket.marketvalue;

import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import java.util.List;
import java.util.Optional;

@FunctionalInterface
public interface CalculationMarketValueMapperFunction<T extends CalculationMarketValue> {

  Optional<T> toValue(
      String key,
      MarketDataProviders provider,
      List<MarketDataValueFlatView> values,
      InstrumentPriceType priceType);
}
