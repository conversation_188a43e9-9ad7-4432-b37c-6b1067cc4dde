package com.solum.xplain.core.ipv.group.filter;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;

@Data
public class IpvDataGroupFilter {
  private final Boolean archived;
  private final String companyId;

  public static IpvDataGroupFilter nonArchived(String companyId) {
    return new IpvDataGroupFilter(false, companyId);
  }

  public Criteria criteria() {
    var validCompanyId = StringUtils.isNotEmpty(companyId) && ObjectId.isValid(companyId);

    Criteria notArchived = where(IpvDataGroup.Fields.archived).is(BooleanUtils.isTrue(archived));

    if (!validCompanyId) {
      return notArchived;
    }

    return notArchived.orOperator(
        where(propertyName(IpvDataGroup.Fields.companies, EntityReference.Fields.entityId))
            .is(companyId),
        where(IpvDataGroup.Fields.allowAllCompanies).is(true));
  }
}
