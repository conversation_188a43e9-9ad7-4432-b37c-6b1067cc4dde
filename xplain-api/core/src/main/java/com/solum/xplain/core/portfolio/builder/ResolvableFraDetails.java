package com.solum.xplain.core.portfolio.builder;

import static com.solum.xplain.core.portfolio.calendars.TradeCalendarUtils.getSwapTradeCalendar;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;
import org.springframework.lang.Nullable;

@Builder
@ToString
@EqualsAndHashCode
public class ResolvableFraDetails implements ResolvableTradeDetails {

  @NonNull private final LocalDate startDate;
  @NonNull private final LocalDate endDate;
  @NonNull private final Currency currency;
  @Nullable private final Double notional;
  @NonNull private final String businessDayConvention;
  @NonNull private final String index;
  @NonNull private final PositionType positionType;
  @NonNull private final DayCount dayCount;
  @NonNull private final Double initialValue;
  @Nullable private final String extIborLegIdentifier;
  @Nullable private final String extFixedLegIdentifier;

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    var tradeDetails = TradeDetails.newOf(tradeInfo);

    tradeDetails.setPositionType(positionType);
    tradeDetails.setStartDate(startDate);
    tradeDetails.setEndDate(endDate);
    tradeDetails.setBusinessDayConvention(businessDayConvention);
    if (positionType == PositionType.BUY) {
      tradeDetails.setReceiveLeg(iborLegDetails());
      tradeDetails.setPayLeg(fixedLegDetails());
    } else {
      tradeDetails.setReceiveLeg(fixedLegDetails());
      tradeDetails.setPayLeg(iborLegDetails());
    }
    tradeDetails.setPositionType(positionType);
    tradeDetails.setCalendar(getSwapTradeCalendar(iborLegDetails(), fixedLegDetails()));
    return tradeDetails;
  }

  private TradeLegDetails iborLegDetails() {
    return ResolvableIborLeg.builder()
        .payReceive(positionType.toPayReceive())
        .index(index)
        .dayCount(dayCount)
        .notional(notional)
        .currency(currency)
        .extLegIdentifier(extIborLegIdentifier)
        .build()
        .toTradeLegDetails();
  }

  private TradeLegDetails fixedLegDetails() {
    return ResolvableFixedLeg.builder()
        .payReceive(positionType.toPayReceive().opposite())
        .dayCount(dayCount)
        .notional(notional)
        .currency(currency)
        .initialValue(initialValue)
        .extLegIdentifier(extFixedLegIdentifier)
        .build()
        .toTradeLegDetails();
  }
}
