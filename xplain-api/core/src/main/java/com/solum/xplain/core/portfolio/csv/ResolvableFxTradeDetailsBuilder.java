package com.solum.xplain.core.portfolio.csv;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;

public interface ResolvableFxTradeDetailsBuilder<T extends ResolvableTradeDetails> {
  ResolvableFxTradeDetailsBuilder<T> payLegExtIdentifier(String payLegExtIdentifier);

  ResolvableFxTradeDetailsBuilder<T> receiveLegExtIdentifier(String receiveLegExtIdentifier);

  ResolvableFxTradeDetailsBuilder<T> payCurrency(Currency payCurrency);

  ResolvableFxTradeDetailsBuilder<T> receiveCurrency(Currency receiveCurrency);
}
