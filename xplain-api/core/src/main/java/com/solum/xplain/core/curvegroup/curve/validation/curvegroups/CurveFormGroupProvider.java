package com.solum.xplain.core.curvegroup.curve.validation.curvegroups;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.market.ValueType;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.extensions.curve.ExtendedCurveInterpolators;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class CurveFormGroupProvider implements DefaultGroupSequenceProvider<CurveUpdateForm> {

  @Override
  public List<Class<?>> getValidationGroups(CurveUpdateForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CurveUpdateForm.class);
    if (form != null
        && allNotNull(
            form.getCurveType(), form.getYInterpolationMethod(), form.getInterpolator())) {
      CurveType.toOptionalValue(form.getCurveType())
          .ifPresent(
              type -> {
                switch (type) {
                  case IR_INDEX, INDEX_BASIS -> builder.add(CurveFormIndexGroup.class);
                  case INFLATION_INDEX -> builder.add(CurveFormInflationGroup.class);
                  case XCCY -> builder.add(CurveFormXccyGroup.class);
                  default -> {}
                }
              });
      if (form.getYInterpolationMethod().equals(ValueType.DISCOUNT_FACTOR.getName())) {
        builder.add(CurveFormDiscountFactorGroup.class);
      } else {
        builder.add(CurveFormNonDiscountFactorGroup.class);
      }
      if (form.getInterpolator()
          .equals(ExtendedCurveInterpolators.LOG_LINEAR_DISCOUNT_FACTOR.getName())) {
        builder.add(CurveFormLogLinearDiscountFactorGroup.class);
      } else {
        builder.add(CurveFormGenericInterpolatorGroup.class);
      }
    }
    return builder.build();
  }
}
