package com.solum.xplain.core.curvegroup.curvecredit.validation;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCommonForm;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class CreditCurveCommonFormGroupProvider
    implements DefaultGroupSequenceProvider<CreditCurveCommonForm> {

  @Override
  public List<Class<?>> getValidationGroups(CreditCurveCommonForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CreditCurveCommonForm.class);
    if (form != null && isFixedCouponGroup(form)) {
      builder.add(CreditCurveFixedCouponGroup.class);
    }
    return builder.build();
  }

  private static boolean isFixedCouponGroup(CreditCurveCommonForm form) {
    return StringUtils.equalsAny(
        form.getQuoteConvention(),
        CdsQuoteConvention.QUOTED_SPREAD.name(),
        CdsQuoteConvention.POINTS_UPFRONT.name());
  }
}
