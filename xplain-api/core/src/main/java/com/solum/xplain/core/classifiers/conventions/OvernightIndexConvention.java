package com.solum.xplain.core.classifiers.conventions;

import static com.solum.xplain.core.utils.FrequencyUtils.toStringNoPrefix;

import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class OvernightIndexConvention {
  private final String name;
  private final String tenor;
  private final String currency;
  private final String dayCount;
  private final String fixingCalendar;
  private final int publicationDateOffset;
  private final int effectiveDateOffset;

  public static OvernightIndexConvention newOf(OvernightIndex overnightIndex) {
    return new OvernightIndexConvention(
        overnightIndex.getName(),
        toStringNoPrefix(Frequency.of(overnightIndex.getTenor().getPeriod())),
        ConventionMapper.INSTANCE.map(overnightIndex.getCurrency()),
        ConventionMapper.INSTANCE.map(overnightIndex.getDayCount()),
        ConventionMapper.INSTANCE.map(overnightIndex.getFixingCalendar()),
        overnightIndex.getPublicationDateOffset(),
        overnightIndex.getEffectiveDateOffset());
  }
}
