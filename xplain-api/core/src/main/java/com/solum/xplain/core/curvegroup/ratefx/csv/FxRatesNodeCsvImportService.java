package com.solum.xplain.core.curvegroup.ratefx.csv;

import static com.solum.xplain.core.common.EntityId.entityId;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.GroupedItemsImportService;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRateMapper;
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRatesRepository;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FxRatesNodeCsvImportService
    extends GroupedItemsImportService<
        CurveGroupFxRates, CurveGroupFxRatesNodeForm, FxRatesNodeUniqueKey> {

  private final FxRatesNodeCsvLoader csvLoader;
  private final CurveGroupFxRateMapper mapper;
  private final CurveGroupFxRatesRepository repository;

  public FxRatesNodeCsvImportService(
      AuditEntryService auditEntryService,
      FxRatesNodeCsvLoader csvLoader,
      CurveGroupFxRateMapper mapper,
      CurveGroupFxRatesRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.mapper = mapper;
    this.repository = repository;
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> importRates(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    // NOTE: if FX Rates does not exist - it will be created
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(
            forms ->
                importNodes(
                    fetchRates(groupId, importOptions.getStateDate()),
                    importOptions,
                    forms.getParsedLines()))
        .fold(
            e -> toErrorReturn(importOptions.getDuplicateAction(), e),
            result -> toReturn(importOptions.getDuplicateAction(), result))
        .map(r -> entityId(groupId));
  }

  protected List<LogItem> importNodes(
      CurveGroupFxRates rates, ImportOptions importOptions, List<CurveGroupFxRatesNodeForm> nodes) {
    var importItems =
        ImportItems
            .<CurveGroupFxRatesNodeForm, FxRatesNodeUniqueKey, CurveGroupFxRatesNodeForm>builder()
            .existingActiveItems(nodeForms(rates))
            .existingItemToKeyFn(FxRatesNodeUniqueKey::from)
            .importItems(nodes)
            .importItemToKeyFn(FxRatesNodeUniqueKey::from)
            .build();
    return importForEntity(rates, importItems, importOptions);
  }

  private List<CurveGroupFxRatesNodeForm> nodeForms(CurveGroupFxRates rates) {
    return rates.getNodes().stream().map(mapper::toNodeForm).toList();
  }

  @Override
  public String getCollection() {
    return CurveGroupFxRates.CURVE_GROUP_FX_RATES_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "FX Rates nodes";
  }

  @Override
  protected String entityIdentifier(CurveGroupFxRates curve) {
    return "FX Rates";
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      CurveGroupFxRates rates,
      List<CurveGroupFxRatesNodeForm> nodeForms,
      NewVersionFormV2 versionForm) {
    CurveGroupFxRatesForm form = mapper.toForm(rates);
    form.setVersionForm(versionForm);
    form.setNodes(nodeForms);
    return update(rates, form);
  }

  @Override
  protected boolean hasFutureVersions(CurveGroupFxRates rates, LocalDate stateDate) {
    return repository.getFutureVersions(rates.getEntityId(), stateDate).notEmpty();
  }

  private Either<ErrorItem, EntityId> update(CurveGroupFxRates e, CurveGroupFxRatesForm f) {
    return repository.createRates(e.getEntityId(), f); // Will update if exists
  }

  private CurveGroupFxRates fetchRates(String groupId, LocalDate stateDate) {
    return repository
        .getActiveRates(groupId, BitemporalDate.newOf(stateDate))
        .getOrElse(defaultFxRates(groupId));
  }

  private CurveGroupFxRates defaultFxRates(String groupId) {
    var e = CurveGroupFxRates.newOf(groupId);
    e.setValidFrom(NewVersionFormV2.ROOT_DATE);
    return e;
  }
}
