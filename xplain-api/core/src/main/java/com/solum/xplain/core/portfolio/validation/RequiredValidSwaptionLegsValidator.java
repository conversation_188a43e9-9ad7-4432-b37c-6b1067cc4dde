package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.value.CalculationType.FIXED;
import static com.solum.xplain.core.portfolio.value.CalculationType.IBOR;
import static com.solum.xplain.core.portfolio.value.CalculationType.OVERNIGHT;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;

public class RequiredValidSwaptionLegsValidator
    implements ConstraintValidator<RequiredValidSwaptionLegs, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    if (hasCalculationType(form.getLeg1()) && hasCalculationType(form.getLeg2())) {
      context.disableDefaultConstraintViolation();
      boolean res = true;

      if (!validLegType(form.getLeg1())) {
        addViolation(context, "leg1");
        res = false;
      }
      final boolean hasFixed = hasCalculationType(form, FIXED);
      final boolean hasFloatingLeg = hasCalculationType(form, IBOR, OVERNIGHT);
      if (!hasFixed || !hasFloatingLeg) {
        addViolation(context, "leg2");
        res = false;
      }
      return res;
    }
    return true;
  }

  private void addViolation(ConstraintValidatorContext context, String legName) {
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode(legName)
        .addPropertyNode("calculationType")
        .addConstraintViolation();
  }

  private boolean validLegType(SwapLegForm leg) {
    return switch (leg.getCalculationType()) {
      case FIXED, IBOR, OVERNIGHT -> true;
      case INFLATION -> false;
    };
  }

  private boolean hasCalculationType(SwapTradeForm form, CalculationType... calcType) {
    var requiredTypes = Arrays.asList(calcType);
    return Stream.of(form.getLeg1().getCalculationType(), form.getLeg2().getCalculationType())
        .anyMatch(c -> CollectionUtils.containsAny(requiredTypes, c));
  }

  private boolean hasCalculationType(SwapLegForm form) {
    return form != null && form.getCalculationType() != null;
  }
}
