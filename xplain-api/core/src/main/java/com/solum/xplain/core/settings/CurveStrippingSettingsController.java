package com.solum.xplain.core.settings;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VALUATION_SETTINGS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_SETTINGS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.VALUATION_SETTINGS_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.settings.form.CurveStrippingProductForm;
import com.solum.xplain.core.settings.service.CurveStrippingSettingsControllerService;
import com.solum.xplain.core.settings.value.CurveStrippingProductSettingsView;
import com.solum.xplain.core.settings.value.GlobalSettingsSearch;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/settings/curve-stripping")
@AllArgsConstructor
public class CurveStrippingSettingsController {
  private final CurveStrippingSettingsControllerService service;

  @Operation(summary = "Get curve stripping product settings")
  @CommonErrors
  @GetMapping("/product")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public CurveStrippingProductSettingsView getCurveStrippingProductSettings(
      @RequestParam LocalDate stateDate) {
    return service.getCurveStrippingProductSettings(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Get curve stripping product settings versions")
  @CommonErrors
  @GetMapping("/product/versions")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public List<CurveStrippingProductSettingsView> getCurveStrippingProductSettingsVersions() {
    return service.productSettingsVersions();
  }

  @Operation(summary = "Get curve stripping product settings future versions")
  @GetMapping("/product/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_SETTINGS)
  public DateList getCurveStrippingProductSettingsFutureVersions(
      @Valid GlobalSettingsSearch search) {
    return service.getCurveStrippingProductSettingsFutureVersions(search);
  }

  @Operation(summary = "Save curve stripping product settings")
  @CommonErrors
  @PostMapping("/product/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> saveCurveStrippingProductSettings(
      @Valid @RequestBody CurveStrippingProductForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveCurveStrippingProductSettings(version, form));
  }

  @Operation(summary = "Delete curve stripping product settings")
  @CommonErrors
  @PutMapping("/product/{version}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_SETTINGS)
  @RequireLock(name = VALUATION_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> deleteCurveStrippingProductSettings(
      @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteCurveStrippingProductSettings(version));
  }
}
