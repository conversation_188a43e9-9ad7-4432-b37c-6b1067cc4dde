package com.solum.xplain.core.ipv.data;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.ArchiveForm;
import com.solum.xplain.core.common.value.UserEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.data.csv.IpvDataUploadService;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm;
import com.solum.xplain.core.ipv.data.value.IpvDataNavValueView;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView;
import com.solum.xplain.core.ipv.nav.repository.CompanyLegalEntityNavRepository;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class IpvDataService {

  private final AuthenticationContext authenticationContext;
  private final IpvDataRepository ipvDataRepository;
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final IpvDataUploadService uploadService;
  private final IpvDataModificationHandler ipvDataModificationHandler;
  private final ViewQueryTranslatorFactory viewQueryTranslatorFactory;
  private final CompanyLegalEntityNavRepository companyLegalEntityNavRepository;

  @Transactional
  public Either<ErrorItem, EntityId> createValue(
      Authentication auth, String groupId, IpvDataProviderValueForm form) {
    IpvDataType dataType =
        "NAV".equals(form.getProvider()) ? IpvDataType.NAV : IpvDataType.EXCLUDE_NAV;
    return performWithGroupUpdate(
        auth,
        groupId,
        () -> {
          EntityId result =
              isEntityLevelNav(BitemporalDate.newOf(form.getDate()), dataType)
                  ? companyLegalEntityNavRepository.createValue(groupId, form)
                  : ipvDataRepository.createValue(groupId, form);
          return Either.right(result);
        });
  }

  public Either<ErrorItem, ScrollableEntry<IpvDataProviderValueView>> getValues(
      Authentication auth,
      String groupId,
      IpvDataProviderValueFilter filter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(IpvDataProviderValueView.class);
    return ipvDataGroup(auth, groupId)
        .map(
            g ->
                ipvDataRepository.getValueViews(
                    groupId,
                    filter,
                    viewQueryTranslator.translate(tableFilter),
                    viewQueryTranslator.translate(scrollRequest)));
  }

  public Either<ErrorItem, ScrollableEntry<IpvDataNavValueView>> getNavValues(
      Authentication auth,
      String groupId,
      IpvDataProviderValueFilter filter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(IpvDataNavValueView.class);
    return ipvDataGroup(auth, groupId)
        .map(
            g ->
                ipvDataRepository.getNavValueViews(
                    groupId,
                    filter,
                    viewQueryTranslator.translate(tableFilter),
                    viewQueryTranslator.translate(scrollRequest)));
  }

  public Either<ErrorItem, IpvDataProviderValueView> getValue(
      Authentication auth,
      String groupId,
      String id,
      BitemporalDate stateDate,
      IpvDataType dataType) {
    return ipvDataGroup(auth, groupId)
        .flatMap(
            v ->
                isEntityLevelNav(stateDate, dataType)
                    ? companyLegalEntityNavRepository.getValueView(groupId, id, stateDate)
                    : ipvDataRepository.getValueView(groupId, id, stateDate));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateValue(
      Authentication auth,
      String groupId,
      String id,
      IpvDataProviderValueUpdateForm form,
      IpvDataType dataType,
      LocalDate stateDate) {
    return performWithGroupUpdate(
        auth,
        groupId,
        () ->
            isEntityLevelNav(BitemporalDate.newOf(stateDate), dataType)
                ? companyLegalEntityNavRepository.updateValue(id, form)
                : ipvDataRepository.updateValue(id, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveValue(
      Authentication auth,
      String groupId,
      String id,
      ArchiveForm form,
      IpvDataType dataType,
      LocalDate stateDate) {
    String navLevel = ipvDataRepository.getGlobalNavLevelSettings(BitemporalDate.newOf(stateDate));
    boolean isEntityLevelNav = navLevel.equals("ENTITY_LEVEL") && dataType.equals(IpvDataType.NAV);
    return performWithGroupUpdate(
        auth,
        groupId,
        () ->
            isEntityLevelNav
                ? companyLegalEntityNavRepository.archiveValue(id, form)
                : ipvDataRepository.archiveValue(id, form));
  }

  public Either<List<ErrorItem>, EntityId> upload(
      Authentication auth,
      String groupId,
      byte[] csvBytes,
      ImportOptions importOptions,
      IpvDataType dataType) {
    return ipvDataGroup(auth, groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            e ->
                uploadService
                    .upload(groupId, csvBytes, importOptions, dataType)
                    .flatMap(
                        id -> groupUpdated(groupId, e, id).leftMap(ErrorItem.ListOfErrors::from)))
        .flatMap(
            groupEntityId ->
                initiateIpvDataValueCreation(
                    groupEntityId, dataType, importOptions.getStateDate()));
  }

  public Either<List<ErrorItem>, ValidationResponse> validateFile(
      Authentication auth,
      String groupId,
      ParsingMode parsingMode,
      byte[] csvBytes,
      IpvDataType dataType) {
    return ipvDataGroup(auth, groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(UserEntity::getView)
        .flatMap(view -> uploadService.validateFile(view.getId(), parsingMode, csvBytes, dataType));
  }

  private Either<List<ErrorItem>, EntityId> initiateIpvDataValueCreation(
      EntityId groupEntityId, IpvDataType dataType, LocalDate stateDate) {
    boolean isEntityLevelNav = isEntityLevelNav(BitemporalDate.newOf(stateDate), dataType);

    if (isEntityLevelNav) {
      ipvDataModificationHandler.onCompanyLegalEntityNavDataValueCreation(groupEntityId.getId());
    } else {
      ipvDataModificationHandler.onIpvDataValueCreation(groupEntityId.getId());
    }

    return Either.right(groupEntityId);
  }

  private Either<ErrorItem, EntityId> performWithGroupUpdate(
      Authentication auth, String groupId, Supplier<Either<ErrorItem, EntityId>> modifyFn) {
    return ipvDataGroup(auth, groupId)
        .flatMap(u -> modifyFn.get().flatMap(id -> groupUpdated(groupId, u, id)));
  }

  private Either<ErrorItem, EntityId> groupUpdated(
      String groupId, UserEntity<IpvDataGroupView> u, EntityId resultId) {
    return ipvDataGroupRepository.groupUpdated(u.getUser(), groupId).map(id -> resultId);
  }

  private Either<ErrorItem, UserEntity<IpvDataGroupView>> ipvDataGroup(
      Authentication auth, String groupId) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u ->
                ipvDataGroupRepository
                    .ipvDataGroupView(u, groupId)
                    .map(v -> UserEntity.ipvDataGroup(u, v)));
  }

  private boolean isEntityLevelNav(BitemporalDate date, IpvDataType dataType) {
    String navLevel = ipvDataRepository.getGlobalNavLevelSettings(date);
    return "ENTITY_LEVEL".equals(navLevel) && IpvDataType.NAV.equals(dataType);
  }
}
