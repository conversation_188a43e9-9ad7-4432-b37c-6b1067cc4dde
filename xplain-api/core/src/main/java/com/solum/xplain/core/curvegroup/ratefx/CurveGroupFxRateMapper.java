package com.solum.xplain.core.curvegroup.ratefx;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView;
import java.math.BigDecimal;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = {CollectionUtils.class},
    nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface CurveGroupFxRateMapper extends VersionedEntityMapper<CurveGroupFxRates> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "comment", source = "versionForm.comment")
  @Mapping(target = "validFrom", source = "versionForm.validFrom")
  CurveGroupFxRates fromForm(CurveGroupFxRatesForm form, @MappingTarget CurveGroupFxRates r);

  @Mapping(target = "versionForm", ignore = true)
  CurveGroupFxRatesForm toForm(CurveGroupFxRates entity);

  CurveGroupFxRatesNodeForm toNodeForm(CurveGroupFxRatesNode entity);

  CurveGroupFxRatesNode fromForm(CurveGroupFxRatesNodeForm form);

  CurveGroupFxRatesNodeValueView toNodeValueView(CurveGroupFxRatesNode node, BigDecimal value);

  @Mapping(target = "numberOfFxRates", expression = "java(CollectionUtils.size(rates.getNodes()))")
  CurveGroupFxRatesView toView(CurveGroupFxRates rates);
}
