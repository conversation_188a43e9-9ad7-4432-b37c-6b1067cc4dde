package com.solum.xplain.core.common.validation;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Collection;
import java.util.function.Supplier;

@Documented
@Constraint(validatedBy = ValidStringMapKeysValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Repeatable(ValidStringMapKeys.List.class)
public @interface ValidStringMapKeys {
  Class<? extends Supplier<Collection<String>>>[] valueForKeys();

  String supplierArgumentForKeys() default "";

  String message() default
      "{com.solum.xplain.api.common.validation" + ".ValidStringMapKeys.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  @Target(ElementType.FIELD)
  @Retention(RUNTIME)
  @Documented
  @interface List {

    ValidStringMapKeys[] value();
  }
}
