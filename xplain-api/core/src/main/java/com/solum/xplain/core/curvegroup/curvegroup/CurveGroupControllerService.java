package com.solum.xplain.core.curvegroup.curvegroup;

import static com.solum.xplain.core.common.EntityId.entityId;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupCountedView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityService;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveGroupControllerService {

  private final CurveGroupRepository curveGroupRepository;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;
  private final CurveGroupFxVolatilityService curveGroupFxVolatilityService;

  public CurveGroupControllerService(
      CurveGroupRepository curveGroupRepository,
      InstrumentMarketKeyDefinitionExportService definitionExportService,
      CurveGroupFxVolatilityService curveGroupFxVolatilityService) {
    this.curveGroupRepository = curveGroupRepository;
    this.definitionExportService = definitionExportService;
    this.curveGroupFxVolatilityService = curveGroupFxVolatilityService;
  }

  public ScrollableEntry<CurveGroupCountedView> getAll(
      LocalDate stateDate,
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      CurveGroupFilter filter) {
    return curveGroupRepository.curveGroupCountedList(
        stateDate, scrollRequest, tableFilter, filter);
  }

  @Transactional
  public Either<ErrorItem, EntityId> create(CurveGroupForm newForm) {
    var curveGroupResult = curveGroupRepository.insert(newForm).map(p -> entityId(p.getId()));
    curveGroupResult.flatMap(
        curveGroup -> curveGroupFxVolatilityService.createDefault(curveGroup.getId()));
    return curveGroupResult;
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(String id, CurveGroupForm edit) {
    return getCurveGroup(id)
        .flatMap(p -> curveGroupRepository.update(p, edit))
        .flatMap(u -> curveGroupRepository.clearCalibrationResults(id).map(r -> u));
  }

  private Either<ErrorItem, CurveGroupView> getCurveGroup(String id) {
    return curveGroupRepository.getEither(id);
  }

  public Either<ErrorItem, CurveGroupCountedView> get(String id, LocalDate stateDate) {
    return curveGroupRepository.getCountedEither(stateDate, id);
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveCurveGroup(String curveGroupId) {
    return getCurveGroup(curveGroupId).flatMap(u -> curveGroupRepository.archive(curveGroupId));
  }

  public Either<ErrorItem, FileResponseEntity> getAllCurveGroupMDKDefinitionsCsvBytes(
      String groupId, BitemporalDate stateDate, String configurationId) {
    var instruments = curveGroupRepository.allInstruments(groupId, stateDate);
    return definitionExportService.allInstrumentMdkDefinitions(
        instruments, stateDate.getActualDate(), configurationId);
  }
}
