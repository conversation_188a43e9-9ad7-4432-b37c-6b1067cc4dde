package com.solum.xplain.core.common.validation;

import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.common.PayReceive;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class PayReceiveSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.of(PayReceive.values()).map(Named::getName).toList();
  }
}
