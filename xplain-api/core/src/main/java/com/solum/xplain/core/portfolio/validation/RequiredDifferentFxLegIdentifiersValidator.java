package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.CommonFxTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentFxLegIdentifiersValidator
    implements ConstraintValidator<RequiredDifferentFxLegIdentifiers, CommonFxTradeForm> {

  public boolean isValid(CommonFxTradeForm form, ConstraintValidatorContext context) {
    return form.getPayLegExtIdentifier() == null
        || form.getReceiveLegExtIdentifier() == null
        || !form.getPayLegExtIdentifier().equalsIgnoreCase(form.getReceiveLegExtIdentifier());
  }
}
