package com.solum.xplain.core.ipv.nav.entity;

import com.solum.xplain.core.datavalue.VersionedValue;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@Data
@FieldNameConstants
public class NavVersion extends VersionedValue {

  @Nullable private String currency;

  @Override
  public boolean valueEquals(Object value) {
    if (value instanceof NavVersion ipvValue) {
      return super.valueEquals(value) && Objects.equals(this.currency, ipvValue.currency);
    }
    return false;
  }
}
