package com.solum.xplain.core.company.form;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class ValuationSettingsFormGroupProvider
    implements DefaultGroupSequenceProvider<ValuationSettingsForm> {
  public static List<Class<?>> validationGroups(String configurationType) {
    CurveConfigurationType type = tryParseConfiguration(configurationType);
    if (configurationType != null && type != null) {
      return switch (type) {
        case SINGLE -> List.of(SingleConfigurationGroup.class);
        case FX_V_IRS -> List.of(FxVsIrsConfigurationGroup.class);
      };
    }
    return List.of();
  }

  private static CurveConfigurationType tryParseConfiguration(String type) {
    try {
      return CurveConfigurationType.valueOf(type);
    } catch (Exception e) {
      return null;
    }
  }

  @Override
  public List<Class<?>> getValidationGroups(ValuationSettingsForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(ValuationSettingsForm.class);
    if (form != null) {

      if (form.getSettingsType() != null && tryParseSettingsType(form.getSettingsType()) != null) {
        switch (CompanySettingsType.valueOf(form.getSettingsType())) {
          case DEFAULT -> builder.add(DefaultValuationSettingsGroup.class);
          case BESPOKE -> builder.add(BespokeValuationSettingFormGroup.class);
          default -> {}
        }
      }
      builder.addAll(validationGroups(form.getConfigurationType()));
    }

    return builder.build();
  }

  private CompanySettingsType tryParseSettingsType(String type) {
    try {
      return CompanySettingsType.valueOf(type);
    } catch (Exception e) {
      return null;
    }
  }
}
