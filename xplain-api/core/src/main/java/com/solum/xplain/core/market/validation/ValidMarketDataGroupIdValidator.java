package com.solum.xplain.core.market.validation;

import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class ValidMarketDataGroupIdValidator
    implements ConstraintValidator<ValidMarketDataGroupId, String> {

  private final MarketDataGroupRepository repository;

  public ValidMarketDataGroupIdValidator(MarketDataGroupRepository repository) {
    this.repository = repository;
  }

  public boolean isValid(String obj, ConstraintValidatorContext context) {
    if (StringUtils.isEmpty(obj)) {
      return true;
    } else {
      return repository.dataGroupName(obj).isPresent();
    }
  }
}
