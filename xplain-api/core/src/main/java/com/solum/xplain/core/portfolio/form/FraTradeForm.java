package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.FRA;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.DayCountsSupplier;
import com.solum.xplain.core.common.validation.IborIndicesSupplier;
import com.solum.xplain.core.common.validation.PositionTypeSupplier;
import com.solum.xplain.core.common.validation.UpperCase;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFraDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.ValidPaymentPeriod;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.HasPaymentPeriod;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;

@Data
@ValidPaymentPeriod
@FieldNameConstants
@Slf4j
public class FraTradeForm extends BespokeTradeForm
    implements ParsableToTradeValue, HasPaymentPeriod {

  @NotNull
  @ValidStringSet(PositionTypeSupplier.class)
  @Schema(
      description =
          "Whether the FRA is buy or sell. A value of 'BUY' implies that the floating rate is received from the counterparty.")
  private String position;

  @NotEmpty
  @ValidStringSet(BusinessDayConventionsSupplier.class)
  @Schema(
      description =
          "Convention defining how to adjust a date if it falls on a day other than a business day.")
  private String businessDayConvention;

  @NotNull
  @Schema(description = "The start date, which is the effective date of the FRA.")
  private LocalDate startDate;

  @NotNull
  @Schema(description = "The end date, which is the termination date of the FRA.")
  private LocalDate endDate;

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  @Schema(description = "This is the currency of the FRA and the currency that payment is made in.")
  private String notionalCurrency;

  @Null(groups = ReferenceTradeGroup.class)
  @NotNull(groups = BespokeTradeGroup.class)
  @Positive
  @Schema(
      description =
          "The notional amount. The notional expressed here must be positive. Must be null for reference securities.")
  private Double notionalValue;

  @NotEmpty
  @ValidStringSet(DayCountsSupplier.class)
  @Schema(description = "The day count convention applicable.")
  private String dayCount;

  @NotEmpty
  @ValidStringSet(value = IborIndicesSupplier.class)
  @Schema(
      description =
          "The Ibor index. The floating rate to be paid is based on this index. It will be a well known market index such as 'GBP-LIBOR-3M'.")
  private String calculationIborIndex;

  @NotNull
  @Schema(description = "The fixed rate of interest. A 5% rate will be expressed as 0.05.")
  private Double calculationFixedRateInitialValue;

  @UpperCase
  @ValidIdentifier
  @Schema(description = "Optional external identifier for implied Ibor leg.")
  private String extIborLegIdentifier;

  @UpperCase
  @ValidIdentifier
  @Schema(description = "Optional external identifier for implied fixed leg.")
  private String extFixedLegIdentifier;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return createTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(FRA, details));
  }

  private Either<ErrorItem, TradeDetails> createTradeDetails(TradeInfoDetails tradeInfo) {
    try {
      ResolvableFraDetails result =
          ResolvableFraDetails.builder()
              .startDate(startDate)
              .endDate(endDate)
              .businessDayConvention(businessDayConvention)
              .currency(Currency.parse(notionalCurrency))
              .notional(notionalValue)
              .index(calculationIborIndex)
              .initialValue(calculationFixedRateInitialValue)
              .positionType(PositionType.valueOf(position))
              .dayCount(DayCount.of(dayCount))
              .extIborLegIdentifier(extIborLegIdentifier)
              .extFixedLegIdentifier(extFixedLegIdentifier)
              .build();
      return Either.right(result.toTradeDetails(tradeInfo));
    } catch (RuntimeException ex) {
      log.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }

  @Override
  protected String tradeCurrency() {
    return notionalCurrency;
  }
}
