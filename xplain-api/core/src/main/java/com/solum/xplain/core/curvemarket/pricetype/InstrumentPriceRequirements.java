package com.solum.xplain.core.curvemarket.pricetype;

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

@Data
@AllArgsConstructor
public class InstrumentPriceRequirements implements Serializable {
  @NonNull private final InstrumentPriceType curvesPriceType;
  @NonNull private final InstrumentPriceType dscCurvesPriceType;
  @NonNull private final InstrumentPriceType fxRatesPriceType;
  @NonNull private final InstrumentPriceType volsPriceType;
  @NonNull private final InstrumentPriceType volsSkewsPriceType;

  public static InstrumentPriceRequirements bidRequirements() {
    return new InstrumentPriceRequirements(BID_PRICE, BID_PRICE, BID_PRICE, BID_PRICE, BID_PRICE);
  }
}
