package com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility;

import static com.solum.xplain.core.common.EntityId.entityId;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.GroupedItemsImportService;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityRepository;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FxVolatilityNodeCsvImportService
    extends GroupedItemsImportService<
        CurveGroupFxVolatility, CurveGroupFxVolatilityNodeForm, FxVolatilityNodeKey> {

  private final FxVolatilityNodeCsvLoader csvLoader;
  private final CurveGroupFxVolatilityMapper mapper;
  private final CurveGroupFxVolatilityRepository repository;

  public FxVolatilityNodeCsvImportService(
      AuditEntryService auditEntryService,
      FxVolatilityNodeCsvLoader csvLoader,
      CurveGroupFxVolatilityMapper mapper,
      CurveGroupFxVolatilityRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.mapper = mapper;
    this.repository = repository;
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> importVolatilities(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    // NOTE: if FX Volatility does not exist - it will be created
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(
            forms ->
                importNodes(
                    fetchVolatility(groupId, importOptions.getStateDate()),
                    importOptions,
                    forms.getParsedLines()))
        .fold(
            e -> toErrorReturn(importOptions.getDuplicateAction(), e),
            result -> toReturn(importOptions.getDuplicateAction(), result))
        .map(r -> entityId(groupId));
  }

  protected List<LogItem> importNodes(
      CurveGroupFxVolatility volatility,
      ImportOptions importOptions,
      List<CurveGroupFxVolatilityNodeForm> nodes) {
    var importItems =
        ImportItems
            .<CurveGroupFxVolatilityNodeForm, FxVolatilityNodeKey, CurveGroupFxVolatilityNodeForm>
                builder()
            .existingActiveItems(nodeForms(volatility))
            .existingItemToKeyFn(FxVolatilityNodeKey::from)
            .importItems(nodes)
            .importItemToKeyFn(FxVolatilityNodeKey::from)
            .build();
    return importForEntity(volatility, importItems, importOptions);
  }

  private List<CurveGroupFxVolatilityNodeForm> nodeForms(CurveGroupFxVolatility volatility) {
    return volatility.getNodes().stream().map(mapper::toNodeForm).toList();
  }

  @Override
  public String getCollection() {
    return CurveGroupFxVolatility.CURVE_GROUP_FX_VOLATILITY_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "FX Volatility nodes";
  }

  @Override
  protected String entityIdentifier(CurveGroupFxVolatility curve) {
    return "FX Volatility";
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      CurveGroupFxVolatility volatility,
      List<CurveGroupFxVolatilityNodeForm> nodeForms,
      NewVersionFormV2 versionForm) {
    CurveGroupFxVolatilityForm form = mapper.toForm(volatility);
    form.setVersionForm(versionForm);
    form.setNodes(nodeForms);
    form.setSkews(skewForms(volatility));
    return update(volatility, form);
  }

  private List<FxVolatilitySkewForm> skewForms(CurveGroupFxVolatility volatility) {
    return volatility.getNodes().stream()
        .filter(CurveGroupFxVolatilityNode::hasSkew)
        .map(mapper::toSkewForm)
        .distinct()
        .toList();
  }

  @Override
  protected boolean hasFutureVersions(CurveGroupFxVolatility volatility, LocalDate stateDate) {
    return repository.getFutureVersions(volatility.getEntityId(), stateDate).notEmpty();
  }

  private Either<ErrorItem, EntityId> update(
      CurveGroupFxVolatility e, CurveGroupFxVolatilityForm f) {
    return repository.createVolatility(e.getEntityId(), f); // Will update if exists
  }

  private CurveGroupFxVolatility fetchVolatility(String groupId, LocalDate stateDate) {
    return repository
        .getActiveVolatility(groupId, BitemporalDate.newOf(stateDate))
        .getOrElse(defaultFxVolatility(groupId));
  }

  private CurveGroupFxVolatility defaultFxVolatility(String groupId) {
    var e = CurveGroupFxVolatility.newOf(groupId);
    e.setValidFrom(NewVersionFormV2.ROOT_DATE);
    return e;
  }
}
