package com.solum.xplain.core.portfolio.value;

import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CurrencyAmountForm {

  @NotEmpty
  @Schema(description = "Currency")
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private String currency;

  @NotNull(groups = BespokeTradeGroup.class)
  @Schema(description = "Amount. (Null for reference trades)")
  private Double amount;

  public static CurrencyAmountForm of(CurrencyAmount currencyAmount) {
    CurrencyAmountForm form = new CurrencyAmountForm();
    form.setAmount(currencyAmount.getAmount());
    form.setCurrency(currencyAmount.getCurrency().getCode());
    return form;
  }

  public static CurrencyAmountForm of(String currency, Double amount) {
    CurrencyAmountForm form = new CurrencyAmountForm();
    form.setAmount(amount);
    form.setCurrency(currency);
    return form;
  }
}
