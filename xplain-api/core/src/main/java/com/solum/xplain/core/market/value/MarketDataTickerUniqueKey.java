package com.solum.xplain.core.market.value;

import static java.lang.String.format;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MarketDataTickerUniqueKey {

  private String marketDataKey;
  private String providerCode;

  public static MarketDataTickerUniqueKey fromForm(MarketDataKeyProviderCsvForm form) {
    return new MarketDataTickerUniqueKey(form.getKey(), form.getCode());
  }

  public String uniqueString() {
    return format("Key: %s Provider: %s", marketDataKey, providerCode);
  }
}
