package com.solum.xplain.core.portfolio.trade;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.classifiers.conventions.SwapLegKey;
import com.solum.xplain.core.portfolio.value.CalculationType;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class TradeLegDetails implements Serializable {
  private String extLegIdentifier;
  private PayReceive payReceive;
  private CalculationType type;
  private Double notional;

  /**
   * For swaps, the amount that will be paid or received at the start of the period in the same
   * currency as the notional which will be paid or received at the end of the period.
   */
  private Double nearNotional;

  private String currency;

  private String accrualFrequency;
  private String accrualMethod;

  private String paymentFrequency;
  private String paymentCompounding;
  private int paymentOffsetDays;

  private String index;
  private Integer fixingDateOffsetDays;
  private String dayCount;
  private Double initialValue;
  private String inflationLag;
  private String indexCalculationMethod;
  private Integer overnightRateCutOffDays;
  private Boolean isOffshore;

  public SwapLegKey key() {
    if (type == CalculationType.FIXED) {
      return SwapLegKey.fixedLeg();
    }
    return new SwapLegKey(type, index);
  }

  public Currency currency() {
    return Currency.of(currency);
  }
}
