package com.solum.xplain.core.company;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_COMPANY_VALUATION_SETTINGS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_COMPANY_VALUATION_SETTINGS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.form.ValuationSettingsForm;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.company.value.CompanyValuationSettingsView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/companies/{companyId}/valuations")
public class CompanyValuationSettingsController {

  private final CompanyValuationSettingsService service;

  public CompanyValuationSettingsController(CompanyValuationSettingsService service) {
    this.service = service;
  }

  @Operation(summary = "Get company valuation settings")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_VALUATION_SETTINGS)
  public ResponseEntity<CompanyValuationSettingsView> getCompanyValuationSettings(
      Authentication auth,
      @RequestParam("stateDate") LocalDate stateDate,
      @PathVariable String companyId) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemResponse(
        service.getCompanyValuationSettings(auth, companyId, bitemporalStateDate));
  }

  @Operation(summary = "Update company valuation settings")
  @CommonErrors
  @PutMapping("/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> updateValuationSettings(
      @Valid @RequestBody ValuationSettingsForm newForm,
      @PathVariable LocalDate version,
      @PathVariable String companyId,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.updateDefaultSettingsVersion(companyId, version, authentication, newForm));
  }

  @Operation(summary = "Deletes (sets status to DELETED) valuation settings")
  @PutMapping("/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> deleteValuationSettings(
      Authentication auth, @PathVariable String companyId, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteValuationSettings(auth, companyId, version));
  }

  @Operation(summary = "Get default settings versions")
  @CommonErrors
  @GetMapping("/versions")
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_VALUATION_SETTINGS)
  public ResponseEntity<List<CompanyValuationSettingsView>> getValuationSettingsVersions(
      Authentication auth, @PathVariable String companyId) {
    return eitherErrorItemResponse(service.getCompanySettingsVersions(auth, companyId));
  }

  @Operation(summary = "Gets default settings versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_VALUATION_SETTINGS)
  public ResponseEntity<DateList> getValuationSettingsFutureVersionsDates(
      Authentication auth,
      @PathVariable("companyId") String companyId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getValuationSettingsFutureVersions(auth, companyId, stateDate));
  }

  @Operation(summary = "Get company entities valuation settings")
  @CommonErrors
  @GetMapping("/entities")
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  public ResponseEntity<List<CompanyLegalEntityValuationSettingsView>>
      getCompanyEntitiesSettingsList(
          Authentication auth, @PathVariable String companyId, @RequestParam LocalDate stateDate) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemResponse(
        service.getAllEntitySettings(auth, companyId, bitemporalStateDate));
  }

  @Operation(summary = "Get company entities valuation settings")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  @GetMapping("/entities/{entityId}/{version}")
  public ResponseEntity<CompanyLegalEntityValuationSettingsView> getCompanyEntityValuationSettings(
      Authentication auth,
      @PathVariable String companyId,
      @PathVariable String entityId,
      @PathVariable LocalDate version) {
    var bitemporalStateDate = BitemporalDate.newOf(version);
    return eitherErrorItemResponse(
        service.getEntitySettings(auth, companyId, entityId, bitemporalStateDate));
  }

  @Operation(summary = "Deletes (sets status to DELETED) entity valuation settings")
  @PutMapping("/entities/{entityId}/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> deleteEntityValuationSettings(
      Authentication auth,
      @PathVariable String companyId,
      @PathVariable String entityId,
      @PathVariable LocalDate version) {
    return eitherErrorItemResponse(
        service.deleteEntityValuationSettings(auth, companyId, entityId, version));
  }

  @Operation(summary = "Get company entity valuation settings versions list")
  @CommonErrors
  @GetMapping("/entities/{entityId}/versions")
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  public ResponseEntity<List<CompanyLegalEntityValuationSettingsView>>
      getCompanyEntityValuationSettingsVersions(
          Authentication auth, @PathVariable String companyId, @PathVariable String entityId) {
    return eitherErrorItemResponse(
        service.getCompanyEntitySettingsVersions(auth, companyId, entityId));
  }

  @Operation(summary = "Update company entity valuation settings")
  @CommonErrors
  @PutMapping("/entities/{entityId}/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> updateCompanyEntityValuationSettings(
      @Valid @RequestBody ValuationSettingsForm newForm,
      @PathVariable LocalDate version,
      @PathVariable String entityId,
      @PathVariable String companyId,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.updateCompanyEntitySettingsVersion(
            companyId, entityId, version, authentication, newForm));
  }

  @Operation(summary = "Gets company entity settings versions dates")
  @GetMapping("/entities/{entityId}/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS)
  public ResponseEntity<DateList> getCompanyEntitiesVersionsDates(
      Authentication auth,
      @PathVariable String entityId,
      @PathVariable String companyId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getLegalEntitiesSettingsFutureVersions(auth, companyId, entityId, stateDate));
  }
}
