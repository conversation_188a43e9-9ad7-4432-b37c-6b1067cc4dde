package com.solum.xplain.core.curvegroup.volatilityfx.value;

import com.solum.xplain.core.common.value.MatrixNode;
import com.solum.xplain.core.curvegroup.volatilityfx.HasFxPair;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class FxVolatilityNodeView implements MatrixNode, HasFxPair {

  private String nodeId;
  private String expiry;
  private String domesticCurrency;
  private String foreignCurrency;

  private Integer delta1;
  private Integer delta2;

  private String key;
  private String deltaVolatility1StrangleKey;
  private String deltaVolatility2StrangleKey;
  private String deltaVolatility1RiskReversalKey;
  private String deltaVolatility2RiskReversalKey;

  @Override
  public String getColumn() {
    return getFxPair();
  }

  @Override
  public String getRow() {
    return getExpiry();
  }
}
