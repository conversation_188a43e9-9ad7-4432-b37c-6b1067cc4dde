package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.xccyTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.xccyTradeFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.SwapLegFormBuilder
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.XccyTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [XccyController])
@Import([TestSecurityConfig])
class XccyControllerTest extends Specification {

  static String URI = "/portfolio/{id}/trades/xccy"
  static String PORTFOLIO_ID = "000000000000000000000001"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should get xccy"() {
    setup:
    def trade = PortfolioItemBuilder.trade(CoreProductType.XCCY, TradeDetailsBuilder.xccySwap(now()))
    1 * service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(trade)

    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
      .param("stateDate", "2020-01-01")
      .with(userWithAuthority(VIEW_TRADE))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new xccy with form #form, role #role, and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as XccyTradeForm) >> right(entityId("1"))
    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                                                                 | code | response
    VIEW_TRADE   | xccyTradeForm()                                                                                      | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | xccyTradeForm()                                                                                      | 200  | """{"id":"1"}"""
    MODIFY_TRADE | xccyTradeFormWith("tradeDate", null)                                                                 | 200  | """{"id":"1"}"""
    MODIFY_TRADE | xccyTradeFormWith("startDate", null)                                                                 | 412  | "NotNull.xccyTradeForm.startDate"
    MODIFY_TRADE | xccyTradeFormWith("endDate", null)                                                                   | 412  | "NotNull.xccyTradeForm.endDate"
    MODIFY_TRADE | xccyTradeFormWith("leg1", null)                                                                      | 412  | "NotNull.xccyTradeForm.leg1"
    MODIFY_TRADE | xccyTradeFormWith("leg2", null)                                                                      | 412  | "NotNull.xccyTradeForm.leg2"
    MODIFY_TRADE | xccyTradeFormWith("externalTradeId", null)                                                           | 412  | "NotEmpty.xccyTradeForm.externalTradeId"
    MODIFY_TRADE | xccyTradeFormWith("externalTradeId", "WITH SPACES")                                                  | 412  | "ValidIdentifier.xccyTradeForm.externalTradeId"
    MODIFY_TRADE | xccyTradeFormWith("externalTradeId", "lowercase")                                                    | 412  | "ValidIdentifier.xccyTradeForm.externalTradeId"
    MODIFY_TRADE | xccyTradeFormWith("leg1", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.PAY, "6M"))     | 200  | """{"id":"1"}"""
    MODIFY_TRADE | xccyTradeFormWith("leg1", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.PAY, "3M"))     | 412  | "ValidIborLegAccrualFrequency.xccyTradeForm.leg1"
    MODIFY_TRADE | xccyTradeFormWith("leg1", SwapLegFormBuilder.withPayReceiveAndPaymentFreq(PayReceive.PAY, "2M"))     | 412  | "ValidStringSet.xccyTradeForm.leg1.paymentFrequency"
    MODIFY_TRADE | xccyTradeFormWith("leg2", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.RECEIVE, "8M")) | 412  | "ValidStringSet.xccyTradeForm.leg2.accrualFrequency"
    MODIFY_TRADE | xccyTradeFormWith("leg2", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.PAY, "3M"))     | 412  | "Different currencies required"
  }

  def "should update XCCY when role #role then responseStatus #responseStatus and response #response"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2020-01-01"), _ as XccyTradeForm, "1") >> right(entityId("1"))
    when:
    def results = mockMvc.perform(put(URI + "/1/2020-01-01", PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(xccyTradeForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseStatus
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | responseStatus | response
    VIEW_TRADE   | 403            | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | 200            | """{"id":"1"}"""
  }
}
