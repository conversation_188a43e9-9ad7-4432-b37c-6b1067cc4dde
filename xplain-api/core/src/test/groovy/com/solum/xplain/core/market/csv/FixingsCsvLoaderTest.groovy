package com.solum.xplain.core.market.csv

import static com.opengamma.strata.collect.io.ResourceLocator.ofClasspath
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static java.time.LocalDate.parse
import static org.hamcrest.Matchers.startsWith
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.fixings.csv.FixingsCsvLoader
import spock.lang.Specification

class FixingsCsvLoaderTest extends Specification {

  def "should parse fixings csv"() {
    setup:
    def csvBytes = ofClasspath("/market/csv/Fixings.csv").getByteSource().read()
    def loader = new FixingsCsvLoader()

    when:
    def parsedData = loader.parse(csvBytes, STRICT)

    then:
    parsedData.isRight()
    parsedData.getOrNull().warnings.size() == 0
    parsedData.getOrNull().parsedLines.size() == 3
    parsedData.getOrNull().parsedLines[0].date == parse("2017-08-15")
    parsedData.getOrNull().parsedLines[0].reference == "EUR-EURIBOR-3M"
    parsedData.getOrNull().parsedLines[0].value == -0.00329
    parsedData.getOrNull().parsedLines[1].date == parse("2017-07-31")
    parsedData.getOrNull().parsedLines[1].publicationDate == parse("2017-09-26")
    parsedData.getOrNull().parsedLines[1].value == 0.0131417
  }

  def "should throw error if missing header"() {
    setup:
    def csvString = """\
        "EUR-EURIBOR-3M",2017-08-15,-0.00329
        "EUR-EURIBOR-3M",2017-08-14,-0.00329
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")
    def loader = new FixingsCsvLoader()

    when:
    def parsedData = loader.parse(csvBytes, STRICT)

    then:
    parsedData.isLeft()
    that parsedData.left().get().get(0).description, startsWith("Missing header:")
  }

  def "should throw error if missing data"() {
    setup:
    def csvString = """\
        Reference,Date,Publication Date,Value
        EUR-EURIBOR-3M,,,-0.00329
        EUR-EURIBOR-3M,2017-08-14,,-0.00329
        ,2017-08-11,,-0.00329
        AUD-BBSW-12M,2017-08-14,,-0.00329        
        GB-RPI,2017-08-14,,-0.00329        
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")
    def loader = new FixingsCsvLoader()

    when:
    def parsedData = loader.parse(csvBytes, STRICT)

    then:
    parsedData.isLeft()
    def errors = parsedData.left().get() as List<ErrorItem>

    errors.size() == 4
    errors[0].description == "Error parsing line 2: No value was found for 'Date'"
    errors[1].description == "Error parsing line 4: No value was found for 'Reference'"
    errors[2].description.startsWith("Error parsing line 5: Unsupported value: AUD-BBSW-12M")
    errors[3].description == "Error parsing line 6: Error parsing field Date. Inflation fixing must have end of month date"
  }

  def "should not allow import fixings with duplicate date and reference"() {
    setup:
    def bytes = """\
        "Reference","Date","Publication Date","Value"
        "EUR-EURIBOR-3M",2017-08-15,,-0.00329
        "EUR-EURIBOR-3M",2017-08-15,,-1
        """.stripIndent().bytes

    when:
    def result = new FixingsCsvLoader().parse(bytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].description == "Error parsing lines [2,3]: Duplicate entry found: EUR-EURIBOR-3M - 2017-08-15"
  }
}
