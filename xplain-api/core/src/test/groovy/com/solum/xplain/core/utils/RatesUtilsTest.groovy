package com.solum.xplain.core.utils

import static com.solum.xplain.core.calibration.CurveSample.discountCurve
import static com.solum.xplain.core.calibration.CurveSample.indexCurve

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import java.time.LocalDate
import spock.lang.Specification

class RatesUtilsTest extends Specification {

  def "should add tenors #t1 and #t2 with result #sum"() {
    setup:
    def valuationDate = LocalDate.parse("2016-01-01")

    def dscCurve = discountCurve()
    def r1 = ImmutableRatesProvider
      .builder(valuationDate)
      .discountCurve(Currency.EUR, dscCurve)
      .build()

    def idxCurve = indexCurve()
    def r2 = ImmutableRatesProvider
      .builder(valuationDate)
      .indexCurve(IborIndices.EUR_EURIBOR_1M, idxCurve)
      .build()
    def marketData = Mock(MarketData)
    1 * marketData.getValuationDate() >> valuationDate

    when:
    def result = RatesUtils.combine(marketData, [r1, r2])

    then:
    result.getValuationDate() == valuationDate
    result.getCurves().size() == 2
    result.getCurves().get(dscCurve.getName()) == dscCurve
    result.getCurves().get(idxCurve.getName()) == idxCurve
  }
}
