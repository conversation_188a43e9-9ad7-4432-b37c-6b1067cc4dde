package com.solum.xplain.core.permissions

import com.solum.xplain.core.classifiers.Classifier
import com.solum.xplain.core.permissions.extension.PermissionCategory
import com.solum.xplain.core.permissions.provider.PermissionCategoryProvider
import spock.lang.Specification

class PermissionsClassifierProviderTest extends Specification {
  def aggregator = new PermissionCategoryAggregatorImpl([new TestCategoryProvider()])
  def provider = new PermissionsClassifierProvider(aggregator)

  def "should load permissions classifier"() {
    setup:
    def classifiers = provider.classifiers()
    expect:
    classifiers.size() == 1
    def classifier = classifiers[0]
    classifier.id == "taskSubCategoriesByCategories"
    classifier.values.size() == 1
    classifier.values[0].id == "ADMIN"
    classifier.values[0].values == [new Classifier("ROLES")]
  }


  static class TestCategoryProvider implements PermissionCategoryProvider {
    @Override
    List<PermissionCategory> permissionCategories() {
      return [CorePermissionCategory.ROLES]
    }
  }
}
