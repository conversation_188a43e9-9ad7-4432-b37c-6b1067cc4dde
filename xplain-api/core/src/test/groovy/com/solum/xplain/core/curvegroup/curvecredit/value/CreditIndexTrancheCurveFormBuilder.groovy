package com.solum.xplain.core.curvegroup.curvecredit.value

import com.solum.xplain.core.common.value.NewVersionFormV2
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDate

@Builder(builderStrategy = ExternalStrategy, forClass = CreditIndexTrancheCurveForm, includeSuperProperties = true)
class CreditIndexTrancheCurveFormBuilder {
  CreditIndexTrancheCurveFormBuilder() {
    recoveryRate(BigDecimal.ONE)
    currency("EUR")
    fixedCoupon(0.005)
    quoteConvention("POINTS_UPFRONT")
    docClause("CR14")
    sector("FINANCIALS")
    reference("REF")
    creditIndexSeries(1)
    creditIndexVersion(2)
    entityLongName("CDX_NA_HY")
    creditIndexTranche("0-15")
    creditIndexStartDate(LocalDate.parse("2022-01-04"))
    creditIndexFactor(BigDecimal.ONE)
    versionForm(NewVersionFormV2.newDefault())
  }

  static creditIndexTrancheCurveForm(Closure<CreditIndexTrancheCurveFormBuilder> closure = { a -> a }) {
    return new CreditIndexTrancheCurveFormBuilder().with(closure).build()
  }

  static creditIndexUpdateForm(Closure<CreditIndexTrancheCurveFormBuilder> closure = { a -> a }) {
    return (CreditIndexCurveUpdateForm) new CreditIndexTrancheCurveFormBuilder().with(closure).build()
  }
}
