package com.solum.xplain.core.portfolio.csv.loader

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.extensions.enums.PositionType
import io.atlassian.fugue.Either
import spock.lang.Specification

class FraTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def commonLoader = new FullSwapTradeCsvLoader()
  def loader = new FraTradeCsvLoader(commonLoader)

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CoreProductType.FRA)
  }

  def "should parse FRA trade"() {
    setup:
    def rows = loadResource("FRATrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 4
    parsedRows.stream().allMatch(Either::isRight)

    and: "inferred position type row parses correctly"
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AUD"))

    // non-fra fields
    result.businessDayAdjustmentType == null
    result.notionalScheduleInitialExchange == null
    result.notionalScheduleFinalExchange == null
    result.rollConvention == null
    result.stubConvention == null

    result.positionType == PositionType.SELL // Implied from legs

    def payLeg = result.payLeg
    payLeg.payReceive == PayReceive.PAY
    payLeg.paymentOffsetDays == 0
    payLeg.currency == "AUD"
    payLeg.notional == 1000000
    payLeg.index == "AUD-BBSW-6M"
    payLeg.type == CalculationType.IBOR
    !payLeg.isOffshore

    def receiveLeg = result.getReceiveLeg()
    receiveLeg.type == CalculationType.FIXED
    receiveLeg.payReceive == PayReceive.RECEIVE
    receiveLeg.paymentOffsetDays == 0
    receiveLeg.currency == "AUD"
    receiveLeg.notional == 1000000
    receiveLeg.dayCount == "Act/365F"

    and: "inferred pay/receive row parses correctly"
    def impliedPayLeg = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AUD")).payLeg
    impliedPayLeg.type == CalculationType.IBOR
    impliedPayLeg.payReceive == PayReceive.PAY
    def impliedReceiveLeg = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AUD")).receiveLeg
    impliedReceiveLeg.type == CalculationType.FIXED
    impliedReceiveLeg.payReceive == PayReceive.RECEIVE
  }

  def "should return FRA parse errors"() {
    setup:
    def rows = loadResource("FRATradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)
    def hasRefSecError = loader.parse(rows[row], true).isLeft()

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)
    hasRefSecError == refSecError

    where:
    row | refSecError | expectedError
    0   | true        | "Error at line number 2. Error: FRA can only contain Fixed and Ibor, legs"
    1   | true        | "Error at line number 3. Error: Unknown date format, must be formatted as 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy/M/d', 'd/M/yyyy', 'd-MMM-yyyy', 'dMMMyyyy', 'd/M/yy', 'd-MMM-yy' or 'dMMMyy' but was: 16-01-01"
    2   | true        | "Error at line number 4. Error: Error parsing field End Date. Must be after Start Date"
    3   | true        | "Error at line number 5. Error: IborIndex name not found: CH-CPI"
    4   | true        | "Error at line number 6. Error: Error parsing field Leg2.Ccy. Invalid currency USD for index EUR-EURIBOR-3M"
    5   | true        | "Error at line number 7. Error: Unsupported value: XXX for field Leg1.Ibor.DayCount. Supported values: [One/One, 30/360 ISDA, 30E/360, 30E/360 ISDA, 30U/360, Act/360, Act/365 Actual, Act/365F, Act/365L, Act/Act ISDA, Act/Act Year, Bus/252 BRBD]"
    6   | false       | "Error at line number 8. Error: Value must be positive for field: Leg2.Notional"
    7   | true        | "Error at line number 9. Error: FRA must not contain overnight legs."
    8   | true        | "Error at line number 10. Error: FRA must not contain inflation legs."
    9   | true        | "Error at line number 11. Error: FRA requires same currency for both legs."
    10  | false       | "Error at line number 12. Error: FRA requires same notional for both legs."
    11  | true        | "Error at line number 13. Error: FRA requires same day count for both legs."
    12  | false       | "Error at line number 14. Error: Error parsing field Leg1.Notional. No value was found for 'Leg1.Notional'"
    13  | false       | "Error at line number 15. Error: Error parsing field Leg1.Notional. No value was found for 'Leg1.Notional'"
    14  | true        | "Error at line number 16. Error: FRA with BUY requires Receive Ibor leg"
  }
}
