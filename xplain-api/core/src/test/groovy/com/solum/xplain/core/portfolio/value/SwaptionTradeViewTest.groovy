package com.solum.xplain.core.portfolio.value

import static java.time.LocalDate.parse

import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import spock.lang.Specification

class SwaptionTradeViewTest extends Specification {

  def "should create SwaptionTradeView"() {
    setup:
    def item = PortfolioItemBuilder.trade(CoreProductType.SWAPTION, TradeDetailsBuilder.swaptionTradeDetails())
    item.clientMetrics = new ClientMetrics(presentValue: 10)
    item.description = "Comment"
    item.tradeDetails.info.csaDiscountingGroup = "USD"

    when:
    def form = SwaptionTradeView.of(item)

    then:
    form.isRight()
    def result = form.getOrNull()
    result instanceof SwaptionTradeView
    result.tradeId == item.entityId
    result.leg1.extLegIdentifier == "PAY_ID"
    result.leg1.payReceive == "Pay"
    result.leg1.accrualFrequency == "12M"
    result.leg1.paymentFrequency == "12M"
    result.leg2.extLegIdentifier == "REC_ID"
    result.leg2.payReceive == "Receive"
    result.calendar == "EUTA"
    result.clientMetrics.presentValue == 10
    result.endDate == item.tradeDetails.endDate
    result.startDate == item.tradeDetails.startDate
    result.stubConvention == item.tradeDetails.stubConvention
    result.position == "BUY"
    result.expiryDate == parse("2018-04-12")
    result.expiryZone == "Europe/London"
    result.swaptionSettlementType == "CASH"
    result.businessDayAdjustmentType == "ACCRUAL_AND_PAYMENT"
    result.businessDayConvention == "ModifiedFollowing"
    result.tradeSettlementDate == parse("2018-05-23")
    result.description == "Comment"
    result.csaDiscountingGroup == "USD"
    result.externalIdentifiers == [new ExternalIdentifierForm("externalSource", "externalSourceId")]
  }
}
