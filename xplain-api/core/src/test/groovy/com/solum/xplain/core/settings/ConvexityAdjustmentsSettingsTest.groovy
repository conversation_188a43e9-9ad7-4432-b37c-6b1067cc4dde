package com.solum.xplain.core.settings

import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.pricer.model.HullWhiteOneFactorPiecewiseConstantParameters
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings
import com.solum.xplain.core.settings.entity.CurveConvexityAdjustment
import spock.lang.Specification

class ConvexityAdjustmentsSettingsTest extends Specification {

  def "should return empty if no convexity adjustments"() {
    setup:
    def s = new ConvexityAdjustmentsSettings()

    when:
    def result = s.definitions()

    then:
    result.isEmpty()
  }

  def "should return correct curve convexity adjustments definition"() {
    setup:
    def s = new ConvexityAdjustmentsSettings(
      curveConvexityAdjustments: [
        "EUR 3M": new CurveConvexityAdjustment(
        mean: 0.01d,
        zeroTenorVol: 0.05d,
        volatilities: ["3M": BigDecimal.valueOf(0.2d)]
        )
      ])

    when:
    def result = s.definitions()

    then:
    result.size() == 1
    result.get(IborIndices.EUR_EURIBOR_3M) != null
    result.get(IborIndices.EUR_EURIBOR_3M) ==
      HullWhiteOneFactorPiecewiseConstantParameters.of(
      0.01d,
      DoubleArray.of(0.05d, 0.2d),
      DoubleArray.of(0.25))
  }
}
