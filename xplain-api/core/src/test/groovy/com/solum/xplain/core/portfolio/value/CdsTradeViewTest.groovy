package com.solum.xplain.core.portfolio.value

import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.trade

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import java.time.LocalDate
import spock.lang.Specification

class CdsTradeViewTest extends Specification {
  def "should create from portfolio item"() {
    setup:
    def portfolioItem = trade(CoreProductType.CDS, TradeDetailsBuilder.cdsTradeDetails(LocalDate.now()))
    portfolioItem.tradeDetails.info.csaDiscountingGroup = "USD"

    when:
    def result = CdsTradeView.of(portfolioItem)

    then:
    result.isRight()
    def view = result.getOrNull()
    view.position == "BUY"
    view.reference == "SELF"
    view.seniority == "SNRFOR"
    view.corpTicker == "CORP"
    view.docClause == "CR14"
    view.currency == "EUR"
    view.calendar == "EUTA"
    view.entityLongName == "LONG NAME"
    view.notionalValue == 10000d
    view.startDate == LocalDate.now()
    view.endDate == LocalDate.now().plusYears(1)
    view.frequency == "3M"
    view.fixedRate == 2
    view.upfrontFee.amount == 10
    view.dayCount == "Act/360"
    view.csaDiscountingGroup == "USD"
  }
}
