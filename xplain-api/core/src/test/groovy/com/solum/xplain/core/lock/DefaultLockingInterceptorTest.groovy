package com.solum.xplain.core.lock

import static io.atlassian.fugue.Either.right

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.shared.datagrid.ClusterLock
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.web.method.HandlerMethod
import spock.lang.Specification

class DefaultLockingInterceptorTest extends Specification {

  class InterceptedClass {

    @SuppressWarnings("unused")
    void method() {}
  }

  XplainLockResolver lockResolver = Mock()

  LockingSupport lockingSupport = Mock()

  LockingInterceptor interceptor = new DefaultLockingInterceptor(lockResolver, lockingSupport, new ObjectMapper())

  def "should correctly pre handle lock"() {
    setup:
    def request = new MockHttpServletRequest()
    def response = new MockHttpServletResponse()
    def method = InterceptedClass.class.getMethod("method")
    def handler = new HandlerMethod(new InterceptedClass(), method)

    def lock = new XplainLock("lock")
    1 * lockResolver.resolveLocks(method) >> [lock]

    def fencedLock = Mock(ClusterLock)
    1 * lockingSupport.tryLock(lock) >> right(fencedLock)

    0 * fencedLock._

    expect:
    interceptor.preHandle(request, response, handler)
  }
}
