package com.solum.xplain.core.ipv.data

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE
import static com.solum.xplain.core.providers.enums.DataProviderType.VALUATION
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.csv.ParsingMode
import com.solum.xplain.core.common.value.ArchiveForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.csv.ValidationResponse
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.ipv.data.csv.IpvDataExportService
import com.solum.xplain.core.ipv.data.entity.IpvDataValue
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.core.ipv.dataresolution.IpvDataResolutionJobService
import com.solum.xplain.core.ipv.dataresolution.value.IpvDataResolutionStatus
import com.solum.xplain.core.ipv.nav.repository.CompanyLegalEntityNavRepository
import com.solum.xplain.core.providers.DataProviderRepository
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.utils.filter.TableFilter
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [IpvDataController])
class IpvDataControllerTest extends Specification {

  static final String GROUP_ID = "groupId"
  static final String ID = "id"
  static final LocalDate VALUATION_DATE = LocalDate.now()
  static final String KEY = "KEY"
  static final String KEY_ANOTHER = "KEY_ANOTHER"
  static final String PROVIDER = "P"
  static final String NAV_PROVIDER = "NAV"

  @SpringBean
  private IpvDataService ipvDataService = Mock()

  @SpringBean
  private IpvDataExportService ipvDataExportService = Mock()

  @SpringBean
  private DataProviderRepository providerRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  private IpvDataRepository ipvDataRepository = Mock()

  @SpringBean
  private CompanyLegalEntityNavRepository companyLegalEntityNavRepository = Mock()

  @SpringBean
  private IpvDataResolutionJobService ipvDataResolutionJobService = Mock()

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper objectMapper

  def "should create new value with form #form and response #response"() {
    setup:
    ipvDataService.createValue(
      _ as Authentication,
      GROUP_ID,
      _ as IpvDataProviderValueForm
      ) >> right(entityId("1"))

    providerRepository.existsByType(PROVIDER, VALUATION) >> true
    providerRepository.existsByType(NAV_PROVIDER_CODE, VALUATION) >> true
    ipvDataRepository.valueExists(GROUP_ID, { it.getActualDate() == VALUATION_DATE }, _ as String, _ as String) >> false

    when:
    def results = mockMvc
      .perform(post("/ipv-data-groups/{groupId}/values", GROUP_ID)
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                             | code | response
    form()                                           | 200  | """{"id":"1"}"""
    form().tap({ v -> v.put("provider", "NAV") })    | 200  | """{"id":"1"}"""
    form().tap({ v -> v.put("key", null) })          | 412  | "NotEmpty.ipvDataProviderValueForm.key"
    form().tap({ v -> v.put("key", "WITH SPACES") }) | 412  | "ValidIdentifier.ipvDataProviderValueForm.key"
    form().tap({ v -> v.put("key", "lowercase") })   | 412  | "ValidIdentifier.ipvDataProviderValueForm.key"
    form().tap({ v -> v.put("provider", null) })     | 412  | "NotEmpty.ipvDataProviderValueForm.provider"
    form().tap({ v -> v.put("provider", "?") })      | 412  | "com.solum.xplain.api.common.validation.ValidDataProviderCode.message"
    form().tap({ v -> v.put("date", null) })         | 412  | "NotNull.ipvDataProviderValueForm.date"
    form().tap({ v -> v.put("value", null) })        | 412  | "NotNull.ipvDataProviderValueForm.value"
    form().tap({ v -> v.put("versionForm", null) })  | 412  | "NotNull.ipvDataProviderValueForm.versionForm"
    form().tap({ v ->
      v.put("provider", "NAV")
      v.put("delta", "0.1")
    })                                               | 412  | "NAV must not have delta/vega values"
  }

  def "should validate duplication when creating new value with form #form"() {
    setup:
    ipvDataService.createValue(
      _ as Authentication,
      GROUP_ID,
      _ as IpvDataProviderValueForm
      ) >> right(entityId("1"))

    providerRepository.existsByType(_ as String, VALUATION) >> true
    ipvDataRepository.valueExists(GROUP_ID, { it.getActualDate() == VALUATION_DATE }, KEY, "P1") >> true
    ipvDataRepository.valueExists(GROUP_ID, { it.getActualDate() == VALUATION_DATE }, KEY, PROVIDER) >> false

    when:
    def results = mockMvc
      .perform(post("/ipv-data-groups/{groupId}/values", GROUP_ID)
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                         | code | response
    form().tap({ v -> v.put("provider", "P1") }) | 412  | "Value already exists"
    form()                                       | 200  | """{"id":"1"}"""
  }

  def "should validate duplication when creating new NAV value at entity level with form #form"() {
    setup:
    ipvDataService.createValue(
      _ as Authentication,
      GROUP_ID,
      _ as IpvDataProviderValueForm
      ) >> right(entityId("1"))

    providerRepository.existsByType(_ as String, VALUATION) >> true
    ipvDataRepository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"
    companyLegalEntityNavRepository.valueExists(GROUP_ID, { it.getActualDate() == VALUATION_DATE }, KEY_ANOTHER) >> true
    companyLegalEntityNavRepository.valueExists(GROUP_ID, { it.getActualDate() == VALUATION_DATE }, KEY) >> false

    when:
    def results = mockMvc
      .perform(post("/ipv-data-groups/{groupId}/values", GROUP_ID)
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                                                              | code | response
    navForm().tap({ v -> v.put("key", KEY_ANOTHER) }) | 412  | "Value already exists"
    navForm()                                                                         | 200  | """{"id":"1"}"""
  }

  def "should get values with #responseBody"() {
    setup:
    ipvDataService.getValues(
      _ as Authentication,
      GROUP_ID,
      new IpvDataProviderValueFilter(VALUATION_DATE, false, false),
      _ as TableFilter,
      _ as ScrollRequest
      ) >> result

    def results = mockMvc
      .perform(get("/ipv-data-groups/{groupId}/values", GROUP_ID)
      .with(csrf())
      .param("valuationDate", VALUATION_DATE.toString())
      .param("resolved", Boolean.FALSE.toString())
      .param("archived", Boolean.FALSE.toString())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                 | code | responseBody
    right(List.of())       | 200  | "[]"
    left(OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get value with #responseBody"() {
    setup:
    ipvDataService.getValue(_ as Authentication, GROUP_ID, ID, _ as BitemporalDate, IpvDataType.EXCLUDE_NAV) >> result

    def results = mockMvc
      .perform(get("/ipv-data-groups/{groupId}/values/{id}", GROUP_ID, ID)
      .param("dataType", "EXCLUDE_NAV")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                       | code | responseBody
    right(new IpvDataProviderValueView(id: "1")) | 200  | "\"id\":\"1\""
    left(OBJECT_NOT_FOUND)                       | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get NAV value with #responseBody"() {
    setup:
    ipvDataService.getValue(_ as Authentication, GROUP_ID, ID, _ as BitemporalDate, IpvDataType.NAV) >> result

    def results = mockMvc
      .perform(get("/ipv-data-groups/{groupId}/values/{id}", GROUP_ID, ID)
      .param("dataType", "NAV")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                       | code | responseBody
    right(new IpvDataProviderValueView(id: "1")) | 200  | "\"id\":\"1\""
    left(OBJECT_NOT_FOUND)                       | 422  | "OBJECT_NOT_FOUND"
  }

  def "should update value with form #form, #result and #code #response"() {
    setup:
    ipvDataService.updateValue(_ as Authentication, GROUP_ID, ID, _ as IpvDataProviderValueUpdateForm, IpvDataType.NAV, VALUATION_DATE) >> result
    ipvDataRepository.getValueView(GROUP_ID, ID, _ as BitemporalDate) >> right(new IpvDataProviderValueView(provider: NAV_PROVIDER_CODE))
    providerRepository.existsByType(PROVIDER, VALUATION) >> true

    when:
    def results = mockMvc
      .perform(put("/ipv-data-groups/{groupId}/values/{id}", GROUP_ID, ID)
      .param("dataType", "NAV")
      .param("stateDate", VALUATION_DATE.toString())
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == code
    results.getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                    | code | response                              | result
    providerValueUpdateForm()                               | 200  | "1"                                   | right(entityId("1"))
    providerValueUpdateForm().tap { it.put("vega", "0.2") } | 412  | "NAV must not have delta/vega values" | right(entityId("1"))
    providerValueUpdateForm()                               | 422  | "OBJECT_NOT_FOUND"                    | left(OBJECT_NOT_FOUND.entity())
  }

  def "should archive value with #result and #response"() {
    setup:
    ipvDataService.archiveValue(_ as Authentication, GROUP_ID, ID, _ as ArchiveForm, IpvDataType.EXCLUDE_NAV, VALUATION_DATE) >> result

    when:
    def results = mockMvc
      .perform(put("/ipv-data-groups/{groupId}/values/{id}/archive", GROUP_ID, ID)
      .param("dataType", "EXCLUDE_NAV")
      .param("stateDate", VALUATION_DATE.toString())
      .with(csrf())
      .content(objectMapper.writeValueAsString(archiveForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response

    where:
    result                 | response
    left(OBJECT_NOT_FOUND) | 422
    right(entityId("1"))   | 200
  }

  def "should upload values csv with result #responseBody"() {
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    1 * ipvDataService.upload(
      _ as Authentication,
      GROUP_ID,
      file.getBytes(),
      _ as ImportOptions,
      IpvDataType.ALL
      ) >> result

    def results = mockMvc.perform(multipart("/ipv-data-groups/{groupId}/values/upload", GROUP_ID)
      .file(file)
      .param("duplicateAction", "APPEND")
      .param("dataType", "ALL")
      .param("stateDate", VALUATION_DATE.toString())
      .with(csrf()))
      .andReturn()

    expect:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                   | code | responseBody
    right(entityId(GROUP_ID))                | 200  | GROUP_ID
    left(List.of(OBJECT_NOT_FOUND.entity())) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should validate values csv with result #responseBody"() {
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    1 * ipvDataService.validateFile(
      _ as Authentication,
      GROUP_ID,
      ParsingMode.STRICT,
      file.getBytes(),
      IpvDataType.ALL
      ) >> result

    def results = mockMvc.perform(multipart("/ipv-data-groups/{groupId}/values/upload/validate", GROUP_ID)
      .file(file)
      .with(csrf()))
      .andReturn()

    expect:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                   | code | responseBody
    right(new ValidationResponse(List.of())) | 200  | "validationTimestamp"
    left(List.of(OBJECT_NOT_FOUND.entity())) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get values csv with result #responseBody"() {
    setup:
    ipvDataExportService.getProviderValuesCsv(
      _ as Authentication,
      GROUP_ID,
      new IpvDataProviderValueFilter(VALUATION_DATE, false, false),
      VALUATION_DATE,
      IpvDataType.ALL
      ) >> result

    def results = mockMvc.perform(get("/ipv-data-groups/{groupId}/values/csv", GROUP_ID)
      .with(csrf())
      .param("valuationDate", VALUATION_DATE.toString())
      .param("resolved", Boolean.FALSE.toString())
      .param("archived", Boolean.FALSE.toString())
      .param("stateDate", VALUATION_DATE.toString())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                                                             | code | responseBody
    right(FileResponseEntity.csvFile(new ByteArrayResource("resource".bytes), "name")) | 200  | "resource"
    left(new ErrorItem(OBJECT_NOT_FOUND))                                              | 422  | "OBJECT_NOT_FOUND"
  }

  def "should return IPV resolution process status"() {
    setup:
    def response = IpvDataResolutionStatus.newOf(false, GROUP_ID)
    1 * ipvDataResolutionJobService.getResolutionProcessStatus(GROUP_ID) >> right(response)

    when:
    def results = mockMvc.perform(get("/ipv-data-groups/{groupId}/values/resolution-process", GROUP_ID)
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString() == objectMapper.writeValueAsString(response)
    }
  }

  static def providerValue() {
    def pv = new IpvDataValue()
    pv.id = ID
    pv.groupId = GROUP_ID
    pv.date = VALUATION_DATE
    pv.key = KEY
    pv.provider = PROVIDER
    pv.resolved = null
    def v = new IpvDataValueVersion(value: 0.1d)
    pv.values = [v]
    pv
  }

  static def providerValueUpdateForm() {
    [
      value      : 0.1d,
      versionForm: [
        comment: "comment"
      ]
    ]
  }

  static def form() {
    return [
      date       : VALUATION_DATE,
      provider   : PROVIDER,
      key        : KEY,
      value      : 0.1d,
      versionForm: [
        comment: "comment"
      ]
    ]
  }

  static def navForm() {
    return [
      date       : VALUATION_DATE,
      provider   : NAV_PROVIDER,
      key        : KEY,
      value      : 0.1d,
      versionForm: [
        comment: "comment"
      ]
    ]
  }

  static def archiveForm() {
    [
      comment: "comment"
    ]
  }
}
