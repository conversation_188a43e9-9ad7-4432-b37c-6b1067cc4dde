package com.solum.xplain.core.curvegroup.curve.extension

import static com.opengamma.strata.basics.currency.Currency.CAD
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.currency.Currency.ZAR
import static com.opengamma.strata.basics.date.MarketTenor.ofSpotMonths
import static com.opengamma.strata.market.curve.CurveNodeClashAction.DROP_OTHER
import static com.opengamma.strata.product.common.BuySell.BUY
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData
import static com.solum.xplain.core.market.mapping.MarketDataUtils.quoteId
import static com.solum.xplain.core.portfolio.TradeSample.fxSwapTrade
import static org.hamcrest.Matchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.currency.FxRate
import com.opengamma.strata.basics.date.MarketTenor
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.data.FxRateId
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.data.MarketDataNotFoundException
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.CurveNodeDateOrder
import com.opengamma.strata.product.fx.FxSwapTrade
import com.opengamma.strata.product.fx.type.FxSwapConvention
import com.opengamma.strata.product.fx.type.FxSwapConventions
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData
import java.time.LocalDate
import spock.lang.Specification

class FxSwapCurveNodeTest extends Specification {
  static CurrencyPair CURRENCY_PAIR = CurrencyPair.parse("EUR/USD")

  def "should return correct requirements"() {
    setup:
    def quoteIdentifier = quoteId("KEY")
    def tnQuoteId = quoteId("TN")
    def node = new FxSwapCurveNode(template(), quoteIdentifier, "label", tnQuoteId, 0d)
    expect:
    node.requirements().size() == 2
    node.requirements().contains(FxRateId.of(CURRENCY_PAIR))
    node.requirements().contains(quoteIdentifier)
    !node.requirements().contains(tnQuoteId)
  }

  def "should return correct requirements for ON node"() {
    setup:
    def quoteIdentifier = quoteId("ON")
    def tnQuoteId = quoteId("TN")
    def nodeSpotOffset2 = new FxSwapCurveNode(template(MarketTenor.ON), quoteIdentifier, "label", tnQuoteId, 0d)
    def nodeSpotOffset1 = new FxSwapCurveNode(template(MarketTenor.ON, CurrencyPair.parse("USD/CAD")), quoteIdentifier, "label", tnQuoteId, 0d)
    expect:
    nodeSpotOffset2.requirements().size() == 2
    nodeSpotOffset2.requirements().contains(FxRateId.of(CURRENCY_PAIR))
    nodeSpotOffset2.requirements().contains(quoteIdentifier)

    nodeSpotOffset1.requirements().size() == 2
    nodeSpotOffset1.requirements().contains(FxRateId.of(CurrencyPair.parse("USD/CAD")))
    nodeSpotOffset1.requirements().contains(quoteIdentifier)
    !nodeSpotOffset1.requirements().contains(tnQuoteId)
  }

  def "should calculate end date"() {
    setup:
    def quoteIdValue = quoteId("KEY")
    def quoteIdTn = quoteId("KEY!")
    def node = new FxSwapCurveNode(template, quoteIdValue, "label", quoteIdTn, 0d)
    expect:
    node.date(valuationDate, ReferenceData.standard()) == endDate
    where:
    template                                                 | valuationDate                 | endDate
    template(ofSpotMonths(1))                                | LocalDate.parse("2021-02-10") | LocalDate.parse("2021-03-12")
    template(MarketTenor.SN)                                 | LocalDate.parse("2021-02-10") | LocalDate.parse("2021-02-16")
    template(MarketTenor.TN)                                 | LocalDate.parse("2021-02-10") | LocalDate.parse("2021-02-12")
    template(MarketTenor.ON)                                 | LocalDate.parse("2021-02-10") | LocalDate.parse("2021-02-11")
    template(ofSpotMonths(1), CurrencyPair.parse("USD/RUB")) | LocalDate.parse("2021-02-10") | LocalDate.parse("2021-03-11")
    template(ofSpotMonths(1), CurrencyPair.parse("GBP/NZD")) | LocalDate.parse("2023-01-14") | LocalDate.parse("2023-02-17")
    template(ofSpotMonths(1), CurrencyPair.parse("USD/MXN")) | LocalDate.parse("2023-04-06") | LocalDate.parse("2023-05-11")
  }

  def "should return initial guess"() {
    setup:
    def quoteIdValue = quoteId("KEY")
    def quoteIdTn = quoteId("KEY!")
    def node = new FxSwapCurveNode(template(), quoteIdValue, "label", quoteIdTn, 0d)
    expect:
    node.initialGuess(ogMarketData(), valueType) == initialGuess
    where:
    valueType                 | initialGuess
    ValueType.DISCOUNT_FACTOR | 1
    ValueType.ZERO_RATE       | 0
    ValueType.PRICE           | 0
  }

  def "should return trade"() {
    setup:
    def valuationDate = LocalDate.of(2023, 6, 26)
    def quoteIdOn = quoteId("KEY_ON")
    def quoteIdTn = quoteId("KEY_TN")
    def marketData = Mock(MarketData)
    2 * marketData.getValuationDate() >> valuationDate
    1 * marketData.findValue(quoteIdOn) >> Optional.of(0d)
    1 * marketData.findValue(quoteIdTn) >> Optional.of(0d)
    1 * marketData.getValue(FxRateId.of(CURRENCY_PAIR)) >> FxRate.of(CURRENCY_PAIR, 1)

    def template = Mock(MarketTenorFxSwapTemplate)
    1 * template.createTrade(valuationDate, BUY, 10, 1, 0d, ReferenceData.standard()) >> fxSwapTrade()
    1 * template.createTrade(valuationDate, BUY, 1, 1, 0d, ReferenceData.standard()) >> fxSwapTrade()
    2 * template.currencyPair() >> CURRENCY_PAIR
    1 * template.marketTenor() >> MarketTenor.ON
    2 * template.convention() >> FxSwapConventions.EUR_USD

    def node = new FxSwapCurveNode(template, quoteIdOn, "label", quoteIdTn, 0d)

    expect:
    node.trade(10, marketData, ReferenceData.standard())
  }

  def "should return resolved trade"() {
    setup:
    def tnQuoteId = quoteId("TN")
    def node = new FxSwapCurveNode(template(), quoteId("1M_EUR/USD"), "label", tnQuoteId, 0d)

    expect:
    node.resolvedTrade(10, ogMarketData(), ReferenceData.standard()) != null
  }

  def "should return correctly resolved market tenor trade"() {
    setup:
    def node = new FxSwapCurveNode(template(tenor, CurrencyPair.parse(currencyPair)), quoteId(key), "label", tnId, 0d)
    def marketData = ogMarketData(valDate).toBuilder()
      .addValue(FxRateId.of(USD, CAD), FxRate.of(USD, CAD, 0.77777 as double))
      .addValue(FxRateId.of(USD, ZAR), FxRate.of(USD, ZAR, 0.77777 as double))
      .addValue(quoteId("ON_EUR/USD"), 0.00077 as double)
      .addValue(quoteId("TN_EUR/USD"), 0.00080 as double)
      .addValue(quoteId("1M_EUR/USD"), 0.77777 as double)
      .addValue(quoteId("ON_USD/CAD"), 0.00077 as double)
      .addValue(quoteId("ON_USD/ZAR"), 0.00077 as double)
      .addValue(quoteId("TN_USD/ZAR"), 0.00080 as double)
      .build()
    def trade = node.resolvedTrade(10, marketData, ValuationDateReferenceData.wrap(ReferenceData.standard(), valDate))

    expect:
    trade != null

    trade.product.nearLeg.baseCurrencyPayment.value.currency == nearLegBaseCcyPayment.currency
    trade.product.nearLeg.counterCurrencyPayment.value.currency == nearLegCounterCcyPayment.currency
    trade.product.farLeg.baseCurrencyPayment.value.currency == farLegBaseCcyPayment.currency
    trade.product.farLeg.counterCurrencyPayment.value.currency == farLegCounterCcyPayment.currency
    that trade.product.nearLeg.baseCurrencyPayment.value.amount, closeTo(nearLegBaseCcyPayment.amount, 0.0001d)
    that trade.product.nearLeg.counterCurrencyPayment.value.amount, closeTo(nearLegCounterCcyPayment.amount, 0.0001d)
    that trade.product.farLeg.baseCurrencyPayment.value.amount, closeTo(farLegBaseCcyPayment.amount, 0.0001d)
    that trade.product.farLeg.counterCurrencyPayment.value.amount, closeTo(farLegCounterCcyPayment.amount, 0.0001d)

    where:
    valDate                       | key          | tenor          | currencyPair | tnId                  | nearLegBaseCcyPayment        | nearLegCounterCcyPayment           | farLegBaseCcyPayment          | farLegCounterCcyPayment
    VAL_DT                        | "ON_EUR/USD" | MarketTenor.ON | "EUR/USD"    | quoteId("TN_EUR/USD") | CurrencyAmount.of("EUR", 10) | CurrencyAmount.of("USD", -12.3728) | CurrencyAmount.of("EUR", -10) | CurrencyAmount.of("USD", 12.3805)
    VAL_DT                        | "TN_EUR/USD" | MarketTenor.TN | "EUR/USD"    | quoteId("TN_EUR/USD") | CurrencyAmount.of("EUR", 10) | CurrencyAmount.of("USD", -12.3805) | CurrencyAmount.of("EUR", -10) | CurrencyAmount.of("USD", 12.3885)
    VAL_DT                        | "ON_USD/CAD" | MarketTenor.ON | "USD/CAD"    | quoteId("TN_USD/CAD") | CurrencyAmount.of("USD", 10) | CurrencyAmount.of("CAD", -7.770)   | CurrencyAmount.of("USD", -10) | CurrencyAmount.of("CAD", 7.7777)
    LocalDate.parse("2023-06-14") | "ON_USD/ZAR" | MarketTenor.ON | "USD/ZAR"    | quoteId("TN_USD/ZAR") | CurrencyAmount.of("USD", 10) | CurrencyAmount.of("ZAR", -7.762)   | CurrencyAmount.of("USD", -10) | CurrencyAmount.of("ZAR", 7.7697)
    LocalDate.parse("2023-06-16") | "ON_USD/ZAR" | MarketTenor.ON | "USD/ZAR"    | quoteId("TN_USD/ZAR") | CurrencyAmount.of("USD", 10) | CurrencyAmount.of("ZAR", -7.770)   | CurrencyAmount.of("USD", -10) | CurrencyAmount.of("ZAR", 7.7777)  // US holiday @ T+1: ON end date is spot date, ignore TN rate
  }

  def "should throw error if TN rate doesn't exist when resolving ON node trade"() {
    when:
    def node = new FxSwapCurveNode(template(MarketTenor.ON, CurrencyPair.parse("EUR/USD")), quoteId("ON_EUR/USD"), "label", quoteId("TN_EUR/USD"), 0d)
    def marketData = ogMarketData().toBuilder()
      .addValue(FxRateId.of(USD, CAD), FxRate.of(USD, CAD, 0.77777 as double)).addValue(quoteId("ON_EUR/USD"), 0.00077 as double).build()
    node.resolvedTrade(10, marketData, ReferenceData.standard())

    then:
    MarketDataNotFoundException exception = thrown()
    exception.message == "Market data not found for 'QuoteId:XPL~TN_EUR/USD/MarketValue' of type 'QuoteId'"
  }

  def "should not require TN rate when ON node end date is spot date"() {
    when:
    def node = new FxSwapCurveNode(template(MarketTenor.ON, CurrencyPair.parse("USD/ZAR")), quoteId("ON_USD/ZAR"), "label", quoteId("TN_USD/ZAR"), 0d)
    def marketData = ogMarketData(LocalDate.parse("2023-06-16")).toBuilder()
      .addValue(FxRateId.of(USD, ZAR), FxRate.of(USD, ZAR, 0.77777 as double)).addValue(quoteId("ON_USD/ZAR"), 0.00077 as double).build()
    node.trade(10, marketData, ReferenceData.standard())

    then:
    noExceptionThrown()
  }

  def "should build FX swap trade with spread #spread"() {
    setup:
    def node = new FxSwapCurveNode(template(MarketTenor.ofSpotDays(1), CurrencyPair.parse("AUD/USD")), quoteId("1D_AUD/USD"), "label", quoteId("1D_AUD/USD"), spread)
    def marketData = MarketData.of(
      VAL_DT,
      [
        (quoteId("1D_AUD/USD"))                     : 10d,
        (FxRateId.of(CurrencyPair.parse("AUD/USD"))): FxRate.of(CurrencyPair.parse("AUD/USD"), 1d)
      ]
      )

    when:
    def trade = (FxSwapTrade) node.trade(100, marketData, ReferenceData.standard())

    then:
    trade.product.nearLeg.baseCurrencyAmount.amount == 100
    trade.product.farLeg.counterCurrencyAmount.amount == counterCurrencyAmount //shifted value - must be the farLeg as the spread is added to the fwd rate and not the spot rate

    where:
    spread | counterCurrencyAmount
    1d     | 1200
    0.5d   | 1150
    0d     | 1100
  }

  static MarketTenorFxSwapTemplate template(MarketTenor tenor = ofSpotMonths(1), CurrencyPair currencyPair = CURRENCY_PAIR) {
    new MarketTenorFxSwapTemplate(tenor, FxSwapConvention.of(currencyPair))
  }
}
