package com.solum.xplain.core.portfolio.convention

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.core.test.MockMvcConfiguration
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [TradeConventionController])
@MockMvcConfiguration
class TradeConventionControllerTest extends Specification {

  @Autowired
  private MockMvc mockMvc

  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()

  @Unroll
  def "should fetch default calculated FXOPT payment date currencies #ccy1 #ccy2 #expiryDate with response #code #responseBody"() {
    setup:
    def results = mockMvc.perform(get("/portfolio/convention/fx-opt/default-payment-date")
      .param("domesticCcy", ccy1)
      .param("foreignCcy", ccy2)
      .param("expiryDate", expiryDate))
      .andReturn()

    expect:
    results.getResponse().getStatus() == code
    results.getResponse().getContentAsString().indexOf(responseBody) >= 0

    where:
    ccy1   | ccy2   | expiryDate   | code | responseBody
    "USD"  | "EUR"  | "2023-01-01" | 200  | """2023-01-03"""
    "USD"  | ""     | "2023-01-01" | 412  | """NotEmpty.fxOptDefaultPaymentDateCalcForm.foreignCcy"""
    ""     | "USD"  | "2023-01-01" | 412  | """NotEmpty.fxOptDefaultPaymentDateCalcForm.domesticCcy"""
    "FAKE" | "GBP"  | "2023-01-01" | 412  | """ValidStringSet.fxOptDefaultPaymentDateCalcForm.domesticCcy"""
    "GBP"  | "FAKE" | "2023-01-01" | 412  | """ValidStringSet.fxOptDefaultPaymentDateCalcForm.foreignCcy"""
    "USD"  | "EUR"  | ""           | 412  | """NotNull.fxOptDefaultPaymentDateCalcForm.expiryDate"""
    "USD"  | "EUR"  | "FAKE"       | 412  | """Failed to convert value of type 'java.lang.String' to required type 'java.time.LocalDate'"""
  }
}
