package com.solum.xplain.core.curveconfiguration.csv.configuration

import static com.solum.xplain.core.curveconfiguration.csv.configuration.CurveConfigurationCsvMapper.CURVE_CONFIGURATION_CSV_HEADER
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CREDIT_INDEX
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FRA
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderView
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView
import spock.lang.Specification

class CurveConfigurationCsvMapperTest extends Specification {

  def "should correctly map curve configurations"() {
    setup:
    def config1 = new CurveConfigurationView(
      name: "name",
      curveGroupName: "groupName",
      instruments: Map.of(
      FX_RATE, new CurveConfigurationProviderView(primary: "T"),
      FRA, new CurveConfigurationProviderView(primary: "T", secondary: "R"),
      CDS, new CurveConfigurationProviderView(primary: "P", secondary: "S"),
      CREDIT_INDEX, new CurveConfigurationProviderView(primary: "P2", secondary: "S2"))
      )

    when:
    def rows = CurveConfigurationCsvMapper.toCsvRows(config1)
    def csv = new CsvOutputFile(CURVE_CONFIGURATION_CSV_HEADER, rows)
    def result = csv.write()


    then:
    result == """\
Name,Curve Group,Instrument Type,Primary Provider,Secondary Provider
name,groupName,CDS,P,S
name,groupName,Credit Index,P2,S2
name,groupName,ForwardRateAgreement,T,R
name,groupName,FX,T,
"""
  }
}
