package com.solum.xplain.core.curvegroup.conventions


import com.solum.xplain.core.classifiers.CurveNodeTypes
import spock.lang.Specification

class CurveConfigurationsTest extends Specification {

  def "should return at least one curve convention for each node type"() {
    expect:
    CurveConfigurations.lookupByNodeTypes([type]) != null
    CurveConfigurations.lookupByNodeTypes([type]).size() > 0
    where:
    type << CurveNodeTypes.VALUES
  }
}
