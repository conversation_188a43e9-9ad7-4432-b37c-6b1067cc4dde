package com.solum.xplain.core.classifiers

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.core.instrument.InstrumentTypeProvider
import com.solum.xplain.core.instrument.MockInstrument
import spock.lang.Specification

class CurveConfigurationInstrumentsClassifierProviderTest extends Specification {
  def INSTRUMENT_1 = new MockInstrument("NAME1", "1", CoreAssetClass.IR_RATE, CoreInstrumentGroup.IR)
  def INSTRUMENT_2 = new MockInstrument("NAME2", "2", CoreAssetClass.CDS, CoreInstrumentGroup.ALL_CREDIT)
  def INSTRUMENT_3 = new MockInstrument("NAME3", "3", CoreAssetClass.FX_RATES, CoreInstrumentGroup.ALL_FX)
  def INSTRUMENT_4 = new MockInstrument("NAME4", "4", CoreAssetClass.IR_RATE, CoreInstrumentGroup.INFLATION)
  def INSTRUMENT_5 = new MockInstrument("NAME5", "5", CoreAssetClass.CAPFLOOR_VOLS, CoreInstrumentGroup.VOL)

  def "should return curve config instruments in correct order"() {
    setup:
    def provider = new CurveConfigurationInstrumentsClassifierProvider([new Provider1(), new Provider2()])

    expect:
    provider.classifiers().size() == 1
    def classifier = provider.classifiers()[0]
    classifier.id == "curveConfigurationInstruments"
    classifier.values[0].id == "IR"
    classifier.values[1].id == "INFLATION"
    classifier.values[2].id == "VOL"
    classifier.values[3].id == "ALL_CREDIT"
    classifier.values[4].id == "ALL_FX"
  }

  class Provider1 implements InstrumentTypeProvider {

    @Override
    List<InstrumentType> instruments() {
      return [INSTRUMENT_1, INSTRUMENT_4]
    }
  }

  class Provider2 implements InstrumentTypeProvider {

    @Override
    List<InstrumentType> instruments() {
      return [INSTRUMENT_2, INSTRUMENT_3, INSTRUMENT_5]
    }
  }
}
