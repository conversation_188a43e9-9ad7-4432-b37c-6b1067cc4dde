package com.solum.xplain.core.curvegroup.curve.value

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_OIS_SWAP_NODE

import spock.lang.Specification

class CurveNodeFormTest extends Specification {
  def "should return correct identifier"() {
    expect:
    form.identifier() == expected

    where:
    form                                                                                        | expected
    new CurveNodeForm()                                                                         | Optional.empty()
    new CurveNodeForm(type: FRA_NODE, convention: "random")                                     | Optional.empty()
    new CurveNodeForm(type: FRA_NODE, convention: "EUR-EURIBOR-6M", fraSettlement: "3M")        | Optional.of("9M")
    new CurveNodeForm(type: IMM_FRA_NODE, convention: "DKK-CIBOR-6M-IMM", serialFuture: "2D+1") | Optional.of("1")
    new CurveNodeForm(type: IBOR_FUTURE_NODE, serialFuture: "2D+1")                             | Optional.of("1")
    new CurveNodeForm(type: IBOR_FUTURE_NODE, serialFuture: "2D")                               | Optional.empty()
    new CurveNodeForm(type: FX_SWAP_NODE, period: "TN")                                         | Optional.of("TN")
    new CurveNodeForm(type: FX_SWAP_NODE, period: "1D")                                         | Optional.of("1D")
    new CurveNodeForm(type: FX_SWAP_NODE, period: "random")                                     | Optional.empty()
    new CurveNodeForm(type: TERM_DEPOSIT_NODE, convention: "INR-Deposit-T0")                    | Optional.of("ON")
    new CurveNodeForm(type: TERM_DEPOSIT_NODE, convention: "AUD-Deposit-T2")                    | Optional.of("AUD-Deposit-T2")
    new CurveNodeForm(type: IBOR_FIXING_DEPOSIT_NODE, period: "3M")                             | Optional.of("3M")
    new CurveNodeForm(type: FIXED_OVERNIGHT_SWAP_NODE, period: "3M")                            | Optional.of("3M")
    new CurveNodeForm(type: FIXED_IBOR_SWAP_NODE, period: "3M")                                 | Optional.of("3M")
    new CurveNodeForm(type: IBOR_IBOR_SWAP_NODE, period: "3M")                                  | Optional.of("3M")
    new CurveNodeForm(type: OVERNIGHT_IBOR_BASIS_SWAP_NODE, period: "3M")                       | Optional.of("3M")
    new CurveNodeForm(type: XCCY_IBOR_IBOR_SWAP_NODE, period: "3M")                             | Optional.of("3M")
    new CurveNodeForm(type: XCCY_IBOR_OIS_SWAP_NODE, period: "3M")                              | Optional.of("3M")
    new CurveNodeForm(type: FIXED_INFLATION_SWAP_NODE, period: "3M")                            | Optional.of("3M")
  }
}
