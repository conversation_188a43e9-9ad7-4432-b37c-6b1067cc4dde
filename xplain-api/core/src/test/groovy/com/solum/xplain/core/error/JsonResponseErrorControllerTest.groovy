package com.solum.xplain.core.error


import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get

import com.solum.xplain.core.test.MockMvcConfiguration
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@WebMvcTest(controllers = [JsonResponseErrorController])
@MockMvcConfiguration
class JsonResponseErrorControllerTest extends Specification {

  @Autowired
  MockMvc mockMvc

  @WithMockUser
  def "should smoke test /error API"() {
    setup:
    def results = mockMvc.perform(get("/error")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getContentAsString().contains("\"error\":\"None\"")
    }
  }
}
