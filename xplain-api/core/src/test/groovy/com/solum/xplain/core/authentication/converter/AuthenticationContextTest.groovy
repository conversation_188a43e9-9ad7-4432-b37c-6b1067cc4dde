package com.solum.xplain.core.authentication.converter

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.authentication.AuthenticationCacheService
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.CachedPrincipal
import com.solum.xplain.core.users.UserBuilder
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import spock.lang.Specification

class AuthenticationContextTest extends Specification {

  def authenticationCacheService = Mock(AuthenticationCacheService)

  AuthenticationContext context = new AuthenticationContext(authenticationCacheService)

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
  }

  def "should resolve current user"() {
    setup:
    def user = user("userId")
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth

    when:
    def expectedResult = context.currentUser()

    then:
    expectedResult == user
  }

  def "should fetch active users for teams"() {
    setup:
    def user = UserBuilder.user("userId")
    def userRevoked = UserBuilder.user("userIdRevoked")
    1 * authenticationCacheService.fetchAllCachedPrincipals() >> [new CachedPrincipal(user, false), new CachedPrincipal(userRevoked, true)]

    when:
    def expectedResult = context.activeUserIdsInTeams([user.getTeams()[0].toString()])

    then:
    expectedResult == [user.getId()]
  }
}
