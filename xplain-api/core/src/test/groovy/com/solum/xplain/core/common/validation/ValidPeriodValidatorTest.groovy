package com.solum.xplain.core.common.validation

import jakarta.validation.ConstraintValidatorContext
import spock.lang.Specification
import spock.lang.Unroll

class ValidPeriodValidatorTest extends Specification {

  @Unroll
  "should return #result for input #input"() {
    setup:
    def validator = new ValidPeriodValidator()
    def ctx = Mock(ConstraintValidatorContext)
    ctx.buildConstraintViolationWithTemplate(_) >> <PERSON><PERSON>(ConstraintValidatorContext.ConstraintViolationBuilder)

    expect:
    validator.isValid(input, ctx) == result
    where:
    input   | result
    "3M"    | true
    "3m"    | false
    "1Y"    | true
    "P1Y"   | true
    "3Mx6M" | false
    "1M+1"  | false
    "Q"     | false
    "0M"    | false
    ""      | true
    null    | true
  }

  @Unroll
  "should return #result for input #input when style is YMW"() {
    setup:
    def validPeriodConstraint = Mock(ValidPeriod)
    1 * validPeriodConstraint.style() >> ValidPeriod.PeriodStyle.YMW

    def validator = new ValidPeriodValidator()
    validator.initialize(validPeriodConstraint)
    def ctx = Mock(ConstraintValidatorContext)
    ctx.buildConstraintViolationWithTemplate(_) >> Mock(ConstraintValidatorContext.ConstraintViolationBuilder)

    expect:
    validator.isValid(input, ctx) == result
    where:
    input    | result
    "3M"     | true
    "20000M" | false
    "3m"     | false
    "1Y"     | true
    "1000Y"  | false
    "1W"     | true
    "3Mx6M"  | false
    "1M+1"   | false
    "W"      | false
    "0M"     | false
    "0W"     | false
    ""       | true
    null     | true
  }

  @Unroll
  "should return #result for input #input when style is BRL"() {
    setup:
    def validPeriodConstraint = Mock(ValidPeriod)
    1 * validPeriodConstraint.style() >> ValidPeriod.PeriodStyle.BRL

    def validator = new ValidPeriodValidator()
    validator.initialize(validPeriodConstraint)
    def ctx = Mock(ConstraintValidatorContext)
    ctx.buildConstraintViolationWithTemplate(_) >> Mock(ConstraintValidatorContext.ConstraintViolationBuilder)

    expect:
    validator.isValid(input, ctx) == result
    where:
    input    | result
    "3M"     | true
    "20000M" | false
    "3m"     | false
    "1Y"     | true
    "1000Y"  | false
    "1W"     | false
    "1Q"     | true
    "20000Q" | false
    "0Q"     | false
    "1S"     | true
    "1000S"  | false
    "0S"     | false
    ""       | true
    null     | true
  }
}
