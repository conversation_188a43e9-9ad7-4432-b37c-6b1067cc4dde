package com.solum.xplain.core.market.csv

import com.solum.xplain.core.providers.DataProviderRepository
import spock.lang.Specification

class MarketDataKeyProviderCsvLoaderFactoryTest extends Specification {
  DataProviderRepository providerRepository = Mock()
  MarketDataKeyProviderCsvLoaderFactory factory = new MarketDataKeyProviderCsvLoaderFactory(providerRepository)

  def "should get loader"() {
    setup:
    1 * providerRepository.dataProvidersList() >> []
    expect:
    factory.getLoader([]) != null
  }
}
