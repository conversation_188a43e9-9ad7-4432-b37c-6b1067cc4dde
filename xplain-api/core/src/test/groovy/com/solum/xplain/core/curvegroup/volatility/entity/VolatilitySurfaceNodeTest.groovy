package com.solum.xplain.core.curvegroup.volatility.entity

import com.solum.xplain.core.curvegroup.volatility.classifier.SkewType
import spock.lang.Specification
import spock.lang.Unroll

class VolatilitySurfaceNodeTest extends Specification {

  @Unroll
  def "should return correct skew #type key"() {
    setup:
    def node = new VolatilitySurfaceNodeBuilder().build()

    when:
    def result = node.getSkewKey("BRG", new BigDecimal("0.0003"), type)

    then:
    result.key == key

    where:
    type               | key
    SkewType.MONEYNESS | "1YV2Y_+3_BRG"
    SkewType.STRIKE    | "1YV2Y_0.03%_BRG"
  }

  @Unroll
  def "should return correct skew #type name"() {
    setup:
    def node = new VolatilitySurfaceNodeBuilder()
      .expiry("5Y")
      .tenor("10Y")
      .build()

    when:
    def result = node.getSkewName("USD 3M Vols", new BigDecimal("0.0005"), type)

    then:
    result == name

    where:
    type               | name
    SkewType.MONEYNESS | "USD 3M VOLS SWO +5BP 5YV10Y"
    SkewType.STRIKE    | "USD 3M VOLS SWO 0.05% 5YV10Y"
  }

  def "should return correct swaption skew key and name"() {
    setup:
    def instrument = new VolatilitySurfaceNodeBuilder()
      .expiry("5Y")
      .tenor("10Y")
      .build()
      .atmInstrument("USD 3M Vols", "USD-LIBOR-3M", "USD", "EUR")

    expect:
    instrument.key == "5YV10Y_ATM_USD-LIBOR-3M"
    instrument.mdkName == "USD 3M VOLS SWO ATM 5YV10Y"
  }
}
