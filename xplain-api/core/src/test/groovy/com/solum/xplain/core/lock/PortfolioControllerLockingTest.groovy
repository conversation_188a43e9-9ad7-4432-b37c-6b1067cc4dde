package com.solum.xplain.core.lock

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyTeamValidationService
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.PortfolioController
import com.solum.xplain.core.portfolio.PortfolioControllerService
import com.solum.xplain.core.portfolio.PortfolioExportService
import com.solum.xplain.core.portfolio.PortfolioUploadService
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.datagrid.ClusterLock
import com.solum.xplain.shared.utils.filter.TableFilter
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PortfolioController])
class PortfolioControllerLockingTest extends Specification {

  @SpringBean
  private PortfolioControllerService service = Mock()

  @SpringBean
  PortfolioUploadService uploadService = Mock()

  @SpringBean
  PortfolioExportService exportService = Mock()

  @SpringBean
  ResourceValidationService resourceValidationService = Mock()

  @SpringBean
  private CompanyMarketDataGroupValidator mdValidator = Mock()

  @SpringBean
  private CurveGroupRepository curveGroupRepository = Mock()

  @SpringBean
  private CompanyRepository companyRepository = Mock()

  @SpringBean
  private CurveConfigurationRepository configurationRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  PortfolioRepository portfolioRepository = Mock()

  @SpringBean
  CompanyTeamValidationService companyTeamValidationService = Mock()

  @SpringBean
  XplainLockResolver lockResolver = new XplainLockResolver(requestPathVariablesSupport)

  @SpringBean
  LockingSupport lockingSupport = Mock()

  @SpringBean
  DefaultLockingInterceptor lockingInterceptor = new DefaultLockingInterceptor(lockResolver, lockingSupport, new ObjectMapper())

  @Autowired
  private MockMvc mockMvc

  def "should complete request without locking"() {
    setup:
    1 * service.getAll(
      _ as ScrollRequest,
      _ as TableFilter,
      activePortfolios(),
      _ as BitemporalDate) >> ScrollableEntry.empty()
    0 * lockingSupport._

    when:
    def results = mockMvc.perform(get('/portfolio')
      .param("archived", String.valueOf(false))
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when modifying portfolio"() {
    setup:
    companyTeamValidationService.validCompanyEntityTeams(
      _ as String,
      _ as String,
      _ as AllowedTeamsForm) >> true
    companyRepository.companyEntity(_ as String) >> right(new Company())
    1 * service.update("id", _ as PortfolioUpdateForm) >> right(EntityId.entityId("id"))
    def mockLock = Mock(ClusterLock)
    1 * lockingSupport.tryLock(new XplainLock(TRADES_LOCK_ID)) >> right(mockLock)
    1 * lockingSupport.releaseLock(mockLock)
    when:
    def results = mockMvc.perform(put('/portfolio/id')
      .with(csrf())
      .content(toJson(editPortfolioForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should unlock when modifying portfolio with invalid form"() {
    setup:
    companyTeamValidationService.validCompanyEntityTeams(
      _ as String,
      _ as String,
      _ as AllowedTeamsForm) >> true
    companyRepository.companyEntity(_ as String) >> right(new Company())
    0 * service.update(_ as Authentication, "id", _ as PortfolioUpdateForm) >> right(EntityId.entityId("id"))
    def mockLock = Mock(ClusterLock)
    1 * lockingSupport.tryLock(new XplainLock(TRADES_LOCK_ID)) >> right(mockLock)
    1 * lockingSupport.releaseLock(mockLock)
    when:
    def results = mockMvc.perform(put('/portfolio/id')
      .with(csrf())
      .content(toJson([:]))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 412
  }

  def "should return error when lock not fetched"() {
    setup:
    companyTeamValidationService.validCompanyEntityTeams(
      _ as String,
      _ as String,
      _ as AllowedTeamsForm) >> true
    companyRepository.companyEntity(_ as String) >> right(new Company())
    0 * service.update(_ as Authentication, "id", _ as PortfolioUpdateForm) >> right(EntityId.entityId("id"))
    def mockLock = Mock(ClusterLock)
    1 * lockingSupport.tryLock(new XplainLock(TRADES_LOCK_ID)) >> left(OPERATION_NOT_ALLOWED.entity("ERR"))
    0 * mockLock.unlock()
    when:
    def results = mockMvc.perform(put('/portfolio/id')
      .with(csrf())
      .content(toJson(editPortfolioForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 403
    results.getResponse().getContentAsString().indexOf("ERR") >= 0
  }

  static editPortfolioForm() {
    return [
      name            : "PORTFOLIO_NAME",
      allowedTeamsForm: new AllowedTeamsForm(true, []),
      entityId        : "000000000000000000000000",
      companyId       : "000000000000000000000000",
      description     : "description"
    ]
  }
}
