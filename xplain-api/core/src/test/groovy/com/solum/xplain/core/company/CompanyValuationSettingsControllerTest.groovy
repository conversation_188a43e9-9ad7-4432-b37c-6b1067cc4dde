package com.solum.xplain.core.company

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.company.form.ValuationSettingsForm
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.company.value.ValuationSettingsView
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.test.MockMvcConfiguration
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [CompanyValuationSettingsController])
class CompanyValuationSettingsControllerTest extends Specification {

  static def STATE_DATE = parse("1999-01-01")

  @SpringBean
  CompanyValuationSettingsService service = Mock()

  @SpringBean
  CurveConfigurationRepository curveConfigurationRepository = Mock()

  @SpringBean
  MarketDataGroupRepository marketDataGroupRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport support = new RequestPathVariablesSupport()

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper mapper

  def "should get default valuation settings"() {
    setup:
    service.getCompanyValuationSettings(
      _ as Authentication,
      "id",
      { it.getActualDate() == STATE_DATE } as BitemporalDate
      ) >> right(new ValuationSettingsView())

    def results = mockMvc.perform(get('/companies/id/valuations')
      .param("stateDate", STATE_DATE.toString())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  @Unroll
  def "should update default valuation settings with response #responseBody"() {
    setup:
    def companyId = "000000000000000000000000"

    marketDataGroupRepository.validEntity(_ as String) >> right(
      new MarketDataGroup(
      companies: [new CompanyReference(entityId: companyId)],
      allowAllCompanies: false
      ))
    marketDataGroupRepository.validEntity("aaa") >> left(OBJECT_NOT_FOUND.entity())
    curveConfigurationRepository.validCurveConfigurationId(_ as String) >> true
    service.updateDefaultSettingsVersion(
      companyId,
      STATE_DATE,
      _ as Authentication,
      _ as ValuationSettingsForm
      ) >> right(EntityId.entityId("id"))

    def results = mockMvc.perform(put('/companies/{id}/valuations/1999-01-01', companyId)
      .with(csrf())
      .content(mapper.writeValueAsString(settingsForm))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }
    where:
    settingsForm                                                                                                                              | code | responseBody
    valuationSettingsForm()                                                                                                                   | 200  | "id"
    valuationSettingsForm().tap({ c -> c.put("nonFxCurveConfigurationId", null) })                                                            | 200  | "id"
    valuationSettingsForm().tap({ c -> c.put("configurationType", null) })                                                                    | 412  | "NotEmpty.valuationSettingsForm.configurationType"
    valuationSettingsForm().tap({ c -> c.put("curveConfigurationId", null) })                                                                 | 412  | "NotEmpty.valuationSettingsForm.curveConfigurationId"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", null, "USD", false)) })   | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.strippingType"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "test", "USD", false)) }) | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.strippingType"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("test", "OIS", "USD", false)) })          | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.discountingType"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(null, "OIS", "USD", false)) })            | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.discountingType"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", "test", false)) }) | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.triangulationCcy"
    valuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", null, false)) })   | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.triangulationCcy"
    valuationSettingsForm().tap({ c -> c.put("reportingCurrency", null) })                                                                    | 412  | "NotEmpty.valuationSettingsForm.reportingCurrency"
    valuationSettingsForm().tap({ c -> c.put("reportingCurrency", "t") })                                                                     | 412  | "ValidStringSet.valuationSettingsForm.reportingCurrency"
    valuationSettingsForm().tap({ c -> c.put("configurationType", "a") })                                                                     | 412  | "ValidStringSet.valuationSettingsForm.configurationType"
    valuationSettingsForm().tap({ c -> c.put("marketDataGroupId", "aaa") })                                                                   | 412  | "ValidObjectId.valuationSettingsForm"
    valuationSettingsForm().tap({ c -> c.putAll(["nonFxCurveConfigurationId": null, configurationType: "FX_V_IRS"]) })                        | 412  | "NotEmpty.valuationSettingsForm.nonFxCurveConfigurationId"
  }

  @WithMockUser
  def "should delete valuation settings"() {
    setup:
    1 * service.deleteValuationSettings(
      _,
      "companyId",
      STATE_DATE,
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/companies/companyId/valuations/1999-01-01/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.response.status == 200
  }

  def "should get default valuation settings versions"() {
    setup:

    service.getCompanySettingsVersions(_ as Authentication, "id") >> right([])

    def results = mockMvc.perform(get('/companies/id/valuations/versions')
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get company entities valuation settings lists"() {
    setup:

    service.getAllEntitySettings(
      _ as Authentication,
      "id",
      { it.getActualDate() == STATE_DATE } as BitemporalDate
      ) >> right(List.of())

    def results = mockMvc.perform(get('/companies/id/valuations/entities')
      .param("stateDate", STATE_DATE.toString())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get entity valuation settings"() {
    setup:
    service.getEntitySettings(
      _ as Authentication,
      "id",
      "id",
      { it.getActualDate() == STATE_DATE } as BitemporalDate
      ) >> right(new CompanyLegalEntityValuationSettingsView())

    def results = mockMvc.perform(get('/companies/id/valuations/entities/id/1999-01-01')
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get entities valuation settings versions list"() {
    setup:
    service.getCompanyEntitySettingsVersions(_ as Authentication, "id", "id") >> right([])

    def results = mockMvc.perform(get('/companies/id/valuations/entities/id/versions')
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  @Unroll
  def "should update entity valuation settings with response #responseBody"() {
    setup:
    def companyId = "000000000000000000000000"
    marketDataGroupRepository.validEntity(_ as String) >> right(
      new MarketDataGroup(
      companies: [new CompanyReference(entityId: companyId)],
      allowAllCompanies: false
      ))
    marketDataGroupRepository.validEntity("aaa") >> left(OBJECT_NOT_FOUND.entity())
    curveConfigurationRepository.validCurveConfigurationId(_ as String) >> configExists
    service.updateCompanyEntitySettingsVersion(
      companyId,
      "id",
      parse("1999-01-01"),
      _ as Authentication,
      _ as ValuationSettingsForm
      ) >> right(EntityId.entityId("id"))

    def results = mockMvc.perform(put('/companies/{id}/valuations/entities/id/1999-01-01', companyId)
      .with(csrf())
      .content(mapper.writeValueAsString(settingsForm))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    configExists | settingsForm                                                                                                                                    | code | responseBody
    true         | entityValuationSettingsForm()                                                                                                                   | 200  | "id"
    false        | entityValuationSettingsForm()                                                                                                                   | 412  | "ValidCurveConfigurationId"
    true         | entityValuationSettingsForm().tap({ c -> c.put("marketDataGroupId", "aaa") })                                                                   | 412  | "ValidObjectId.valuationSettingsForm"
    true         | entityValuationSettingsForm().tap({ c -> c.put("nonFxCurveConfigurationId", null) })                                                            | 200  | "id"
    true         | entityValuationSettingsForm().tap({ c -> c.put("configurationType", null) })                                                                    | 412  | "NotEmpty.valuationSettingsForm.configurationType"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveConfigurationId", null) })                                                                 | 412  | "NotEmpty.valuationSettingsForm.curveConfigurationId"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", null, "USD", false)) })   | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.strippingType"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "test", "USD", false)) }) | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.strippingType"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("test", "OIS", "USD", false)) })          | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.discountingType"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(null, "OIS", "USD", false)) })            | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.discountingType"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", "test", false)) }) | 412  | "ValidStringSet.valuationSettingsForm.curveDiscountingForm.triangulationCcy"
    true         | entityValuationSettingsForm().tap({ c -> c.put("curveDiscountingForm", new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", null, false)) })   | 412  | "NotEmpty.valuationSettingsForm.curveDiscountingForm.triangulationCcy"
    true         | entityValuationSettingsForm().tap({ c -> c.put("reportingCurrency", null) })                                                                    | 412  | "NotEmpty.valuationSettingsForm.reportingCurrency"
    true         | entityValuationSettingsForm().tap({ c -> c.put("reportingCurrency", "t") })                                                                     | 412  | "ValidStringSet.valuationSettingsForm.reportingCurrency"
    true         | entityValuationSettingsForm().tap({ c -> c.put("configurationType", "a") })                                                                     | 412  | "ValidStringSet.valuationSettingsForm.configurationType"
    true         | entityValuationSettingsForm().tap({ c -> c.putAll(["nonFxCurveConfigurationId": null, configurationType: "FX_V_IRS"]) })                        | 412  | "NotEmpty.valuationSettingsForm.nonFxCurveConfigurationId"
    true         | defaultValuationSettings()                                                                                                                      | 200  | "id"
  }

  @WithMockUser
  def "should delete entity valuation settings"() {
    setup:
    1 * service.deleteEntityValuationSettings(
      _,
      "companyId",
      "id",
      STATE_DATE,
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/companies/companyId/valuations/entities/id/1999-01-01/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.response.status == 200
  }

  def "should update entity valuation settings to default"() {
    setup:
    service.updateCompanyEntitySettingsVersion(
      "id",
      "id",
      parse("1999-01-01"),
      _ as Authentication,
      _ as ValuationSettingsForm
      ) >> right(EntityId.entityId("id"))

    def results = mockMvc.perform(put('/companies/id/valuations/entities/id/1999-01-01')
      .with(csrf())
      .content(mapper.writeValueAsString(settingsForm))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    settingsForm                                                            | code | responseBody
    ["settingsType": "DEFAULT", versionForm: NewVersionFormV2.newDefault()] | 200  | "id"
    ["settingsType": "DEFAULT", versionForm: NewVersionFormV2.newDefault()] | 200  | "id"
    ["settingsType": "BESPOKE", versionForm: NewVersionFormV2.newDefault()] | 412  | ""
  }

  def "should get company settings versions dates"() {
    setup:
    1 * service.getValuationSettingsFutureVersions(_, "id", STATE_DATE) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/companies/id/valuations/future-versions/search")
      .param("stateDate", STATE_DATE.toString()))
      .andReturn()

    then:
    results.response.status == 200
  }

  def "should get legal entity settings versions dates"() {
    setup:
    1 * service.getLegalEntitiesSettingsFutureVersions(_, "id", "entityId", STATE_DATE) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/companies/id/valuations/entities/entityId/future-versions/search")
      .param("stateDate", STATE_DATE.toString()))
      .andReturn()

    then:
    results.response.status == 200
  }

  def valuationSettingsForm() {
    [
      settingsType             : "BESPOKE",
      marketDataGroupId        : new ObjectId().toHexString(),
      configurationType        : "SINGLE",
      curveConfigurationId     : new ObjectId().toHexString(),
      nonFxCurveConfigurationId: new ObjectId().toHexString(),
      curveDiscountingForm     : new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", "USD", true),
      reportingCurrency        : "EUR",
      useCsaDiscounting        : "true",
      versionForm              : NewVersionFormV2.newDefault()

    ]
  }

  def entityValuationSettingsForm() {
    [
      settingsType             : "BESPOKE",
      marketDataGroupId        : new ObjectId().toHexString(),
      configurationType        : "SINGLE",
      curveConfigurationId     : new ObjectId().toHexString(),
      nonFxCurveConfigurationId: new ObjectId().toHexString(),
      curveDiscountingForm     : new CalculationDiscountingForm("DISCOUNT_EUR", "OIS", "USD", true),
      reportingCurrency        : "EUR",
      useCsaDiscounting        : "true",
      versionForm              : NewVersionFormV2.newDefault()

    ]
  }

  def defaultValuationSettings() {
    [
      settingsType: "DEFAULT",
      versionForm : NewVersionFormV2.newDefault()
    ]
  }
}
