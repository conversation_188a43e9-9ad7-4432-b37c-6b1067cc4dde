package com.solum.xplain.core.mdvalue

import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ASK
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.value.UserEntity
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvemarket.InstrumentCurveDate
import com.solum.xplain.core.curvemarket.InstrumentOverrideCurveDateDetails
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.service.MarketDataGroupService
import com.solum.xplain.core.market.service.MarketDataKeyResolver
import com.solum.xplain.core.market.value.MarketDataGroupView
import com.solum.xplain.core.market.value.MarketDataKeyView
import com.solum.xplain.core.market.value.MarketDataProviderTickerView
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.springframework.security.core.Authentication
import spock.lang.Specification

class MarketDataValueServiceTest extends Specification {

  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.now())
  private static def CURVE_DATE = LocalDate.of(2021, 01, 02)
  private static def PREVIOUS_CURVE_DATE = LocalDate.of(2021, 01, 01)

  private static def GROUP_ID = "groupId"

  private MarketDataValueRepository repository = Mock()
  private MarketDataKeyRepository mdkRepository = Mock()
  private MarketDataGroupService marketDataGroupService = Mock()
  private AuditEntryService auditEntryService = Mock()

  def service = new MarketDataValueService(
  repository,
  mdkRepository
  )

  def setup() {
    marketDataGroupService.userEntity(_ as Authentication, _ as String) >> Either.right(
      UserEntity.marketDataGroup(user(), new MarketDataGroupView(id: GROUP_ID))
      )
  }

  def "should get resolved values by key"() {
    setup:
    def mdValueView = new MarketDataValueFlatView(provider: "PROVIDER1", ticker: "TICKER1", value: BigDecimal.TEN, bidAsk: BID)
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.recordDate),
      false
      ) >> [mdValueView]

    def keyView = new MarketDataKeyView(
      key: "KEY",
      assetGroup: CoreAssetGroup.RATES,
      instrumentType: CoreInstrumentType.IBOR_FIXING_DEPOSIT,
      providerTickers: [
        new MarketDataProviderTickerView(code: "PROVIDER1", ticker: "TICKER1", factor: BigDecimal.ONE, bidAskType: BID_ONLY),
      ]
      )
    def resolver = new MarketDataKeyResolver([keyView])
    1 * mdkRepository.keyResolver(STATE_DATE, CoreAssetGroup.values()) >> resolver

    0 * auditEntryService._

    when:
    def result = service.getResolvedValuesByKey(GROUP_ID, STATE_DATE, CURVE_DATE, Arrays.asList(CoreAssetGroup.values()))

    then:
    result.isRight()
    def resolvedValuesByKey = result.getOrNull() as Map<String, ResolvedMarketDataValueView>
    resolvedValuesByKey.size() == 1
    resolvedValuesByKey.get("KEY") == new ResolvedMarketDataValueView(
      key: "KEY",
      assetGroup: "RATES",
      instrumentType: "IBOR_FIXING_DEPOSIT",
      values: [mdValueView]
      )
  }

  def "should get resolved values by key across different curve dates"() {
    setup:
    def iborFixingDepositMdk = mdkView("IBOR-FIX-KEY", "IBOR_FIXING_DEPOSIT", "TICKER1")
    def fxVolsKey = mdkView("FX-VOL-KEY", "FX_VOL", "TICKER2")
    def fxVolsSkewKey = mdkView("FX-VOL-SKEW-KEY", "FX_VOL_SKEW", "TICKER3")
    def resolver = new MarketDataKeyResolver([iborFixingDepositMdk, fxVolsKey, fxVolsSkewKey])
    1 * mdkRepository.keyResolver(STATE_DATE, CoreAssetGroup.values()) >> resolver

    def previousIborFixingDepositMdValueView = mdView("TICKER1", BigDecimal.valueOf(10.5))
    def previousFxVolsMdValueView = mdView("TICKER2", BigDecimal.valueOf(1.05))
    def previousFxVolsSkewMdValueView = mdView("TICKER3", BigDecimal.valueOf(10.5))

    def iborFixingDepositMdValueView = mdView("TICKER1", BigDecimal.TEN)
    def fxVolsMdValueView = mdView("TICKER2", BigDecimal.ONE)
    def fxVolsSkewMdValueView = mdView("TICKER3", BigDecimal.TEN)

    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(PREVIOUS_CURVE_DATE, STATE_DATE.recordDate),
      false
      ) >> [previousIborFixingDepositMdValueView, previousFxVolsMdValueView, previousFxVolsSkewMdValueView]

    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.recordDate),
      false
      ) >> [iborFixingDepositMdValueView, fxVolsMdValueView, fxVolsSkewMdValueView]

    0 * auditEntryService._

    def extractor = new InstrumentOverrideCurveDateDetails(
      CURVE_DATE,
      PREVIOUS_CURVE_DATE,
      new InstrumentCurveDate(
      CURVE_DATE,
      [CoreInstrumentType.FX_VOL, CoreInstrumentType.FX_VOL_SKEW]
      )
      )

    when:
    def result = service.getResolvedValuesByKey(GROUP_ID, STATE_DATE, extractor, Arrays.asList(CoreAssetGroup.values()))

    then:
    result.isRight()
    def resolvedValuesByKey = result.getOrNull() as Map<String, ResolvedMarketDataValueView>
    resolvedValuesByKey.size() == 3

    def iborFixKey = resolvedValuesByKey.get("IBOR-FIX-KEY")
    iborFixKey.key == "IBOR-FIX-KEY"
    iborFixKey.instrumentType == "IBOR_FIXING_DEPOSIT"
    iborFixKey.values[0] == previousIborFixingDepositMdValueView

    def fxVolKey = resolvedValuesByKey.get("FX-VOL-KEY")
    fxVolKey.key == "FX-VOL-KEY"
    fxVolKey.instrumentType == "FX_VOL"
    fxVolKey.values[0] == fxVolsMdValueView

    def fxVolSkewKey = resolvedValuesByKey.get("FX-VOL-SKEW-KEY")
    fxVolSkewKey.key == "FX-VOL-SKEW-KEY"
    fxVolSkewKey.instrumentType == "FX_VOL_SKEW"
    fxVolSkewKey.values[0] == fxVolsSkewMdValueView
  }

  def "should get resolved values by key when no market data value"() {
    setup:
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.recordDate),
      false
      ) >> [new MarketDataValueFlatView()]

    def keyView = mdkView()
    def resolver = new MarketDataKeyResolver([keyView])
    1 * mdkRepository.keyResolver(STATE_DATE, CoreAssetGroup.values()) >> resolver

    0 * auditEntryService._

    when:
    def result = service.getResolvedValuesByKey(GROUP_ID, STATE_DATE, CURVE_DATE, Arrays.asList(CoreAssetGroup.values()))

    then:
    result.isRight()
    def resolvedValuesByKey = result.getOrNull() as Map<String, ResolvedMarketDataValueView>
    resolvedValuesByKey.size() == 1
    with(resolvedValuesByKey.get("KEY")) {
      key == "KEY"
      assetGroup == "RATES"
      instrumentType == "IBOR_FIXING_DEPOSIT"
      values.sort(false, { it.getBidAsk() }) == [
        new MarketDataValueFlatView(provider: "PROVIDER1", ticker: "TICKER1", value: null, bidAsk: BID),
        new MarketDataValueFlatView(provider: "PROVIDER1", ticker: "TICKER1", value: null, bidAsk: ASK),
      ]
    }
  }

  def mdkView(String key = "KEY", String instrumentType = "IBOR_FIXING_DEPOSIT", String p1Ticker = "TICKER1") {
    new MarketDataKeyView(
      key: key,
      assetGroup: CoreAssetGroup.RATES,
      instrumentType: instrumentType,
      providerTickers: [
        new MarketDataProviderTickerView(code: "PROVIDER1", ticker: p1Ticker, factor: BigDecimal.ONE, bidAskType: BID_ASK),
      ]
      )
  }

  def mdView(String ticker = "TICKER1", BigDecimal value = BigDecimal.TEN) {
    new MarketDataValueFlatView(
      provider: "PROVIDER1",
      ticker: ticker,
      value: value,
      bidAsk: BID
      )
  }
}
