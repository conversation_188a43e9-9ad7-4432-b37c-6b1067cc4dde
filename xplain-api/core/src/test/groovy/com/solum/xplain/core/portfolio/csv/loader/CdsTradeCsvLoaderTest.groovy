package com.solum.xplain.core.portfolio.csv.loader

import static com.solum.xplain.core.portfolio.CoreProductType.CDS

import com.opengamma.strata.product.common.BuySell
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSeniority
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class CdsTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def commonLoader = new CommonCreditTradeCsvLoader()
  def loader = new CdsTradeCsvLoader(commonLoader)

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CDS)
  }

  def "should parse CDS trade"() {
    setup:
    def rows = loadResource("CdsTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 3
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "EUR"))
    result.businessDayAdjustmentType == null
    result.positionType.toBuySell() == BuySell.SELL
    result.startDate == LocalDate.parse("2016-01-01")
    result.endDate == LocalDate.parse("2017-01-01")
    result.creditTradeDetails.reference == "BARC_GB_LON"
    result.creditTradeDetails.seniority == CreditSeniority.SNRFOR
    result.creditTradeDetails.docClause == CreditDocClause.CR14
    result.creditTradeDetails.upfront == 6
    result.creditTradeDetails.upfrontConvention == "Following"
    result.creditTradeDetails.upfrontDate == LocalDate.parse("2016-01-01")
    result.creditTradeDetails.entityLongName == "LONG NAME"
    result.creditTradeDetails.corpTicker == "CORP"

    def leg = result.tradePositionLeg().orElse(null)
    leg.paymentFrequency == "1M"
    leg.currency == "EUR"
    leg.notional == 1000000
    leg.initialValue == 9
  }

  def "should return CDS parse errors"() {
    setup:
    def rows = loadResource("CdsTradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description == expectedError

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Unsupported value: INVALID for field Credit Freq. Supported values: [1W, 2W, 4W, 1M, 2M, 7W, 3M, 13W, 4M, 5M, 6M, 26W, 7M, 8M, 9M, 10M, 11M, 12M, TERM]"
    1   | "Error at line number 3. Error: Unknown BuySell value, must be 'Buy' or 'Sell' but was 'INVALID'; parser is case insensitive and also accepts 'B' and 'S'"
    2   | "Error at line number 4. Error: Unsupported value: X for field Credit Doc Clause. Supported values: [CR, CR14, MM, MM14, MR, MR14, UNDEFINED, XR, XR14]"
    3   | "Error at line number 5. Error: No value was found for 'Credit Reference'"
    4   | "Error at line number 6. Error: Unsupported value: ModifiedFollowing for field Business Day Convention. Supported values: [Following]"
    5   | "Error at line number 7. Error: Unsupported value: ShortInitial for field Stub. Supported values: [SmartInitial]"
    6   | "Error at line number 8. Error: Unsupported value: Act/364 for field Credit Day Count. Supported values: [Act/360]"
    7   | "Error at line number 9. Error: Error parsing field End Date. Must be after Start Date"
    8   | "Error at line number 10. Error: Unsupported value: AUD. Supported values: [EUR, GBP, JPY, USD]"
    9   | "Error at line number 11. Error: No value was found for 'Credit Ccy'"
    10  | "Error at line number 12. Error: No value was found for 'Credit Doc Clause'"
    11  | "Error at line number 13. Error: Value must be positive for field: Credit Notional"
  }
}
