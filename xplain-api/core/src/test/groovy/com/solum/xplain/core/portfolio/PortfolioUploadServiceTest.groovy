package com.solum.xplain.core.portfolio

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.LENIENT
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.error.Error.DUPLICATE_ENTRY
import static com.solum.xplain.core.error.Error.IMPORT_INFO
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.InfoItem
import com.solum.xplain.core.portfolio.csv.PortfolioCsvLoader
import com.solum.xplain.core.portfolio.csv.PortfolioCsvLoaderFactory
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.ImportPortfolio
import com.solum.xplain.core.portfolio.value.PortfolioUniqueKey
import spock.lang.Specification

class PortfolioUploadServiceTest extends Specification {

  PortfolioRepository repository = Mock(PortfolioRepository)
  AuthenticationContext userRepository = Mock(AuthenticationContext)
  PortfolioCsvLoader loader = Mock(PortfolioCsvLoader)
  PortfolioCsvLoaderFactory loaderFactory = Mock(PortfolioCsvLoaderFactory)
  AuditEntryService auditEntryService = Mock(AuditEntryService)
  PortfolioTeamFilterProvider teamFilterProvider = Mock(PortfolioTeamFilterProvider)
  PortfolioUploadService upload = new PortfolioUploadService(
  repository,
  userRepository,
  loaderFactory,
  auditEntryService,
  teamFilterProvider)

  def "setup"() {
    loaderFactory.getLoader(_ as XplainPrincipal) >> loader
  }

  def "should return parse errors"() {
    setup:

    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> left([new ErrorItem(PARSING_ERROR, "Parsing error")])

    when:
    def result = upload.uploadPortfolios(STRICT, ERROR, [] as byte[])

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    errors[0].description == "Parsing error"
  }

  def "should return DUPLICATE_ENTRY for DUPLICATE portfolio when ERROR"() {
    setup:
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm()]))
    repository.activeImportPortfoliosList(_) >> [importPortfolio()]

    when:
    def result = upload.uploadPortfolios(STRICT, ERROR, [] as byte[])

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "name already exists"
  }

  def "should return MISSING_ENTRY for MISSING portfolio when ERROR"() {
    setup:
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm()]))

    when:
    def result = upload.uploadPortfolios(STRICT, ERROR, [] as byte[])

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "missingName is missing"

    1 * repository.activeImportPortfoliosList(activePortfolios()) >> [missingImportPortfolio()]
  }

  def "should import NEW portfolio when APPEND"() {
    setup:
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm()]))
    1 * repository.activeImportPortfoliosList(_) >> []

    1 * repository.bulkInsert(_) >> [(new PortfolioUniqueKey("companyId", "entityId", "name")):right(entityId("entityId"))]
    0 * repository.bulkUpdate(_)
    0 * repository.bulkArchive(_)

    when:
    def result = upload.uploadPortfolios(STRICT, APPEND, [] as byte[])

    then:
    result.isRight()
    result.getOrNull().size() == 1
    result.getOrNull()[0] == entityId("entityId")
  }

  def "should import NEW portfolio when APPEND and log parsing warnings"() {
    setup:
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], LENIENT) >> right(csvResult([portfolioForm()], [PARSING_ERROR.entity("ERR")]))
    1 * repository.activeImportPortfoliosList(_) >> []

    1 * repository.bulkInsert(_) >> [(new PortfolioUniqueKey("companyId", "entityId", "name")):right(entityId("entityId"))]
    0 * repository.bulkUpdate(_)
    0 * repository.bulkArchive(_)

    when:
    def result = upload.uploadPortfolios(LENIENT, APPEND, [] as byte[])

    then:
    result.isRight()
    result.getOrNull().size() == 1
    result.getOrNull()[0] == entityId("entityId")

    and:
    1 * auditEntryService.newEntryWithLogs(
      AuditEntry.of("portfolio", "Finished Portfolio import with 0 errors, 1 warnings and 1 changes"),
      [
        InfoItem.of(IMPORT_INFO, entityId("entityId"), "Inserted name"),
        PARSING_ERROR.entity("ERR")
      ]
      )
  }

  def "should import NEW portfolio and archive MISSING when APPEND_DELETE"() {
    setup:
    var missingPortfolio = missingImportPortfolio("actualUniqueId")
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm(), duplicatePortfolioForm()]))
    1 * repository.activeImportPortfoliosList(activePortfolios()) >> [duplicateImportPortfolio(), missingPortfolio]
    2 * repository.duplicatePortfoliosCount(_, _) >> 0
    1 * repository.bulkInsert(_) >> [(new PortfolioUniqueKey("companyId", "entityId", "name")):right(entityId("entityId"))]
    0 * repository.bulkUpdate(_)
    1 * repository.bulkArchive(_) >> [right(entityId(missingPortfolio.id()))]
    when:
    def result = upload.uploadPortfolios(STRICT, APPEND_DELETE, [] as byte[])

    then:
    result.isRight()
    result.getOrNull().size() == 2
    result.getOrNull()[0] == entityId("entityId")
    result.getOrNull()[1] == entityId(missingPortfolio.id())
  }

  def "should import NEW portfolio and replace DUPLICATE when REPLACE"() {
    setup:
    var duplicatePortfolio = duplicateImportPortfolio("actualUniqueId")
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm(), duplicatePortfolioForm()]))
    1 * repository.activeImportPortfoliosList(_) >> [duplicatePortfolio, missingImportPortfolio()]
    2 * repository.duplicatePortfoliosCount(_, _) >> 0

    1 * repository.bulkInsert(_) >> [(new PortfolioUniqueKey("companyId", "entityId", "name")):right(entityId("newEntityId"))]
    1 * repository.bulkUpdate(_) >> [entityId(duplicatePortfolio.id())]
    0 * repository.bulkArchive(_)
    when:
    def result = upload.uploadPortfolios(STRICT, REPLACE, [] as byte[])

    then:
    result.isRight()
    result.getOrNull().size() == 2
    result.getOrNull()[0] == entityId("newEntityId")
    result.getOrNull()[1] == entityId(duplicatePortfolio.id())
  }

  def "should import NEW portfolio, replace DUPLICATE and archive MISSING when REPLACE_DELETE"() {
    setup:
    var dupPortfolio = duplicateImportPortfolio("dup")
    var missPortfolio = missingImportPortfolio("missId")
    userRepository.currentUser() >> user()
    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm(), duplicatePortfolioForm()]))
    1 * repository.activeImportPortfoliosList(_) >> [dupPortfolio, missPortfolio]
    2 * repository.duplicatePortfoliosCount(_, _) >> 0

    1 * repository.bulkInsert(_) >> [(new PortfolioUniqueKey("companyId", "entityId", "name")):right(entityId("newEntityId"))]
    1 * repository.bulkUpdate(_) >> [entityId(dupPortfolio.id())]
    1 * repository.bulkArchive(_) >> [right(entityId(missPortfolio.id()))]
    when:
    def result = upload.uploadPortfolios(STRICT, REPLACE_DELETE, [] as byte[])

    then:
    result.isRight()
    result.getOrNull().size() == 3
    result.getOrNull()[0] == entityId("newEntityId")
    result.getOrNull()[1] == entityId(dupPortfolio.id())
    result.getOrNull()[2] == entityId(missPortfolio.id())
  }

  def "should NOT REPLACE portfolio if no access"() {
    setup:
    def filter = new PortfolioTeamFilter([], [], [])

    userRepository.currentUser() >> user()

    loader.parse(_ as byte[], STRICT) >> right(csvResult([portfolioForm()]))
    1 * repository.activeImportPortfoliosList(_) >> [importPortfolio()]
    1 * teamFilterProvider.provideFilter(user()) >> filter
    1 * repository.duplicatePortfoliosCount(_, PortfolioTeamFilter.emptyFilter()) >> 1
    1 * repository.duplicatePortfoliosCount(_, filter) >> 0


    0 * repository.archive("id")
    0 * repository.bulkInsert(_) >> right([])
    0 * repository.update("id", (PortfolioUpdateForm) _)
    when:
    def result = upload.uploadPortfolios(STRICT, REPLACE, [] as byte[])

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == OPERATION_NOT_ALLOWED
    errors[0].description == "Unable to modify portfolio without access"
  }

  static CsvParserResult<PortfolioUpdateForm> csvResult(List<PortfolioUpdateForm> portfolios, List<ErrorItem> errors = []) {
    return new CsvParserResult<PortfolioUpdateForm>(portfolios, errors)
  }

  static PortfolioCreateForm portfolioForm(externalId = "name") {
    def form = new PortfolioCreateForm()
    form.companyId = "companyId"
    form.entityId = "entityId"
    form.externalPortfolioId = externalId
    return form
  }

  static PortfolioCreateForm duplicatePortfolioForm() {
    return portfolioForm("duplicateName")
  }

  static PortfolioUniqueKey uniqueKey(String externalId = "name") {
    return new PortfolioUniqueKey("companyId", "entityId", externalId)
  }

  static ImportPortfolio importPortfolio(String id = "id", String externalId = "name") {
    return new ImportPortfolio(id, uniqueKey(externalId))
  }

  static ImportPortfolio missingImportPortfolio(String id = "id") {
    return importPortfolio(id, "missingName")
  }

  static ImportPortfolio duplicateImportPortfolio(String id = "id") {
    return importPortfolio(id,"duplicateName")
  }
}
