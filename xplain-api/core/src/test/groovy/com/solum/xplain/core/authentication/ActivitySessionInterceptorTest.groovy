package com.solum.xplain.core.authentication


import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.config.properties.OAuth2CustomProperties
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.KeyValueCache
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import org.bson.types.ObjectId
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.AnonymousAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import spock.lang.Specification

class ActivitySessionInterceptorTest extends Specification {
  private static final Clock CLOCK = Clock.fixed(Instant.parse("2023-09-26T12:42:13.412Z"), ZoneOffset.UTC)
  def dataGrid = Mock(DataGrid)
  def authContext = Mock(AuthenticationContext)
  def interceptor = new ActivitySessionInterceptor(dataGrid, authContext, new OAuth2CustomProperties(inactivityTimeoutMinutes: 1))

  def setup() {
    interceptor.configureClock(CLOCK)
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
  }

  def "should skip validations/storing when client principal"() {
    setup:
    def user = Mock(XplainPrincipal)
    1 * user.isTrustedPrincipal() >> true
    1 * authContext.currentUser() >> user

    when:
    def result = interceptor.preHandle(null, null, null)

    then:
    result
    0 * dataGrid._
  }

  def "should skip validations/storing when anonymous client"() {
    setup:
    def anonymousUser = Mock(AnonymousAuthenticationToken)
    SecurityContextHolder.getContext().authentication = anonymousUser

    when:
    def result = interceptor.preHandle(null, null, null)

    then:
    result
    0 * dataGrid._
  }

  def "should store new session and respond with 401"() {
    setup:
    def user = Mock(XplainPrincipal)
    1 * user.isTrustedPrincipal() >> false
    1 * authContext.currentUser() >> user

    def request = Mock(HttpServletRequest)
    1 * request.getCookies() >> null
    1 * request.getRequestURI() >> "/api"
    def responseWriter = Mock(PrintWriter)
    def response = Mock(HttpServletResponse)
    1 * response.getWriter() >> responseWriter

    def cacheMap = Mock(KeyValueCache)
    1 * dataGrid.getKeyValueCache("ACTIVITY_SESSION") >> cacheMap

    when:
    def result = interceptor.preHandle(request, response, null)

    then:
    !result
    1 * cacheMap.set(_, CLOCK.instant(), Duration.ofMinutes(1))
    1 * response.addCookie({ it -> it.getName() == "ACTIVITY_SESSION" })
    1 * response.setStatus(HttpStatus.UNAUTHORIZED.value())
  }

  def "should update current session and proceed"() {
    setup:
    def user = Mock(XplainPrincipal)
    1 * user.isTrustedPrincipal() >> false
    1 * authContext.currentUser() >> user

    def currentSessionId = ObjectId.get().toHexString()

    def request = Mock(HttpServletRequest)
    1 * request.getCookies() >> [new Cookie("ACTIVITY_SESSION", currentSessionId)].toArray()
    def response = Mock(HttpServletResponse)

    def cacheMap = Mock(KeyValueCache)
    1 * cacheMap.get(currentSessionId) >> CLOCK.instant()
    2 * dataGrid.getKeyValueCache("ACTIVITY_SESSION") >> cacheMap

    when:
    def result = interceptor.preHandle(request, response, null)

    then:
    result
    1 * cacheMap.set(currentSessionId, CLOCK.instant(), Duration.ofMinutes(1))
    0 * response._
  }

  def "should skip validations/storing for SSE request"() {
    setup:
    def user = Mock(XplainPrincipal)
    1 * user.isTrustedPrincipal() >> false
    1 * authContext.currentUser() >> user


    def mockRequest = Mock(HttpServletRequest)
    def mockResponse = Mock(HttpServletResponse)
    def cacheMap = Mock(KeyValueCache)

    1 * mockRequest.getHeader("Accept") >> "text/event-stream"

    when:
    def result = interceptor.preHandle(mockRequest, mockResponse, null)

    then:
    result
    0 * dataGrid._
    0 * cacheMap._
    0 * mockResponse._
  }

  def "should respond with unauthorized for expired session on request after SSE"() {
    setup:
    def user = Mock(XplainPrincipal)
    def request = Mock(HttpServletRequest)
    def response = Mock(HttpServletResponse)
    def responseWriter = Mock(PrintWriter)
    def cacheMap = Mock(KeyValueCache)

    def expiredSessionId = ObjectId.get().toHexString()

    1 * user.isTrustedPrincipal() >> false
    1 * authContext.currentUser() >> user
    1 * request.getRequestURI() >> "/api/move"
    1 * request.getCookies() >> [new Cookie("ACTIVITY_SESSION", expiredSessionId)].toArray()

    2 * dataGrid.getKeyValueCache("ACTIVITY_SESSION") >> cacheMap
    1 * cacheMap.get(expiredSessionId) >> CLOCK.instant().minusSeconds(200) // Simulate expired session
    1 * response.getWriter() >> responseWriter

    when:
    def result = interceptor.preHandle(request, response, null)

    then:
    !result
    1 * response.setStatus(HttpStatus.UNAUTHORIZED.value())
    1 * response.addCookie({ it.name == "ACTIVITY_SESSION" })
    1 * cacheMap.set({ it -> it != expiredSessionId }, CLOCK.instant(), Duration.ofMinutes(1))
  }

  def "should restrict current session when expired"() {
    setup:
    def user = Mock(XplainPrincipal)
    1 * user.isTrustedPrincipal() >> false
    1 * authContext.currentUser() >> user

    def currentSessionId = ObjectId.get().toHexString()

    def request = Mock(HttpServletRequest)
    1 * request.getRequestURI() >> "/api"
    1 * request.getCookies() >> [new Cookie("ACTIVITY_SESSION", currentSessionId)].toArray()
    def responseWriter = Mock(PrintWriter)
    def response = Mock(HttpServletResponse)
    1 * response.getWriter() >> responseWriter

    def cacheMap = Mock(KeyValueCache)
    1 * cacheMap.get(currentSessionId) >> CLOCK.instant().minusSeconds(100)
    2 * dataGrid.getKeyValueCache("ACTIVITY_SESSION") >> cacheMap

    when:
    def result = interceptor.preHandle(request, response, null)

    then:
    !result
    1 * cacheMap.set({ it ->
      currentSessionId != it
    }, CLOCK.instant(), Duration.ofMinutes(1))
    1 * response.addCookie({ it -> it.getName() == "ACTIVITY_SESSION" })
    1 * response.setStatus(HttpStatus.UNAUTHORIZED.value())
  }
}
