package com.solum.xplain.core.classifiers

import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.EUR_ESTR_USD_SOFR
import static java.util.Optional.of

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention
import com.opengamma.strata.product.deposit.type.TermDepositConventions
import com.opengamma.strata.product.fra.type.FraConventions
import com.opengamma.strata.product.fx.type.FxSwapConvention
import com.opengamma.strata.product.index.type.IborFutureContractSpecs
import com.opengamma.strata.product.swap.type.FixedIborSwapConventions
import com.opengamma.strata.product.swap.type.FixedInflationSwapConventions
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions
import com.opengamma.strata.product.swap.type.IborIborSwapConventions
import com.opengamma.strata.product.swap.type.OvernightIborSwapConventions
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions
import com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions
import com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions
import spock.lang.Specification

class CurveNodeTypesTest extends Specification {
  def "should resolve node type from convention"() {
    expect:
    CurveNodeTypes.nodeTypeFromConvention(convention) == expected
    where:
    convention                                                       | expected
    EUR_ESTR_USD_SOFR                                                | of(CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE)
    FraConventions.of(IborIndices.EUR_EURIBOR_1M)                    | of(CurveNodeTypes.FRA_NODE)
    IborFixingDepositConvention.of(IborIndices.EUR_EURIBOR_1M)       | of(CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE)
    IborFutureContractSpecs.EUR_EURIBOR_3M_IMM_EUREX                 | of(CurveNodeTypes.IBOR_FUTURE_NODE)
    FixedOvernightSwapConventions.CHF_FIXED_1Y_SARON_OIS             | of(CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE)
    FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M                 | of(CurveNodeTypes.FIXED_IBOR_SWAP_NODE)
    IborIborSwapConventions.JPY_LIBOR_1M_LIBOR_6M                    | of(CurveNodeTypes.IBOR_IBOR_SWAP_NODE)
    OvernightIborSwapConventions.GBP_SONIA_OIS_1Y_LIBOR_3M           | of(CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE)
    XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M          | of(CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE)
    StandardXCcyIborOvernightSwapConventions.AUD_BBSW_3M_USD_SOFR    | of(CurveNodeTypes.XCCY_IBOR_OIS_SWAP_NODE)
    StandardXCcyFixedOvernightSwapConventions.INR_FIXED_6M_USD_SOFR  | of(CurveNodeTypes.XCCY_FIXED_OVERNIGHT_SWAP_NODE)
    FxSwapConvention.of(CurrencyPair.of(Currency.EUR, Currency.USD)) | of(CurveNodeTypes.FX_SWAP_NODE)
    FixedInflationSwapConventions.CHF_FIXED_ZC_CH_CPI                | of(CurveNodeTypes.FIXED_INFLATION_SWAP_NODE)
    TermDepositConventions.CHF_DEPOSIT_T2                            | of(CurveNodeTypes.TERM_DEPOSIT_NODE)
  }
}
