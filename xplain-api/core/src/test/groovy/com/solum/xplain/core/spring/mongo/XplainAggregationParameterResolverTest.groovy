package com.solum.xplain.core.spring.mongo

import com.solum.xplain.shared.utils.filter.TableFilter
import org.springframework.core.convert.ConversionService
import org.springframework.data.mongodb.core.aggregation.MatchOperation
import org.springframework.data.mongodb.core.query.Criteria
import spock.lang.Specification

class XplainAggregationParameterResolverTest extends Specification {
  def conversionService = Mock(ConversionService)

  static class TestEntity {}

  def "resolves TableFilter"() {
    given:
    def resolver = new XplainAggregationParameterResolver(conversionService: conversionService)
    def tableFilter = Mock(TableFilter)
    def criteria = Criteria.where("foo").is("bar")

    when:
    def result = resolver.getParameterOperations(tableFilter, TestEntity)

    then:
    1 * tableFilter.criteria(TestEntity, conversionService) >> criteria
    result.size() == 1
    result[0] instanceof MatchOperation
    result[0].criteriaDefinition == criteria
  }
}
