package com.solum.xplain.core.ccyexposure


import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.test.MockMvcConfiguration
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [CcyExposureController])
@MockMvcConfiguration
class CcyExposureControllerTest extends Specification {

  @SpringBean
  private CcyExposureControllerService service = Mock()

  @SpringBean
  private CcyExposureRepository repository = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper

  @Unroll
  @WithMockUser
  def "should perform validation when inserting with response #code #responseBody and form (#form)"() {
    setup:
    service.create(_ as CcyExposureCreateForm) >> right(EntityId.entityId("1"))
    def json = objectMapper.writeValueAsString(form)
    def results = mockMvc.perform(post("/ccy-exposure")
      .with(csrf())
      .content(json)
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                          | code | responseBody
    ccyExposureForm()                                             | 200 | "id"
    ccyExposureForm { b -> b.put("name", null) }                  | 412 | "NotEmpty.ccyExposureCreateForm.name"
    ccyExposureForm { b -> b.put("name", "a_a!//") }              | 412 | "ValidIdentifier.ccyExposureCreateForm.name"
    ccyExposureForm { b -> b.put("currency", null) }              | 412 | "NotEmpty.ccyExposureCreateForm.currency"
    ccyExposureForm { b -> b.put("currency", "BTC") }             | 412 | "ValidStringSet.ccyExposureCreateForm.currency"
  }

  def ccyExposureForm(Closure c = { f -> f }) {
    return [
      "name"             : "CCY_EXPOSURE__1",
      "currency"         : "USD",
      "versionForm"      : NewVersionFormV2.newDefault()]
    .tap(c)
  }
}
