package com.solum.xplain.core.datavalue.csv

import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.datavalue.DataValueUpdateResolver
import com.solum.xplain.core.datavalue.DataValuesHolderWriteRepository
import com.solum.xplain.core.fixings.Fixing
import com.solum.xplain.core.fixings.FixingVersion
import spock.lang.Specification

class DataValueFileImporterTest extends Specification {
  def "should import with #action"() {
    def repository = Mock(DataValuesHolderWriteRepository)
    def response = Mock(DataUpdateSummary)
    def importer = new DataValueFileImporter(repository)

    given:
    DataValueImportItems<FixingVersion, String, Fixing> items = DataValueImportItems.builder()
    .importComment("comment")
    .importItemsByKeys([a: new Fixing(id: "a"), c: new Fixing(id: "c")])
    .existingItemsByKey([b: new Fixing(id: "b"), c: new Fixing(id: "c")])
    .build()

    when:
    def result = importer.importItems(action, items)

    then:
    result == response

    and:
    1 * repository.updateForImport(_) >> (DataValueUpdateResolver<FixingVersion, Fixing> r) -> {
      assert r.entitiesToAppend.collect {
        it.id
      } == append
      assert r.entitiesToReplace.collect {
        it.entityId()
      } == replace
      assert r.entitiesToArchive.collect {
        it.id
      } == archive
      return response
    }

    where:
    action                          | append   | replace    | archive
    DuplicateAction.REPLACE_DELETE  | ["a"]    | ["c"]      | ["b"]
    DuplicateAction.REPLACE         | ["a"]    | ["c"]      | []
    DuplicateAction.APPEND          | ["a"]    | []         | []
    DuplicateAction.APPEND_DELETE   | ["a"]    | []         | ["b"]
  }
}
