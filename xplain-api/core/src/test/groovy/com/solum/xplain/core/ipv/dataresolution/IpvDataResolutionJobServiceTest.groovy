package com.solum.xplain.core.ipv.dataresolution

import static com.solum.xplain.core.ipv.dataresolution.IpvDataResolutionJobExecutor.GROUP_ID
import static com.solum.xplain.core.ipv.dataresolution.IpvDataResolutionJobExecutor.IPV_DATA_RESOLUTION_JOB
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.ipvDataResolutionStatus

import com.solum.xplain.core.ipv.dataresolution.value.IpvDataResolutionStatus
import com.solum.xplain.core.jobs.JobExecution
import com.solum.xplain.core.jobs.JobExecutionEvent
import com.solum.xplain.core.jobs.JobExecutionRepository
import com.solum.xplain.core.jobs.JobExecutionStatus
import com.solum.xplain.core.sockets.events.EventType
import com.solum.xplain.core.sockets.events.SocketEvent
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification
import spock.lang.Unroll

class IpvDataResolutionJobServiceTest extends Specification {

  private static final GROUP_ID_VALUE = "group_id_value"

  IpvDataResolutionJobService testObject
  ApplicationEventPublisher publisher = Mock()
  JobExecutionRepository jobExecutionRepository = Mock()

  def setup() {
    testObject = new IpvDataResolutionJobService(publisher, jobExecutionRepository)
  }

  def "should return status false when no IPV Data resolution is in progress"() {
    setup:
    def jobParameters = Map.of(GROUP_ID, GROUP_ID_VALUE)
    1 * jobExecutionRepository.inProgress(IPV_DATA_RESOLUTION_JOB, null) >> []
    1 * jobExecutionRepository.inProgress(IPV_DATA_RESOLUTION_JOB, jobParameters) >> []

    when:
    def result = testObject.getResolutionProcessStatus(GROUP_ID_VALUE)

    then:
    result.isRight()
    def status = result.right().get() as IpvDataResolutionStatus
    status.inProgress == false
    status.groupId == GROUP_ID_VALUE
  }

  @Unroll
  def "when IPV Data resolution is in progress then message is returned: #globalJobResult #groupIdJobResult"() {
    setup:
    def jobParameters = Map.of(GROUP_ID, GROUP_ID_VALUE)
    1 * jobExecutionRepository.inProgress(IPV_DATA_RESOLUTION_JOB, null) >> globalJobResult
    1 * jobExecutionRepository.inProgress(IPV_DATA_RESOLUTION_JOB, jobParameters) >> groupIdJobResult

    when:
    def result = testObject.getResolutionProcessStatus(GROUP_ID_VALUE)

    then:
    result.isRight()
    def status = result.right().get() as IpvDataResolutionStatus
    status.inProgress == true
    status.groupId == GROUP_ID_VALUE

    where:
    globalJobResult      | groupIdJobResult
    [new JobExecution()] | []
    []                   | [new JobExecution()]
    [new JobExecution()] | [new JobExecution()]
  }

  @Unroll
  def "should handle IPV Data resolution job events with job parameters. #jobExecution #socketEvent"() {
    setup:
    def event = JobExecutionEvent.newOf(jobExecution.getName(), jobExecution)

    when:
    testObject.handleJobExecutionEvent(event)

    then:
    1 * publisher.publishEvent({
      verifyAll(it, SocketEvent) {
        audienceType == socketEvent.audienceType
        audienceIds == socketEvent.audienceIds
        eventType == socketEvent.eventType
        data == socketEvent.data
      }
    })

    where:
    jobExecution                                                                                                                                    | socketEvent
    ipvDataJobExecutionSample(name: IPV_DATA_RESOLUTION_JOB, status: JobExecutionStatus.STARTED, jobParameters: Map.of(GROUP_ID, GROUP_ID_VALUE))   | ipvDataResolutionStatus(GROUP_ID_VALUE, EventType.IPV_DATA_RESOLUTION_IN_PROGRESS)
    ipvDataJobExecutionSample(name: IPV_DATA_RESOLUTION_JOB, status: JobExecutionStatus.COMPLETED, jobParameters: Map.of(GROUP_ID, GROUP_ID_VALUE)) | ipvDataResolutionStatus(GROUP_ID_VALUE, EventType.IPV_DATA_RESOLUTION_COMPLETED)
  }

  @Unroll
  def "should not publish Socket event when unsupported or invalid job event is given #jobExecution"() {
    setup:
    def event = JobExecutionEvent.newOf(jobExecution.getName(), jobExecution)

    when:
    testObject.handleJobExecutionEvent(event)

    then:
    0 * publisher.publishEvent(_)

    where:
    jobExecution << [
      ipvDataJobExecutionSample(name: "random_job", jobParameters: Map.of(GROUP_ID, GROUP_ID_VALUE)),
      ipvDataJobExecutionSample(name: "random_job", jobParameters: null),
      ipvDataJobExecutionSample(name: IPV_DATA_RESOLUTION_JOB, jobParameters: null),
      ipvDataJobExecutionSample(name: IPV_DATA_RESOLUTION_JOB, jobParameters: [:])
    ]
  }

  JobExecution ipvDataJobExecutionSample(Map args) {
    def job = new JobExecution()
    job.name = args.name as String
    job.status = args.status as JobExecutionStatus
    job.jobParameters = args.jobParameters as Map<String, Object>
    job
  }
}
