package com.solum.xplain.core.portfolio.value

import com.solum.xplain.core.portfolio.CoreProductType
import spock.lang.Specification

class Sens01ValueTest extends Specification {
  def view = new PortfolioItemCalculatedExcMngmntView(
  metricsBr01: 1.1d,
  metricsCs01: 2.2d,
  metricsDv01: 3.3d,
  metricsInf01: 4.4d
  )

  def "should return correct value for #tradeType"() {
    expect:
    Sens01Value.sensitivity01Value(
      tradeType,
      view.metricsDv01,
      view.metricsBr01,
      view.metricsInf01,
      view.metricsCs01
      ) == value

    where:
    tradeType                    | value
    CoreProductType.IRS          | 3.3d
    CoreProductType.CAP_FLOOR    | 3.3d
    CoreProductType.SWAPTION     | 3.3d
    CoreProductType.XCCY         | 1.1d
    CoreProductType.FXFWD        | 1.1d
    CoreProductType.FXOPT        | 1.1d
    CoreProductType.INFLATION    | 4.4d
    CoreProductType.CDS          | 2.2d
    CoreProductType.CREDIT_INDEX | 2.2d
    CoreProductType.LOAN_NOTE    | null
  }
}
