package com.solum.xplain.core.settings

import static com.solum.xplain.core.common.EntityId.entityId
import static groovy.json.JsonOutput.toJson
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.solum.xplain.core.settings.form.TaskDefaultTeamsForm
import com.solum.xplain.core.settings.service.TaskDefaultSettingsControllerService
import com.solum.xplain.core.settings.value.TaskDefaultTeamsView
import com.solum.xplain.core.test.MockMvcConfiguration
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [TaskDefaultSettingsController])
class TaskDefaultSettingsControllerTest extends Specification {

  @SpringBean
  TaskDefaultSettingsControllerService service = Mock()

  @Autowired
  MockMvc mockMvc

  @Unroll
  def "should save MD task default teams form #form"() {
    setup:
    service.saveTaskDefaultTeams(_ as TaskDefaultTeamsForm) >> entityId("1")

    when:
    def results = mockMvc.perform(post("/settings/tasks/teams/market-data")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response
    results.getResponse().getContentAsString().contains(body)

    where:
    form                                                            | response | body
    defaultTeamsForm { { c ->
        c
      }
    }                                 | 200      | "id"
    defaultTeamsForm { { c ->
        c.remove("defaultResolutionTeam")
      }
    } | 200      | "id"
    defaultTeamsForm { { c ->
        c.remove("defaultApprovalTeam")
      }
    }   | 200      | "id"
  }

  def "should get market data tasks default teams"() {

    setup:
    service.getTaskDefaultTeams() >> new TaskDefaultTeamsView()

    when:
    def results = mockMvc.perform(get("/settings/tasks/teams/market-data")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  @Unroll
  def "should save VD task default teams #form"() {
    setup:
    service.saveIpvTaskDefaultTeams(_ as TaskDefaultTeamsForm) >> entityId("1")

    when:
    def results = mockMvc.perform(post("/settings/tasks/teams/valuation-data")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response
    results.getResponse().getContentAsString().contains(body)

    where:
    form                                                               | response | body
    defaultIpvTeamsForm { { c ->
        c
      }
    }                                 | 200      | "id"
    defaultIpvTeamsForm { { c ->
        c.remove("defaultResolutionTeam")
      }
    } | 200      | "id"
    defaultIpvTeamsForm { { c ->
        c.remove("defaultApprovalTeam")
      }
    }   | 200      | "id"
  }

  def "should valuation data default teams"() {
    setup:
    service.getTaskDefaultTeams() >> new TaskDefaultTeamsView()

    when:
    def results = mockMvc.perform(get("/settings/tasks/teams/valuation-data")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def defaultTeamsForm(Closure c) {
    [
      defaultResolutionTeam: ObjectId.get().toString(),
      defaultApprovalTeam  : ObjectId.get().toString()
    ].with(true, c)
  }

  def defaultIpvTeamsForm(Closure c) {
    [
      defaultResolutionTeam: ObjectId.get().toString(),
      defaultApprovalTeam  : ObjectId.get().toString()
    ].with(true, c)
  }
}
