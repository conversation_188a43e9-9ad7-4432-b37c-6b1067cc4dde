package com.solum.xplain.core.ipv.data

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.ArchiveForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.ipv.data.csv.IpvDataUploadService
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm
import com.solum.xplain.core.ipv.data.value.IpvDataNavValueView
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView
import com.solum.xplain.core.ipv.nav.repository.CompanyLegalEntityNavRepository
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory
import com.solum.xplain.shared.utils.filter.TableFilter
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class IpvDataServiceTest extends Specification {

  static final def USER = UserBuilder.user()
  static final TestingAuthenticationToken AUTH = new TestingAuthenticationToken(USER, null)

  static final String GROUP_ID = "groupId"
  static final String ID = "dataId"
  static final EntityId ENTITY_ID = EntityId.entityId(ID)

  static final LocalDate VALUATION_DATE = LocalDate.now()
  static final BitemporalDate STATE_DATE = BitemporalDate.newOfNow()

  AuthenticationContext userRepository = Mock()
  IpvDataRepository repository = Mock()
  IpvDataGroupRepository ipvDataGroupRepository = Mock()
  IpvDataUploadService uploadService = Mock()
  IpvDataModificationHandler ipvDataModificationHandler = Mock()
  ViewQueryTranslatorFactory viewQueryTranslatorFactory = Mock()
  CompanyLegalEntityNavRepository companyLegalEntityNavRepository = Mock()

  IpvDataService service = new IpvDataService(userRepository, repository, ipvDataGroupRepository, uploadService, ipvDataModificationHandler, viewQueryTranslatorFactory, companyLegalEntityNavRepository)

  def setup() {
    userRepository.userEither(AUTH) >> Either.right(USER)

    def group = Mock(IpvDataGroupView)
    group.id >> GROUP_ID
    ipvDataGroupRepository.ipvDataGroupView(USER, GROUP_ID) >> Either.right(group)
  }

  def "should create provider value"() {
    setup:
    def form = Mock(IpvDataProviderValueForm)
    1 * form.getProvider() >> "ICE"
    1 * repository.createValue(GROUP_ID, form) >> ENTITY_ID
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "TRADE_LEVEL"

    ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))

    when:
    def result = service.createValue(AUTH, GROUP_ID, form)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "should create NAV value at entity level"() {
    setup:
    def form = Mock(IpvDataProviderValueForm)
    1 * form.getProvider() >> "NAV"
    1 * companyLegalEntityNavRepository.createValue(GROUP_ID, form) >> ENTITY_ID
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"

    ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))

    when:
    def result = service.createValue(AUTH, GROUP_ID, form)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "should get values"() {
    setup:
    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, false, true)
    def scrollableViews = ScrollableEntry.<IpvDataProviderValueView> empty()
    def translator = Mock(ViewQueryTranslator)
    def tableFilter = TableFilter.emptyTableFilter()
    def scrollRequest = ScrollRequest.unconstrained()
    1 * repository.getValueViews(
      GROUP_ID,
      filter,
      tableFilter,
      scrollRequest
      ) >> scrollableViews
    viewQueryTranslatorFactory.getTranslator(IpvDataProviderValueView) >> translator

    when:
    def result = service.getValues(
      AUTH,
      GROUP_ID,
      filter,
      tableFilter,
      scrollRequest
      )

    then:
    result.isRight()
    result.getOrNull() == scrollableViews

    and: "filter and sort were translated from grid columns"
    1 * translator.translate(tableFilter) >> tableFilter
    1 * translator.translate(scrollRequest) >> scrollRequest
  }

  def "should get nav values"() {
    setup:
    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, null, true)
    def scrollableViews = ScrollableEntry.<IpvDataNavValueView> empty()
    def translator = Mock(ViewQueryTranslator)
    def tableFilter = TableFilter.emptyTableFilter()
    def scrollRequest = ScrollRequest.unconstrained()
    1 * repository.getNavValueViews(
      GROUP_ID,
      filter,
      tableFilter,
      scrollRequest
      ) >> scrollableViews
    viewQueryTranslatorFactory.getTranslator(IpvDataNavValueView) >> translator

    when:
    def result = service.getNavValues(
      AUTH,
      GROUP_ID,
      filter,
      tableFilter,
      scrollRequest
      )

    then:
    result.isRight()
    result.getOrNull() == scrollableViews

    and: "filter and sort were translated from grid columns"
    1 * translator.translate(tableFilter) >> tableFilter
    1 * translator.translate(scrollRequest) >> scrollRequest
  }

  def "should get provider value"() {
    setup:
    def view = Mock(IpvDataProviderValueView)
    1 * repository.getValueView(GROUP_ID, ID, STATE_DATE) >> Either.right(view)
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "TRADE_LEVEL"

    when:
    def result = service.getValue(AUTH, GROUP_ID, ID, STATE_DATE, IpvDataType.EXCLUDE_NAV)

    then:
    result.isRight()
    result.getOrNull() == view
  }

  def "should get NAV value at entity level"() {
    setup:
    def view = Mock(IpvDataProviderValueView)
    1 * companyLegalEntityNavRepository.getValueView(GROUP_ID, ID, STATE_DATE) >> Either.right(view)
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"

    when:
    def result = service.getValue(AUTH, GROUP_ID, ID, STATE_DATE, IpvDataType.NAV)

    then:
    result.isRight()
    result.getOrNull() == view
  }

  def "should update provider value"() {
    setup:
    def form = Mock(IpvDataProviderValueUpdateForm)
    1 * repository.updateValue(ID, form) >> Either.right(ENTITY_ID)

    ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "TRADE_LEVEL"

    when:
    def result = service.updateValue(AUTH, GROUP_ID, ID, form, IpvDataType.EXCLUDE_NAV, VALUATION_DATE)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "should update NAV value at entity level"() {
    setup:
    def form = Mock(IpvDataProviderValueUpdateForm)
    1 * companyLegalEntityNavRepository.updateValue(ID, form) >> Either.right(ENTITY_ID)

    ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"

    when:
    def result = service.updateValue(AUTH, GROUP_ID, ID, form, IpvDataType.NAV, VALUATION_DATE)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "should archive provider value"() {
    setup:
    def form = Mock(ArchiveForm)
    1 * repository.archiveValue(ID, form) >> Either.right(ENTITY_ID)

    1 * ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))

    when:
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "TRADE_LEVEL"
    def result = service.archiveValue(AUTH, GROUP_ID, ID, form, IpvDataType.EXCLUDE_NAV, VALUATION_DATE)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "should archive provider value at ENTITY_LEVEL for NAV"() {
    setup:
    def form = Mock(ArchiveForm)
    1 * companyLegalEntityNavRepository.archiveValue(ID, form) >> Either.right(ENTITY_ID)

    1 * ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))

    when:
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"
    def result = service.archiveValue(AUTH, GROUP_ID, ID, form, IpvDataType.NAV, VALUATION_DATE)

    then:
    result.isRight()
    result.getOrNull() == ENTITY_ID
  }

  def "When IPV Data Group upload is successful then IPV Data Valuation job is invoked"() {
    setup:
    def csvBytes = "FAKE_FILE".bytes
    def importOptions = new ImportOptions(LocalDate.parse("2022-11-29"), DuplicateAction.APPEND, STRICT, null, null, null, null, null)

    when:
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "TRADE_LEVEL"
    var result = service.upload(AUTH, GROUP_ID, csvBytes, importOptions, IpvDataType.ALL)

    then:
    1 * ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * uploadService.upload(GROUP_ID, csvBytes, importOptions, IpvDataType.ALL) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * ipvDataModificationHandler.onIpvDataValueCreation(GROUP_ID)
    0* ipvDataModificationHandler.onCompanyLegalEntityNavDataValueCreation(GROUP_ID)
    result.right().get() == EntityId.entityId(GROUP_ID)
  }

  def "When entity level NAV data upload is successful then Company Legal Entity Nav Data Valuation job is invoked"() {
    setup:
    def csvBytes = "FAKE_FILE".bytes
    def importOptions = new ImportOptions(LocalDate.parse("2022-11-29"), DuplicateAction.APPEND, STRICT, null, null, null, null, null)

    when:
    1 * repository.getGlobalNavLevelSettings(_ as BitemporalDate) >> "ENTITY_LEVEL"
    var result = service.upload(AUTH, GROUP_ID, csvBytes, importOptions, IpvDataType.NAV)

    then:
    1 * ipvDataGroupRepository.groupUpdated(USER, GROUP_ID) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * uploadService.upload(GROUP_ID, csvBytes, importOptions, IpvDataType.NAV) >> Either.right(EntityId.entityId(GROUP_ID))
    1 * ipvDataModificationHandler.onCompanyLegalEntityNavDataValueCreation(GROUP_ID)
    0 * ipvDataModificationHandler.onIpvDataValueCreation(GROUP_ID)
    result.right().get() == EntityId.entityId(GROUP_ID)
  }

  def "When IPV Data Group upload has failed then IPV Data Valuation job is not invoked"() {
    setup:
    def csvBytes = "FAKE_FILE".bytes
    def importOptions = new ImportOptions(LocalDate.parse("2022-11-29"), DuplicateAction.APPEND, STRICT, null, null, null, null, null)
    def expectedErrors = [new ErrorItem(Error.OBJECT_NOT_FOUND)]

    when:
    var result = service.upload(AUTH, GROUP_ID, csvBytes, importOptions,IpvDataType.ALL)

    then:
    1 * uploadService.upload(GROUP_ID, csvBytes, importOptions, IpvDataType.ALL) >> Either.left(expectedErrors)
    0 * ipvDataGroupRepository.groupUpdated(USER, GROUP_ID)
    0 * ipvDataModificationHandler.onIpvDataValueCreation(GROUP_ID)
    0 * ipvDataModificationHandler.onCompanyLegalEntityNavDataValueCreation(GROUP_ID)
    result.left().get() == expectedErrors
  }
}
