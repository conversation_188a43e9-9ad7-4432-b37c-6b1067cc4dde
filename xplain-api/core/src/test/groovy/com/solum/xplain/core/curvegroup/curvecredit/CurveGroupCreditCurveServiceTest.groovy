package com.solum.xplain.core.curvegroup.curvecredit

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.nonDeleted
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.time.LocalDate.now
import static java.time.LocalDate.of

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.ChartPoint
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import io.atlassian.fugue.Either
import org.springframework.data.domain.Sort
import spock.lang.Specification
import spock.lang.Unroll

class CurveGroupCreditCurveServiceTest extends Specification {

  private static final SAMPLE_DATE = of(2016, 10, 10)

  def curveGroupRepository = Mock(CurveGroupRepository)
  def repository = Mock(CurveGroupCreditCurveRepository)
  def marketDataQuotesSupport = Mock(MarketDataQuotesSupport)

  def service = new CurveGroupCreditCurveService(curveGroupRepository, repository, marketDataQuotesSupport)

  def setup() {
    def curveGroup = new CurveGroupView(id: "groupId")
    curveGroupRepository.getEither("groupId") >> Either.right(curveGroup)
  }

  def "should create curve"() {
    setup:
    1 * repository.createCurve(_, _) >> Either.right(new EntityId("1"))
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> Either.right(new EntityId("1"))

    when:
    def result = service.createCdsCurve("groupId", new CdsCurveForm())

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should update curve"() {
    setup:
    1 * repository.updateCurve(_, _, _, _) >> Either.right(new EntityId("1"))
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> Either.right(new EntityId("1"))

    when:
    def result = service.updateCdsCurve("groupId", "curveId", SAMPLE_DATE, new CdsCurveUpdateForm())

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }


  def "should archive curve"() {
    setup:
    def form = new ArchiveEntityForm(NewVersionFormV2.newDefault())

    1 * repository.archiveCurve(_, _, _, form) >> Either.right(new EntityId("1"))
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> Either.right(new EntityId("1"))

    when:
    def result = service.archiveCurve("groupId", "id", now(), form)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should delete curve"() {
    setup:
    1 * repository.deleteCurve(_, _, _) >> Either.right(new EntityId("1"))
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> Either.right(new EntityId("1"))

    when:
    def result = service.deleteCurve("groupId", "curveId", SAMPLE_DATE)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  @Unroll
  def "should get curves with archived curves #withArchived"() {
    setup:
    def stateDate = BitemporalDate.newOf(SAMPLE_DATE)
    def sort = Sort.unsorted()
    1 * repository.getCurveViews("groupId", stateDate, filter, emptyTableFilter(), sort) >>
      [new CreditCurveView()]

    when:
    def result = service.getCurves("groupId", stateDate, withArchived, sort)

    then:
    result.isRight()

    def curves = result.right().get() as List<CreditCurveView>
    curves.size() == 1

    where:
    withArchived | filter
    true         | nonDeleted()
    false        | active()
  }

  @Unroll
  def "should get cds nodes when calibrationCurrency is #calibrationCurrency"() {
    setup:
    def quotes = [:] as Map<String, CalculationMarketValueFullView>
    def marketStateQuotes = new CurveConfigMarketStateQuotes(CurveMarketSample.MARKET_STATE_FORM, quotes)

    1 * marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> quotes
    1 * repository.curveCdsNodes(_, _, _, marketStateQuotes, _, calibrationCurrency) >> Collections.emptyList()

    when:
    def result = service.getCurveCdsNodes("groupId", "curveId", SAMPLE_DATE, SAMPLE_DATE, CurveMarketSample.MARKET_STATE_FORM, calibrationCurrency)

    then:
    result.isRight()

    where:
    calibrationCurrency << [null, CalculationDiscountingType.DISCOUNT_CAD]
  }

  @Unroll
  def "should get index nodes when calibrationCurrency is #calibrationCurrency"() {
    when:
    def result = service.getCurveIndexNodes("groupId", "curveId", SAMPLE_DATE, SAMPLE_DATE, CurveMarketSample.MARKET_STATE_FORM, calibrationCurrency)

    then:
    1 * marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> [:]
    1 * repository.getIndexNodes(_, _, _, _, _, CurveMarketSample.MARKET_STATE_FORM, calibrationCurrency) >> Collections.emptyList()

    and:
    result.isRight()

    where:
    calibrationCurrency << [null, CalculationDiscountingType.DISCOUNT_CAD]
  }

  def "should get cds nodes when resolved market data is duplicated"() {
    setup:
    def quotes = [
      "k1": new CalculationMarketValueFullView(key: "k1"),
      "k2": new CalculationMarketValueFullView(key: "k1")]
    def stateQuotes = new CurveConfigMarketStateQuotes(CurveMarketSample.MARKET_STATE_FORM, quotes)

    when:
    def result = service.getCurveCdsNodes(
      "groupId", "curveId", SAMPLE_DATE, SAMPLE_DATE, CurveMarketSample.MARKET_STATE_FORM, null)

    then:
    1 * marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> quotes
    1 * repository.curveCdsNodes(_, _, _, stateQuotes, _, _) >> Collections.emptyList()

    and:
    result.isRight()
  }

  def "should get cds chart points sorted by date"() {
    when:
    def result = service.getCurveCdsChartPoints("groupId", "curveId", CurveMarketSample.MARKET_STATE_FORM, CalculationDiscountingType.DISCOUNT_EUR)

    then:
    1 * repository.getCurveChartPoints(_, "curveId", CurveMarketSample.MARKET_STATE_FORM, CalculationDiscountingType.DISCOUNT_EUR) >> [
      (of(2022, 1, 1)): (new ChartPoint(of(2022, 1, 1), 2, 2)),
      (of(2021, 1, 1)): (new ChartPoint(of(2021, 1, 1), 1, 1))
    ]

    and:
    result.isRight()
    result.getOrNull() == [new ChartPoint(of(2021, 1, 1), 1, 1), new ChartPoint(of(2022, 1, 1), 2, 2)]
  }
}
