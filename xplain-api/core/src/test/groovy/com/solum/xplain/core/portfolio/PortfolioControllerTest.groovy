package com.solum.xplain.core.portfolio

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyTeamValidationService
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.utils.filter.TableFilter
import java.time.LocalDate
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Order
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PortfolioController])
class PortfolioControllerTest extends Specification {

  def static EXPORTED_CSV = FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")
  static String PORTFOLIO_ID = "000000000000000000000001"
  private static String validCompanyId = new ObjectId().toHexString()

  @SpringBean
  private PortfolioControllerService service = Mock()

  @SpringBean
  PortfolioUploadService uploadService = Mock()

  @SpringBean
  PortfolioExportService exportService = Mock()

  @SpringBean
  private CompanyMarketDataGroupValidator mdValidator = Mock()

  @SpringBean
  private CurveGroupRepository curveGroupRepository = Mock()

  @SpringBean
  private CompanyRepository companyRepository = Mock()

  @SpringBean
  private CurveConfigurationRepository configurationRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  PortfolioRepository portfolioRepository = Mock()

  @SpringBean
  CompanyTeamValidationService companyTeamValidationService = Mock()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def "should get portfolios with onlyOwned #onlyOwned"() {
    setup:
    def defaultSort = Sort.by(
      PortfolioView.Fields.companyName,
      PortfolioView.Fields.entityName,
      PortfolioCondensedView.Fields.externalPortfolioId)
    1 * service.getAll( {
      verifyAll(it, ScrollRequest) {
        sort == defaultSort
      }
    },
    _ as TableFilter,
    activePortfolios(),
    _ as BitemporalDate) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get('/portfolio')
      .param("onlyOwned", String.valueOf(onlyOwned))
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response

    where:
    onlyOwned | response
    true      | 200
    false     | 200
  }

  def "should get sorted portfolios by given sorting"() {
    setup:
    def givenSort = Sort.by(Order.desc(PortfolioCondensedView.Fields.externalPortfolioId))
    1 * service.getAll( {
      verifyAll(it, ScrollRequest) {
        sort == givenSort
      }
    },
    _ as TableFilter,
    activePortfolios(),
    _ as BitemporalDate) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get('/portfolio')
      .param("stateDate", "2020-01-01")
      .param("sort", "externalPortfolioId,desc")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.getResponse().getStatus() == 200
  }

  def "should create portfolios with form #portfolioForm and response #response"() {
    setup:
    companyTeamValidationService.validCompanyEntityTeams(
      _ as String,
      _ as String,
      _ as AllowedTeamsForm) >> true
    service.create(_ as PortfolioCreateForm) >> right(entityId("1"))
    companyRepository.companyEntity(validCompanyId) >> right(new Company())
    companyRepository.companyEntity(_ as String) >> left(OBJECT_NOT_FOUND.entity())
    portfolioRepository.existsByExternalIdExcludingSelf(
      validCompanyId,
      "000000000000000000000000",
      "EXTERNALID",
      null) >> false
    portfolioRepository.existsByExternalIdExcludingSelf(
      validCompanyId,
      "000000000000000000000000",
      "EXISTS",
      null) >> true

    when:
    def results = mockMvc.perform(post('/portfolio')
      .with(csrf())
      .content(toJson(portfolioForm))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    portfolioForm                                                                    | code | response
    portfolioForm()                                                                  | 200  | """{"id":"1"}"""
    portfolioForm().with(true, { b -> b.put("description", null) })                  | 200  | """{"id":"1"}"""
    portfolioForm().with(true, { b -> b.put("name", null) })                         | 412  | "NotEmpty.portfolioCreateForm.name"
    portfolioForm().with(true, { b -> b.put("externalPortfolioId", null) })          | 412  | "NotEmpty.portfolioCreateForm.externalPortfolioId"
    portfolioForm().with(true, { b -> b.put("externalPortfolioId", "WITH SPACES") }) | 412  | "ValidIdentifier.portfolioCreateForm.externalPortfolioId"
    portfolioForm().with(true, { b -> b.put("externalPortfolioId", "lowercase") })   | 412  | "ValidIdentifier.portfolioCreateForm.externalPortfolioId"
    portfolioForm().with(true, { b -> b.put("externalPortfolioId", "EXISTS") })      | 412  | "UniquePortfolioExtId.portfolioCreateForm.externalPortfolioId"
    portfolioForm().with(true, { b -> b.put("allowedTeamsForm", null) })             | 412  | "NotNull.portfolioCreateForm.allowedTeamsForm"
    portfolioForm().with(true, { b -> b.put("companyId", "") })                      | 412  | "NotEmpty.portfolioCreateForm.companyId"
    portfolioForm().with(true, { b -> b.put("companyId", "test") })                  | 412  | "ValidCompanyId.portfolioCreateForm.companyId"
    portfolioForm().with(true, { b -> b.put("companyId", null) })                    | 412  | "NotEmpty.portfolioCreateForm.companyId"
  }

  def "should fail to create portfolio with duplicate id"() {
    setup:
    companyRepository.companyEntity(_ as String) >> right(new Company())
    portfolioRepository.existsByExternalIdExcludingSelf(
      _ as String,
      _ as String,
      _ as String,
      null) >> true

    when:
    def results = mockMvc.perform(post('/portfolio')
      .with(csrf())
      .content(toJson(portfolioForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 412
    results.getResponse().getContentAsString().indexOf("Portfolio ID must be unique") >= 0
  }

  @WithMockUser
  def "should archive portfolio"() {
    setup:
    1 * service.archivePortfolio(PORTFOLIO_ID) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/portfolio/{id}/archive", PORTFOLIO_ID)
      .with(csrf())).andReturn()

    then:
    results.response.status == 200
  }

  def "should get portfolio valuation data keys in csv format"() {
    setup:
    1 * exportService.exportPortfolioTradeDataKeys("portfolioId", {
      verifyAll(it, BitemporalDate) {
        actualDate == LocalDate.parse("2020-01-01")
      }
    }) >>
    right(EXPORTED_CSV)

    def result = mockMvc.perform(get("/portfolio/{id}/valuation-data-keys/csv", "portfolioId")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    result.getResponse().getStatus() == 200
  }

  def "should get all valuation data keys in csv format"() {
    setup:
    1 * exportService.exportAllPortfolioTradeDataKeys({
      verifyAll(it, BitemporalDate) {
        actualDate == LocalDate.parse("2020-01-01")
      }
    }) >>
    right(EXPORTED_CSV)

    def result = mockMvc.perform(get("/portfolio/valuation-data-keys/csv")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    result.getResponse().getStatus() == 200
  }

  static portfolioForm() {
    return [
      name               : "name",
      externalPortfolioId: "EXTERNALID",
      allowedTeamsForm   : new AllowedTeamsForm(true, []),
      entityId           : "000000000000000000000000",
      companyId          : validCompanyId,
      description        : "description"
    ]
  }
}
