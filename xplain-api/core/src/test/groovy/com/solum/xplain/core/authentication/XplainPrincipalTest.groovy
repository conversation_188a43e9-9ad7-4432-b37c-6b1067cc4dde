package com.solum.xplain.core.authentication

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.core.users.UserBuilder.userWithTeams
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import org.bson.types.ObjectId
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class XplainPrincipalTest extends Specification {
  @Shared
  def team1 = new ObjectId()
  @Shared
  def team2 = new ObjectId()

  @Unroll
  "AllowedTeams should return #expectedResult for user #user"() {
    expect:
    expectedResult == user.allowedTeams([team1.toString(), team2.toString()])

    where:
    user                                  | expectedResult
    userWithTeams("id", [new ObjectId()]) | left(OPERATION_NOT_ALLOWED.entity("No team access"))
    userWithTeams("id", [team1])          | right(user)
    userWithTeams("id", [team1, team2])   | right(user)
  }

  def "AllowedTeams should return OPERATION_NOT_ALLOWED for null teamIds"() {
    setup:
    def user = userWithTeams("id", null)

    when:
    def expectedResult = user.allowedTeams(null)

    then:
    expectedResult == left(OPERATION_NOT_ALLOWED.entity("No team access"))
  }
}
