package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.inflationLeg
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.inflationTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.inflationTradeFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.SwapLegFormBuilder
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.InflationTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [InflationController])
@Import([TestSecurityConfig])
class InflationControllerTest extends Specification {

  static String URI = "/portfolio/{id}/trades/inflation"
  static String PORTFOLIO_ID = "000000000000000000000001"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  @Unroll
  def "should get inflation trade when role #role then #responseStatus and #response"() {
    setup:
    def trade = PortfolioItemBuilder.trade(CoreProductType.INFLATION, TradeDetailsBuilder.inflationSwap(now()))
    service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(trade)

    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
    .with(userWithAuthority(role))
    .param("stateDate", "2020-01-01")
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseStatus
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | responseStatus | response
    MODIFY_TRADE | 403            | "OPERATION_NOT_ALLOWED"
    VIEW_TRADE   | 200            | "externalTradeId"
  }

  @Unroll
  def "should create new inflation trade with form #form role #role and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as InflationTradeForm) >> right(entityId("1"))
    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
    .with(userWithAuthority(role))
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                                                                       | code | response
    MODIFY_TRADE | inflationTradeForm()                                                                                       | 200  | """{"id":"1"}"""
    VIEW_TRADE   | inflationTradeForm()                                                                                       | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | inflationTradeFormWith("leg1", new SwapLegFormBuilder().build())                                           | 412  | "RequiredPayReceive.inflationTradeForm.leg2"
    MODIFY_TRADE | inflationTradeFormWith("externalTradeId", null)                                                            | 412  | "NotEmpty.inflationTradeForm.externalTradeId"
    MODIFY_TRADE | inflationTradeFormWith("externalTradeId", "WITH SPACES")                                                   | 412  | "ValidIdentifier.inflationTradeForm.externalTradeId"
    MODIFY_TRADE | inflationTradeFormWith("externalTradeId", "lowercase")                                                     | 412  | "ValidIdentifier.inflationTradeForm.externalTradeId"
    MODIFY_TRADE | inflationTradeFormWith("businessDayAdjustmentType", "ACCRUAL_AND_PAYMENT")                                 | 412  | "ValidStringSet.inflationTradeForm.businessDayAdjustmentType"
    MODIFY_TRADE | inflationTradeFormWith("leg1", inflationLeg(c -> c.calculationInflationIndex = null))                      | 412  | "RequiredSwapLegCalculation.inflationTradeForm.leg1.calculationInflationIndex"
    MODIFY_TRADE | inflationTradeFormWith("leg1", inflationLeg(c -> c.calculationInflationLag = null))                        | 412  | "RequiredSwapLegCalculation.inflationTradeForm.leg1.calculationInflationLag"
    MODIFY_TRADE | inflationTradeFormWith("leg1", inflationLeg(c -> c.calculationInflationLag = "1D"))                        | 412  | "Invalid period. Only M/Y allowed (2Y, 3M)."
    MODIFY_TRADE | inflationTradeFormWith("leg2", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.RECEIVE, "6M"))  | 200  | """{"id":"1"}"""
    MODIFY_TRADE | inflationTradeFormWith("leg2", SwapLegFormBuilder.withPayReceiveAndPaymentFreq(PayReceive.RECEIVE, "7M"))  | 412  | "ValidStringSet.inflationTradeForm.leg2.paymentFrequency"
    MODIFY_TRADE | inflationTradeFormWith("leg1", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.RECEIVE, "2Y"))  | 412  | "RequiredPayReceive.inflationTradeForm.leg2"
    MODIFY_TRADE | inflationTradeFormWith("leg1", SwapLegFormBuilder.withPayReceiveAndAccrualFreq(PayReceive.RECEIVE, "12M")) | 412  | "RequiredPayReceive.inflationTradeForm.leg2"
  }

  @Unroll
  def "should update inflation trade when role #role then #responseStatus"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2020-01-01"), _ as InflationTradeForm, "1") >> right(entityId("1"))
    when:
    def results = mockMvc.perform(put(URI + "/1/2020-01-01", PORTFOLIO_ID)
    .with(userWithAuthority(role))
    .with(csrf())
    .content(objectMapper.writeValueAsString(inflationTradeForm()))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:

    results != null
    results.getResponse().getStatus() == responseStatus

    where:
    role         | responseStatus
    MODIFY_TRADE | 200
    VIEW_TRADE   | 403
  }
}
