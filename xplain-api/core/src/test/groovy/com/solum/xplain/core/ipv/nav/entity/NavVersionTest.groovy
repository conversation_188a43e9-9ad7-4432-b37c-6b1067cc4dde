package com.solum.xplain.core.ipv.nav.entity

import spock.lang.Specification

class NavVersionTest extends Specification {
  def "should compare currency and value for equality"() {
    given:
    NavVersion v1 = new NavVersion(currency: ccy1, value: 100.0)
    NavVersion v2 = new NavVersion(currency: ccy2, value: value)

    when:
    def areEquals = v1.valueEquals(v2)

    then:
    areEquals == expected

    where:
    ccy1  | ccy2  | value | expected
    "USD" | "USD" | 100.0 | true
    "USD" | "USD" | 200.0 | false
    "USD" | "EUR" | 100.0 | false
    "USD" | "EUR" | 200.0 | false
    "USD" | null  | 100.0 | false
    null  | "USD" | 100.0 | false
  }
}
