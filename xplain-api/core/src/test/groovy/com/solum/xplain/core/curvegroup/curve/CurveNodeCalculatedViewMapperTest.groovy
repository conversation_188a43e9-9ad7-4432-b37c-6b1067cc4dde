package com.solum.xplain.core.curvegroup.curve

import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.MID_PRICE
import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey
import static com.solum.xplain.extensions.product.ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_CLEARED

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.core.classifiers.CurveNodeTypes
import com.solum.xplain.core.common.value.CalculatedValueAtDate
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import com.solum.xplain.core.curvegroup.curve.value.IborFutureCurveNodeCalculatedView
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.extensions.product.ExtendedIborContractSpecs
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [CurveNodeCalculatedViewMapperImpl.class])
class CurveNodeCalculatedViewMapperTest extends Specification {
  @Autowired
  CurveNodeCalculatedViewMapper mapper
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()

  def static final valuationDate = LocalDate.parse("2017-01-01")
  def static final node = curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1D")
  def static final nodeDate = LocalDate.parse("2017-01-04")
  def static final curve = new CurveBuilder().name("EUR 3M").nodes([node]).build()

  def "should map full view with all details"() {
    setup:
    def clearingHouse = ClearingHouse.NONE
    def quotes = [(semanticMarketDataKey(node, clearingHouse)): new CalculationMarketValueFullView(
      key: semanticMarketDataKey(node, clearingHouse),
      provider: "BBG",
      ticker: "EONIATICK",
      value: 10.0,
      askValue: 11.0,
      midValue: 11.6, // Deliberately not average of bid/ask
      bidValue: 12.0,
      )]

    var calibrationResults = new CurveCalibrationResult(
      nodeValues: [new CalculatedValueAtDate(nodeDate, 1)],
      discountFactorPoints: [new CalculatedValueAtDate(nodeDate, 2)],
      nodesUsedInCalibration: [node.getInstrument()],
      priceRequirements: InstrumentPriceRequirements.bidRequirements(),
      )

    when:
    def views = mapper.toNodeViews(curve, quotes, valuationDate, calibrationResults)

    then:
    views.size() == 1
    with(views[0]) {
      type == node.getType()
      convention == node.getConvention()
      period == node.getPeriod()
      serialFuture == node.getSerialFuture()
      fraSettlement == node.getFraSettlement()
      date == nodeDate
      ticker == "EONIATICK"
      dataSource == "BBG"
      marketValue == 10.0
      marketValueAsk == 11.0
      marketValueMid == 11.6
      marketValueBid == 12.0
      calibrationPriceType == BID_PRICE
      rateValue == 1.0
      discountFactor == 2.0
      key == "1D_EUR-FIXED-1Y-EURIBOR-3M"
    }
  }

  @Unroll
  def "should correctly map MDK"() {
    expect:
    def views = mapper.toNodeViews(curve, [:], valuationDate, null)[0]
    views.key == mdk

    where:
    curve | mdk
    inflationCurveWithNode("FR EXT CPI") | "1Y_EUR-FIXED-ZC-FR-CPI-CLEARED"
    inflationCurveWithNode("FR EXT CPI LCH") | "1Y_EUR-FIXED-ZC-FR-CPI-CLEARED-LCH"
  }

  def inflationCurveWithNode(String curveName) {
    def node = curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_FR_CPI_CLEARED.name, "1Y")
    new CurveBuilder().curveType(CurveType.INFLATION_INDEX).name(curveName).nodes([node]).build()
  }

  @Unroll
  def "should map calculated values to view when #nodeValues #discountFactors #nodesUsed then #expectedRate #expectedDf"() {
    setup:
    var calibrationResults = new CurveCalibrationResult(
      nodeValues: nodeValues,
      discountFactorPoints: discountFactors,
      nodesUsedInCalibration: nodesUsed,
      )

    expect:
    def view = mapper.toNodeViews(curve, [:], valuationDate, calibrationResults)[0]
    view.date == nodeDate
    view.rateValue == expectedRate
    view.discountFactor == expectedDf

    where:
    nodeValues                                    | discountFactors                               | nodesUsed              | expectedRate | expectedDf
    null                                          | null                                          | null                   | null         | null
    [new CalculatedValueAtDate(nodeDate, 1)]      | [new CalculatedValueAtDate(nodeDate, 2)]      | []                     | null         | null
    [new CalculatedValueAtDate(valuationDate, 1)] | [new CalculatedValueAtDate(valuationDate, 2)] | [node.getInstrument()] | null         | null
    [new CalculatedValueAtDate(nodeDate, 1)]      | [new CalculatedValueAtDate(nodeDate, 2)]      | [node.getInstrument()] | 1            | 2
  }

  @Unroll
  def "should map price types to view when #priceRequirements #discountCurrency then #priceType"() {
    setup:
    var calibrationResults = new CurveCalibrationResult(
      priceRequirements: priceRequirements,
      discountCurrency: discountCurrency,
      nodesUsedInCalibration: [node.getInstrument()],
      )

    expect:
    def view = mapper.toNodeViews(curve, [(semanticMarketDataKey(node, ClearingHouse.NONE)): new CalculationMarketValueFullView()], valuationDate, calibrationResults)[0]
    view.date == nodeDate
    view.calibrationPriceType == priceType

    where:
    priceRequirements                                                                      | discountCurrency | priceType
    null                                                                                   | null             | null
    new InstrumentPriceRequirements(BID_PRICE, ASK_PRICE, BID_PRICE, BID_PRICE, BID_PRICE) | null             | BID_PRICE
    new InstrumentPriceRequirements(BID_PRICE, ASK_PRICE, BID_PRICE, BID_PRICE, BID_PRICE) | "EUR"            | ASK_PRICE
    new InstrumentPriceRequirements(BID_PRICE, MID_PRICE, BID_PRICE, BID_PRICE, BID_PRICE) | "EUR"            | MID_PRICE
  }

  def "should create views with empty market data"() {
    when:
    def views = mapper.toNodeViews(curve, [:], valuationDate, null)

    then:
    views.size() == 1
    with(views[0]) {
      quoteId == null
      ticker == null
      marketValue == null
      marketValueAsk == null
      marketValueBid == null
      calibrationPriceType == null
      rateValue == null
      discountFactor == null
    }
  }

  def "should return IborFutureCurveNodeCalculatedView with futureEndDate"() {
    given:
    def node = curveNode(CurveNodeTypes.IBOR_FUTURE_NODE, ExtendedIborContractSpecs.CAD_CDOR_3M_IMM_MSE.name, "1Y")
    node.setSerialFuture("2D+1")
    def curve = new CurveBuilder().name("CAD 3M").nodes([node]).build()
    def nodeDate = LocalDate.of(2023, 1, 1)
    def valuationDate = LocalDate.of(2023, 1, 1)
    def calibrationResults = new CurveCalibrationResult(
      nodeValues: [new CalculatedValueAtDate(nodeDate, 1)],
      discountFactorPoints: [new CalculatedValueAtDate(nodeDate, 2)],
      nodesUsedInCalibration: [node.getInstrument()],
      priceRequirements: InstrumentPriceRequirements.bidRequirements(),
      )

    when:
    def views = mapper.toNodeViews(curve, [:], valuationDate, calibrationResults)

    then:
    views.size() == 1
    views[0] instanceof IborFutureCurveNodeCalculatedView
    def iborFutureView = views[0] as IborFutureCurveNodeCalculatedView
    iborFutureView.futureEndDate == LocalDate.parse("2023-06-15")
  }
}
