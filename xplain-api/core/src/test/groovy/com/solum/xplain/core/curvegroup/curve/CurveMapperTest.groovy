package com.solum.xplain.core.curvegroup.curve

import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey

import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class CurveMapperTest extends Specification {
  CurveMapper mapper = Mappers.getMapper(CurveMapper.class)


  def "should map FRA Curve node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "AED-EIBOR-3M",
      type: "ForwardRateAgreement",
      fraSettlement: "3M"
      )

    when:
    def result = mapper.fromNodeForm("AED 3M", form)

    then:
    result.period == "3M"
    semanticMarketDataKey(result, ClearingHouse.NONE) == "3MX6M_AED-EIBOR-3M"
    result.type == "ForwardRateAgreement"
    result.convention == "AED-EIBOR-3M"
    result.instrument == "3Mx6M"
  }

  def "should map IMM FRA Curve node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "NOK-NIBOR-6M-IMM",
      type: "IMMForwardRateAgreement",
      serialFuture: "2D+12"
      )

    when:
    def result = mapper.fromNodeForm("NOK 6M", form)

    then:
    result.period == "6M"
    semanticMarketDataKey(result,ClearingHouse.NONE) == "2D+12_NOK-NIBOR-6M-IMM"
    result.type == "IMMForwardRateAgreement"
    result.convention == "NOK-NIBOR-6M-IMM"
    result.instrument == "2D+12"
  }

  def "should map IborFixingDeposit Curve node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "AED-EIBOR-3M",
      type: "IborFixingDeposit",
      )

    when:
    def result = mapper.fromNodeForm("AED 3M", form)

    then:
    result.period == "3M"
    result.type == "IborFixingDeposit"
    result.convention == "AED-EIBOR-3M"
    result.instrument == "3M"
    semanticMarketDataKey(result,ClearingHouse.NONE) == "3M_AED-EIBOR-3M"
  }

  def "should map IBOR Future Curve node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "CHF-LIBOR-3M-IMM-ICE",
      type: "IborFuture",
      serialFuture: "2D+12"
      )

    when:
    def result = mapper.fromNodeForm("CHF 3M", form)

    then:
    result.period == "3M"
    semanticMarketDataKey(result, ClearingHouse.NONE) == "2D+12_CHF-LIBOR-3M-IMM-ICE"
    result.type == "IborFuture"
    result.convention == "CHF-LIBOR-3M-IMM-ICE"
    result.instrument == "2D+12"
  }

  def "should map Term Deposit Curve node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "AUD-Deposit-T1",
      type: "TermDeposit",
      )
    def formInr = new CurveNodeForm(
      convention: "INR-Deposit-T0",
      type: "TermDeposit",
      )

    when:
    def result = mapper.fromNodeForm("AUD 3M", form)
    def resultInr = mapper.fromNodeForm("INR OMIBOR", formInr)

    then:
    result.period == "3M"
    semanticMarketDataKey(result, ClearingHouse.NONE) == "3M_AUD-DEPOSIT-T1"
    result.type == "TermDeposit"
    result.convention == "AUD-Deposit-T1"
    result.instrument == "3M"

    resultInr.period == "1D"
    semanticMarketDataKey(resultInr, ClearingHouse.NONE) == "1D_INR-DEPOSIT-T0"
    resultInr.type == "TermDeposit"
    resultInr.convention == "INR-Deposit-T0"
    resultInr.instrument == "1D"
  }

  def "should map any other node"() {
    setup:
    def form = new CurveNodeForm(
      convention: "AUD-FIXED-1Y-AONIA-OIS",
      type: "FixedOvernightSwap",
      period: "3M"
      )

    when:
    def result = mapper.fromNodeForm("ANY NAME", form)

    then:
    result.period == "3M"
    semanticMarketDataKey(result, ClearingHouse.NONE) == "3M_AUD-FIXED-1Y-AONIA-OIS"
    result.type == "FixedOvernightSwap"
    result.convention == "AUD-FIXED-1Y-AONIA-OIS"
    result.instrument == "3M"
  }

  def "should map any other node with clearing house"() {
    setup:
    def form = new CurveNodeForm(
      convention: "AUD-FIXED-1Y-AONIA-OIS",
      type: "FixedOvernightSwap",
      period: "3M"
      )

    when:
    def result = mapper.fromNodeForm("ANY NAME", form)

    then:
    result.period == "3M"
    semanticMarketDataKey(result, ClearingHouse.LCH) == "3M_AUD-FIXED-1Y-AONIA-OIS-LCH"
    result.type == "FixedOvernightSwap"
    result.convention == "AUD-FIXED-1Y-AONIA-OIS"
    result.instrument == "3M"
  }
}
