package com.solum.xplain.core.portfolio.csv.loader

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class IrsTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def commonLoader = new FullSwapTradeCsvLoader()
  def loader = new IrsTradeCsvLoader(commonLoader)

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CoreProductType.IRS)
  }

  def "should parse IRS trade"() {
    setup:
    def rows = loadResource("SwapTradesIRS.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 6
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "USD"))
    result.businessDayAdjustmentType == BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT

    def payLeg = result.payLeg
    payLeg.payReceive == PayReceive.PAY
    payLeg.accrualFrequency == "3M"
    payLeg.paymentFrequency == "3M"
    payLeg.paymentOffsetDays == 0
    payLeg.currency == "USD"
    payLeg.notional == 10000000
    payLeg.paymentCompounding == "None"
    payLeg.index == "USD-LIBOR-3M"
    payLeg.type == CalculationType.IBOR
    !payLeg.isOffshore
    payLeg.extLegIdentifier == "USD_3M_LEG1"

    def receiveLeg = result.getReceiveLeg()
    receiveLeg.type == CalculationType.FIXED
    receiveLeg.payReceive == PayReceive.RECEIVE
    receiveLeg.accrualFrequency == "1Y"
    receiveLeg.paymentFrequency == "1Y"
    receiveLeg.paymentOffsetDays == 0
    receiveLeg.currency == "USD"
    receiveLeg.notional == 10000000
    receiveLeg.dayCount == "30E/360 ISDA"
    receiveLeg.paymentCompounding == "None"
    receiveLeg.extLegIdentifier == "USD_3M_LEG2"


    def item6 = parsedRows[4].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "CNY"))
    item6.getPayLeg().getAccrualFrequency() == "1W"
    item6.getPayLeg().getPaymentFrequency() == "1W"
    item6.getPayLeg().isOffshore
    item6.getPayLeg().extLegIdentifier == "TRADE001_LEG1"
    item6.getReceiveLeg().extLegIdentifier == null


    def item7 = parsedRows[5].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "EUR"))
    item7.stubConvention == "Both"
    item7.firstRegularStartDate == LocalDate.parse("2021-02-12")
    item7.lastRegularEndDate == LocalDate.parse("2026-02-12")
    item7.businessDayAdjustmentType == BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT
  }

  def "should return IRS parse errors"() {
    setup:
    def rows = loadResource("SwapTradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Unsupported value: P5M for field Leg1.AccrualFreq. Supported values: [3M]"
    1   | "Error at line number 3. Error: Unsupported value: P2M for field Leg1.PaymentFreq. Supported values: [1W, 4W, 13W, 26W, 1M, 3M, 6M, 12M, 1Y, TERM]"
    2   | "Error at line number 4. Error: Unsupported value: 7M for field Leg2.AccrualFreq. Supported values: [1W, 4W, 13W, 26W, 1M, 3M, 6M, 12M, 1Y, TERM]"
    3   | "Error at line number 5. Error: Unsupported value: P10M for field Leg2.PaymentFreq. Supported values: [1W, 4W, 13W, 26W, 1M, 3M, 6M, 12M, 1Y, TERM]"
    4   | "Error at line number 6. Error: IRS must not contain both Fixed legs."
    5   | "Error at line number 7. Error: Unknown date format, must be formatted as 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy/M/d', 'd/M/yyyy', 'd-MMM-yyyy', 'dMMMyyyy', 'd/M/yy', 'd-MMM-yy' or 'dMMMyy' but was: 16-01-01"
    6   | "Error at line number 8. Error: Error parsing field End Date. Must be after Start Date"
    7   | "Error at line number 9. Error: IborIndex name not found: CH-CPI"
    8   | "Error at line number 10. Error: Error parsing field Leg2.Ccy. Invalid currency USD for index EUR-EURIBOR-3M"
    9   | "Error at line number 11. Error: Error parsing field Leg2.CompoundingMethod. Compounding must be None when accrual and payment frequencies are equal"
    10  | "Error at line number 12. Error: Unsupported value: 13W for field Leg2.AccrualFreq. Supported values: [3M]"
    11  | "Error at line number 13. Error: Unsupported value: XXX for field Leg1.Ibor.DayCount. Supported values: [One/One, 30/360 ISDA, 30E/360, 30E/360 ISDA, 30U/360, Act/360, Act/365 Actual, Act/365F, Act/365L, Act/Act ISDA, Act/Act Year, Bus/252 BRBD]"
    12  | "Error at line number 14. Error: No value was found for 'Leg1.PaymentFreq'"
    13  | "Error at line number 15. Error: Value must be positive for field: Leg2.Notional"
    14  | "Error at line number 16. Error: Unsupported value: 1DayWed. Supported values: [Day1"
    15  | "Error at line number 17. Error: Error parsing field Leg1.Ibor.FixingDaysOffset. Value must be negative or zero."
    16  | "Error at line number 18. Error: Error parsing field Leg1.Offshore. Offshore index not found for index EUR-EURIBOR-3M"
  }

  def "should return IRS overnight compounding parse errors"() {
    setup:
    def rows = loadResource("SwapTradesInvalidOvernight.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Fixed accrual method must be set when overnight accrual method is OvernightCompoundedAnnualRate."
    1   | "Error at line number 3. Error: Fixed accrual method must be set when overnight accrual method is OvernightCompoundedAnnualRate."
    2   | "Error at line number 4. Error: Unsupported value: Compounded. Supported values: [Default, OvernightCompoundedAnnualRate]"
    3   | "Error at line number 5. Error: Error parsing field End Date. Must be after Start Date"
    4   | "Error at line number 6. Error: Unsupported value: XXX. Supported values: [Averaged, AveragedDaily, Compounded, OvernightCompoundedAnnualRate]"
  }
}
