package com.solum.xplain.core.curveconfiguration.csv.configuration

import static com.solum.xplain.core.common.csv.ParsingMode.LENIENT
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm.emptyForm
import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupViewBuilder.curveGroupView
import static com.solum.xplain.core.instrument.InstrumentTypeResolverHelper.coreResolver

import com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm
import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupViewBuilder
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.instrument.InstrumentTypeResolver
import com.solum.xplain.core.providers.DataProviderRepository
import com.solum.xplain.core.providers.enums.DataProviderType
import com.solum.xplain.core.providers.value.DataProviderView
import jakarta.inject.Provider
import java.util.function.Function
import java.util.stream.Collectors
import spock.lang.Specification

class CurveConfigurationCsvLoaderTest extends Specification {
  CurveConfigurationCsvLoader loader
  InstrumentTypeResolver instrumentTypeResolver = coreResolver()

  def setup() {
    def curveGroupRepository = Mock(CurveGroupRepository)
    curveGroupRepository.curveGroupList() >> [
      curveGroupView(),
      new CurveGroupViewBuilder()
      .name("Name2")
      .id("id2")
      .build()
    ]

    DataProviderRepository providerRepository = Mock(DataProviderRepository)
    providerRepository.dataProvidersViewListForType(DataProviderType.MARKET) >> [new DataProviderView(externalId: "BBG"), new DataProviderView(externalId: "MMR")]
    def providerLoader = new CurveConfigurationInstrumentCsvLoader(providerRepository, instrumentTypeResolver)
    Provider<CurveConfigurationInstrumentCsvLoader> loaderProvider = Mock()
    loaderProvider.get() >> providerLoader
    loader = new CurveConfigurationCsvLoader(loaderProvider, instrumentTypeResolver, curveGroupRepository)
  }

  def "should parse curve configuration"() {
    setup:
    def bytes = """\
        Name,Curve Group,Instrument Type,Primary Provider,Secondary Provider
        Config,Name,IborFixingDeposit,BBG,MMR
        Config,Name,FixedOvernightSwap,BBG,MMR
        Config,Name,OvernightIborBasisSwap,BBG,MMR
        """.stripIndent().bytes
    def instruments = parsedInstruments({ c ->
      c.put(CoreInstrumentType.IBOR_FIXING_DEPOSIT, providerForm())
      c.put(CoreInstrumentType.FIXED_OVERNIGHT_SWAP, providerForm())
      c.put(CoreInstrumentType.OVERNIGHT_IBOR_BASIS_SWAP, providerForm())
    })

    when:
    def result = loader.parse(bytes, STRICT)

    then:
    result.isRight()
    result.getOrNull().warnings.isEmpty()
    result.getOrNull().parsedLines.size() == 1
    result.getOrNull().parsedLines[0] == new CurveConfigurationForm(
      name: "CONFIG",
      curveGroupId: "id",
      instruments: instruments)
  }

  def "should return error for duplicate instrument mapping"() {
    setup:
    def bytes = """\
        Name,Curve Group,Instrument Type,Primary Provider,Secondary Provider
        CONFIG,Name,IborFixingDeposit,BBG,MMR
        CONFIG,Name,IborFixingDeposit,BBG,MMR
        """.stripIndent().bytes

    when:
    def result = loader.parse(bytes, STRICT)

    then:
    result.isLeft()
    result.left().getOrNull().size() == 1
    result.left().getOrNull()[0].description == "Duplicate instrument types: [IBOR_FIXING_DEPOSIT] provided for CONFIG curve configuration"
  }

  def "should return error for mixed curve group names"() {
    setup:
    def bytes = """\
        Name,Curve Group,Instrument Type,Primary Provider,Secondary Provider
        CONFIG,Name,IborFixingDeposit,BBG,MMR
        CONFIG,Name2,OvernightIborBasisSwap,BBG,MMR
        """.stripIndent().bytes

    when:
    def result = loader.parse(bytes, STRICT)

    then:
    result.isLeft()
    result.left().getOrNull().size() == 1
    result.left().getOrNull()[0].description == "Several curve groups defined for CONFIG curve configuration!"
  }

  def "should skip curve configuration with mixed curve group names when LENIENT"() {
    setup:
    def bytes = """\
        Name,Curve Group,Instrument Type,Primary Provider,Secondary Provider
        Config,Name,IborFixingDeposit,BBG,MMR
        Config,Name,FixedOvernightSwap,BBG,MMR
        Config,Name,OvernightIborBasisSwap,BBG,MMR
        Config1,Name2,IborFixingDeposit,BBG,MMR
        Config1,Name,FixedOvernightSwap,BBG,MMR
        """.stripIndent().bytes

    def instruments = parsedInstruments({ c ->
      c.put(CoreInstrumentType.IBOR_FIXING_DEPOSIT, providerForm())
      c.put(CoreInstrumentType.FIXED_OVERNIGHT_SWAP, providerForm())
      c.put(CoreInstrumentType.OVERNIGHT_IBOR_BASIS_SWAP, providerForm())
    })

    when:
    def result = loader.parse(bytes, LENIENT)

    then:
    result.isRight()
    result.getOrNull().warnings.size() == 1
    result.getOrNull().warnings[0].description == "Several curve groups defined for CONFIG1 curve configuration!"
    result.getOrNull().parsedLines.size() == 1
    result.getOrNull().parsedLines[0] == new CurveConfigurationForm(
      name: "CONFIG",
      curveGroupId: "id",
      instruments: instruments)
  }

  Map<CoreInstrumentType, MarketDataProviderForm> parsedInstruments(Closure c) {
    Arrays.stream(CoreInstrumentType.values())
      .collect(Collectors.toMap(Function.identity(), { a -> emptyForm() }))
      .tap(c)
  }

  MarketDataProviderForm providerForm() {
    new MarketDataProviderForm("BBG", "MMR")
  }
}
