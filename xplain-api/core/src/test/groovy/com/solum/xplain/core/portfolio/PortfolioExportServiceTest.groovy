package com.solum.xplain.core.portfolio

import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.common.team.UserTeamEntity
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.csv.PortfolioItemCsvMapper
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.trade.CreditTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamListView
import com.solum.xplain.core.users.UserBuilder
import io.atlassian.fugue.Either
import java.util.stream.Stream
import org.springframework.data.domain.Sort
import spock.lang.Specification

class PortfolioExportServiceTest extends Specification {
  static XplainPrincipal USER = UserBuilder.user("userId")

  def repository = Mock(PortfolioRepository)
  def portfolioItemRepository = Mock(PortfolioItemRepository)
  def userRepository = Mock(AuthenticationContext)
  def teamFilterProvider = Mock(PortfolioTeamFilterProvider)
  def mapper = Mock(PortfolioItemCsvMapper)
  def teamRepository = Mock(TeamRepository)

  def service = new PortfolioExportService(teamFilterProvider,
  userRepository,
  repository,
  portfolioItemRepository,
  mapper,
  teamRepository)


  def "should correctly export portfolio item"() {
    setup:
    def sort = Sort.by("test")
    def stateDate = BitemporalDate.newOfNow()
    def tableFilter = emptyTableFilter()
    def itemsStream = Stream.of(PortfolioItemBuilder.trade(CoreProductType.CDS, new TradeDetails(info: new TradeInfoDetails(), creditTradeDetails: new CreditTradeDetails())))
    1 * userRepository.currentUser() >> USER
    1 * repository.getUserPortfolioView(USER, "portfolioId") >> userPortfolioEither(USER)
    1 * mapper.generatePortfolioItemCsv(itemsStream) >> new CsvOutputFile(["header"], [])
    portfolioItemRepository.portfolioItemsStream("portfolioId",
      stateDate,
      sort,
      tableFilter) >> itemsStream

    when:
    def result = service.exportPortfolioItems(stateDate, "portfolioId", sort, tableFilter)

    then:
    result.isRight()
    result.getOrNull().bytes != null
  }

  def "should correctly export portfolio list items"() {
    setup:
    def sort = Sort.unsorted()
    def stateDate = BitemporalDate.newOfNow()
    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * repository.portfolioViewList(_, _) >> [
      new PortfolioView(allowAllTeams: true,
      id: "000000000000000000000000",
      externalPortfolioId: "externalId",
      entityId: "entityId",
      companyId: "cId",
      externalEntityId: "EXTERNAL_ENTITY",
      externalCompanyId: "EXTERNAL_COMPANY"),
      new PortfolioView(allowAllTeams: true,
      id: "000000000000000000000001",
      externalPortfolioId: "externalId2",
      entityId: "entityId",
      companyId: "cId",
      externalEntityId: "EXTERNAL_ENTITY",
      externalCompanyId: "EXTERNAL_COMPANY")
    ]

    def items = Stream.of(
      PortfolioItemBuilder.trade(CoreProductType.CDS, new TradeDetails(info: new TradeInfoDetails(), creditTradeDetails: new CreditTradeDetails(upfrontConvention: "Following")), "000000000000000000000000"),
      PortfolioItemBuilder.trade(CoreProductType.CDS, new TradeDetails(info: new TradeInfoDetails(), creditTradeDetails: new CreditTradeDetails(upfrontConvention: "Following")), "000000000000000000000001")
      )
    portfolioItemRepository.portfoliosItemsStream(_ as Set<String>, stateDate, sort) >> items
    1 * mapper.generatePortfolioItemCsv(items) >> new CsvOutputFile(["header"], [])

    when:
    def result = service.exportAllPortfolioItems(stateDate, sort)

    then:
    result.isRight()
    result.getOrNull().bytes != null
  }

  def "should correctly export portfolio items sorted by valuation data keys"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    1 * userRepository.currentUser() >> USER
    1 * repository.getUserPortfolioView(USER, "000000000000000000000001") >> userPortfolioEither(USER)

    def portfolioItem = PortfolioItemBuilder.irsPortfolioItem("000000000000000000000001", "TRADE_ID_1")
    def portfolioItem2 = PortfolioItemBuilder.irsPortfolioItem("000000000000000000000001", "TRADE_ID_0")
    1 * portfolioItemRepository.activePortfolioItemsStream("000000000000000000000001", stateDate) >> Stream.of(portfolioItem, portfolioItem2)
    when:
    def result = service.exportPortfolioTradeDataKeys("000000000000000000000001", stateDate)

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "utf-8") == """\
        Valuation_Date,Provider,Key,Value,Delta,Vega,Gamma,Theta,Rho,Par_Rate,Spot_Rate,Implied_Vol,ATM_Implied_Vol,Realised_Vol,Fair_Vol
        ,,COMPANY_ID_ENTITY_ID_PORTFOLIO_ID_TRADE_ID_0,,,,,,,,,,,,
        ,,COMPANY_ID_ENTITY_ID_PORTFOLIO_ID_TRADE_ID_1,,,,,,,,,,,, 
        """.stripIndent()
  }

  def "should correctly export all portfolio items sorted by valuation data keys"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * repository.portfolioViewList(_, _) >> [
      new PortfolioView(id: "000000000000000000000000",
      allowAllTeams: true,
      externalPortfolioId: "P1",
      entityId: "entityId",
      externalEntityId: "E1",
      companyId: "companyId",
      externalCompanyId: "C1"),
      new PortfolioView(id: "000000000000000000000001",
      allowAllTeams: true,
      externalPortfolioId: "P2",
      entityId: "entityId",
      externalEntityId: "E2",
      companyId: "companyId",
      externalCompanyId: "C2")
    ]
    def portfolioItem1 = PortfolioItemBuilder.irsPortfolioItem("000000000000000000000000", "TRADE_ID")
    def portfolioItem2 = PortfolioItemBuilder.irsPortfolioItem("000000000000000000000001", "TRADE_ID")
    portfolioItemRepository.portfoliosItemsStream(_ as Set, stateDate) >> Stream.of(portfolioItem2, portfolioItem1)

    when:
    def result = service.exportAllPortfolioTradeDataKeys(stateDate)

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "utf-8") == """\
        Valuation_Date,Provider,Key,Value,Delta,Vega,Gamma,Theta,Rho,Par_Rate,Spot_Rate,Implied_Vol,ATM_Implied_Vol,Realised_Vol,Fair_Vol
        ,,C1_E1_P1_TRADE_ID,,,,,,,,,,,,
        ,,C2_E2_P2_TRADE_ID,,,,,,,,,,,, 
        """.stripIndent()
  }

  def "should correctly export portfolios"() {
    setup:
    def sort = Sort.unsorted()
    def stateDate = BitemporalDate.newOfNow()
    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * repository.portfolioViewStream(emptyTableFilter(), sort, activePortfolios(), PortfolioTeamFilter.emptyFilter()) >> [
      portfolioView(),
      new PortfolioView(id: "000000000000000000000002",
      name: "Portfolio 2",
      description: "Restricted team",
      allowAllTeams: false,
      teamIds: ["teamId1", "teamId2"],
      externalPortfolioId: "PORTFOLIO_ID_2",
      entityId: "entityId",
      externalEntityId: "ENTITY_ID",
      companyId: "companyId",
      externalCompanyId: "COMPANY_ID")
    ].stream()
    1 * teamRepository.getTeamsById() >> [
      teamId1: new TeamListView(id: "teamId1", name: "Team 1"),
      teamId2: new TeamListView(id: "teamId2", name: "Team 2")
    ]

    when:
    def result = service.exportPortfolios(sort, emptyTableFilter(), activePortfolios(), stateDate.actualDate, null)

    then:
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == """\
      Portfolio ID,Portfolio Name,Company ID,Entity ID,Team names,Allow all teams,Description
      PORTFOLIO_ID,Portfolio Name,COMPANY_ID,ENTITY_ID,,true,
      PORTFOLIO_ID_2,Portfolio 2,COMPANY_ID,ENTITY_ID,Team 1|Team 2,false,Restricted team
      """.stripIndent()
  }

  static PortfolioView portfolioView() {
    new PortfolioView(
      id: "000000000000000000000001",
      name: "Portfolio Name",
      allowAllTeams: true,
      externalPortfolioId: "PORTFOLIO_ID",
      entityId: "entityId",
      externalEntityId: "ENTITY_ID",
      companyId: "companyId",
      externalCompanyId: "COMPANY_ID")
  }

  static Either<ErrorItem, UserTeamEntity<PortfolioView>> userPortfolioEither(XplainPrincipal user) {
    return Either.right(UserTeamEntity.userEntity(user, portfolioView()))
  }
}
