package com.solum.xplain.core.classifiers.csv

import spock.lang.Specification

class ImmForwardRateAgreementConventionCsvMapperTest extends Specification implements ConventionMapperSample {
  def "should export IMM FRA conventions"() {
    setup:
    def bytes = ImmForwardRateAgreementConventionCsvMapper.immFraConventionsCsv()

    expect:
    assertMatchesResource(bytes, "ImmFraConventions.csv")
  }
}
