package com.solum.xplain.core.portfolio.csv.loader


import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.extensions.enums.PositionType
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Collectors
import spock.lang.Specification

class CreditIndexTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def commonLoader = new CommonCreditTradeCsvLoader()
  def loader = new CreditIndexTradeCsvLoader(commonLoader)

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CoreProductType.CREDIT_INDEX)
  }

  def "should parse CREDIT_INDEX trade"() {
    setup:
    def rows = loadResource("CreditIndexTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 2
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "USD"))
    result.positionType == PositionType.SELL
    result.payLeg != null
    result.receiveLeg != null
    result.info != null
    result.optionTradeDetails == null
    result.creditTradeDetails != null
    result.businessDayConvention == null
    result.notionalScheduleInitialExchange == null
    result.notionalScheduleFinalExchange == null
    result.stubConvention == null
    result.startDate == LocalDate.parse("2016-01-01")
    result.endDate == LocalDate.parse("2017-01-01")
    result.firstRegularStartDate == null
    result.lastRegularEndDate == null
    result.rollConvention == null

    result.payLeg.payReceive == null
    result.payLeg.type == null
    result.payLeg.payReceive == null
    result.payLeg.notional == 1000000
    result.payLeg.payReceive == null
    result.payLeg.currency == "USD"
    result.payLeg.accrualFrequency == null
    result.payLeg.accrualMethod == null
    result.payLeg.paymentFrequency == null
    result.payLeg.paymentCompounding == null
    result.payLeg.paymentOffsetDays == 0
    result.payLeg.index == null
    result.payLeg.fixingDateOffsetDays == null
    result.payLeg.dayCount == null
    result.payLeg.initialValue == null
    result.payLeg.inflationLag == null
    result.payLeg.indexCalculationMethod == null
    result.payLeg.overnightRateCutOffDays == null

    result.receiveLeg.payReceive == null
    result.receiveLeg.type == null
    result.receiveLeg.payReceive == null
    result.receiveLeg.notional == 1000000
    result.receiveLeg.payReceive == null
    result.receiveLeg.currency == "USD"
    result.receiveLeg.accrualFrequency == null
    result.receiveLeg.accrualMethod == null
    result.receiveLeg.paymentFrequency == "6M"
    result.receiveLeg.paymentCompounding == null
    result.receiveLeg.paymentOffsetDays == 0
    result.receiveLeg.index == null
    result.receiveLeg.fixingDateOffsetDays == null
    result.receiveLeg.dayCount == null
    result.receiveLeg.initialValue == 9
    result.receiveLeg.inflationLag == null
    result.receiveLeg.indexCalculationMethod == null
    result.receiveLeg.overnightRateCutOffDays == null

    result.creditTradeDetails.reference == "REFERENCE_CODE"
    result.creditTradeDetails.entityLongName == "CDX_NA_HY"
    result.creditTradeDetails.creditIndexSeries == 1
    result.creditTradeDetails.creditIndexVersion == 2
    result.creditTradeDetails.docClause == CreditDocClause.XR14
    result.creditTradeDetails.sector == CreditSector.DIVERSIFIED
    result.creditTradeDetails.seniority == null
    result.creditTradeDetails.upfront == 10
    result.creditTradeDetails.upfrontDate == LocalDate.parse("2017-01-01")
    result.creditTradeDetails.upfrontConvention == "Following"
  }

  def "should return CREDIT_INDEX parse errors"() {
    setup:
    def rows = loadResource("CreditIndexTradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Unsupported value: INVALID. Supported values: [CDX NA IG"
    1   | "Error at line number 3. Error: No value was found for 'Credit Reference'"
    2   | "Error at line number 4. Error: Value must be positive for field: Credit Notional"
  }
}
