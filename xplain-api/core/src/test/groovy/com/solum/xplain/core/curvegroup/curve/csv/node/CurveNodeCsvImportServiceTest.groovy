package com.solum.xplain.core.curvegroup.curve.csv.node

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_1M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.IMPORT_ERROR
import static com.solum.xplain.core.error.Error.IMPORT_INFO
import static com.solum.xplain.core.error.Error.IMPORT_WARNING
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.csv.ItemsGroupCsvResult
import com.solum.xplain.core.common.csv.NamedList
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository
import com.solum.xplain.core.curvegroup.curve.CurveMapper
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode
import com.solum.xplain.core.curvegroup.curve.value.CurveForm
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.InfoItem
import com.solum.xplain.core.error.WarningItem
import java.time.LocalDate
import spock.lang.Specification

class CurveNodeCsvImportServiceTest extends Specification {
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static GROUP_ID = "curveGroupId"
  def static CURVE_ID = "curveId"
  def static FILE_CONTENT = [] as byte[]


  def auditEntryService = Mock(AuditEntryService)
  def csvLoader = Mock(CurveNodesCsvLoader)
  def curveMapper = Mock(CurveMapper)
  def repository = Mock(CurveGroupCurveRepository)

  def nodeImport = Spy(new CurveNodeCsvImportService(
  auditEntryService,
  csvLoader,
  curveMapper,
  repository)
  )

  def "should return errors when importing for curve and curve not found"() {
    setup:
    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> left(OBJECT_NOT_FOUND.entity())
    0 * repository.getFutureVersions(_, _)
    0 * csvLoader.parseForCurve(_, _, _)
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == OBJECT_NOT_FOUND
  }

  def "should return errors when importing for curve and csv parsing error"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult([PARSING_ERROR.entity()])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
  }

  def "should return errors when importing for curve and csv parsing error and nodes"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [IMPORT_ERROR.entity()],
      curve.name,
      [nodeForm("1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
  }

  def "should import nodes for curve and return success result when ERROR"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >>
    [InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should import nodes for curve and return error result"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, REPLACE) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >>
    [new ErrorItem(MISSING_ENTRY, "error description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "error description"
  }

  def "should import nodes for curve with warnings"() {
    setup:
    Curve curve = curve([])
    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "info description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult([], [warning], curve.name, [nodeForm("1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should fail import nodes for curve with warnings"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [new ErrorItem(IMPORT_ERROR, "error description")],
      [WarningItem.of(IMPORT_WARNING, "warning description")],
      curve.name,
      [nodeForm("1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, { it.actualDate == STATE_DATE }) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(GROUP_ID, CURVE_ID, STATE_DATE, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"
  }

  def "should return errors when importing for all curves and csv parsing error"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult([PARSING_ERROR.entity()])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes([curve], _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == PARSING_ERROR
  }

  def "should return errors when importing for all curves and csv parsing error and nodes"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [IMPORT_ERROR.entity()],
      curve.name,
      [nodeForm("1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
  }

  def "should import nodes for all curves and return success result"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >>
    [InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should fail import nodes for all curves and return errors"() {
    setup:
    Curve curve = curve([])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >>
    [new ErrorItem(MISSING_ENTRY, "error description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "error description"
  }

  def "should import nodes for all curves and log info and warning items"() {
    setup:
    Curve curve = curve([])
    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "info description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [],
      [warning],
      curve.name,
      [nodeForm("1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], _, csvResult.namedLists) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, [info, warning])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should fail import nodes for all curves and log error and warning items"() {
    setup:
    Curve curve = curve([])
    ErrorItem error = new ErrorItem(IMPORT_ERROR, "error description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [error],
      [warning],
      curve.name,
      [nodeForm("1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, [error, warning])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"
  }

  def "should fail with unexpected action REPLACE_DELETE"() {
    setup:
    Curve curve = curve([node("1M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm("2M"), nodeForm("3M")])

    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should fail with unexpected action REPLACE"() {
    setup:
    Curve curve = curve([node("1M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm("1M"), nodeForm("3M")])

    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should return MISSING_ENTRY with missing nodes when ERROR "() {
    setup:
    Curve curve = curve([node("1M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == MISSING_ENTRY
    result[0].description == "Node 1M in curve EUR 1M is missing"
  }

  def "should not update curve with only duplicate nodes when ERROR"() {
    setup:
    Curve curve = curve([node("1M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 0
  }

  def "should not update curve with duplicate FRA node when ERROR"() {
    setup:
    Curve curve = curve([node("24M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [fraNode("18M")])

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 0
  }

  def "should return NEW_VERSION_VIABLE when ERROR"() {
    setup:
    Curve curve = curve([], PAST_VERSION_DATE)
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == NEW_VERSION_VIABLE
    result[0].description == "New version is viable for EUR 1M"
  }


  def "should return FUTURE_VERSION_EXISTS when ERROR"() {
    setup:
    Curve curve = curve([])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M")])

    1 * repository.getFutureVersions(_, _) >> new DateList([FUTURE_VERSION_DATE])
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == FUTURE_VERSION_EXISTS
    result[0].description == "EUR 1M has future version(s)"
  }

  def "should return empty result when no errors when ERROR"() {
    setup:
    Curve curve = curve([node("1M")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm("1M"), nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.isEmpty()
  }

  def "should correctly update curve when APPEND"() {
    setup:
    Curve curve = curve([node("1M"), node("1W")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm("1M"), nodeForm("3M")])

    doUpdate(curve, curveForm, [nodeForm("1M"), nodeForm("1W"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(curve.entityId)
  }

  def "should correctly update curve when APPEND_DELETE"() {
    setup:
    Curve curve = curve([node("1M"), node("1W")])
    CurveForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm("1M"), nodeForm("3M")])

    doUpdate(curve, curveForm, [nodeForm("1M"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(curve.entityId)
  }

  def curve(List<CurveNode> nodes, validFrom = STATE_DATE) {
    def curve = Mock(Curve)
    curve.curveGroupId >> GROUP_ID
    curve.entityId >> entityId("curve_entityId").id
    curve.validFrom >> validFrom
    curve.name >> "EUR 1M"
    curve.nodes >> nodes
    return curve
  }

  static fraNode(String settlement) {
    return new CurveNodeForm(
      type: FRA_NODE,
      period: "6M",
      fraSettlement: settlement,
      convention: EUR_EURIBOR_6M.name
      )
  }

  def node(String period) {
    def node = new CurveNode(
      type: IBOR_FIXING_DEPOSIT_NODE,
      period: period,
      convention: EUR_EURIBOR_1M.name
      )
    def nodeForm = nodeForm(period)

    _ * curveMapper.toNodeForm(node) >> nodeForm
    return node
  }

  static nodeForm(String period) {
    def nodeForm = new CurveNodeForm(
      type: IBOR_FIXING_DEPOSIT_NODE,
      period: period,
      convention: EUR_EURIBOR_1M.name
      )
    return nodeForm
  }

  def curveForm(Curve curve) {
    def curveForm = Mock(CurveForm)
    _ * curveMapper.toForm(curve) >> curveForm
    return curveForm
  }


  static nodeGroupCsvResult(List<ErrorItem> errors, List<WarningItem> warnings, String curveName, List<CurveNodeForm> nodesForms) {
    return new ItemsGroupCsvResult(errors, warnings, [new NamedList<>(curveName, nodesForms)])
  }

  static nodeGroupCsvResult(List<ErrorItem> errors) {
    return new ItemsGroupCsvResult(errors, [], [])
  }

  static nodeGroupCsvResult(List<ErrorItem> errors, String curveName, List<CurveNodeForm> nodesForms) {
    return nodeGroupCsvResult(errors, [], curveName, nodesForms)
  }

  static nodeGroupCsvResult(String curveName, List<CurveNodeForm> nodesForms) {
    return nodeGroupCsvResult([], [], curveName, nodesForms)
  }

  def doNotUpdate(CurveForm curveForm) {
    0 * curveForm.setNodes(_)
    0 * curveForm.setVersionForm(_)
    0 * repository.updateCurve(_, _, _, _)
  }

  def returnNoFutureVersions() {
    1 * repository.getFutureVersions(_, _) >> new DateList([])
  }

  def doUpdate(Curve curve, CurveForm curveForm, List<CurveNodeForm> nodeForms) {
    1 * curveForm.setNodes({
      it.size() == nodeForms.size() &&
        nodeForms.withIndex().collect { elem, idx -> elem == nodeForms[idx as Integer] }
    })
    1 * curveForm.setVersionForm({ it.validFrom == curve.validFrom })
    1 * repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, curveForm) >>
      right(entityId(curve.entityId))
  }
}
