package com.solum.xplain.core.portfolio.value

import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.form.InflationTradeForm
import com.solum.xplain.core.portfolio.form.SwapLegForm
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import java.time.LocalDate
import spock.lang.Specification

class InflationTradeFormTest extends Specification {

  def "should create product item"() {
    setup:
    def form = new InflationTradeForm(
      "externalTradeId": "D-14ID216243_A1",
      "tradeDate": LocalDate.parse("2017-11-28"),
      "tradeCounterpartyType": "CLEARED",
      "startDate": LocalDate.parse("2017-11-15"),
      "endDate": LocalDate.parse("2027-11-15"),
      "leg1": new SwapLegForm(
      "extLegIdentifier": "INFLATION_LEG_1",
      "payReceive": "Pay",
      "accrualFrequency": "TERM",
      "paymentFrequency": "TERM",
      "paymentOffsetDays": 0,
      "compoundingMethod": "Flat",
      "notionalCurrency": "GBP",
      "notionalValue": 6002000,
      "calculationType": "INFLATION",
      "calculationInflationIndex": "GB-RPI",
      "calculationInflationLag": "2M",
      "indexCalculationMethod": "Monthly"
      ),
      "leg2": new SwapLegForm(
      "extLegIdentifier": "INFLATION_LEG_2",
      "payReceive": "Receive",
      "accrualFrequency": "12M",
      "paymentFrequency": "TERM",
      "paymentOffsetDays": 0,
      "compoundingMethod": "Flat",
      "calculationFixedRateInitialValue": 0.034225,
      "calculationFixedDayCount": "1/1",
      "notionalCurrency": "GBP",
      "notionalValue": 6002000,
      "calculationType": "FIXED"
      ),
      "businessDayConvention": "ModifiedFollowing",
      "businessDayAdjustmentType": BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
      "initialExchange": false,
      "finalExchange": false,
      "stubConvention": "SmartInitial",
      "versionForm": NewVersionFormV2.newDefault()
      )

    when:
    def item = form.toTradeValue().getOrNull()

    then:
    item.productType == CoreProductType.INFLATION
  }
}
