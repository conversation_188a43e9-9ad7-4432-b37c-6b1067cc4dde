package com.solum.xplain.core.portfolio.value

import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.trade

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import java.time.LocalDate
import spock.lang.Specification

class CreditIndexTradeViewTest extends Specification {

  def "should create from portfolio item"() {
    setup:
    def portfolioItem = trade(CoreProductType.CREDIT_INDEX, TradeDetailsBuilder.creditIndexTradeDetails(LocalDate.now()))
    portfolioItem.tradeDetails.info.csaDiscountingGroup = "USD"

    when:
    def result = CreditIndexTradeView.of(portfolioItem)

    then:
    result.isRight()
    def view = result.getOrNull()
    view.position == "BUY"
    view.creditIndexSeries == 1
    view.creditIndexVersion == 1
    view.entityLongName == "CDX_NA_HY"
    view.currency == "EUR"
    view.notionalValue == 10000d
    view.startDate == LocalDate.now()
    view.endDate == LocalDate.now().plusYears(1)
    view.frequency == "3M"
    view.fixedRate == 2
    view.upfrontFee.amount == 10
    view.reference == "REFERENCE_CODE"
    view.dayCount == "Act/360"
    view.csaDiscountingGroup == "USD"
  }
}
