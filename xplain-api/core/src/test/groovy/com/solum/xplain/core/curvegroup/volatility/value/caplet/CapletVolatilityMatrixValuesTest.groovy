package com.solum.xplain.core.curvegroup.volatility.value.caplet


import java.time.LocalDate
import spock.lang.Specification

class CapletVolatilityMatrixValuesTest extends Specification {
  def "should create matrix"() {
    setup:
    def content = [
      new CapletVolatilityNodeValueView(
      tenor: "1M", strike: new BigDecimal("0.9"), value: 0.75),
      new CapletVolatilityNodeValueView(
      tenor: "1Y", strike: new BigDecimal("0.9"), value: 0.65),
      new CapletVolatilityNodeValueView(
      tenor: "1M", strike: new BigDecimal("0.8"), value: 0.95),
      new CapletVolatilityNodeValueView(
      tenor: "1Y", strike: new BigDecimal("0.8"), value: 0.85),
    ]

    when:
    def matrix = new CapletVolatilityMatrixValues(content, LocalDate.now())

    then:
    matrix.getColumns() == ["0.8", "0.9"]
    matrix.getRows() == ["1M", "1Y"]
    matrix.values == [
      [row  : "1M",
        "0.8": new CapletVolatilityNodeValueView(
        tenor: "1M", strike: new BigDecimal("0.8"), value: 0.95),
        "0.9": new CapletVolatilityNodeValueView(
        tenor: "1M", strike: new BigDecimal("0.9"), value: 0.75)
      ],
      [row  : "1Y",
        "0.8": new CapletVolatilityNodeValueView(
        tenor: "1Y", strike: new BigDecimal("0.8"), value: 0.85),
        "0.9": new CapletVolatilityNodeValueView(
        tenor: "1Y", strike: new BigDecimal("0.9"), value: 0.65)
      ]
    ]
    matrix.matrix() == [[0.95, 0.75], [0.85, 0.65]]
  }
}
