package com.solum.xplain.core.curvegroup.curve.validation

import static com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository.uniqueEntityCriteria
import static com.solum.xplain.core.curvegroup.curve.value.CurveFormBuilder.curveForm

import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.validation.UniqueEntitySupport
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.ConstraintValidatorContext.ConstraintViolationBuilder
import jakarta.validation.ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext
import spock.lang.Specification
import spock.lang.Unroll

class ValidCurveNameValidatorTest extends Specification {

  @Unroll
  def "should validate curve name #name and type #type with result #expected"() {
    when:
    def result = ValidCurveNameValidator.isNameValidForType(name, type)
    then:
    result == expected
    where:
    type              | name      | expected
    "IR_INDEX"        | "AED 3M"  | true
    "INDEX_BASIS"     | "AED 3M"  | false
    "XCCY"            | "AUD/USD" | true
    "INFLATION_INDEX" | "AUD/USD" | false
    "INFLATION_INDEX" | "GB RPI"  | true
    "INFLATION_INDEX" | "AED 1M"  | false
    "INDEX_BASIS"     | "AED 1M"  | true
  }

  @Unroll
  def "should validate curve validity #name and #exist with result error"() {
    when:
    def form = curveForm()
    form.setName(name)
    form.setCurveType(CurveType.IR_INDEX.name())

    def req = Mock(RequestPathVariablesSupport)
    req.getPathVariable("groupId") >> "gId"
    def unq = Mock(UniqueEntitySupport)
    unq.existsByCriteria(form.getVersionForm().getValidFrom(), uniqueEntityCriteria("gId", name), Curve.class) >> exist
    def validator = new ValidCurveNameValidator(req, unq)

    def n = Mock(NodeBuilderCustomizableContext)
    1 * n.addConstraintViolation()

    def b = Mock(ConstraintViolationBuilder)
    1 * b.addPropertyNode("name") >> n

    def ctx = Mock(ConstraintValidatorContext)
    1 * ctx.disableDefaultConstraintViolation()
    1 * ctx.buildConstraintViolationWithTemplate(_) >> {
      assert it[0] == error
      b
    }

    then:
    def result = validator.isValid(form, ctx)
    !result

    where:
    name      | exist | error
    "AED 3M"  | true  | "Curve AED 3M already exists at 1970-01-01 date"
    "INVALID" | false | "Invalid curve name : INVALID for curve type: IR_INDEX"
  }
}
