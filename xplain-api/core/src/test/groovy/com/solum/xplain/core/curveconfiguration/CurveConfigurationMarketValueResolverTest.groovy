package com.solum.xplain.core.curveconfiguration

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_RATES
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.IBOR_FIXING_DEPOSIT
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.SWAPTION_ATM
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.values
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofCreditCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFx
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofVol
import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueFullViewPrimary
import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueFullViewSecondary
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.extensions.enums.CreditSector.BASIC_MATERIALS

import com.solum.xplain.core.common.value.EntityNameView
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.function.Function
import java.util.stream.Collectors
import spock.lang.Specification

class CurveConfigurationMarketValueResolverTest extends Specification {

  private static final BitemporalDate STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  CurveGroupRepository curveGroupRepository = Mock(CurveGroupRepository)
  CurveConfigurationRepository curveConfigurationRepository = Mock(CurveConfigurationRepository)
  MarketDataGroupRepository marketDataGroupRepository = Mock(MarketDataGroupRepository)
  InstrumentPriceTypeResolver priceTypeResolver = Mock(InstrumentPriceTypeResolver)

  CurveConfigurationMarketValueResolver valueResolver = new CurveConfigurationMarketValueResolver(curveGroupRepository, curveConfigurationRepository, marketDataGroupRepository)

  def "should resolve market data values from resolved market data for date"() {
    setup:
    def mdParams = Mock(MarketDataExtractionParams)
    2 * mdParams.getStateDate() >> STATE_DATE
    2 * mdParams.getMarketDataGroupId() >> "mdId"
    2 * mdParams.getConfigurationId() >> "id"
    2 * mdParams.getMarketDataSource() >> MarketDataSourceType.RAW_PRIMARY
    1 * mdParams.getPriceTypeResolver() >> priceTypeResolver
    priceTypeResolver.resolvePriceType(_) >> BID_PRICE
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, "id") >> Either.right(curveConfig())
    1 * marketDataGroupRepository.dataGroupName("mdId") >> Optional.ofNullable(new EntityNameView(name: "name"))
    curveGroupRepository.allInstruments("curveGroupId", STATE_DATE) >> [
      ofIrCurve("EUR", "C1", IBOR_FIXING_DEPOSIT, "1Y", "1Y", "quote2", "name"),
      ofFx(FX_RATES, "EUR/USD", FX_RATE, "EUR/USD", "name"),
      ofCreditCurve("NAME", BASIC_MATERIALS, CDS, "1Y", "spread", "name"),
      ofVol("EUR 3M Vols", "EUR", SWAPTION_ATM, "vol", "3M", "3M", "EUR 3M", "name")
    ]
    when:
    def result = valueResolver.curveConfigurationMarketData(
      mdParams,
      marketDataForDate())

    then:
    result.isRight()
    def md = result.getOrNull()
    md.getValues().size() == 4
    md.getValues()[0].key == "quote2"
    md.getValues()[0].value == BigDecimal.TEN
    md.fxRates().size() == 1
    md.fxRates()[0].key == "EUR/USD"
    md.fxRates()[0].value == BigDecimal.ONE
    md.getCurveConfigurationId() == "id"
  }

  def "should resolve market data values"() {
    setup:
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, "id") >> Either.right(curveConfig())
    1 * curveGroupRepository.allInstruments("curveGroupId", STATE_DATE) >> [
      ofIrCurve("EUR", "C1", IBOR_FIXING_DEPOSIT, "1Y", "1Y", "quote2", "name"),
      ofFx(FX_RATES, "EUR/USD", FX_RATE, "EUR/USD", "name"),
      ofCreditCurve("name", BASIC_MATERIALS, CDS, "1Y", "spread", "name"),
      ofVol("name", "EUR", SWAPTION_ATM, "vol", "3M", "3M", "EUR 3M", "name")
    ]
    priceTypeResolver.resolvePriceType(_) >> BID_PRICE

    when:
    def result = valueResolver.resolveMarketData(
      STATE_DATE,
      "id",
      priceTypeResolver,
      marketDataForDate(),
      toMarketValueFullViewPrimary()
      )
    then:
    result.size() == 4
    result[0].key == "quote2"
    result[0].value == BigDecimal.TEN
    result[0].ticker == "ticker"
    result[0].provider == "MMR"
  }

  def "should not resolve market data values when primary provider is missing"() {
    setup:
    def config = new CurveConfiguration(
      curveGroupId: "curveGroupId",
      instruments: [(IBOR_FIXING_DEPOSIT): new MarketDataProviders(primary: "BBG", secondary: "AAA")]
      )
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, "id") >> Either.right(CurveConfigurationInstrumentResolver.fromConfiguration(config))
    1 * curveGroupRepository.allInstruments("curveGroupId", STATE_DATE) >> [ofIrCurve("EUR", "C1", IBOR_FIXING_DEPOSIT, "1Y", "1Y", "quote2", "name")]
    priceTypeResolver.resolvePriceType(_) >> BID_PRICE

    when:
    def result = valueResolver.resolveMarketData(
      STATE_DATE,
      "id",
      priceTypeResolver,
      marketDataForDate(),
      toMarketValueFullViewSecondary()
      )
    then:
    result == []
  }

  def static curveConfig() {
    return CurveConfigurationHelper.instrumentResolver(
      "Configuration",
      "curveGroupId",
      Arrays.stream(values())
      .collect(Collectors.toMap(Function.identity(), { v ->
        new MarketDataProviders(primary: "BBG", secondary: "BBG")
      })),
      [
        new CurveConfigurationOverrideBuilder()
        .instruments(Arrays.stream(values())
        .collect(Collectors.toMap(Function.identity(), { v ->
          new CurveConfigurationProviderOverride(primary: "MMR", secondary: "MMR", assetNames: ["C1"])
        })))
        .build()
      ]

      )
  }

  def static marketDataForDate() {
    return ["EUR/USD": new ResolvedMarketDataValueView(
      key: "EUR/USD",
      instrumentType: FX_RATE,
      assetGroup: CoreAssetGroup.FX,
      values: [
        new MarketDataValueFlatView(value: BigDecimal.ONE, ticker: "ticker", provider: "BBG", bidAsk: BID),
        new MarketDataValueFlatView(value: BigDecimal.TEN, ticker: "ticker", provider: "MMR", bidAsk: BID)
      ]
      ),
      "quote2" : new ResolvedMarketDataValueView(
      key: "quote2",
      instrumentType: IBOR_FIXING_DEPOSIT,
      assetGroup: CoreAssetGroup.RATES,
      values: [
        new MarketDataValueFlatView(value: BigDecimal.ONE, ticker: "ticker", provider: "BBG", bidAsk: BID),
        new MarketDataValueFlatView(value: BigDecimal.TEN, ticker: "ticker", provider: "MMR", bidAsk: BID)
      ]
      ),
      "spread" : new ResolvedMarketDataValueView(
      key: "spread",
      instrumentType: CDS,
      assetGroup: CoreAssetGroup.CREDIT,
      values: [
        new MarketDataValueFlatView(value: BigDecimal.ONE, ticker: "ticker", provider: "BBG", bidAsk: BID),
        new MarketDataValueFlatView(value: BigDecimal.TEN, ticker: "ticker", provider: "MMR", bidAsk: BID)
      ]
      ),
      "vol"    : new ResolvedMarketDataValueView(
      key: "vol",
      assetGroup: CoreAssetGroup.RATES,
      values: [
        new MarketDataValueFlatView(value: BigDecimal.ONE, ticker: "ticker", provider: "BBG", bidAsk: BID),
        new MarketDataValueFlatView(value: BigDecimal.TEN, ticker: "ticker", provider: "MMR", bidAsk: BID)
      ]
      )
    ]
  }
}
