package com.solum.xplain.core.providers.csv

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.providers.value.DataProviderView
import spock.lang.Specification

class DataProviderCsvMapperTest extends Specification {
  def "should correctly map provider"() {
    setup:

    when:
    def mapper = new DataProviderCsvMapper(null)
    def row = mapper.toCsvRow(new DataProviderView(
      externalId: "XPLAIN",
      name: "XPLAIN",
      types: ["MARKET", "VALUATION"]
      ))
    def csv = new CsvOutputFile(mapper.header(), [row])
    def result = csv.write()

    then:
    result == "Long Name,ID,Data Type\nXPLAIN,XPLAIN,MARKET|VALUATION\n"
  }
}
