package com.solum.xplain.core.config

import com.solum.xplain.core.lock.LockingInterceptor
import com.solum.xplain.core.refdata.ReferenceDataInterceptor
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpSession
import org.apache.catalina.util.ParameterMap
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.WebUtils
import spock.lang.Specification
import spock.lang.Unroll

class RequestLoggingFilterTest extends Specification {
  def lockingInterceptor = Mock(LockingInterceptor)
  def refDataInterceptor = Mock(ReferenceDataInterceptor)

  @Unroll
  def "should correctly log request message  #reqUri, #queryStr, #method, #session, #addr, #user, #expectedRes"() {
    setup:
    def request = Mock(HttpServletRequest)
    request.getRequestURI() >> reqUri
    request.getQueryString() >> queryStr
    request.getMethod() >> method
    request.getSession(false) >> session
    request.getRemoteAddr() >> addr
    request.getRemoteUser() >> user

    expect:
    def result = new WebConfig(lockingInterceptor, refDataInterceptor, null).createLoggingFilter().createMessage(request, "pref", "suf")
    result == expectedRes

    where:
    reqUri   | queryStr   | method | session           | addr   | user   | expectedRes
    "reqUri" | "queryStr" | "POST" | Mock(HttpSession) | "addr" | "user" | "prefuri=reqUri?queryStr;method=POST;client=addr;session=null;user=usersuf"
    null     | null       | null   | null              | null   | null   | "prefuri=null?null;method=nullsuf"
  }

  @Unroll
  def "should correctly log ContentCachingRequestWrapper message  #contentType, #method, #path, #uri, #payload, #payloadLogged"() {
    setup:
    def request = Mock(ContentCachingRequestWrapper)
    request.getContentType() >> contentType
    request.getContentAsByteArray() >> "content".getBytes()
    request.getCharacterEncoding() >> WebUtils.DEFAULT_CHARACTER_ENCODING
    request.getMethod() >> method
    request.getContextPath() >> path
    request.getRequestURI() >> uri
    request.getParameterMap() >> new ParameterMap<>(["password": ["pass"] as String[], "name": ["Bob"] as String[]])

    expect:
    def result = new WebConfig(lockingInterceptor, refDataInterceptor, null).createLoggingFilter().createMessage(request, "pref", "suf")
    result.contains(payload) == payloadLogged

    where:
    contentType                         | method | path   | uri            | payload                    | payloadLogged
    "application/x-www-form-urlencoded" | "POST" | "path" | "uri"          | "payload=content"          | true
    "application/x-www-form-urlencoded" | "GET"  | "path" | "uri"          | "payload=content"          | true
    "application/x-www-form-urlencoded" | "POST" | "path" | "path/oauth/a" | "password=****&name=[Bob]" | true
    "multipart/form-data"               | "POST" | "path" | "uri"          | "payload"                  | false
    "multipart/form-data\n\t"           | "POST" | "path" | "uri"          | "payload"                  | false
  }
}
