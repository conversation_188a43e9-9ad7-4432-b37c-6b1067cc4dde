package com.solum.xplain.core.market.service

import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ASK
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.time.LocalDate.now

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.classifiers.ClassifiersAggregator
import com.solum.xplain.core.classifiers.CoreClassifiersProvider
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.market.MarketDataKeyControllerService
import com.solum.xplain.core.market.csv.MarketDataKeyCsvMapper
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.repository.MarketDataKeyWriteRepository
import com.solum.xplain.core.market.value.MarketDataKeyView
import com.solum.xplain.core.market.value.MarketDataProviderTickerView
import com.solum.xplain.shared.utils.filter.TableFilter
import org.springframework.data.domain.Sort
import spock.lang.Specification
import spock.lang.Unroll

class MarketDataKeyControllerServiceTest extends Specification {

  def repository = Mock(MarketDataKeyRepository)
  def writeRepository = Mock(MarketDataKeyWriteRepository)
  def userRepository = Mock(AuthenticationContext)
  def classifiersAggregator = new ClassifiersAggregator([new CoreClassifiersProvider()])
  def mapper = new MarketDataKeyCsvMapper(classifiersAggregator)
  def service = new MarketDataKeyControllerService(repository, writeRepository, mapper, userRepository)

  @Unroll
  def "should get market data key provider ticker CSV with columns #selectedColumns and response #csvText"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()

    repository.marketDataKeyViews(_ as Sort, _ as TableFilter, stateDate) >> [
      new MarketDataKeyView(
      key: "key",
      name: "name",
      providerTickers: [
        new MarketDataProviderTickerView(code: "BBG", ticker: "tick1", bidAskType: BID_ASK, factor: 10),
        new MarketDataProviderTickerView(code: "MRK", ticker: "tick2", bidAskType: BID_ONLY, factor: 100)
      ]

      )
    ]

    when:
    def result = service.getMarketDataKeyTickerCsvBytes(emptyTableFilter(), Sort.unsorted(), stateDate, selectedColumns)

    then:
    new String(result.bytes.byteArray) == csvText

    where:
    csvText                                                                                    | selectedColumns
    "Key\nkey\nkey\n"                                                                          | ["key"]
    "Key,Provider,Ticker,BidOrAsk,Factor\nkey,BBG,tick1,Bid/Ask,10\nkey,MRK,tick2,Bid Only,100\n" | null
  }

  def "should get market data key CSV with columns #selectedColumns and response #csvText"() {
    setup:
    def csvText = "Key,Name,Asset Class,Instrument Type\nkey,name,FX,FX\n"
    repository.marketDataKeyViews(_ as Sort, _ as TableFilter, _) >> [
      new MarketDataKeyView(
      key: "key",
      name: "name",
      instrumentType: CoreInstrumentType.FX_RATE,
      assetGroup: CoreAssetGroup.FX,
      providerTickers: [
        new MarketDataProviderTickerView(code: "BBG", ticker: "tick1", factor: 1),
        new MarketDataProviderTickerView(code: "MRK", ticker: "tick2", factor: 10)
      ]

      )
    ]

    when:
    def result = service.getMarketDataKeyCsvBytes(emptyTableFilter(), Sort.unsorted(), now())

    then:
    new String(result.bytes.byteArray) == csvText
  }
}
