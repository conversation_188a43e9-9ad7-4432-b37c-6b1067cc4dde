package com.solum.xplain.core.portfolio.trade

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.team.UserTeamEntity
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.users.UserBuilder
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.springframework.data.domain.Sort
import spock.lang.Specification

class TradeValuationHistoryControllerServiceTest extends Specification {

  AuthenticationContext authenticationContext = Mock()
  PortfolioRepository portfolioRepository = Mock()
  TradeValuationHistoryProvider calculationHistoryProvider = Mock()
  TradeValuationHistoryProvider ipvXmValuationHistoryProvider = Mock()
  List<TradeValuationHistoryProvider> historyProviders = List.of(calculationHistoryProvider, ipvXmValuationHistoryProvider)

  TradeValuationHistoryControllerService service = new TradeValuationHistoryControllerService(historyProviders, portfolioRepository, authenticationContext)

  def "should get aggregated trade history from both providers"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(calculationHistoryViewItem(), calculationHistoryViewItem("2021-12-31", "calcResult2"))
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(ipvXmValuationHistoryViewItem(), ipvXmValuationHistoryViewItem("2022-01-02", "xmResult2"))

    when:
    var result = service.getTradeValuationHistory("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().size() == 4

    with(result.getOrNull()[0]) {
      valuationDate == LocalDate.parse("2022-01-02")
      calculationResultId == null
      exceptionManagementResultId == "xmResult2"
      pricingSource == PricingSource.EXCEPTION_MANAGEMENT.getLabel()
    }

    with(result.getOrNull()[1]) {
      valuationDate == LocalDate.parse("2022-01-01")
      calculationResultId == "calcResult1"
      exceptionManagementResultId == null
      pricingSource == PricingSource.XPLAIN_VALUATIONS.getLabel()
    }

    with(result.getOrNull()[2]) {
      valuationDate == LocalDate.parse("2022-01-01")
      calculationResultId == null
      exceptionManagementResultId == "xmResult1"
      pricingSource == PricingSource.EXCEPTION_MANAGEMENT.getLabel()
    }

    with(result.getOrNull()[3]) {
      valuationDate == LocalDate.parse("2021-12-31")
      calculationResultId == "calcResult2"
      exceptionManagementResultId == null
      pricingSource == PricingSource.XPLAIN_VALUATIONS.getLabel()
    }
  }

  def "should get aggregated trade history CSV from both providers"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(calculationHistoryViewItem(), calculationHistoryViewItem("2021-12-31", "calcResult2"))
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(ipvXmValuationHistoryViewItem(), ipvXmValuationHistoryViewItem("2022-01-02", "xmResult2"))

    when:
    var result = service.getTradeValuationHistoryCsv("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().name.contains("tradeId")
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == "Trade ID,Market Data Group,Valuation Date,PV (Reporting Ccy),Pricing Source\ntradeId,mdg01,2022-01-02,100,Excpt Mngt\ntradeId,mdg01,2022-01-01,100,Valuations\ntradeId,mdg01,2022-01-01,100,Excpt Mngt\ntradeId,mdg01,2021-12-31,100,Valuations\n"
  }

  def "should get aggregated trade history when only calculation history provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(calculationHistoryViewItem(), calculationHistoryViewItem("2021-12-31", "calcResult2"))
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()

    when:
    var result = service.getTradeValuationHistory("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().size() == 2

    with(result.getOrNull()[0]) {
      valuationDate == LocalDate.parse("2022-01-01")
      calculationResultId == "calcResult1"
      exceptionManagementResultId == null
      pricingSource == PricingSource.XPLAIN_VALUATIONS.getLabel()
    }

    with(result.getOrNull()[1]) {
      valuationDate == LocalDate.parse("2021-12-31")
      calculationResultId == "calcResult2"
      exceptionManagementResultId == null
      pricingSource == PricingSource.XPLAIN_VALUATIONS.getLabel()
    }
  }

  def "should get aggregated trade history CSV when only calculation history provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(calculationHistoryViewItem(), calculationHistoryViewItem("2021-12-31", "calcResult2"))
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()

    when:
    var result = service.getTradeValuationHistoryCsv("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().name.contains("tradeId")
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == "Trade ID,Market Data Group,Valuation Date,PV (Reporting Ccy),Pricing Source\ntradeId,mdg01,2022-01-01,100,Valuations\ntradeId,mdg01,2021-12-31,100,Valuations\n"
  }

  def "should get aggregated trade history when only xm valuation history provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(ipvXmValuationHistoryViewItem(), ipvXmValuationHistoryViewItem("2022-01-02", "xmResult2"))

    when:
    var result = service.getTradeValuationHistory("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().size() == 2

    with(result.getOrNull()[0]) {
      valuationDate == LocalDate.parse("2022-01-02")
      calculationResultId == null
      exceptionManagementResultId == "xmResult2"
      pricingSource == PricingSource.EXCEPTION_MANAGEMENT.getLabel()
    }

    with(result.getOrNull()[1]) {
      valuationDate == LocalDate.parse("2022-01-01")
      calculationResultId == null
      exceptionManagementResultId == "xmResult1"
      pricingSource == PricingSource.EXCEPTION_MANAGEMENT.getLabel()
    }
  }

  def "should get aggregated trade history CSV when only xm valuation history provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> List.of(ipvXmValuationHistoryViewItem(), ipvXmValuationHistoryViewItem("2022-01-02", "xmResult2"))

    when:
    var result = service.getTradeValuationHistoryCsv("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().name.contains("tradeId")
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == "Trade ID,Market Data Group,Valuation Date,PV (Reporting Ccy),Pricing Source\ntradeId,mdg01,2022-01-02,100,Excpt Mngt\ntradeId,mdg01,2022-01-01,100,Excpt Mngt\n"
  }

  def "should return empty list when neither provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()

    when:
    var result = service.getTradeValuationHistory("portfolioId", "tradeId")

    then:
    result.isRight()
    result.getOrNull().size() == 0
  }

  def "should return CSV with no rows when neither provider returns results"() {
    setup:
    def user = UserBuilder.user()
    def portfolioView = UserTeamEntity.userEntity(user, new PortfolioView())
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.right(portfolioView)
    1 * calculationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()
    1 * ipvXmValuationHistoryProvider.getTradeValuationHistory(portfolioView.getView(), "tradeId") >> Collections.emptyList()

    when:
    var result = service.getTradeValuationHistoryCsv("portfolioId", "tradeId")

    then:
    result.isRight()
    !result.getOrNull().name.contains("tradeId")
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == "Trade ID,Market Data Group,Valuation Date,PV (Reporting Ccy),Pricing Source\n"
  }

  def "should return error when portfolio not found"() {
    setup:
    var user = UserBuilder.user()
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    when:
    var result = service.getTradeValuationHistory("portfolioId", "tradeId")

    then:
    result.isLeft()
    def error = (ErrorItem) result.left().get()
    error == Error.OBJECT_NOT_FOUND.entity()

    and:
    0 * calculationHistoryProvider.getTradeValuationHistory(_, _)
    0 * ipvXmValuationHistoryProvider.getTradeValuationHistory(_, _)
  }

  def "should return error when portfolio not found when exporting trade history csv"() {
    setup:
    var user = UserBuilder.user()
    1 * authenticationContext.currentUser() >> user
    1 * portfolioRepository.getUserPortfolioView(user, "portfolioId") >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    when:
    var result = service.getTradeValuationHistoryCsv("portfolioId", "tradeId")

    then:
    result.isLeft()
    def error = (ErrorItem) result.left().get()
    error == Error.OBJECT_NOT_FOUND.entity()

    and:
    0 * calculationHistoryProvider.getTradeValuationHistory(_, _)
    0 * ipvXmValuationHistoryProvider.getTradeValuationHistory(_, _)
  }

  static calculationHistoryViewItem(String valuationDate = "2022-01-01", String calcResultId = "calcResult1") {
    var calcView = new TradeValuationHistoryView()
    calcView.externalTradeId = "tradeId"
    calcView.marketDataGroupName = "mdg01"
    calcView.valuationDate = LocalDate.parse(valuationDate)
    calcView.presentValueReportingCurrency = 100
    calcView.reportingCurrency = "USD"
    calcView.pricingSource = PricingSource.XPLAIN_VALUATIONS.getLabel()
    calcView.calculationResultId = calcResultId
    return calcView
  }

  static ipvXmValuationHistoryViewItem(String valuationDate = "2022-01-01", String xmResultId = "xmResult1") {
    var xmValuationView = new TradeValuationHistoryView()
    xmValuationView.externalTradeId = "tradeId"
    xmValuationView.marketDataGroupName = "mdg01"
    xmValuationView.valuationDate = LocalDate.parse(valuationDate)
    xmValuationView.presentValueReportingCurrency = 100
    xmValuationView.reportingCurrency = "USD"
    xmValuationView.pricingSource = PricingSource.EXCEPTION_MANAGEMENT.getLabel()
    xmValuationView.exceptionManagementResultId = xmResultId
    xmValuationView.dashboardId = "dashboard1"
    return xmValuationView
  }
}
