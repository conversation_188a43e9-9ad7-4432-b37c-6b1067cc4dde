package com.solum.xplain.core.curvegroup.ratefx

import static com.solum.xplain.core.curvemarket.CurveMarketSample.MARKET_STATE_FORM
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.value.VersionedList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import spock.lang.Specification

class CurveGroupFxRatesServiceTest extends Specification {
  def static GROUP_ID = "groupId"

  def static STATE_DATE = now()
  def static BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)
  def static VERSION_DATE = NewVersionFormV2.ROOT_DATE
  def static ENTITY_ID = new EntityId(GROUP_ID)

  CurveGroupRepository curveGroupRepository = Mock(CurveGroupRepository)
  CurveGroupFxRatesRepository repository = Mock(CurveGroupFxRatesRepository)
  MarketDataQuotesSupport marketDataQuotesSupport = Mock(MarketDataQuotesSupport)

  CurveGroupFxRatesService service = new CurveGroupFxRatesService(
  curveGroupRepository,
  repository,
  marketDataQuotesSupport
  )

  def setup() {
    def curveGroup = new CurveGroupView(id: GROUP_ID)
    curveGroupRepository.getGroup(GROUP_ID) >> right(curveGroup)
  }

  def "should create FX Rates"() {
    setup:
    def form = new CurveGroupFxRatesForm(
      nodes: [new CurveGroupFxRatesNodeForm(domesticCurrency: "EUR", foreignCurrency: "USD")]
      )

    1 * repository.createRates(GROUP_ID, form) >> right(ENTITY_ID)
    1 * curveGroupRepository.clearCalibrationResults(_) >> right(ENTITY_ID)

    when:
    def result = service.create(GROUP_ID, form)

    then:
    result.isRight()
    result.right().get() == ENTITY_ID
  }

  def "should update FX Rates"() {
    setup:
    def form = new CurveGroupFxRatesForm(
      nodes: [new CurveGroupFxRatesNodeForm(domesticCurrency: "EUR", foreignCurrency: "USD")]
      )

    1 * repository.updateRates(GROUP_ID, VERSION_DATE, form) >> right(ENTITY_ID)
    1 * curveGroupRepository.clearCalibrationResults(_) >> right(ENTITY_ID)

    when:
    def result = service.update(GROUP_ID, VERSION_DATE, form)

    then:
    result.isRight()
    result.right().get() == ENTITY_ID
  }

  def "should delete FX Rates"() {
    setup:
    1 * repository.deleteRates(GROUP_ID, VERSION_DATE) >> right(ENTITY_ID)
    1 * curveGroupRepository.clearCalibrationResults(_) >> right(ENTITY_ID)

    when:
    def result = service.delete(GROUP_ID, VERSION_DATE)

    then:
    result.isRight()
    result.right().get() == ENTITY_ID
  }

  def "should get FX Rates"() {
    setup:
    1 * repository.getActiveRatesView(GROUP_ID, BITEMPORAL_STATE_DATE) >> right(new CurveGroupFxRatesView())

    when:
    def result = service.get(GROUP_ID, BITEMPORAL_STATE_DATE)

    then:
    result.isRight()
  }

  def "should get FX Rates versions"() {
    setup:
    1 * repository.getRatesVersionViews(GROUP_ID) >> List.of(new CurveGroupFxRatesView())

    when:
    def result = service.getVersions(GROUP_ID)

    then:
    result.isRight()
  }

  def "should get FX Rates future versions"() {
    setup:
    def dateList = new DateList([])
    1 * repository.getFutureVersions(GROUP_ID, STATE_DATE) >> dateList

    when:
    def result = service.getFutureVersionsDates(GROUP_ID, STATE_DATE)

    then:
    result.isRight()
    def futureVersions = result.right().get() as DateList
    futureVersions == dateList
  }

  def "should get FX Rates values"() {
    setup:
    def quotes = ["EUR/USD": new CalculationMarketValueFullView()]
    marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> quotes
    1 * repository.getRatesNodesValuesViews(GROUP_ID, CurveMarketSample.MARKET_STATE_FORM.getStateDate(), quotes) >> VersionedList.empty()

    when:
    def result = service.getRatesValues(GROUP_ID, CurveMarketSample.MARKET_STATE_FORM)

    then:
    result.isRight()
  }
}
