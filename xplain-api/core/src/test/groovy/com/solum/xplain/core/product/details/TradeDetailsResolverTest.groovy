package com.solum.xplain.core.product.details


import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.product.ProductType
import spock.lang.Specification

class TradeDetailsResolverTest extends Specification {
  static RESOLVER1 = new MockResolver1()
  static RESOLVER2 = new MockResolver2()

  def RESOLVERS = new TradeDetailsResolver(List.of(RESOLVER1, RESOLVER2))

  def "should resolve underlying"() {
    expect:
    RESOLVERS.resolveUnderlying(type, new TradeDetails()) == expected

    where:
    type                      | expected
    CoreProductType.SWAPTION  | "RESOLVER1"
    CoreProductType.INFLATION | "RESOLVER1"
    CoreProductType.CDS       | "RESOLVER2"
  }

  def "should resolve notional"() {
    expect:
    RESOLVERS.resolveNotional(type, new TradeDetails()) == expected

    where:
    type                      | expected
    CoreProductType.SWAPTION  | 0.1d
    CoreProductType.INFLATION | 0.1d
    CoreProductType.CDS       | 0.2d
  }

  def "should return error when unknown type"() {
    when:
    RESOLVERS.resolveNotional(CoreProductType.IRS, new TradeDetails())

    then:
    IllegalArgumentException ex = thrown()
    ex.message == "No details resolver for IRS"
  }

  static class MockResolver1 implements ProductDetailsResolver {

    @Override
    List<ProductType> productTypes() {
      return List.of(CoreProductType.SWAPTION, CoreProductType.INFLATION)
    }

    @Override
    String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
      return "RESOLVER1"
    }

    @Override
    double resolveNotional(TradeDetails tradeDetails) {
      return 0.1d
    }
  }

  static class MockResolver2 implements ProductDetailsResolver {

    @Override
    List<ProductType> productTypes() {
      return List.of(CoreProductType.CDS)
    }

    @Override
    String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
      return "RESOLVER2"
    }

    @Override
    double resolveNotional(TradeDetails tradeDetails) {
      return 0.2d
    }
  }
}
