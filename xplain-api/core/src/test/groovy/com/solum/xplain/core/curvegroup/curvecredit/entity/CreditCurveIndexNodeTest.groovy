package com.solum.xplain.core.curvegroup.curvecredit.entity

import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import spock.lang.Specification
import spock.lang.Unroll

class CreditCurveIndexNodeTest extends Specification {
  @Unroll
  def "should return correct key and name for cds node with #convention convention"() {
    setup:
    def node = new CreditCurveIndexNode(tenor: "1Y", type: CreditCurveNodeType.CREDIT_INDEX)
    var creditCurve = CreditCurveBuilder.indexCurve()
    creditCurve.quoteConvention = convention
    when:
    def result = node.instrument(creditCurve)

    then:
    result.key == key
    result.mdkName == mdkName
    result.instrument == CoreInstrumentType.CREDIT_INDEX

    where:
    convention       | key                         | mdkName
    "POINTS_UPFRONT" | "1Y_CDX_NA_HY_S2_V1_UF"     | "CDX NA HY S2 V1 UF 1Y"
    "QUOTED_SPREAD"  | "1Y_CDX_NA_HY_S2_V1_SPREAD" | "CDX NA HY S2 V1 SPREAD 1Y"
  }
}
