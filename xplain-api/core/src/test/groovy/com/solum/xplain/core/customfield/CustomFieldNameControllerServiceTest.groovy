package com.solum.xplain.core.customfield

import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm
import io.atlassian.fugue.Either
import org.springframework.data.domain.Sort
import spock.lang.Specification

class CustomFieldNameControllerServiceTest extends Specification {
  CustomFieldNameRepository repository = Mock()

  CustomFieldNameControllerService service = new CustomFieldNameControllerService(repository)

  def "should insert custom field name"() {
    setup:
    1 * repository.insert(new CustomFieldNameCreateForm()) >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.insert(new CustomFieldNameCreateForm())
    then:
    result.isRight()
  }

  def "should update custom field name"() {
    setup:
    1 * repository.update(_, new CustomFieldNameUpdateForm()) >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.update("id", new CustomFieldNameUpdateForm())
    then:
    result.isRight()
  }

  def "should archive custom field name"() {
    setup:
    1 * repository.archive("id") >> Either.right(EntityId.entityId("id"))
    when:
    def result = service.archive("id")
    then:
    result.isRight()
  }


  def "should list all custom field names"() {
    setup:
    1 * repository.list(emptyTableFilter(), Sort.unsorted(), false) >> []
    when:
    def result = service.list(emptyTableFilter(), Sort.unsorted(), false)
    then:
    result == []
  }
}
