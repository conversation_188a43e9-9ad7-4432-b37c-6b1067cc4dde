package com.solum.xplain.core.curvegroup.curve.csv

import static com.solum.xplain.core.curvegroup.curve.entity.CalibrationValueType.FORWARD_RATE
import static com.solum.xplain.core.curvegroup.curve.entity.CalibrationValueType.ZERO_COUPON_RATE

import com.solum.xplain.core.common.value.ChartPoint
import java.time.LocalDate
import spock.lang.Specification

class ChartPointsCsvBuilderTest extends Specification {
  private static final CHART_POINT = new ChartPoint(LocalDate.parse("2022-02-02"), 1d, 0.5d)

  def "should correctly map FORWARD_RATE to CSV"() {
    setup:
    def output = CurvePointsCsvBuilder.ofSingleValueType(FORWARD_RATE.label).withPoints([CHART_POINT], "EUR 3M").toCsv().writeToByteArray()

    expect:
    new String(output.getByteArray()) == """\
      Curve Name,Date,FWD Rate,Value Type
      EUR 3M,2022-02-02,1,FWD Rate
      """.stripIndent()
  }

  def "should correctly map DISCOUNT_FACTOR to CSV"() {
    setup:
    def output = CurvePointsCsvBuilder.ofDiscountFactors().withPoints([CHART_POINT], "EURUSD").toCsv().writeToByteArray()
    expect:
    new String(output.getByteArray()) == """\
      Curve Name,Date,Discount Factor,Value Type
      EURUSD,2022-02-02,1,Discount Factor
      """.stripIndent()
  }

  def "should correctly map DISCOUNT_FACTOR with no values to CSV"() {
    setup:
    def output = CurvePointsCsvBuilder.ofDiscountFactors().withOnlyDatePoints([CHART_POINT], "EURUSD").toCsv().writeToByteArray()
    expect:
    new String(output.getByteArray()) == """\
      Curve Name,Date,Discount Factor,Value Type
      EURUSD,2022-02-02,,Discount Factor
      """.stripIndent()
  }

  def "should correctly map multiple curve chart points to CSV"() {
    setup:
    def output = CurvePointsCsvBuilder.ofVariableValueType()
      .withPoints([CHART_POINT], FORWARD_RATE.label, "EUR 3M")
      .withPoints([CHART_POINT], ZERO_COUPON_RATE.label, "EUR 6M")
      .toCsv()
      .writeToByteArray()
    expect:
    new String(output.getByteArray()) == """\
      Curve Name,Date,Value,Value Type
      EUR 3M,2022-02-02,1,FWD Rate
      EUR 6M,2022-02-02,1,ZC Rate
      """.stripIndent()
  }
}
