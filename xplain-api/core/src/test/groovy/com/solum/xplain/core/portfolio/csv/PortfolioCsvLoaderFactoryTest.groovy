package com.solum.xplain.core.portfolio.csv

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.company.CompanyTeamValidationService
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.teams.TeamRepository
import spock.lang.Specification

class PortfolioCsvLoaderFactoryTest extends Specification {
  CompanyRepository companyRepository = Mock()
  CompanyLegalEntityRepository companyLegalEntityRepository = Mock()
  TeamRepository teamRepository = Mock()
  CompanyTeamValidationService companyTeamValidationService = Mock()

  PortfolioCsvLoaderFactory factory = new PortfolioCsvLoaderFactory(
  companyRepository,
  companyLegalEntityRepository,
  teamRepository,
  companyTeamValidationService)

  def "should get portfolio csv loader"() {
    setup:

    1 * companyRepository.companyList(EntityTeamFilter.emptyFilter()) >> []
    1 * companyLegalEntityRepository.allLegalEntities() >> []
    1 * teamRepository.teamNamesList() >> []
    expect:
    factory.getLoader(user()) != null
  }
}
