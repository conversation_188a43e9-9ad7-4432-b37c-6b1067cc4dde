package com.solum.xplain.core.lock

import com.solum.xplain.core.events.RateLimitedEventsService
import com.solum.xplain.core.sockets.constants.CoreSocketTypes
import com.solum.xplain.core.sockets.events.BroadcastSocketEvent
import com.solum.xplain.core.sockets.events.EventType
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.KeyValueCache
import jakarta.servlet.http.HttpServletRequest
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.util.function.BiFunction
import org.springframework.data.domain.AuditorAware
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import spock.lang.Specification

class CachedLockStateRepositoryTest extends Specification {
  public static final Clock CLOCK = Clock.fixed(Instant.parse('2014-03-09T12:00:00Z'), ZoneOffset.UTC)
  public static final AuditUser CURRENT_USER = new AuditUser("userId", "userName", "name")

  KeyValueCache<String, CachedLockState> cacheMap = Mock(KeyValueCache)

  def dataGrid = Mock(DataGrid) {
    getKeyValueCache(_ as String) >> cacheMap
  }
  def auditorAware = Mock(AuditorAware) {
    getCurrentAuditor() >> Optional.of(CURRENT_USER)
  }
  def rateLimitedEventsService = Mock(RateLimitedEventsService)
  def repository = new CachedLockStateRepository(dataGrid, auditorAware, rateLimitedEventsService).tap {
    it.configureClock(CLOCK)
  }

  void cleanup() {
    RequestContextHolder.setRequestAttributes(null)
  }

  def "should create lock entry and send socket event"() {
    given:
    def lock = XplainLock.newOf("lockId")
    def stateUpdate = new CachedLockState(lock, Thread.currentThread().getId(), CURRENT_USER,
      CLOCK.millis(), "/my/request/path", [])
    def existingState = new CachedLockState(lock, null, null,
      null, null, [
        new CachedLockHistory(CURRENT_USER, CLOCK.millis() - 100, CLOCK.millis() - 50, "/previous/request")
      ])
    def expectedState = new CachedLockState(lock, Thread.currentThread().getId(), CURRENT_USER,
      CLOCK.millis(), "/my/request/path", existingState.history())
    repository.request = Mock(HttpServletRequest) {
      getContextPath() >> "/xplain"
      getRequestURI() >> "/xplain/my/request/path"
    }
    RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(repository.request))

    when:
    repository.lockObtained(lock)

    then: "merge is called and supplied remappingFunction preserves history"
    1 * cacheMap.merge("lockId", stateUpdate, _ as BiFunction<CachedLockState, CachedLockState, CachedLockState>, _ as Duration) >> { _1, _2, fn, _3 ->
      assert fn.apply(existingState, stateUpdate) == expectedState
      return expectedState
    }

    and: "a socket event is published"
    1 * rateLimitedEventsService.publishNotificationEvent("locks_updated", _ as BroadcastSocketEvent) >> { key, event ->
      assert key == "locks_updated"
      assert event.audienceType == CoreSocketTypes.LOCKS
      assert event.eventType == EventType.LOCKS_UPDATED
      assert event.data == null
    }
  }

  def "should swallow data grid errors on lock"() {
    when:
    repository.lockObtained(XplainLock.newOf("lockId"))

    then:
    1 * cacheMap.merge("lockId", _, _, _) >> { throw new RuntimeException("Data grid error") }
    0 * rateLimitedEventsService.publishNotificationEvent(_, _) // No event should be published when there's an error
  }

  def "should create unlock entry and send socket event"() {
    given:
    def lock = XplainLock.newOf("lockId")
    def stateUpdate = new CachedLockState(lock, Thread.currentThread().getId(), null,
      CLOCK.millis(), null, [])
    def existingState = new CachedLockState(lock, Thread.currentThread().getId(), CURRENT_USER,
      CLOCK.millis() - 10, "/my/request/path", [
        new CachedLockHistory(CURRENT_USER, CLOCK.millis() - 100, CLOCK.millis() - 50, "/previous/request")
      ])
    def expectedState = new CachedLockState(lock, null, null,
      null, null, [
        existingState.history().get(0),
        new CachedLockHistory(CURRENT_USER, CLOCK.millis() - 10, CLOCK.millis(), "/my/request/path")
      ]
      )

    when:
    repository.lockReleased(lock)

    then: "merge is called and supplied remappingFunction adds to history"
    1 * cacheMap.merge("lockId", stateUpdate, _ as BiFunction<CachedLockState, CachedLockState, CachedLockState>, _ as Duration) >> { _1, _2, fn, _3 ->
      assert fn.apply(existingState, stateUpdate) == expectedState
      return expectedState
    }

    and: "a socket event is published"
    1 * rateLimitedEventsService.publishNotificationEvent("locks_updated", _ as BroadcastSocketEvent) >> { key, event ->
      assert key == "locks_updated"
      assert event.audienceType == CoreSocketTypes.LOCKS
      assert event.eventType == EventType.LOCKS_UPDATED
      assert event.data == null
    }
  }

  def "should swallow data grid errors on unlock"() {
    when:
    repository.lockReleased(XplainLock.newOf("lockId"))

    then:
    1 * cacheMap.merge("lockId", _, _, _) >> { throw new RuntimeException("Hazelcast error") }
    0 * rateLimitedEventsService.publishNotificationEvent(_, _) // No event should be published when there's an error
  }

  def "should list lock entries"() {
    given:
    def all = [
      new CachedLockState(XplainLock.newOf("lockId"), Thread.currentThread().getId(), CURRENT_USER,
      CLOCK.millis(), "/my/request/path", [])
    ]
    cacheMap.values() >> all

    when:
    def result = repository.list()

    then:
    result == all
  }
}
