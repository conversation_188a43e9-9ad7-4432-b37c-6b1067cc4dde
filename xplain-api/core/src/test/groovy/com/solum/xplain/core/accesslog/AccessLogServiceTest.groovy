package com.solum.xplain.core.accesslog

import com.solum.xplain.core.accesslog.entity.AccessLogEntry
import com.solum.xplain.core.authentication.value.XplainPrincipal
import jakarta.servlet.http.HttpServletRequest
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import spock.lang.Specification

class AccessLogServiceTest extends Specification {
  def static IP_ADDRESS = "127.0.0.1"
  def static CLIENT = "client"

  def repository = Mock(AccessLogRepository)
  def service = new AccessLogService(repository)

  def setup() {
    def request = Mock(HttpServletRequest)
    1 * request.getRemoteAddr() >> IP_ADDRESS
    RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request))
  }

  def cleanup() {
    RequestContextHolder.resetRequestAttributes()
  }


  def "should return access log scroll"() {
    setup:
    def principal = Mock(XplainPrincipal)
    principal.getTeams() >> []
    principal.getAuthorities() >> []
    def logEntry = AccessLogEntry.newOf(principal, IP_ADDRESS, CLIENT, [], [], true)

    when:
    service.logPrincipalResolution(principal, CLIENT, [], [], true)

    then:
    1 * repository.saveLogEntry(logEntry)
  }
}
