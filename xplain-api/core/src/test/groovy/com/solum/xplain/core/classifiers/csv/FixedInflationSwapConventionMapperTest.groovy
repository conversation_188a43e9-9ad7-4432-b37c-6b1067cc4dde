package com.solum.xplain.core.classifiers.csv

import spock.lang.Specification

class FixedInflationSwapConventionMapperTest extends Specification implements ConventionMapperSample {
  def "should export FixedInflationSwap conventions"() {
    setup:
    def bytes = FixedInflationSwapConventionMapper.fixedInflationSwapConventions()

    expect:
    assertMatchesResource(bytes, "FixedInflationSwapConventions.csv")
  }
}
