package com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.IMPORT_ERROR
import static com.solum.xplain.core.error.Error.IMPORT_INFO
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.error.Error.PARSING_ERROR

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityRepository
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewForm
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.InfoItem
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class FxVolatilityNodeCsvImportServiceTest extends Specification {
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static GROUP_ID = "volatilityGroupId"
  def static FILE_CONTENT = [] as byte[]

  def auditEntryService = Mock(AuditEntryService)
  def csvLoader = Mock(FxVolatilityNodeCsvLoader)
  def mapper = Mock(CurveGroupFxVolatilityMapper)
  def repository = Mock(CurveGroupFxVolatilityRepository)

  def nodeImport = Spy(new FxVolatilityNodeCsvImportService(
  auditEntryService,
  csvLoader,
  mapper,
  repository)
  )

  def "should return errors when importing volatilities and csv parsing error"() {
    setup:
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      leftCsvResult([PARSING_ERROR.entity()])

    0 * repository.getActiveVolatility(GROUP_ID, STATE_DATE)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importVolatilities(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
  }

  def "should create volatility if no version"() {
    setup:
    1 * repository.getActiveVolatility(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.left(OBJECT_NOT_FOUND.entity())
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> rightCsvResult([nodeForm("1M")])
    1 * nodeImport.importNodes({ CurveGroupFxVolatility.newOf(GROUP_ID).valueEquals(it) }, _, _) >> [InfoItem.of(IMPORT_INFO, entityId(GROUP_ID), "")]

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importVolatilities(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
  }

  def "should not import volatilities and return success result"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    1 * repository.getActiveVolatility(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(volatility)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(volatility, _, csvResult.getOrNull().parsedLines) >> []
    1 * auditEntryService.newEntryWithLogs(_, [])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importVolatilities(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def id = result.right().get() as EntityId
    id == entityId(volatility.entityId)
  }

  def "should import volatilities, log and return success result"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(volatility.entityId), "info description")

    1 * repository.getActiveVolatility(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(volatility)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(volatility, _, csvResult.getOrNull().parsedLines) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, [info])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importVolatilities(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def id = result.right().get() as EntityId
    id == entityId(volatility.entityId)
  }

  def "should fail import volatilities, log and return errors"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    ErrorItem error = new ErrorItem(IMPORT_ERROR, "error description")

    1 * repository.getActiveVolatility(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(volatility)
    0 * repository.getFutureVersions(_)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(_, _, _) >> [error]
    1 * auditEntryService.newEntryWithLogs(_, [error])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importVolatilities(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"
  }

  def "should fail with unexpected action REPLACE_DELETE"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should fail with unexpected action REPLACE"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should return MISSING_ENTRY with missing nodes when ERROR"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == MISSING_ENTRY
    result[0].description == "Node 1M - EUR/USD in volatility FX Volatility is missing"
  }

  def "should not update volatility with only duplicate nodes when ERROR"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    returnNoFutureVersions()
    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 0
  }

  @Unroll
  def "should return NEW_VERSION_VIABLE when ERROR and volatility exists #exists"() {
    setup:
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    returnNoFutureVersions()
    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == NEW_VERSION_VIABLE
    result[0].description == "New version is viable for FX Volatility"

    where:
    exists | volatility
    true   | volatility([], PAST_VERSION_DATE)
    false  | CurveGroupFxVolatility.newOf(GROUP_ID)
  }


  def "should return FUTURE_VERSION_EXISTS when ERROR"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M")])

    1 * repository.getFutureVersions(_, _) >> new DateList([FUTURE_VERSION_DATE])
    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == FUTURE_VERSION_EXISTS
    result[0].description == "FX Volatility has future version(s)"
  }

  def "should return empty result when no errors ERROR"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M"), nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(volatilityForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.isEmpty()
  }

  def "should correctly update volatility when APPEND"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M"), node("1W")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M"), nodeForm("3M")])

    doUpdate(volatility, volatilityForm, [nodeForm("1M"), nodeForm("1W"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(volatility.entityId)
  }

  def "should correctly update volatility when APPEND_DELETE"() {
    setup:
    CurveGroupFxVolatility volatility = volatility([node("1M"), node("1W")])
    CurveGroupFxVolatilityForm volatilityForm = volatilityForm(volatility)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> csvResult =
      rightCsvResult([nodeForm("1M"), nodeForm("3M")])

    doUpdate(volatility, volatilityForm, [nodeForm("1M"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(volatility, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(volatility.entityId)
  }

  def volatility(List<CurveGroupFxVolatilityNode> nodes, validFrom = STATE_DATE) {
    def volatility = Mock(CurveGroupFxVolatility)
    volatility.entityId >> GROUP_ID
    volatility.validFrom >> validFrom
    volatility.nodes >> nodes
    return volatility
  }

  def node(String expiry) {
    def node = new CurveGroupFxVolatilityNode(
      expiry: expiry,
      domesticCurrency: "EUR",
      foreignCurrency: "USD",
      delta1: 1,
      delta2: 2)
    _ * mapper.toNodeForm(node) >> nodeForm(expiry)
    _ * mapper.toSkewForm(node) >> skewForm(node.delta1, node.delta2)
    return node
  }

  def nodeForm(String expiry) {
    new CurveGroupFxVolatilityNodeForm(
      expiry: expiry,
      domesticCurrency: "EUR",
      foreignCurrency: "USD")
  }

  def skewForm(Integer delta1, Integer delta2) {
    new FxVolatilitySkewForm(
      domesticCurrency: "EUR",
      foreignCurrency: "USD",
      delta1: delta1,
      delta2: delta2)
  }

  def volatilityForm(CurveGroupFxVolatility volatility) {
    def volatilityForm = Mock(CurveGroupFxVolatilityForm)
    _ * mapper.toForm(volatility) >> volatilityForm
    return volatilityForm
  }

  def leftCsvResult(List<ErrorItem> errors) {
    return Either.<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> left(errors)
  }

  def rightCsvResult(List<CurveGroupFxVolatilityNodeForm> nodeForms) {
    return Either.<List<ErrorItem>, CsvParserResult<CurveGroupFxVolatilityNodeForm>> right(new CsvParserResult<>(nodeForms, []))
  }

  def doNotUpdate(CurveGroupFxVolatilityForm volatilityForm) {
    0 * volatilityForm.setNodes(_)
    0 * volatilityForm.setSkews(_)
    0 * volatilityForm.setVersionForm(_)
    0 * repository.createVolatility(_, _, _)
  }

  def returnNoFutureVersions() {
    1 * repository.getFutureVersions(_, _) >> new DateList([])
  }

  def doUpdate(CurveGroupFxVolatility volatility, CurveGroupFxVolatilityForm volatilityForm, List<CurveGroupFxVolatilityNodeForm> nodeForms) {
    1 * volatilityForm.setNodes({
      it.size() == nodeForms.size() &&
        nodeForms.withIndex().collect { elem, idx -> elem == nodeForms[idx as Integer] }
    })
    1 * volatilityForm.setSkews({
      it.size() == volatility.getNodes().stream().map { it.getFxPair() }.distinct().count()
    })
    1 * volatilityForm.setVersionForm({ it.validFrom == volatility.validFrom })
    1 * repository.createVolatility(volatility.entityId, volatilityForm) >>
      Either.right(entityId(volatility.entityId))
  }
}
