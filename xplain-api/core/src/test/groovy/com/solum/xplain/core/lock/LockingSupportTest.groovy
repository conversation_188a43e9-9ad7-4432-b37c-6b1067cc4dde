package com.solum.xplain.core.lock

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED

import com.solum.xplain.shared.datagrid.ClusterLock
import com.solum.xplain.shared.datagrid.DataGrid
import java.util.concurrent.TimeUnit
import spock.lang.Specification

class LockingSupportTest extends Specification {
  DataGrid dataGrid = Mock(DataGrid)
  CachedLockStateRepository repository = Mock(CachedLockStateRepository)
  LockingSupport support

  def setup() {
    support = new LockingSupport(dataGrid, repository)
  }

  def "should lock and return locks when all successful"() {
    setup:
    def lock1 = Mock(ClusterLock)
    def lock2 = Mock(ClusterLock)

    1 * dataGrid.getClusterLock("1") >> lock1
    1 * dataGrid.getClusterLock("2") >> lock2

    1 * lock1.tryLock(_, TimeUnit.MILLISECONDS) >> true
    1 * lock2.tryLock(_, TimeUnit.MILLISECONDS) >> true

    0 * lock1.unlock()
    0 * lock2.unlock()

    when:
    def result = support.tryLocks([new XplainLock("1"), new XplainLock("2")])

    then:
    result.isRight()
    result.getOrNull() == [lock1, lock2]
    1 * repository.lockObtained(new XplainLock("1"))
    1 * repository.lockObtained(new XplainLock("2"))
  }

  def "should return error when any lock cannot be acquired and release only locks that were acquired"() {
    setup:
    def lock1 = Mock(ClusterLock) {
      getName() >> "1"
    }
    def lock2 = Mock(ClusterLock) {
      getName() >> "2"
    }

    1 * dataGrid.getClusterLock("1") >> lock1
    1 * dataGrid.getClusterLock("2") >> lock2

    1 * lock1.tryLock(_, TimeUnit.MILLISECONDS) >> true
    1 * lock2.tryLock(_, TimeUnit.MILLISECONDS) >> false

    1 * lock1.unlock()
    0 * lock2.unlock()

    when:
    def result = support.tryLocks([new XplainLock("1"), new XplainLock("2")])

    then:
    result.isLeft()
    result.left().get() == OPERATION_NOT_ALLOWED.entity("One of required entities are locked")
    1 * repository.lockObtained(new XplainLock("1"))
    1 * repository.lockReleased(new XplainLock("1"))
    0 * repository.lockObtained(new XplainLock("2"))
  }

  def "should return a human-readable error when the lock is locked"() {
    setup:
    def lock1 = Mock(ClusterLock)

    1 * dataGrid.getClusterLock("1") >> lock1

    1 * lock1.tryLock(_, TimeUnit.MILLISECONDS) >> false

    1 * lock1.getName() >> "1"

    0 * lock1.unlock()

    when:
    def result = support.tryLock(new XplainLock("1"))

    then:
    result.isLeft()
    result.left().get() == OPERATION_NOT_ALLOWED.entity("Data import or update is in progress. Try again later.")
  }

  def "should release lock"() {
    setup:
    def lock1 = Mock(ClusterLock)
    lock1.getName() >> "1"

    when:
    support.releaseLock(lock1)

    then:
    1 * lock1.unlock()
    1 * repository.lockReleased(new XplainLock("1"))
  }
}
