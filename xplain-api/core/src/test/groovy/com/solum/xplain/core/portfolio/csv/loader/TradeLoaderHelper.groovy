package com.solum.xplain.core.portfolio.csv.loader

import static com.google.common.io.ByteSource.wrap

import com.google.common.io.ByteStreams
import com.opengamma.strata.collect.io.CsvIterator
import com.opengamma.strata.collect.io.CsvRow
import com.opengamma.strata.collect.io.UnicodeBom
import java.util.stream.Collectors

trait TradeLoaderHelper {

  List<CsvRow> loadResource(String fileName) {
    def content = ByteStreams.toByteArray(getClass().getResourceAsStream("/portfolio/csv/" + fileName))
    return CsvIterator.of(UnicodeBom.toCharSource(wrap(content)), true)
      .asStream()
      .toList()
  }
}
