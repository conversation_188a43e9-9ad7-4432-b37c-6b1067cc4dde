package com.solum.xplain.core.curvegroup.curvecredit.csv.curve

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.LENIENT
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCdsCurveName
import static com.solum.xplain.core.error.Error.DUPLICATE_ENTRY
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.MISSING_ENTRY_FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveRepository
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCsvForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveSearch
import com.solum.xplain.core.error.ErrorItem
import java.time.LocalDate
import spock.lang.Specification

class CreditCurveCsvImportServiceTest extends Specification {
  def static CURVE_GROUP_ID = "groupId"
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static FILE_CONTENT = [] as byte[]

  def auditEntryService = Mock(AuditEntryService)
  def repository = Mock(CurveGroupCreditCurveRepository)
  def csvLoader = Mock(CreditCurveCsvLoader)

  def importService = new CreditCurveCsvImportService(auditEntryService, repository, csvLoader)

  def "should return parse errors"() {
    setup:
    def error = new ErrorItem(PARSING_ERROR, "Parsing error")
    1 * csvLoader.parse(_, STRICT) >> left([error])
    0 * repository.getActiveCurves(_, _)
    0 * repository.getFutureVersions(_, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    errors[0].description == "Parsing error"
  }

  def "should return NEW_VERSION_VIABLE for NEW curve when ERROR"() {
    setup:
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newCurveForm]))

    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> []

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(newCurveForm)) >> new DateList([])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == NEW_VERSION_VIABLE
    errors[0].description == "New version is viable for newCurveTicker_currency_seniority_docClause"
  }

  def "should return FUTURE_VERSION_EXISTS (and NEW_VERSION_VIABLE) for NEW curve when ERROR"() {
    setup:
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newCurveForm]))

    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> []

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(newCurveForm)) >> new DateList([FUTURE_VERSION_DATE])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == FUTURE_VERSION_EXISTS
    errors[0].description == "newCurveTicker_currency_seniority_docClause has future version(s)"
    errors[1].reason == NEW_VERSION_VIABLE
    errors[1].description == "New version is viable for newCurveTicker_currency_seniority_docClause"
  }


  def "should return DUPLICATE_ENTRY for DUPLICATE curve when ERROR"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm]))

    def curve = duplicateCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [curve]

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(duplicateCurveForm)) >> new DateList([])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "duplicateCurveTicker_currency_seniority_docClause already exists"
  }

  def "should return DUPLICATE_ENTRY and NEW_VERSION_VIABLE for DUPLICATE curve when ERROR"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm]))

    def duplicateCurve = duplicateCurve(PAST_VERSION_DATE)
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve]

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(duplicateCurveForm)) >> new DateList([])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "duplicateCurveTicker_currency_seniority_docClause already exists"
    errors[1].reason == NEW_VERSION_VIABLE
    errors[1].description == "New version is viable for duplicateCurveTicker_currency_seniority_docClause"
  }

  def "should return DUPLICATE_ENTRY and FUTURE_VERSION_EXISTS for DUPLICATE curve when ERROR"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm]))

    def duplicateCurve = duplicateCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve]

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(duplicateCurveForm)) >>
      new DateList([FUTURE_VERSION_DATE])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "duplicateCurveTicker_currency_seniority_docClause already exists"
    errors[1].reason == FUTURE_VERSION_EXISTS
    errors[1].description == "duplicateCurveTicker_currency_seniority_docClause has future version(s)"
  }

  def "should return MISSING_ENTRY (and NEW_VERSION_VIABLE) for MISSING (and NEW) curves when ERROR"() {
    setup:
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newCurveForm]))

    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [missingCurve]

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(newCurveForm)) >> new DateList([])
    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(missingCurve)) >> new DateList([])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "missingCurveTicker_currency_seniority_docClause is missing"
    errors[1].reason == NEW_VERSION_VIABLE
    errors[1].description == "New version is viable for newCurveTicker_currency_seniority_docClause"
  }

  def "should return MISSING_ENTRY and MISSING_ENTRY_FUTURE_VERSION_EXISTS (and NEW_VERSION_VIABLE) for MISSING (and NEW) curves when ERROR"() {
    setup:
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newCurveForm]))

    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [missingCurve]

    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(newCurveForm)) >> new DateList([])
    1 * repository.getFutureVersions(CURVE_GROUP_ID, search(missingCurve)) >> new DateList([FUTURE_VERSION_DATE])

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 3
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "missingCurveTicker_currency_seniority_docClause is missing"
    errors[1].reason == MISSING_ENTRY_FUTURE_VERSION_EXISTS
    errors[1].description == "missingCurveTicker_currency_seniority_docClause has future version(s)"
    errors[2].reason == NEW_VERSION_VIABLE
    errors[2].description == "New version is viable for newCurveTicker_currency_seniority_docClause"
  }


  def "should create NEW curve when APPEND"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm, newCurveForm]))

    def duplicateCurve = duplicateCurve()
    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve, missingCurve]
    0 * repository.getFutureVersions(CURVE_GROUP_ID, _)

    1 * repository.createCurve(CURVE_GROUP_ID, new CdsCurveForm()) >> right(entityId("new entity id"))
    0 * repository.updateCdsCurve(_, _ as CdsCurveUpdateForm, _)
    0 * repository.archiveCurve(_, _, _)

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId("new entity id")
  }


  def "should create NEW curve and archive MISSING when APPEND_DELETE"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm, newCurveForm]))

    def duplicateCurve = duplicateCurve()
    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve, missingCurve]
    0 * repository.getFutureVersions(CURVE_GROUP_ID, _)

    1 * repository.createCurve(CURVE_GROUP_ID, new CdsCurveForm()) >> right(entityId("new entity id"))
    0 * repository.updateCurve(_, _, _, _ as CdsCurveUpdateForm)

    1 * repository.archiveCurve(CURVE_GROUP_ID,
      missingCurve.entityId,
      missingCurve.validFrom, {
        it.versionForm.validFrom == missingCurve.validFrom
      }
      ) >> right(entityId(missingCurve.entityId))

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 2
    ids[0] == entityId("new entity id")
    ids[1] == entityId(missingCurve.entityId)
  }

  def "should create NEW curve and replace DUPLICATE when REPLACE"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateCurveForm, newCurveForm]))

    def duplicateCurve = duplicateCurve()
    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve, missingCurve]
    0 * repository.getFutureVersions(CURVE_GROUP_ID, _)

    1 * repository.createCurve(CURVE_GROUP_ID, _ as CdsCurveForm) >> right(entityId("new entity id"))
    1 * repository.updateCdsCurve(duplicateCurve, _, false) >> right(entityId(duplicateCurve.entityId))
    0 * repository.archiveCurve(_, _, _)

    1 * auditEntryService.newEntryWithLogs(_, _) >> right(entityId("id"))

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 2
    ids[0] == entityId("new entity id")
    ids[1] == entityId(duplicateCurve.entityId)
  }

  def "should create NEW curve, replace DUPLICATE and archive MISSING when REPLACE_DELETE"() {
    setup:
    def duplicateCurveForm = duplicateCurveForm()
    def newCurveForm = newCurveForm()
    1 * csvLoader.parse(_, LENIENT) >> right(csvResult([duplicateCurveForm, newCurveForm]))

    def duplicateCurve = duplicateCurve()
    def missingCurve = missingCurve()
    1 * repository.getActiveCurves(CURVE_GROUP_ID, { it.getActualDate() ==  STATE_DATE}) >> [duplicateCurve, missingCurve]
    0 * repository.getFutureVersions(CURVE_GROUP_ID, _)

    1 * repository.createCurve(CURVE_GROUP_ID, _ as CdsCurveForm) >> right(entityId("new entity id"))
    1 * repository.updateCdsCurve(duplicateCurve, _, false) >> right(entityId(duplicateCurve.entityId))
    1 * repository.archiveCurve(CURVE_GROUP_ID,
      missingCurve.entityId,
      missingCurve.validFrom, {
        it.versionForm.validFrom == missingCurve.validFrom
      }
      ) >> right(entityId(missingCurve.entityId))

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE_DELETE, LENIENT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = importService.importCurves(CURVE_GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 3
    ids[0] == entityId("new entity id")
    ids[1] == entityId(duplicateCurve.entityId)
    ids[2] == entityId(missingCurve.entityId)
  }

  static CsvParserResult<CreditCurveCsvForm> csvResult(List<CreditCurveCsvForm> forms) {
    new CsvParserResult<CreditCurveCsvForm>(forms, [])
  }

  static CreditCurveSearch search(CreditCurveCsvForm curveForm) {
    return new CreditCurveSearch(curveForm.creditCurveName(), STATE_DATE)
  }

  static CreditCurveSearch search(CreditCurve curve) {
    return new CreditCurveSearch(curve.getName(), STATE_DATE)
  }

  def newCurveForm() {
    def form = Mock(CreditCurveCsvForm)
    form.curveType >> CreditCurveType.CDS
    form.corpTicker >> "newCurveTicker"
    form.currency >> "currency"
    form.seniority >> "seniority"
    form.docClause >> "docClause"
    form.creditCurveName() >> resolveCdsCurveName("newCurveTicker", "currency", "seniority", "docClause")
    form.cdsCurveForm() >> new CdsCurveForm()
    return form
  }

  def missingCurve() {
    def curve = Mock(CreditCurve)
    curve.curveType >> CreditCurveType.CDS
    curve.curveGroupId >> CURVE_GROUP_ID
    curve.entityId >> entityId("missing_entityId").id
    curve.validFrom >> STATE_DATE
    curve.corpTicker >> "missingCurveTicker"
    curve.currency >> "currency"
    curve.seniority >> "seniority"
    curve.docClause >> "docClause"
    curve.state >> State.ACTIVE
    curve.name >> resolveCdsCurveName("missingCurveTicker", "currency", "seniority", "docClause")
    return curve
  }


  def duplicateCurveForm() {
    def form = Mock(CreditCurveCsvForm)
    form.curveType >> CreditCurveType.CDS
    form.corpTicker >> "duplicateCurveTicker"
    form.currency >> "currency"
    form.seniority >> "seniority"
    form.docClause >> "docClause"
    form.creditCurveName() >> resolveCdsCurveName("duplicateCurveTicker", "currency", "seniority", "docClause")
    form.cdsCurveForm() >> new CdsCurveForm()
    return form
  }

  def duplicateCurve(validFrom = STATE_DATE, state = State.ACTIVE) {
    def curve = Mock(CreditCurve)
    curve.curveType >> CreditCurveType.CDS
    curve.curveGroupId >> CURVE_GROUP_ID
    curve.entityId >> entityId("duplicate_entityId").id
    curve.validFrom >> validFrom
    curve.corpTicker >> "duplicateCurveTicker"
    curve.currency >> "currency"
    curve.seniority >> "seniority"
    curve.docClause >> "docClause"
    curve.state >> state
    curve.name >> resolveCdsCurveName("duplicateCurveTicker", "currency", "seniority", "docClause")
    return curve
  }
}
