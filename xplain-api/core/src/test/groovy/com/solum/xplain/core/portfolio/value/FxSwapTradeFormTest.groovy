package com.solum.xplain.core.portfolio.value

import com.opengamma.strata.basics.date.BusinessDayConventions
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.form.ClientMetricsForm
import com.solum.xplain.core.portfolio.form.FxSwapNotionalsForm
import com.solum.xplain.core.portfolio.form.FxSwapTradeForm
import com.solum.xplain.core.portfolio.trade.TradeValue
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class FxSwapTradeFormTest extends Specification {

  def "should convert form to TradeValue"() {
    given:
    def form = new FxSwapTradeForm()
    form.nearLegPaymentDate = LocalDate.of(2023, 1, 1)
    form.paymentDate = LocalDate.of(2024, 1, 1)
    form.businessDayConvention = "MODIFIED_FOLLOWING"
    form.baseCurrency = "USD"
    form.counterCurrency = "EUR"
    form.notionalsForm = new FxSwapNotionalsForm(
      nearDateBaseNotional: -1000000,
      nearDateCounterNotional: 900000,
      farDateBaseNotional: 1000000,
      farDateCounterNotional: -900000
      )
    form.payLegExtIdentifier = "PAY123"
    form.receiveLegExtIdentifier = "REC123"

    when:
    Either<ErrorItem, TradeValue> result = form.toTradeValue()

    then:
    result.isRight()
    TradeValue tradeValue = result.right().get()
    tradeValue.productType == CoreProductType.FXSWAP
    tradeValue.tradeDetails.info.tradeCurrency == "USD"
    tradeValue.tradeDetails.receiveLeg.currency.toString() == "USD"
    tradeValue.tradeDetails.receiveLeg.nearNotional == -1000000
    tradeValue.tradeDetails.receiveLeg.notional == 1000000
    tradeValue.tradeDetails.receiveLeg.extLegIdentifier == "REC123"
    tradeValue.tradeDetails.payLeg.currency.toString() == "EUR"
    tradeValue.tradeDetails.payLeg.nearNotional == 900000
    tradeValue.tradeDetails.payLeg.notional == -900000
    tradeValue.tradeDetails.payLeg.extLegIdentifier == "PAY123"
    tradeValue.tradeDetails.businessDayConvention == "MODIFIED_FOLLOWING"
  }

  def "should convert form with swapped notionals to TradeValue"() {
    given:
    def form = new FxSwapTradeForm()
    form.nearLegPaymentDate = LocalDate.of(2023, 1, 1)
    form.paymentDate = LocalDate.of(2024, 1, 1)
    form.businessDayConvention = "MODIFIED_FOLLOWING"
    form.baseCurrency = "USD"
    form.counterCurrency = "EUR"
    form.notionalsForm = new FxSwapNotionalsForm(
      nearDateBaseNotional: 900000,
      nearDateCounterNotional: -1000000,
      farDateBaseNotional: -900000,
      farDateCounterNotional: 1000000
      )
    form.payLegExtIdentifier = "PAY123"
    form.receiveLegExtIdentifier = "REC123"

    when:
    Either<ErrorItem, TradeValue> result = form.toTradeValue()

    then:
    result.isRight()
    TradeValue tradeValue = result.right().get()
    tradeValue.productType == CoreProductType.FXSWAP
    tradeValue.tradeDetails.info.tradeCurrency == "USD"
    tradeValue.tradeDetails.receiveLeg.currency.toString() == "EUR"
    tradeValue.tradeDetails.receiveLeg.nearNotional == -1000000
    tradeValue.tradeDetails.receiveLeg.notional == 1000000
    tradeValue.tradeDetails.receiveLeg.extLegIdentifier == "REC123"
    tradeValue.tradeDetails.payLeg.currency.toString() == "USD"
    tradeValue.tradeDetails.payLeg.nearNotional == 900000
    tradeValue.tradeDetails.payLeg.notional == -900000
    tradeValue.tradeDetails.payLeg.extLegIdentifier == "PAY123"
    tradeValue.tradeDetails.businessDayConvention == "MODIFIED_FOLLOWING"
  }

  def "should fail to convert form with unequal base currency notionals to TradeValue"() {
    given:
    def form = new FxSwapTradeForm()
    form.nearLegPaymentDate = LocalDate.of(2023, 1, 1)
    form.paymentDate = LocalDate.of(2024, 1, 1)
    form.businessDayConvention = "MODIFIED_FOLLOWING"
    form.baseCurrency = "USD"
    form.counterCurrency = "EUR"
    form.notionalsForm = new FxSwapNotionalsForm(
      nearDateBaseNotional: 900000,
      nearDateCounterNotional: -1000000,
      farDateBaseNotional: -950000,
      farDateCounterNotional: 1000000
      )
    form.payLegExtIdentifier = "PAY123"
    form.receiveLegExtIdentifier = "REC123"

    when:
    Either<ErrorItem, TradeValue> result = form.toTradeValue()

    then:
    result.isLeft()
    ErrorItem errorItem = result.left().get()
    errorItem.reason == Error.CONVERSION_ERROR
    errorItem.description == "Base currency notionals must be the same"
  }

  def "should create ref trade product item when #fxLongShort then #payCcy #recCcy"() {
    setup:
    def form = new FxSwapTradeForm(
      paymentDate: LocalDate.parse("2016-01-01"),
      nearLegPaymentDate: LocalDate.parse("2016-02-01"),
      businessDayConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
      payLegExtIdentifier: "PAY_LEG_ID",
      receiveLegExtIdentifier: "REC_LEG_ID",
      longShort: fxLongShort,
      baseCurrency: "EUR",
      counterCurrency: "USD",
      farDateFxRate: 1.2,
      nearDateFxRate: 1.1,
      clientMetrics: new ClientMetricsForm(
      presentValue: 10
      ),
      versionForm: NewVersionFormV2.newDefault()
      )

    expect:
    def either = form.toTradeValue()
    either.isRight()
    def item = either.getOrNull()
    item.productType == CoreProductType.FXSWAP
    item.tradeDetails.endDate == LocalDate.parse("2016-01-01")
    item.tradeDetails.businessDayConvention == "ModifiedFollowing"
    item.tradeDetails.payLeg.currency == payCcy
    item.tradeDetails.payLeg.extLegIdentifier == "PAY_LEG_ID"
    item.tradeDetails.receiveLeg.currency == recCcy
    item.tradeDetails.receiveLeg.extLegIdentifier == "REC_LEG_ID"
    item.tradeDetails.fxRate == 1.2
    item.tradeDetails.nearDateFxRate == 1.1
    item.clientMetrics.presentValue == 10

    where:
    fxLongShort       | payCcy | recCcy
    FxLongShort.LONG  | "USD"  | "EUR"
    FxLongShort.SHORT | "EUR"  | "USD"
  }
}
