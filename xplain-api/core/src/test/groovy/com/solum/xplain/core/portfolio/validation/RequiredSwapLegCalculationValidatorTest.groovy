package com.solum.xplain.core.portfolio.validation

import com.opengamma.strata.product.swap.FixedAccrualMethod
import com.opengamma.strata.product.swap.OvernightAccrualMethod
import com.solum.xplain.core.portfolio.form.SwapLegForm
import com.solum.xplain.core.portfolio.value.CalculationType
import jakarta.validation.ConstraintValidatorContext
import spock.lang.Specification
import spock.lang.Unroll

class RequiredSwapLegCalculationValidatorTest extends Specification {
  @Unroll
  "validation should return result for form #form"() {
    setup:
    def validator = new RequiredSwapLegCalculationValidator()
    def context = Mock(ConstraintValidatorContext)
    def constraintBuilder = Mock(ConstraintValidatorContext.ConstraintViolationBuilder)
    def nodeContext = Mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext)

    1 * context.disableDefaultConstraintViolation()
    _ * context.buildConstraintViolationWithTemplate("NotNull") >> constraintBuilder
    def p = property
    property.size() * constraintBuilder.addPropertyNode({
      p.contains(it)
    }) >> nodeContext
    _ * nodeContext.addConstraintViolation()


    expect:
    validator.isValid(form, context) == result
    where:
    form | property                                                                                          | result
    new SwapLegForm(
      calculationType: CalculationType.FIXED
      )    | ["calculationFixedDayCount", "calculationFixedRateInitialValue", "calculationFixedAccrualMethod"] | false
    new SwapLegForm(
      calculationType: CalculationType.FIXED,
      calculationFixedAccrualMethod: "Default",
      calculationFixedDayCount: "360"
      )    | ["calculationFixedRateInitialValue"]                                                              | false
    new SwapLegForm(
      calculationType: CalculationType.IBOR,
      calculationIborDayCount: "360",
      )    | [
        "calculationIborIndex",
        "calculationIborFixingDateOffsetDays",
        "calculationIborSpreadInitialValue"
      ]                                                             | false
    new SwapLegForm(
      calculationType: CalculationType.INFLATION,
      calculationInflationIndex: "IDX"
      )    | ["calculationInflationLag", "indexCalculationMethod"]                                             | false
    new SwapLegForm(
      calculationType: CalculationType.OVERNIGHT,
      calculationOvernightDayCount: "360"
      )    | [
        "calculationOvernightIndex",
        "calculationOvernightSpreadInitialValue",
        "calculationOvernightAccrualMethod",
        "calculationOvernightRateCutOffDays"
      ]                                                            | false
    new SwapLegForm(
      calculationType: CalculationType.FIXED,
      calculationFixedDayCount: "360",
      calculationFixedRateInitialValue: 1,
      calculationOvernightAccrualMethod: OvernightAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE,
      calculationFixedAccrualMethod: FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE
      )    | []                                                                                                | true
    new SwapLegForm(
      calculationType: CalculationType.FIXED,
      calculationFixedDayCount: "360",
      calculationFixedAccrualMethod: "Default",
      calculationFixedRateInitialValue: 1
      )    | []                                                                                                | true
  }
}
