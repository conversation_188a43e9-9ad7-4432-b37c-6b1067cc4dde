package com.solum.xplain.core.portfolio.csv

import static com.solum.xplain.core.portfolio.CoreProductType.IRS

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.io.CsvRow
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails
import com.solum.xplain.core.portfolio.csv.loader.CustomFieldsCsvLoader
import com.solum.xplain.core.portfolio.csv.loader.ExternalTradeIdsCsvLoader
import com.solum.xplain.core.portfolio.csv.loader.TradeLoaderHelper
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.trade.OnboardingDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.portfolio.value.PortfolioItemRefDetailsView
import com.solum.xplain.core.product.csv.ProductCsvLoader
import com.solum.xplain.core.product.csv.ProductCsvLoaders
import com.solum.xplain.extensions.enums.PositionType
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Collectors
import spock.lang.Specification

class TradeValueCsvLoaderTest extends Specification implements TradeLoaderHelper {

  ProductCsvLoaders loaders = Mock()
  ExternalTradeIdsCsvLoader externalTradeIdsCsvLoader = new ExternalTradeIdsCsvLoader(["SOURCE1", "SOURCE2"])
  CustomFieldsCsvLoader customFieldsCsvLoader = new CustomFieldsCsvLoader(["FIELD1", "FIELD2"])
  Map<String, PortfolioItemRefDetailsView> activeRefTradesById = ["REFID" : new PortfolioItemRefDetailsView(clientMetrics: new ClientMetrics(20), tradeCounterparty: "rcp", tradeCounterpartyType: CounterpartyType.CLEARED, productType: IRS),
    "REFID2": new PortfolioItemRefDetailsView(productType: CoreProductType.CDS),
    "REFID3": new PortfolioItemRefDetailsView(productType: CoreProductType.FXFWD)]
  TradeValueCsvLoader loader = new TradeValueCsvLoader(loaders, externalTradeIdsCsvLoader, customFieldsCsvLoader, activeRefTradesById, false, false)

  def "should parse bespoke trade"() {
    setup:
    def mockDetails = Mock(ResolvableTradeDetails)
    def mockProductLoader = Mock(ProductCsvLoader)
    def rows = loadResource("CdsTrades.csv")

    when:
    def parsedRow = loader.parseRow(rows[0])

    then:
    parsedRow.isRight()
    def item1 = parsedRow.getOrNull()
    item1.getLeft() == 2
    item1.getMiddle() == "1"
    def tradeValue = item1.getRight()
    tradeValue.onboardingDetails == new OnboardingDetails(dealCost: 1.0d, xplainCostCheck: true, vendorCheck: false, marketConfCheck: false)
    tradeValue.externalIdentifiers == []

    and:
    1 * loaders.parseType("CDS") >> CoreProductType.CDS
    1 * loaders.loader(CoreProductType.CDS, 2) >> Either.right(mockProductLoader)
    1 * loaders.validProducts() >> Set.of("CDS")
    1 * mockProductLoader.parse(_ as CsvRow, false) >> Either.right(mockDetails)
    1 * mockProductLoader.parseTradeCcy(_ as CsvRow) >> Currency.EUR
    1 * mockDetails.toTradeDetails({ it ->
      it.csaDiscountingGroup == "USD"
    }) >> new TradeDetails()
  }

  def "should parse bespoke trade with external identifiers"() {
    setup:
    def mockDetails = Mock(ResolvableTradeDetails)
    def mockProductLoader = Mock(ProductCsvLoader)
    def rows = loadResource("ExternalIdentifiersTrades.csv")

    loaders.parseType("CDS") >> CoreProductType.CDS
    loaders.validProducts() >> Set.of("CDS")
    loaders.loader(CoreProductType.CDS, lineNo) >> Either.right(mockProductLoader)

    mockProductLoader.parse(_ as CsvRow, false) >> Either.right(mockDetails)
    mockProductLoader.parseTradeCcy(_ as CsvRow) >> Currency.EUR
    mockDetails.toTradeDetails(_ as TradeInfoDetails) >> new TradeDetails()

    when:
    def parsedRow = loader.parseRow(rows[rowNo])

    then:
    parsedRow.isRight()
    def tradeValue = parsedRow.getOrNull().getRight()
    tradeValue.externalIdentifiers == expectedIds

    where:
    rowNo | lineNo | expectedIds
    0     | 2      | [new ExternalIdentifier("ID1", "SOURCE1")]
    1     | 3      | [new ExternalIdentifier("ID2", "SOURCE2")]
    2     | 4      | [new ExternalIdentifier("ID1", "SOURCE1"), new ExternalIdentifier("ID2", "SOURCE2")]
  }

  def "should parse bespoke trade with custom fields"() {
    setup:
    def mockDetails = Mock(ResolvableTradeDetails)
    def mockProductLoader = Mock(ProductCsvLoader)
    def rows = loadResource("CustomFieldTrades.csv")

    loaders.parseType("CDS") >> CoreProductType.CDS
    loaders.validProducts() >> Set.of("CDS")
    loaders.loader(CoreProductType.CDS, lineNo) >> Either.right(mockProductLoader)

    mockProductLoader.parse(_ as CsvRow, false) >> Either.right(mockDetails)
    mockProductLoader.parseTradeCcy(_ as CsvRow) >> Currency.EUR
    mockDetails.toTradeDetails(_ as TradeInfoDetails) >> new TradeDetails()

    when:
    def parsedRow = loader.parseRow(rows[rowNo])

    then:
    parsedRow.isRight()
    def tradeValue = parsedRow.getOrNull().getRight()
    tradeValue.customFields == expectedFields

    where:
    rowNo | lineNo | expectedFields
    0     | 2      | [new CustomTradeField("FIELD1", "VAL1")]
    1     | 3      | [new CustomTradeField("FIELD2", "VAL2")]
    2     | 4      | [new CustomTradeField("FIELD1", "VAL1"), new CustomTradeField("FIELD2", "VAL2")]
  }

  def "should return external identifiers parse errors"() {
    setup:
    def mockDetails = Mock(ResolvableTradeDetails)
    def mockProductLoader = Mock(ProductCsvLoader)
    def rows = loadResource("ExternalIdentifiersTradesInvalid.csv")

    loaders.parseType("CDS") >> CoreProductType.CDS
    loaders.validProducts() >> Set.of("CDS")
    loaders.loader(CoreProductType.CDS, 2) >> Either.right(mockProductLoader)

    mockProductLoader.parse(_ as CsvRow, false) >> Either.right(mockDetails)
    mockDetails.toTradeDetails(_ as TradeInfoDetails) >> new TradeDetails()

    when:
    def parsedRow = loader.parseRow(rows[0])

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description == "Error at line number 2. Error: Trade ID.SOURCE1 is invalid. Value can't contain non-printing characters."
  }

  def "should parse allocated trades"() {
    setup:
    def rows = loadResource("AllocatedTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parseRow(it)
    }.toList()

    then:
    parsedRows.size() == 3
    parsedRows.stream().allMatch(Either::isRight)
    0 * loaders._
    and:
    def item = parsedRows[0].getOrNull().getRight()
    item.description == "allocation comments"
    item.tradeDetails == null
    item.allocationTradeDetails.allocationNotional == 1000
    item.allocationTradeDetails.referenceTradeId == "REFID"
    item.allocationTradeDetails.counterParty == "TEST_COUNTERPARTY"
    item.allocationTradeDetails.counterPartyType == null
    item.allocationTradeDetails.clientMetrics == new ClientMetrics()
    item.allocationTradeDetails.positionType == null
    item.allocationTradeDetails.tradeDate == LocalDate.of(2022, 10, 10)
    item.allocationTradeDetails.csaDiscountingGroup == "EUR"
    item.clientMetrics == null
    item.onboardingDetails == OnboardingDetails.defaultOnboardingDetails()

    def itemFull = parsedRows[1].getOrNull().getRight()
    itemFull.description == "comments"
    itemFull.tradeDetails == null
    itemFull.allocationTradeDetails.allocationNotional == 1000
    itemFull.allocationTradeDetails.referenceTradeId == "REFID2"
    itemFull.allocationTradeDetails.counterParty == "CDX_COUNTERPARTY"
    itemFull.allocationTradeDetails.counterPartyType == CounterpartyType.BILATERAL
    itemFull.allocationTradeDetails.clientMetrics.presentValue == 10
    itemFull.allocationTradeDetails.positionType == PositionType.SELL
    itemFull.allocationTradeDetails.description == "comments"
    itemFull.allocationTradeDetails.tradeDate == LocalDate.of(2022, 10, 10)
    itemFull.allocationTradeDetails.csaDiscountingGroup == null
    itemFull.clientMetrics == null
    itemFull.onboardingDetails.dealCost == 1
    itemFull.onboardingDetails.accountingCost == 2
    itemFull.onboardingDetails.vendorOnboardingDate == LocalDate.of(2024, 2, 1)
    itemFull.onboardingDetails.xplainCostCheck
    !itemFull.onboardingDetails.vendorCheck
    !itemFull.onboardingDetails.marketConfCheck

    def allocFx = parsedRows[2].getOrNull().getRight()
    allocFx.allocationTradeDetails.allocationNotional == -1000
    allocFx.allocationTradeDetails.referenceTradeId == "REFID3"
    allocFx.allocationTradeDetails.tradeDate == LocalDate.of(2022, 10, 9)
    allocFx.onboardingDetails == OnboardingDetails.defaultOnboardingDetails()
  }

  def "should return allocation parse error"() {
    setup:
    def rows = loadResource("AllocatedTradesInvalid.csv")

    when:
    def parsedRow = loader.parseRow(rows[row])

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: 1 is not valid Ref Security."
    1   | "Error at line number 3. Error: Error parsing field Allocation Notional. No value was found for 'Allocation Notional'"
    2   | "Error at line number 4. Error: Header not found: 'BuySell'"
    3   | "Error at line number 5. Error: Unsupported value: -10.0. . Supported values are higher than 0"
    4   | "Error at line number 6. Error: Unknown date format, must be formatted as 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy/M/d', 'd/M/yyyy', 'd-MMM-yyyy', 'dMMMyyyy', 'd/M/yy', 'd-MMM-yy' or 'dMMMyy' but was: invaliddate"
    5   | "Error at line number 7. Error: Unsupported value: XAU. Supported values: [AUD, CAD, CHF, EUR, GBP, JPY, NZD, USD]"
  }

  def "should capitalize CounterParty on Import"() {
    setup:
    def allocatedTradesRow = loadResource("AllocatedTrades.csv")

    when:
    def allocationTradesParsedRows = allocatedTradesRow.stream().map { loader.parseRow(it) }.toList()
    def allocationTradeitems = allocationTradesParsedRows[0].getOrNull().getRight()

    then:
    allocationTradeitems.allocationTradeDetails.counterParty == "TEST_COUNTERPARTY"
  }
}
