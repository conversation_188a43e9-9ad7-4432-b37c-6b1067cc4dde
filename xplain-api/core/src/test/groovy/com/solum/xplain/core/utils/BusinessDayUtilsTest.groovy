package com.solum.xplain.core.utils

import com.opengamma.strata.basics.date.HolidayCalendars
import com.solum.xplain.core.common.daterange.DateRange
import java.time.LocalDate
import spock.lang.Specification


class BusinessDayUtilsTest  extends Specification {

  def "should return business days between start and end date using SAT_SUN calendar"() {
    given:
    def startDate = LocalDate.of(2025, 5, 19) // Monday
    def endDate = LocalDate.of(2025, 5, 25)   // Sunday
    def dateRange = DateRange.newOf(startDate, endDate)

    when:
    def result = BusinessDayUtils.businessDays(dateRange, HolidayCalendars.SAT_SUN).toList()

    then:
    result.size() == 5
    result.first() == LocalDate.of(2025, 5, 19)
    result.last() == LocalDate.of(2025, 5, 23)
  }
}
