package com.solum.xplain.core.curvegroup.curvecredit.entity

import com.solum.xplain.extensions.enums.CreditSector
import spock.lang.Specification

class CreditCurveFundingNodeTest extends Specification {

  def "should return correct key and name for Funding node"() {
    setup:
    def node = new CreditCurveFundingNodeBuilder().build()

    when:
    def result = node.instrument(
      "BARC_EUR_SNRFOR_MR14",
      "BARC",
      CreditSector.BASIC_MATERIALS,
      "EUR",
      "BARC EUR"
      )

    then:
    result.key == "1Y_BARC_EUR_FUNDING"
    result.mdkName == "BARC EUR FUNDING 1Y"
  }
}
