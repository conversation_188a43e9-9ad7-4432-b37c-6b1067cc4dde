package com.solum.xplain.core.classifiers

import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.product.swap.type.SingleCurrencySwapConvention
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfigurations
import com.solum.xplain.extensions.constants.OvernightIndexConstants
import com.solum.xplain.extensions.index.ExtendedPriceIndices
import java.util.regex.Pattern
import spock.lang.Specification

class ConstantsTest extends Specification {

  def "should return correct overnight indices"() {
    setup:
    def overnightIndices = [OvernightIndices.AUD_AONIA,
      OvernightIndex.of("CLP-TNA"),
      OvernightIndex.of("COP-OIBR"),
      OvernightIndices.BRL_CDI,
      OvernightIndices.CAD_CORRA,
      OvernightIndices.CHF_SARON,
      OvernightIndexConstants.CZK_CZEONIA,
      OvernightIndices.EUR_EONIA,
      OvernightIndices.EUR_ESTR,
      OvernightIndices.GBP_SONIA,
      OvernightIndex.of("HKD-HONIA"),
      OvernightIndexConstants.IDR_INDONIA,
      OvernightIndex.of("INR-OMIBOR"),
      OvernightIndices.JPY_TONAR,
      OvernightIndexConstants.MXN_F_TIIE,
      OvernightIndexConstants.MYR_MYOR,
      OvernightIndices.NZD_NZIONA,
      OvernightIndex.of("SGD-SORA"),
      OvernightIndices.THB_THOR,
      OvernightIndex.of("TRY-TLREF"),
      OvernightIndices.USD_FED_FUND,
      OvernightIndex.of("DKK-DESTR"),
      OvernightIndex.of("HUF-BUBORON"),
      OvernightIndex.of("RUB-RUONIA"),
      OvernightIndex.of("SEK-SWESTR"),
      OvernightIndex.of("PLN-POLONIA"),
      OvernightIndexConstants.PLN_WIRON,
      OvernightIndices.NOK_NOWA,
      OvernightIndices.USD_SOFR,
      OvernightIndexConstants.ILS_SHIR,
      OvernightIndexConstants.KRW_KOFR,
      OvernightIndexConstants.ZAR_ZARONIA].toSorted(c -> c.name)

    expect:
    Constants.OVERNIGHT_INDICES.toSorted {
      c -> c.name
    } == overnightIndices
  }

  def "should return correct ibor indices"() {
    setup:
    def iborIndices = [
      IborIndex.of("AED-EIBOR-1M"),
      IborIndex.of("AED-EIBOR-3M"),
      IborIndex.of("AED-EIBOR-6M"),
      IborIndex.of("AED-EIBOR-12M"),
      IborIndices.AUD_BBSW_1M,
      IborIndices.AUD_BBSW_3M,
      IborIndices.AUD_BBSW_6M,
      IborIndices.CAD_CDOR_1M,
      IborIndices.CAD_CDOR_3M,
      IborIndices.CHF_LIBOR_1M,
      IborIndices.CHF_LIBOR_3M,
      IborIndices.CHF_LIBOR_6M,
      IborIndex.of("CNY-REPO-1W"),
      IborIndex.of("CNH-HIBOR-3M"),
      IborIndex.of("CNY-SHIBOR-3M"),
      IborIndices.CZK_PRIBOR_3M,
      IborIndices.CZK_PRIBOR_6M,
      IborIndices.DKK_CIBOR_3M,
      IborIndices.DKK_CIBOR_6M,
      IborIndices.EUR_EURIBOR_1M,
      IborIndices.EUR_EURIBOR_3M,
      IborIndices.EUR_EURIBOR_6M,
      IborIndices.EUR_EURIBOR_12M,
      IborIndices.GBP_LIBOR_1M,
      IborIndices.GBP_LIBOR_3M,
      IborIndices.GBP_LIBOR_6M,
      IborIndices.GBP_LIBOR_12M,
      IborIndex.of("HKD-HIBOR-3M"),
      IborIndices.HUF_BUBOR_3M,
      IborIndices.HUF_BUBOR_6M,
      IborIndex.of("ILS-TLBOR-3M"),
      IborIndices.JPY_LIBOR_1M,
      IborIndices.JPY_LIBOR_3M,
      IborIndices.JPY_LIBOR_6M,
      IborIndex.of("KRW-CD-3M"),
      IborIndex.of("MYR-KLIBOR-3M"),
      IborIndices.MXN_TIIE_4W,
      IborIndices.MXN_TIIE_13W,
      IborIndices.MXN_TIIE_26W,
      IborIndices.NOK_NIBOR_3M,
      IborIndices.NOK_NIBOR_6M,
      IborIndices.NZD_BKBM_3M,
      IborIndices.PLN_WIBOR_3M,
      IborIndices.PLN_WIBOR_6M,
      IborIndex.of("SAR-SAIBOR-3M"),
      IborIndices.SEK_STIBOR_3M,
      IborIndices.SEK_STIBOR_6M,
      IborIndex.of("SGD-SOR-1M"),
      IborIndex.of("SGD-SOR-3M"),
      IborIndex.of("SGD-SOR-6M"),
      IborIndex.of("THB-THBFIX-6M"),
      IborIndex.of("TRY-TRLIBOR-3M"),
      IborIndex.of("TWD-TAIBOR-3M"),
      IborIndices.USD_LIBOR_1M,
      IborIndices.USD_LIBOR_3M,
      IborIndices.USD_LIBOR_6M,
      IborIndices.ZAR_JIBAR_3M,
      IborIndex.of("RUB-MOSPRIME-3M")
    ].toSorted { c -> c.name }

    expect:
    Constants.IBOR_INDICES.toSorted { c -> c.name } == iborIndices
  }

  def "should return inflation indices"() {
    setup:
    def inflationIndices =
    [PriceIndices.CH_CPI,
      PriceIndices.EU_AI_CPI,
      PriceIndices.EU_EXT_CPI,
      PriceIndices.FR_EXT_CPI,
      ExtendedPriceIndices.GB_CPI,
      PriceIndices.GB_HICP,
      PriceIndices.GB_RPI,
      PriceIndices.GB_RPIX,
      PriceIndices.JP_CPI_EXF,
      PriceIndices.US_CPI_U].toSorted(c -> c.name)

    expect:
    Constants.INFLATION_INDICES.toSorted {
      c -> c.name
    } == inflationIndices
  }

  def "should return swaption indices in line with volatility surfaces"() {
    setup:
    // e.g. AED-FIXED-1Y-EIBOR-3M -> AED-EIBOR-3M
    def conventionPattern = Pattern.compile("^([A-Z][A-Z][A-Z])-FIXED-[0-9]+[WMY]-([A-Z]+-[0-9]+[WMY])\$")
    def expected = VolatilitySurfaceConfigurations.VOLATILITY_SURFACES.stream()
    .map {conventionPattern.matcher(it.swapConvention)}
    .filter {it.matches()}
    .map {String.join("-", it.group(1), it.group(2))}
    .distinct().sorted().toList()
    def actual = Constants.SWAPTION_INDICES.stream().map { it.name }.sorted().toList()

    expect:
    actual == expected
  }

  def "should ensure that all volatility surface swap conventions exist"() {
    setup:
    def allExisting = true
    VolatilitySurfaceConfigurations.VOLATILITY_SURFACES.forEach {
      volatilitySurface -> {
        var swapConventionName = volatilitySurface.swapConvention
        try {
          SingleCurrencySwapConvention.of(swapConventionName)
        } catch (Exception e) {
          System.out.println("Swap convention not found: " + swapConventionName)
          allExisting = false
        }
      }
    }

    expect:
    allExisting
  }
}
