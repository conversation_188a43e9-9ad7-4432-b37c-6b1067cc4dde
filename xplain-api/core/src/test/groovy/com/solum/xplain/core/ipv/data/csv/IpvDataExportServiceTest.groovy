package com.solum.xplain.core.ipv.data.csv


import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.ipv.data.IpvDataRepository
import com.solum.xplain.core.ipv.data.IpvDataType
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView
import com.solum.xplain.core.users.UserBuilder
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Stream
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification
import spock.lang.Unroll

class IpvDataExportServiceTest extends Specification {

  static final def USER = UserBuilder.user()
  static final TestingAuthenticationToken AUTH = new TestingAuthenticationToken(USER, null)

  static final String IPV_GROUP_ID = "groupId"
  static final LocalDate VALUATION_DATE = LocalDate.of(2021, 01, 01)

  AuthenticationContext userRepository = Mock()
  IpvDataRepository repository = Mock()
  IpvDataGroupRepository ipvDataGroupRepository = Mock()

  IpvDataExportService service = new IpvDataExportService(userRepository, repository, ipvDataGroupRepository)

  def setup() {
    userRepository.userEither(AUTH) >> Either.right(USER)

    def group = Mock(IpvDataGroupView)
    group.id >> IPV_GROUP_ID
    ipvDataGroupRepository.ipvDataGroupView(USER, IPV_GROUP_ID) >> Either.right(group)
  }

  @Unroll
  def "should get resolved=#resolved provider values csv"() {
    setup:
    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, resolved, false)
    1 * repository.getValueViewsStream(
      IPV_GROUP_ID,
      filter,
      IpvDataType.ALL
      ) >> Stream.<IpvDataProviderValueView> of()

    when:
    def result = service.getProviderValuesCsv(AUTH, IPV_GROUP_ID, filter, VALUATION_DATE, IpvDataType.ALL)

    then:
    result.isRight()
    def headers = result.getOrNull().toResponse().getHeaders().toString()
    headers.contains(expectedFilenameStart)

    where:
    resolved | expectedFilenameStart
    true     | "filename=\"2021-01-01_ValuationData_2021-01-01_"
    false    | "filename=\"UnresolvedValuationData_2021-01-01_"
    null     | "filename=\"2021-01-01_ValuationData_2021-01-01_"
  }

  @Unroll
  def "should get nav-only values csv"() {
    setup:
    def filter = new IpvDataProviderValueFilter(VALUATION_DATE, resolved, false)
    1 * repository.getValueViewsStream(
      IPV_GROUP_ID,
      filter,
      IpvDataType.NAV
      ) >> Stream.<IpvDataProviderValueView> of()

    when:
    def result = service.getProviderValuesCsv(AUTH, IPV_GROUP_ID, filter, VALUATION_DATE, IpvDataType.NAV)

    then:
    result.isRight()
    def headers = result.getOrNull().toResponse().getHeaders().toString()
    headers.contains(expectedFilenameStart)

    where:
    resolved | expectedFilenameStart
    true     | "filename=\"2021-01-01_ValuationData_2021-01-01_"
    false    | "filename=\"UnresolvedValuationData_2021-01-01_"
    null     | "filename=\"2021-01-01_ValuationData_2021-01-01_"
  }
}
