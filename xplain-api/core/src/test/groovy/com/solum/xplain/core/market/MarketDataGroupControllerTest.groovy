package com.solum.xplain.core.market

import static com.solum.xplain.core.classifiers.pricingslots.PricingSlot.LDN_1200
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.value.AllowedCompaniesForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.market.filter.MarketDataGroupFilter
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.market.service.MarketDataGroupService
import com.solum.xplain.core.market.value.MarketDataGroupForm
import com.solum.xplain.core.market.value.MarketDataGroupView
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.utils.filter.TableFilter
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [MarketDataGroupController])
class MarketDataGroupControllerTest extends Specification {

  @SpringBean
  private MarketDataGroupService groupService = Mock()

  @SpringBean
  private MarketDataGroupRepository marketDataGroupRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  CompanyRepository companyRepository = Mock()

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper objectMapper

  def "should create new MarketDataGroup with form #form"() {
    setup:
    groupService.create(_ as Authentication, _ as MarketDataGroupForm) >> right(entityId("1"))
    marketDataGroupRepository.existsByName(
      "existing",
      _  // cannot be cast because test fails when null
      ) >> true
    companyRepository.companyEntity("id") >> right(new Company())
    companyRepository.companyEntity(_ as String) >> left(OBJECT_NOT_FOUND.entity())
    when:
    def results = mockMvc.perform(post("/market-data-group", "1")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == responseCode
      getContentAsString().contains(responseBody)
    }

    where:
    form                                                                                             | responseCode | responseBody
    mdGroupForm()                                                                                    | 200          | "id"
    mdGroupForm({ f -> f.remove("pricingSlot") })                                                    | 200          | "id"
    mdGroupForm({ f -> f.put("name", "") })                                                          | 412          | "NotEmpty.marketDataGroupForm.name"
    mdGroupForm({ f -> f.put("allowedTeamsForm", new AllowedTeamsForm(false, [])) })                 | 412          | "At least one team should be selected"
    mdGroupForm({ f -> f.remove("allowedTeamsForm") })                                               | 412          | "NotNull.marketDataGroupForm.allowedTeamsForm"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(true, ["id"])) })      | 412          | "ValidAllowedCompaniesForm.marketDataGroupForm.allowedCompaniesForm"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(false, ["random"])) }) | 412          | "ValidCompanyId.marketDataGroupForm.allowedCompaniesForm.companyIds"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(false, [])) })         | 412          | "At least one company should be selected"
    mdGroupForm({ f -> f.remove("allowedCompaniesForm") })                                           | 412          | "NotNull.marketDataGroupForm.allowedCompaniesForm"
  }

  def "should update market data group with form #form and #responseBody"() {
    setup:
    groupService.updateMarketDataGroup(
      _ as Authentication,
      _ as String,
      _ as MarketDataGroupForm
      ) >> right(new EntityId("1"))
    companyRepository.companyEntity("id") >> right(new Company())
    companyRepository.companyEntity("random") >> left(OBJECT_NOT_FOUND.entity())

    when:
    def results = mockMvc.perform(put("/market-data-group/{groupId}", "groupId")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseCode
      getContentAsString().contains(responseBody)
    }

    where:
    form                                                                                             | responseCode | responseBody
    mdGroupForm()                                                                                    | 200          | "id"
    mdGroupForm({ f -> f.remove("pricingSlot") })                                                    | 200          | "id"
    mdGroupForm({ f -> f.put("name", "") })                                                          | 412          | "NotEmpty.marketDataGroupForm.name"
    mdGroupForm({ f -> f.put("allowedTeamsForm", new AllowedTeamsForm(false, [])) })                 | 412          | "At least one team should be selected"
    mdGroupForm({ f -> f.remove("allowedTeamsForm") })                                               | 412          | "NotNull.marketDataGroupForm.allowedTeamsForm"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(true, ["id"])) })      | 412          | "ValidAllowedCompaniesForm.marketDataGroupForm.allowedCompaniesForm"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(false, ["random"])) }) | 412          | "ValidCompanyId.marketDataGroupForm.allowedCompaniesForm.companyIds"
    mdGroupForm({ f -> f.put("allowedCompaniesForm", new AllowedCompaniesForm(false, [])) })         | 412          | "At least one company should be selected"
    mdGroupForm({ f -> f.remove("allowedCompaniesForm") })                                           | 412          | "NotNull.marketDataGroupForm.allowedCompaniesForm"
  }

  def "should archive market data group with #result and #response"() {
    setup:
    groupService.archiveMarketDataGroup(_ as Authentication, _ as String) >> result

    when:
    def results = mockMvc.perform(put("/market-data-group/{id}/archive", "id")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response

    where:
    result                 | response
    left(OBJECT_NOT_FOUND) | 422
    right(entityId("1"))   | 200
  }

  def "should get market data groups list"() {
    setup:
    groupService.getAll(
      _ as ScrollRequest,
      _ as Authentication,
      _ as MarketDataGroupFilter,
      _ as TableFilter,
      _ as BitemporalDate
      ) >> ScrollableEntry.of(Collections.emptyList(), ScrollRequest.unconstrained())

    when:
    def results = mockMvc.perform(get("/market-data-group")
      .param("stateDate", "1970-01-01")
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get market data group with #result and #response"() {
    setup:
    groupService.get(_ as Authentication, _ as String, _ as BitemporalDate) >> result

    when:
    def results = mockMvc.perform(get("/market-data-group/{id}", "id")
      .param("stateDate", "1970-01-01")
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response

    where:
    result                           | response
    right(new MarketDataGroupView()) | 200
    left(OBJECT_NOT_FOUND)           | 422
  }

  def mdGroupForm(Closure c = {}) {
    [
      name                : "Test market data group",
      pricingSlot         : LDN_1200,
      allowedCompaniesForm: new AllowedCompaniesForm(false, ["id"]),
      allowedTeamsForm    : new AllowedTeamsForm(false, ["id"])
    ].tap(c)
  }
}
