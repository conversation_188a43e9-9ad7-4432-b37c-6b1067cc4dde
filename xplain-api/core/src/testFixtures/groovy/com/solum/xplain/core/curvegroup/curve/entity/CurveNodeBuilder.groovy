package com.solum.xplain.core.curvegroup.curve.entity

import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M

import com.solum.xplain.core.classifiers.CurveNodeTypes
import com.solum.xplain.extensions.immfra.ExtendedImmutableImmFraConventions
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CurveNode)
class CurveNodeBuilder {
  CurveNodeBuilder() {
    type(CurveNodeTypes.FIXED_IBOR_SWAP_NODE)
    convention(EUR_FIXED_1Y_EURIBOR_3M.name)
    period("1Y")
  }

  static curveNode() {
    curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)
  }

  static curveNode(String type, String convention, String period) {
    return new CurveNodeBuilder()
      .type(type)
      .convention(convention)
      .period(period)
      .build()
  }

  static fraCurveNode(String convention, String period, String fraSettlement) {
    return new CurveNodeBuilder()
      .type(CurveNodeTypes.FRA_NODE)
      .fraSettlement(fraSettlement)
      .convention(convention)
      .period(period)
      .build()
  }

  static iborFutureCurveNode(String convention, String serialFuture) {
    return new CurveNodeBuilder()
      .type(CurveNodeTypes.IBOR_FUTURE_NODE)
      .serialFuture(serialFuture)
      .convention(convention)
      .period(null)
      .build()
  }

  static immFraCurveNode(String convention, String serialFuture) {
    return new CurveNodeBuilder()
      .type(CurveNodeTypes.IMM_FRA_NODE)
      .serialFuture(serialFuture)
      .convention(convention)
      .period(null)
      .build()
  }

  static curveNode(String type, String convention) {
    curveNode(type, convention, "1Y")
  }

  static curveNode2Y(String type, String convention) {
    curveNode(type, convention, "2Y")
  }
}
