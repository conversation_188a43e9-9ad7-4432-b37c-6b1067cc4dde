package com.solum.xplain.core.curvegroup.instrument

import static CoreInstrumentType.CAP_FLOOR_VOL
import static CoreInstrumentType.CDS
import static CoreInstrumentType.FIXED_IBOR_SWAP
import static CoreInstrumentType.FIXED_INFLATION_SWAP
import static CoreInstrumentType.FIXED_OVERNIGHT_SWAP
import static CoreInstrumentType.FRA
import static CoreInstrumentType.FX_RATE
import static CoreInstrumentType.FX_SWAP
import static CoreInstrumentType.FX_VOL
import static CoreInstrumentType.FX_VOL_SKEW
import static CoreInstrumentType.IBOR_FIXING_DEPOSIT
import static CoreInstrumentType.IBOR_FUTURE
import static CoreInstrumentType.SWAPTION_ATM
import static CoreInstrumentType.SWAPTION_SKEW
import static CoreInstrumentType.XCCY_IBOR_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFxCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFxVol

import com.solum.xplain.extensions.enums.CreditSector


class InstrumentDefinitionBuilder {

  static InstrumentDefinition DUMMY_INSTRUMENT = new InstrumentDefinition(
  CoreAssetClass.CDS,
  CoreAssetGroup.CREDIT,
  "CURRENCY",
  "FX PAIR",
  CreditSector.UTILITIES,
  "ASSET NAME",
  CDS,
  "1D",
  "NODE INSTRUMENT",
  "KEY",
  "MDK NAME",
  "UNDERLYING"
  )

  static InstrumentDefinition IBOR_FIXING_DEPOSIT_EUR_6M = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR 3M",
  IBOR_FIXING_DEPOSIT,
  "6M",
  "6M",
  "6M_EUR-EURIBOR-6M",
  "EUR 6M Deposit")


  static InstrumentDefinition FIXED_IBOR_SWAP_EUR_6M = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR 6M",
  FIXED_IBOR_SWAP,
  "2Y",
  "1Y",
  "2Y_EUR-FIXED-1Y-EURIBOR-6M",
  "EUR 30U/360 VS 6M SWAP 2Y")


  static InstrumentDefinition FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR EONIA",
  FIXED_OVERNIGHT_SWAP,
  "6M",
  "6M",
  "6M_EUR-FIXED-TERM-EONIA-OIS",
  "EUR-EONIA OIS SWAP 6M")

  static InstrumentDefinition FRA_EUR_6M = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR 6M",
  FRA,
  "6M",
  "1Mx7M",
  "1Mx7M_EUR-EURIBOR-6M",
  "EUR FRA 1Mx7M")

  static InstrumentDefinition IBOR_FUTURE_EUR_3M = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR 3M",
  IBOR_FUTURE,
  "3M",
  "2D+1",
  "2D+1_EUR-EURIBOR-3M-Quarterly-IMM",
  "EUR 3M Future 1")

  static InstrumentDefinition OVERNIGHT_IBOR_BASIS_SWAP = InstrumentDefinition.ofIrCurve(
  "EUR",
  "C1",
  CoreInstrumentType.OVERNIGHT_IBOR_BASIS_SWAP,
  "1Y",
  "1Y",
  "quote2",
  "name")

  static InstrumentDefinition IBOR_IBOR_SWAP = InstrumentDefinition.ofIrCurve(
  "EUR",
  "C1",
  IBOR_FUTURE,
  "1Y",
  "1Y",
  "quote2",
  "name")

  static InstrumentDefinition XCCY_IBOR_IBOR_SWAP_EUR_VS_USD = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EUR 3M vs USD 3M",
  XCCY_IBOR_IBOR_SWAP,
  "6M",
  "6M",
  "6M_EUR-EURIBOR-3M-USD-LIBOR-3M",
  "EUR 3M vs USD 3M 6M")


  static InstrumentDefinition FIXED_INFLATION_SWAP_EUR_EXT_CPI = InstrumentDefinition.ofIrCurve(
  "EUR",
  "EU EXT CPI",
  FIXED_INFLATION_SWAP,
  "1Y",
  "1Y",
  "1Y_EUR-FIXED-ZC-EU-EXT-CPI",
  "EU-EXT-CPI SWAP 1Y")

  static InstrumentDefinition SWAPTION_ATM_EUR_6M_VOLS = InstrumentDefinition.ofVol(
  "EUR 6M Vols",
  "EUR",
  SWAPTION_ATM,
  "vol",
  "EUR 6M Vols SWO Vols ATM 3Mv1Y",
  "1Y",
  "3Mx1Y",
  "EUR 6M")

  static InstrumentDefinition SWAPTION_SKEW_USD_3M_VOLS = InstrumentDefinition.ofVol(
  "USD 3M Vols",
  "USD",
  SWAPTION_SKEW,
  "6YV1Y_+100_USD-LIBOR-3M",
  "USD 3M Vols SWO Vols +100BP 6Yv1Y",
  "1Y",
  "6Yx1Y",
  "USD 3M")

  static InstrumentDefinition CAP_FLOOR_VOL_USD_3M_VOLS = InstrumentDefinition.ofVol(
  "USD 3M Vols",
  "USD",
  CAP_FLOOR_VOL,
  "1Y_0.50%_CF_USD-LIBOR-3M",
  "USD 3M Vols CF Vols 0.50% 1Y",
  "1Y",
  "1Y",
  "USD 3M")

  static InstrumentDefinition FX_RATE_EUR_USD = InstrumentDefinition.ofFx(
  CoreAssetClass.FX_RATES,
  "EUR/USD",
  FX_RATE,
  "EUR/USD",
  "EUR/USD Spot")

  static InstrumentDefinition FX_SWAP_EUR_USD = ofFxCurve(
  "EUR/USD",
  "EUR/USD",
  FX_SWAP,
  "6M",
  "6M",
  "6M_EUR/USD",
  "6M_EUR/USD")

  static InstrumentDefinition FX_VOL_EUR_USD = ofFxVol(
  FX_VOL,
  "EUR/USD",
  "3M",
  "3M_EUR/USDV",
  "EUR/USD ATM FX Vols 3M")

  static InstrumentDefinition FX_VOL_SKEW_EUR_USD = ofFxVol(
  FX_VOL_SKEW,
  "EUR/USD",
  "3M",
  "3M_EUR/USD25B",
  "EUR/USD 25D BF FX Vols 3M")

  static InstrumentDefinition CDS_1Y = InstrumentDefinition.ofCreditCurve(
  "Y4E82RAD3_USD_SNRFOR_CR14",
  CreditSector.INDUSTRIALS,
  CDS,
  "1Y",
  "1Y_Y4E82RAD3_USD_SNRFOR_CR14_SPREAD",
  "Y4E82RAD3 USD SNRFOR CR14 1Y")
}
