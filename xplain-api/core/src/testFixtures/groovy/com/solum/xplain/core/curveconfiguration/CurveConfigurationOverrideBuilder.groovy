package com.solum.xplain.core.curveconfiguration

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS

import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationOverride
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CurveConfigurationOverride)
class CurveConfigurationOverrideBuilder {

  CurveConfigurationOverrideBuilder() {
    priority(1)
    instruments([(CDS): new CurveConfigurationProviderOverride(
      primary: "P", secondary: "S", assetNames: ["EUR"])])
    enabled(true)
  }
}
