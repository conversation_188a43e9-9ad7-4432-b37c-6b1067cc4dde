package com.solum.xplain.core.market

import com.solum.xplain.core.market.value.MarketDataProviderTickerView
import com.solum.xplain.core.market.value.MdkProviderBidAskType

trait MarketDataProviderTickerViewSample {

  private Map MARKET_DATA_PROVIDER_TICKER_VIEW_SAMPLE = [
    code      : "BBG",
    ticker    : "EONIATICK",
    bidAskType: MdkProviderBidAskType.BID_ONLY,
    factor    : BigDecimal.ONE
  ]

  MarketDataProviderTickerView sampleMarketDataProviderTickerView(Map args = [:]) {
    args = MARKET_DATA_PROVIDER_TICKER_VIEW_SAMPLE + args
    def view = new MarketDataProviderTickerView()
    view.setCode(args.code as String)
    view.setTicker(args.ticker as String)
    view.setBidAskType(args.bidAskType as MdkProviderBidAskType)
    view.setFactor(args.factor as BigDecimal)
    view
  }
}
