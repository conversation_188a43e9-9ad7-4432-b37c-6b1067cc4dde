package com.solum.xplain.core.product

import com.solum.xplain.core.portfolio.csv.mapper.CapFloorCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.CdsTradeCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.CommonCreditCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.CommonSwapCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.CreditIndexTradeCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.CreditIndexTrancheCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.FxCollarCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.FxFwdCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.FxOptionCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.FxSwapCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.LoanNoteTradeCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.SwapCsvMapper
import com.solum.xplain.core.portfolio.csv.mapper.SwaptionCsvMapper
import com.solum.xplain.core.product.csv.ProductCsvMapper

trait CoreProductCsvMappers {
  def COMMON_CREDIT_MAPPER = new CommonCreditCsvMapper()
  def COMMON_SWAP_MAPPER = new CommonSwapCsvMapper()

  def CAPFLOOR_MAPPER = new CapFloorCsvMapper()
  def CDS_MAPPER = new CdsTradeCsvMapper(COMMON_CREDIT_MAPPER)
  def CREDIT_INDEX_MAPPER = new CreditIndexTradeCsvMapper(COMMON_CREDIT_MAPPER)
  def CREDIT_INDEX_TRANCHE_MAPPER = new CreditIndexTrancheCsvMapper(COMMON_CREDIT_MAPPER)
  def FX_MAPPER = new FxFwdCsvMapper()
  def FX_SWAP_MAPPER = new FxSwapCsvMapper(FX_MAPPER)
  def FX_OPT_MAPPER = new FxOptionCsvMapper(FX_MAPPER)
  def FX_COLLAR_MAPPER = new FxCollarCsvMapper(FX_OPT_MAPPER)
  def SWAP_MAPPER = new SwapCsvMapper(COMMON_SWAP_MAPPER)
  def LOAN_MAPPER = new LoanNoteTradeCsvMapper()
  def SWAPTION_MAPPER = new SwaptionCsvMapper(COMMON_SWAP_MAPPER)

  List<ProductCsvMapper> CORE_MAPPERS = [
    CAPFLOOR_MAPPER,
    CDS_MAPPER,
    CREDIT_INDEX_MAPPER,
    CREDIT_INDEX_TRANCHE_MAPPER,
    FX_MAPPER,
    FX_SWAP_MAPPER,
    FX_OPT_MAPPER,
    FX_COLLAR_MAPPER,
    SWAP_MAPPER,
    LOAN_MAPPER,
    SWAPTION_MAPPER,
  ]
}
