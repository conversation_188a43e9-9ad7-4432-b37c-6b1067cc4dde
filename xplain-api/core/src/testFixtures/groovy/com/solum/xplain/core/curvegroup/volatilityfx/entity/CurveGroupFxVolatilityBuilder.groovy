package com.solum.xplain.core.curvegroup.volatilityfx.entity

import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.market.MarketDataSample
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CurveGroupFxVolatility, includeSuperProperties = true)
class CurveGroupFxVolatilityBuilder {
  CurveGroupFxVolatilityBuilder() {
    entityId("groupId")
    state(State.ACTIVE)
    validFrom(NewVersionFormV2.ROOT_DATE)
    comment("Version comment")
    recordDate(MarketDataSample.STATE_DATE.recordDate.minusSeconds(1))
    nodes([] as Set)
    timeInterpolator("TimeSquare")
    timeExtrapolatorLeft("Flat")
    timeExtrapolatorRight("Flat")
    strikeInterpolator("Linear")
    strikeExtrapolatorLeft("Flat")
    strikeExtrapolatorRight("Flat")
  }
}
