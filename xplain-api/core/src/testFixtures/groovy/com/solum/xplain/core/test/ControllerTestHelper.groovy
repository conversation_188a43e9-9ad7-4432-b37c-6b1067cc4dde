package com.solum.xplain.core.test

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user

import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.UserRequestPostProcessor

class ControllerTestHelper {
  static UserRequestPostProcessor userWithAuthority(String role) {
    user("user").authorities(new SimpleGrantedAuthority(role))
  }

  static TestingAuthenticationToken tokenWithAuthorities(String... authorities) {
    new TestingAuthenticationToken("user", "password", authorities)
  }
}
