package com.solum.xplain.core.company

import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.market.MarketDataKey
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = CompanyLegalEntity, includeSuperProperties = true)
class CompanyLegalEntityBuilder {

  public static final String NAME = "name"
  public static final String EXTERNAL_ENTITY_ID = "externalEntityId"

  CompanyLegalEntityBuilder(String companyIdStr) {
    id(new ObjectId().toHexString())
    companyId(companyIdStr)
    name(NAME)
    externalId(EXTERNAL_ENTITY_ID)
    description("description")
    allowAllTeams(true)
  }
}
