package com.solum.xplain.core.ipv.group.entity

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = IpvDataGroup)
class IpvDataGroupBuilder {

  public static final String NAME = "Test data group"

  IpvDataGroupBuilder() {
    name(NAME)
    pricingSlot(PricingSlot.LDN_1500)
    allowAllCompanies(true)
    allowAllTeams(true)
  }

  static ipvDataGroup() {
    return new IpvDataGroupBuilder().build()
  }
}
