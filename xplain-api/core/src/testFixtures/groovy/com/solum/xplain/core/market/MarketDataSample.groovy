package com.solum.xplain.core.market


import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.market.mapping.MarketDataUtils.quoteId

import com.opengamma.strata.basics.currency.FxRate
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.data.FxRateId
import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.market.observable.IndexQuoteId
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView
import java.time.LocalDate

class MarketDataSample {

  public static final LocalDate VAL_DT = LocalDate.parse("2017-11-09")
  public static final BitemporalDate STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  static CalculationMarketValueView value(String key, String value) {
    return new CalculationMarketValueView(key: key, value: new BigDecimal(value))
  }

  static ImmutableMarketData marketData() {
    ImmutableMarketData.builder(VAL_DT).build()
  }

  static ImmutableMarketData ogMarketData(LocalDate valuationDate = VAL_DT) {
    ImmutableMarketData.builder(valuationDate)
      .addValue(quoteId("1Y_EUR-FIXED-TERM-EONIA-OIS"), 0.0051 as double)
      .addValue(quoteId("1D_EUR-FIXED-TERM-EONIA-OIS"), 0.0051 as double)
      .addValue(quoteId("1D_USD-FIXED-TERM-FED-FUND-OIS"), 0.0051 as double)
      .addValue(quoteId("1Y_USD-FIXED-TERM-FED-FUND-OIS"), 0.0051 as double)
      .addValue(quoteId("1D_EUR-FIXED-1Y-EURIBOR-3M"), 0.0051 as double)
      .addValue(quoteId("1Y_EUR-FIXED-1Y-EURIBOR-3M"), 0.0051 as double)
      .addValue(quoteId("1D_USD-FIXED-6M-LIBOR-3M"), 0.0051 as double)
      .addValue(quoteId("1Y_USD-FIXED-6M-LIBOR-3M"), 0.0051 as double)
      .addValue(quoteId("1D_GBP-FIXED-TERM-SONIA-OIS"), 0.0051 as double)
      .addValue(quoteId("1Y_GBP-FIXED-TERM-SONIA-OIS"), 0.0051 as double)
      .addValue(quoteId("3MX9M_EUR-EURIBOR-6M"), 0.0051 as double)
      .addValue(quoteId("EUR003M"), 0.0051 as double)
      .addValue(quoteId("1M_GBP-FIXED-ZC-GB-RPI"), 0.0051 as double)
      .addValue(quoteId("1Y_GBP-FIXED-ZC-GB-RPI"), 0.0051 as double)
      .addValue(quoteId("2Y_GBP-FIXED-ZC-GB-RPI"), 0.0051 as double)
      .addValue(quoteId("1M_EUR-FIXED-ZC-EU-AI-CPI"), 0.0051 as double)
      .addValue(quoteId("1Y_EUR-FIXED-ZC-EU-AI-CPI"), 0.0051 as double)
      .addValue(quoteId("2Y_EUR-FIXED-ZC-EU-AI-CPI"), 0.0051 as double)
      .addValue(quoteId("1M_EUR-FIXED-ZC-EU-EXT-CPI"), 0.0051 as double)
      .addValue(quoteId("1Y_EUR-FIXED-ZC-EU-EXT-CPI"), 0.0051 as double)
      .addValue(quoteId("2Y_EUR-FIXED-ZC-EU-EXT-CPI"), 0.0051 as double)
      .addValue(quoteId("1M_EUR-FIXED-ZC-FR-CPI"), 0.0051 as double)
      .addValue(quoteId("1Y_EUR-FIXED-ZC-FR-CPI"), 0.0051 as double)
      .addValue(quoteId("2Y_EUR-FIXED-ZC-FR-CPI"), 0.0051 as double)
      .addValue(quoteId("3M_EUR-EURIBOR-6M"), 0.0051 as double)
      .addValue(quoteId("2D+1_EUR-EURIBOR-3M-IMM-ICE"), 0.0051 as double)
      .addValue(quoteId("1Y_BARC_EUR_SNRFOR_CR14_SPREAD"), 0.0051 as double)
      .addValue(quoteId("2Y_BARC_EUR_SNRFOR_CR14_SPREAD"), 0.0051 as double)
      .addValue(quoteId("1Y_CDX_NA_HY_S2_V1_SPREAD"), 0.0051 as double)
      .addValue(quoteId("2Y_CDX_NA_HY_S2_V1_SPREAD"), 0.0051 as double)
      .addValue(quoteId("1Y_CDX.NA.IG.S39.V1.3-7_SPREAD"), 0.0051 as double)
      .addValue(quoteId("2Y_CDX.NA.IG.S39.V1.3-7_SPREAD"), 0.0051 as double)
      .addValue(FxRateId.of(EUR, GBP), FxRate.of(EUR, GBP, 0.887015358 as double))
      .addValue(FxRateId.of(EUR, USD), FxRate.of(EUR, USD, 1.23885 as double))
      .addValue(FxRateId.of(USD, GBP), FxRate.of(USD, GBP, 0.77777 as double))
      .addValue(quoteId("1M_EUR/USD"), 0.77777 as double)
      .addValue(quoteId("1YV1Y_ATM_EUR-EURIBOR-3M"), 0.004 as double)
      .addValue(quoteId("2YV1Y_ATM_EUR-EURIBOR-3M"), 0.005 as double)
      .addValue(quoteId("1YV2Y_ATM_EUR-EURIBOR-3M"), 0.005 as double)
      .addValue(quoteId("2YV2Y_ATM_EUR-EURIBOR-3M"), 0.006 as double)
      .addValue(quoteId("1YV1Y_1%_EUR-EURIBOR-3M"), 0.008 as double)
      .addValue(quoteId("2YV1Y_1%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("1YV2Y_1%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("2YV2Y_1%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("1YV1Y_10%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("2YV1Y_10%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("1YV2Y_10%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("2YV2Y_10%_EUR-EURIBOR-3M"), 0.009 as double)
      .addValue(quoteId("1YV3Y_10%_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("2YV5Y_10%_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("2YV6Y_10%_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("1YV1Y_0_EUR-EURIBOR-3M"), 0.01 as double)
      .addValue(quoteId("2YV1Y_0_EUR-EURIBOR-3M"), 0.02 as double)
      .addValue(quoteId("1YV2Y_0_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("2YV2Y_0_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("1YV1Y_+1_EUR-EURIBOR-3M"), 0.01 as double)
      .addValue(quoteId("2YV1Y_+1_EUR-EURIBOR-3M"), 0.02 as double)
      .addValue(quoteId("1YV2Y_+1_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("2YV2Y_+1_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("1YV1Y_+10_EUR-EURIBOR-3M"), 0.01 as double)
      .addValue(quoteId("2YV1Y_+10_EUR-EURIBOR-3M"), 0.02 as double)
      .addValue(quoteId("1YV2Y_+10_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("2YV2Y_+10_EUR-EURIBOR-3M"), 0.03 as double)
      .addValue(quoteId("1Y_57.00%_CF_EUR-EURIBOR-3M"), 0.11 as double)
      .addValue(quoteId("2Y_57.00%_CF_EUR-EURIBOR-3M"), 0.12 as double)
      .addValue(quoteId("3Y_57.00%_CF_EUR-EURIBOR-3M"), 0.13 as double)
      .addValue(quoteId("1Y_67.00%_CF_EUR-EURIBOR-3M"), 0.21 as double)
      .addValue(quoteId("2Y_67.00%_CF_EUR-EURIBOR-3M"), 0.22 as double)
      .addValue(quoteId("3Y_67.00%_CF_EUR-EURIBOR-3M"), 0.23 as double)
      .addValue(quoteId("1Y_EUR/USDV"), 0.01 as double)
      .addValue(quoteId("2Y_EUR/USDV"), 0.02 as double)
      .addValue(quoteId("3Y_EUR/USDV"), 0.03 as double)
      .addValue(quoteId("1Y_EUR/USD1B"), 0.04 as double)
      .addValue(quoteId("1Y_EUR/USD1R"), 0.05 as double)
      .addValue(quoteId("1Y_EUR/USD2B"), 0.06 as double)
      .addValue(quoteId("1Y_EUR/USD2R"), 0.07 as double)
      .addValue(quoteId("2Y_EUR/USD1B"), 0.40 as double)
      .addValue(quoteId("2Y_EUR/USD1R"), 0.50 as double)
      .addValue(quoteId("2Y_EUR/USD2B"), 0.60 as double)
      .addValue(quoteId("2Y_EUR/USD2R"), 0.70 as double)
      .addValue(quoteId("1D_EUR-EURIBOR-3M-USD-LIBOR-3M"), 0.55 as double)
      .addValue(quoteId("1Y_EUR-EURIBOR-3M-USD-LIBOR-3M"), 0.55 as double)
      .addValue(quoteId("1D_EUR-ESTR-USD-SOFR"), 0.55 as double)
      .addValue(quoteId("1Y_EUR-ESTR-USD-SOFR"), 0.55 as double)
      .addValue(quoteId("1D_EUR-FIXED-TERM-ESTR-OIS"), 0.0051 as double)
      .addValue(quoteId("1Y_EUR-FIXED-TERM-ESTR-OIS"), 0.0051 as double)
      .addValue(quoteId("1D_USD-FIXED-TERM-SOFR-OIS"), 0.0051 as double)
      .addValue(quoteId("1Y_USD-FIXED-TERM-SOFR-OIS"), 0.0051 as double)
      .addValue(quoteId("01JAN2023_CUSIP"), 0.1 as double)
      .addValue(quoteId("01JAN2024_CUSIP"), 0.1 as double)
      .addValue(quoteId("2D+1_AUD-BBSW-3M-QUARTERLY-FUTURE"), 0.1 as double)
      .addTimeSeries(IndexQuoteId.of(PriceIndices.GB_RPI), LocalDateDoubleTimeSeries.of(LocalDate.of(2017, 1, 1), 100))
      .addTimeSeries(IndexQuoteId.of(PriceIndices.EU_AI_CPI), LocalDateDoubleTimeSeries.of(LocalDate.of(2017, 1, 1), 100))
      .addTimeSeries(IndexQuoteId.of(PriceIndices.EU_EXT_CPI), LocalDateDoubleTimeSeries.of(LocalDate.of(2017, 1, 1), 100))
      .addTimeSeries(IndexQuoteId.of(PriceIndices.FR_EXT_CPI), LocalDateDoubleTimeSeries.of(LocalDate.of(2017, 1, 1), 100))
      .build()
  }
}
