package com.solum.xplain.core.ipv.data.entity

import com.solum.xplain.core.market.MarketDataKey
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDate

@Builder(builderStrategy = ExternalStrategy, forClass = IpvDataValue, includeSuperProperties = true)
class IpvDataValueBuilder {
  IpvDataValueBuilder() {
    date(LocalDate.now())
    resolved(true)
  }
}
