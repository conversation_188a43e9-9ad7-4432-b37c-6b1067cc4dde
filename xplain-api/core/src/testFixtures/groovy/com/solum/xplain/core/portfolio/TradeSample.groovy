package com.solum.xplain.core.portfolio


import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.basics.date.HolidayCalendarIds.SAT_SUN
import static com.opengamma.strata.product.common.BuySell.BUY
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swaption.CashSwaptionSettlementMethod.PAR_YIELD
import static java.time.LocalDate.now
import static java.time.LocalDate.parse

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.AdjustablePayment
import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.basics.date.AdjustableDate
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.MarketTenor
import com.opengamma.strata.basics.date.SequenceDate
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.SecurityId
import com.opengamma.strata.product.TradeInfo
import com.opengamma.strata.product.common.LongShort
import com.opengamma.strata.product.fx.FxSwapTrade
import com.opengamma.strata.product.fx.type.FxSwapConventions
import com.opengamma.strata.product.index.type.IborFutureTemplate
import com.opengamma.strata.product.swap.CompoundingMethod
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention
import com.opengamma.strata.product.swap.type.FixedInflationSwapConventions
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention
import com.opengamma.strata.product.swap.type.ImmutableIborIborSwapConvention
import com.opengamma.strata.product.swap.type.OvernightIborSwapConventions
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConventions
import com.opengamma.strata.product.swaption.CashSwaptionSettlement
import com.opengamma.strata.product.swaption.Swaption
import com.opengamma.strata.product.swaption.SwaptionTrade
import com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions
import com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions
import com.solum.xplain.extensions.product.ExtendedIborContractSpecs
import java.time.LocalDate
import java.time.LocalTime
import java.time.Period
import java.time.YearMonth
import java.time.ZoneId

class TradeSample {
  final static MODIFIED_FOLLOWING_SAT_SUN = BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN)

  static cashSwaptionTrade() {
    return cashSwaptionTrade(parse("2018-04-12"))
  }

  static SwaptionTrade cashSwaptionTrade(LocalDate tradeDate) {
    def swapTrade = EUR_FIXED_1Y_EURIBOR_3M.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())

    def swaption = Swaption.builder()
      .expiryDate(AdjustableDate.of(parse("2018-04-12"), MODIFIED_FOLLOWING_SAT_SUN))
      .expiryTime(LocalTime.NOON)
      .expiryZone(ZoneId.of("Europe/London"))
      .longShort(LongShort.LONG)
      .swaptionSettlement(CashSwaptionSettlement.of(parse("2018-05-23"), PAR_YIELD))
      .underlying(swapTrade.getProduct())
      .build()

    def premium = AdjustablePayment.of(CurrencyAmount.of(EUR, 10000), AdjustableDate.of(parse("2019-04-12"), MODIFIED_FOLLOWING_SAT_SUN))
    return SwaptionTrade.of(
      TradeInfo.builder()
      .tradeDate(tradeDate)
      .settlementDate(parse("2018-05-23"))
      .build(),
      swaption,
      premium)
  }

  static swapTrade() {
    swapTrade(parse("2018-04-12"))
  }

  static FxSwapTrade fxSwapTrade() {
    FxSwapConventions.EUR_USD.createTrade(
      now(),
      MarketTenor.ON,
      BUY,
      10,
      10,
      10,
      ReferenceData.standard())
  }

  static IborFutureTemplate iborFutureTrade() {
    ExtendedIborContractSpecs.AUD_BBSW_3_M_QUARTERLY_FUTURE
      .createTrade(LocalDate.of(2018, 1, 1),
      SecurityId.of("SECURITY", "1"),
      SequenceDate.base(YearMonth.from(LocalDate.of(2018, 1, 1))),
      10_000_000d,
      0.025d,
      ReferenceData.standard()) as IborFutureTemplate
  }

  static swapTrade(LocalDate tradeDate) {
    EUR_FIXED_1Y_EURIBOR_3M.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeOffshore(LocalDate tradeDate) {
    ExtendedFixedIborSwapConventions.THB_FIXED_6M_THBFIX_6M_OFFSHORE.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTrade(LocalDate tradeDate, Frequency frequency) {
    def convention = ImmutableIborIborSwapConvention.of(
      "USD-LIBOR-3M-LIBOR-3M",
      IborRateSwapLegConvention.builder()
      .index(IborIndices.USD_LIBOR_3M)
      .paymentFrequency(frequency)
      .accrualFrequency(frequency)
      .compoundingMethod(CompoundingMethod.FLAT)
      .stubConvention(StubConvention.SHORT_INITIAL)
      .build(),
      IborRateSwapLegConvention.of(IborIndices.USD_LIBOR_3M))

    convention.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeCross(LocalDate tradeDate) {
    XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M.createTrade(
      tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      11_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeOvernight(LocalDate tradeDate) {
    OvernightIborSwapConventions.USD_FED_FUND_AA_LIBOR_3M.createTrade(
      tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeOvernightOffshore(LocalDate tradeDate) {
    ExtendedFixedOvernightSwapConventions.THB_FIXED_3M_THOR_OIS_OFFSHORE.createTrade(
      tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeBrlOvernight(LocalDate tradeDate) {
    FixedOvernightSwapConvention.of("BRL-FIXED-TERM-CDI-OIS").createTrade(
      tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeInflation(LocalDate tradeDate) {
    FixedInflationSwapConventions.EUR_FIXED_ZC_EU_AI_CPI
      .createTrade(tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeInflationCleared(LocalDate tradeDate) {
    FixedInflationSwapConvention.of("GBP-FIXED-ZC-GB-RPI-CLEARED")
      .createTrade(tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapTradeOisOisCross(LocalDate tradeDate) {
    XCcyOvernightOvernightSwapConventions.EUR_ESTR_3M_USD_SOFR_3M.createTrade(
      tradeDate,
      Tenor.of(Period.ofYears(8)),
      BUY,
      10_000_000d,
      11_000_000d,
      0.025d,
      ReferenceData.standard())
  }
}
