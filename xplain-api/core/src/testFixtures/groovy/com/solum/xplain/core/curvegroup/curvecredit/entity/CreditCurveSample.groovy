package com.solum.xplain.core.curvegroup.curvecredit.entity


import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNodeBuilder.creditCurveCdsNode

import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import java.time.LocalDate

class CreditCurveSample {
  static CreditCurve barcCreditCurve(LocalDate date) {
    new CreditCurveBuilder()
      .validFrom(date)
      .cdsNodes([creditCurveCdsNode("1Y"), creditCurveCdsNode("2Y")])
      .build()
  }

  static CreditCurve self(LocalDate date) {
    new CreditCurveBuilder()
      .validFrom(date)
      .corpTicker("SELF")
      .name("SELF_EUR_SNRFOR_CR14")
      .cdsNodes([creditCurveCdsNode("1Y"), creditCurveCdsNode("2Y")])
      .build()
  }

  static CreditCurve creditIndex(LocalDate date) {
    new CreditCurveBuilder()
      .validFrom(date)
      .curveType(CreditCurveType.CREDIT_INDEX)
      .creditIndexVersion(1)
      .creditIndexSeries(2)
      .currency("USD")
      .corpTicker("SELF")
      .name("CDX_NA_HY_S2_V1")
      .entityLongName("CDX_NA_HY")
      .creditIndexFactor(BigDecimal.ONE)
      .cdsNodes([creditCurveCdsNode("1Y"), creditCurveCdsNode("2Y")])
      .build()
  }
}
