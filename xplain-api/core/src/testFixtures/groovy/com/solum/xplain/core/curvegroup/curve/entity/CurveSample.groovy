package com.solum.xplain.core.curvegroup.curve.entity

import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.EUR_EURIBOR_3M_IMM_ICE
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_6M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_EU_AI_CPI
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.EUR_FIXED_TERM_EONIA_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.EUR_FIXED_TERM_ESTR_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.GBP_FIXED_TERM_SONIA_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.USD_FIXED_TERM_FED_FUND_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.USD_FIXED_TERM_SOFR_OIS
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_OIS_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.fraCurveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.iborFutureCurveNode
import static com.solum.xplain.extensions.product.ExtendedFixedIborSwapConventions.CNY_FIXED_3M_REPO_1W
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.EUR_ESTR_USD_SOFR

import com.opengamma.strata.basics.index.IborIndices
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions

class CurveSample {

  static List<Curve> calibrationCurves() {
    [eurOis(), eur3m(), usdOis(), usd3m(), eurUsdBasis(),]
  }

  static Curve eurOis() {
    new CurveBuilder()
      .name("EUR EONIA")
      .entityId("EUR-EONIA")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_TERM_EONIA_OIS.name, "1D"),
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_TERM_EONIA_OIS.name, "1Y")
      ])
      .build()
  }

  static Curve eur3m() {
    new CurveBuilder()
      .name("EUR 3M")
      .entityId("EUR3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1D"),
        curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1Y")
      ])
      .build()
  }

  static Curve usdOis() {
    new CurveBuilder()
      .name("USD FEDFUNDS")
      .entityId("USDOIS")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_FED_FUND_OIS.name, "1D"),
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_FED_FUND_OIS.name, "1Y")
      ])
      .build()
  }

  static Curve usd3m() {
    new CurveBuilder()
      .name("USD 3M")
      .entityId("USD3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name, "1D"),
        curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name, "1Y")
      ])
      .build()
  }

  static Curve cny1W() {
    new CurveBuilder()
      .name("CNY 1W")
      .entityId("CNY1W")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_IBOR_SWAP_NODE, CNY_FIXED_3M_REPO_1W.name, "1D"),
        curveNode(FIXED_IBOR_SWAP_NODE, CNY_FIXED_3M_REPO_1W.name, "1Y")
      ])
      .build()
  }

  static Curve eurUsdBasis() {
    new CurveBuilder()
      .name("EUR/USD")
      .entityId("EURUSDBASIS")
      .curveType(CurveType.XCCY)
      .nodes([
        curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name, "1D"),
        curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name, "1Y")
      ])
      .build()
  }

  static Curve gbpOis() {
    new CurveBuilder()
      .name("GBP SONIA")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, GBP_FIXED_TERM_SONIA_OIS.name, "1D"),
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, GBP_FIXED_TERM_SONIA_OIS.name, "1Y")
      ])
      .build()
  }


  static Curve eurUsdOisOis() {
    new CurveBuilder()
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([
        curveNode(XCCY_OIS_OIS_SWAP_NODE, EUR_ESTR_USD_SOFR.name, "1D"),
        curveNode(XCCY_OIS_OIS_SWAP_NODE, EUR_ESTR_USD_SOFR.name, "1Y")
      ])
      .build()
  }

  static Curve eurUsdIborOis() {
    new CurveBuilder()
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([
        curveNode(XCCY_IBOR_OIS_SWAP_NODE, StandardXCcyIborOvernightSwapConventions.EUR_EURIBOR_3M_USD_SOFR.name , "1D"),
        curveNode(XCCY_IBOR_OIS_SWAP_NODE, StandardXCcyIborOvernightSwapConventions.EUR_EURIBOR_3M_USD_SOFR.name, "1Y")
      ])
      .build()
  }

  static Curve eurUsdFx() {
    new CurveBuilder()
      .name("EUR/USD")
      .entityId("EURUSDFX")
      .curveType(CurveType.XCCY)
      .build()
  }

  static Curve gbpUsdFx() {
    new CurveBuilder()
      .name("GBP/USD")
      .entityId("GBPUSDFX")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, "GBP/USD", "1D"), curveNode(FX_SWAP_NODE, "GBP/USD", "1Y")])
      .build()
  }

  static Curve usdOisSofr() {
    new CurveBuilder()
      .name("USD SOFR")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1D"),
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1Y")
      ])
      .build()
  }

  static Curve eur6m() {
    new CurveBuilder()
      .name("EUR 6M")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1D"),
        curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1Y"),
        fraCurveNode(IborIndices.EUR_EURIBOR_6M.name, "6M", "3M"),
        curveNode(IBOR_FIXING_DEPOSIT_NODE, IborIndices.EUR_EURIBOR_6M.name, "3M"),
        iborFutureCurveNode(EUR_EURIBOR_3M_IMM_ICE.name, "2D+1")
      ])
      .build()
  }

  static Curve eurEstrOis() {
    new CurveBuilder()
      .name("EUR ESTR")
      .entityId("EUR-ESTR")
      .curveType(CurveType.IR_INDEX)
      .nodes([
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_TERM_ESTR_OIS.name, "1D"),
        curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_TERM_ESTR_OIS.name, "1Y")
      ])
      .build()
  }


  static Curve gbpInflation() {
    new CurveBuilder()
      .name("GB RPI")
      .curveType(CurveType.INFLATION_INDEX)
      .nodes([
        curveNode(FIXED_INFLATION_SWAP_NODE, GBP_FIXED_ZC_GB_RPI.name, "1Y"),
        curveNode(FIXED_INFLATION_SWAP_NODE, GBP_FIXED_ZC_GB_RPI.name, "2Y")
      ])
      .build()
  }

  static Curve eurInflation() {
    new CurveBuilder()
      .name("EU AI CPI")
      .curveType(CurveType.INFLATION_INDEX)
      .nodes([
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_AI_CPI.name, "1Y"),
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_AI_CPI.name, "2Y")
      ])
      .build()
  }

  static Curve eurExtInflation() {
    new CurveBuilder()
      .name("EU EXT CPI")
      .curveType(CurveType.INFLATION_INDEX)
      .nodes([
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_EXT_CPI.name, "1Y"),
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_EXT_CPI.name, "2Y")
      ])
      .build()
  }

  static Curve eurExtInflationLch() {
    new CurveBuilder()
      .name("EU EXT CPI LCH")
      .curveType(CurveType.INFLATION_INDEX)
      .nodes([
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_AI_CPI.name, "1Y"),
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_EU_AI_CPI.name, "2Y")
      ])
      .build()
  }

  static Curve frInflation() {
    new CurveBuilder()
      .name("FR EXT CPI")
      .curveType(CurveType.INFLATION_INDEX)
      .nodes([
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_FR_CPI.name, "1Y"),
        curveNode(FIXED_INFLATION_SWAP_NODE, EUR_FIXED_ZC_FR_CPI.name, "2Y")
      ])
      .build()
  }
}
