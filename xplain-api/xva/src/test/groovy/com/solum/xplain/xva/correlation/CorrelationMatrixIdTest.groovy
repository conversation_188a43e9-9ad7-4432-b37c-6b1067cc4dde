package com.solum.xplain.xva.correlation

import static CorrelationMatrixId.of
import static com.solum.xplain.xva.correlation.value.CorrelationType.FX
import static com.solum.xplain.xva.correlation.value.CorrelationType.IR

import spock.lang.Specification
import spock.lang.Unroll

class CorrelationMatrixIdTest extends Specification {

  @Unroll
  "should convert #ccy1 #ccy2 to conventional pair #expected"() {
    setup:
    def id = of(ccy1, type1, ccy2, type2)

    expect:
    id.toConventional() == expected

    where:
    ccy1  | type1 | ccy2  | type2 | expected
    "USD" | FX    | "EUR" | IR    | of("EUR", IR, "USD", FX)
    "EUR" | IR    | "USD" | FX    | of("EUR", IR, "USD", FX)
    "EUR" | IR    | "EUR" | FX    | of("EUR", IR, "EUR", FX)
    "EUR" | FX    | "EUR" | IR    | of("EUR", IR, "EUR", FX)
    "USD" | IR    | "EUR" | FX    | of("EUR", FX, "USD", IR)
    "USD" | FX    | "EUR" | FX    | of("EUR", FX, "USD", FX)
    "EUR" | FX    | "USD" | IR    | of("EUR", FX, "USD", IR)
    "XAG" | IR    | "USD" | IR    | of("USD", IR, "XAG", IR)
    "USD" | IR    | "XAG" | IR    | of("USD", IR, "XAG", IR)
  }
}
