package com.solum.xplain.xva.integration

import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.KeyValueCache
import com.solum.xplain.xva.integration.data.XvaCreditCurveNames
import com.solum.xplain.xva.integration.data.XvaValuationsCache
import org.bson.types.ObjectId
import spock.lang.Specification

class XvaValuationsCacheTest extends Specification {
  static CALCULATION_ID = ObjectId.get().toHexString()

  DataGrid dataGrid = Mock()

  XvaValuationsCache cache = new XvaValuationsCache(dataGrid)

  def "should cache curve names mapping"() {
    setup:
    def keyValueCache = Mock(KeyValueCache)
    def curveNames = ["TICKER": "LONG_NAME"]

    when:
    cache.cacheCurveNamesMapping(CALCULATION_ID, curveNames)

    then:
    1 * dataGrid.getKeyValueCache("XVA_CURVES") >> keyValueCache
    1 * keyValueCache.set(CALCULATION_ID, new XvaCreditCurveNames(curveNames))
  }

  def "should get curve names mapping"() {
    setup:
    def keyValueCache = Mock(KeyValueCache)
    def curveNames = ["TICKER": "LONG_NAME"]

    1 * dataGrid.getKeyValueCache("XVA_CURVES") >> keyValueCache
    1 * keyValueCache.get(CALCULATION_ID) >> new XvaCreditCurveNames(curveNames)
    when:
    def result = cache.getCurveNamesMapping(CALCULATION_ID)

    then:
    result == curveNames
  }

  def "should get empty curve names mapping when n"() {
    setup:
    def keyValueCache = Mock(KeyValueCache)
    1 * dataGrid.getKeyValueCache("XVA_CURVES") >> keyValueCache
    when:
    def result = cache.getCurveNamesMapping(CALCULATION_ID)

    then:
    result == [:]
  }

  def "should clear cache"() {
    setup:
    def keyValueCache = Mock(KeyValueCache)

    when:
    cache.clearCache(CALCULATION_ID)

    then:
    1 * dataGrid.getKeyValueCache("XVA_CURVES") >> keyValueCache
    1 * keyValueCache.delete(CALCULATION_ID)
  }
}
