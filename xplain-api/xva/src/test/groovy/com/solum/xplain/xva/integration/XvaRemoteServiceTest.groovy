package com.solum.xplain.xva.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.core.portfolio.PortfolioBuilder
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.PortfolioViewBuilder
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import com.solum.xplain.xva.calculation.XvaCalculationTrades
import com.solum.xplain.xva.calculation.constants.CalculationLevel
import com.solum.xplain.xva.calculation.constants.CalculationPerspective
import com.solum.xplain.xva.calculation.value.XvaPortfolioCalculationData
import com.solum.xplain.xva.integration.data.S3DataStore
import com.solum.xplain.xva.integration.data.XvaRemoteDataService
import com.solum.xplain.xva.integration.data.XvaValuationsCache
import com.solum.xplain.xva.integration.trades.XvaTradesFileService
import com.solum.xplain.xva.proxy.messages.XvaMarketFile
import com.solum.xplain.xva.settings.value.XvaSettingsView
import java.nio.file.Path
import java.util.stream.Stream
import org.bson.types.ObjectId
import spock.lang.Specification
import spock.lang.TempDir

class XvaRemoteServiceTest extends Specification {
  static CALCULATION_ID = ObjectId.get()
  @TempDir
  Path tempFilesDir
  S3DataStore s3DataStore = Mock()
  ObjectMapper objectMapper = Mock()
  XvaTradesFileService tradesFileService = Mock()
  XvaValuationsCache valuationsCache = Mock()

  XvaRemoteDataService service

  def setup() {
    service = new XvaRemoteDataService(tempFilesDir, s3DataStore, objectMapper, tradesFileService, valuationsCache)
  }

  def "should invoke remote XVA library"() {
    setup:
    def data = xvaPortfolioCalculationData()
    when:
    service.storeData(data)

    then:
    1 * objectMapper.writeValue(_, data.marketFile)
    1 * tradesFileService.writeTradesToFile(_, data.getCalculationTrades().getTrades()) >> []
    1 * s3DataStore.uploadFile({ path -> assert path.toString().contains("marketFile.json") })
    1 * s3DataStore.uploadFile({ path -> assert path.toString().contains("trades.csv") })
    1 * valuationsCache.cacheCurveNamesMapping(CALCULATION_ID.toHexString(), data.getCounterPartyNames())
  }

  def xvaPortfolioCalculationData() {
    def calculationTrades = Mock(CalculationTrades)
    calculationTrades.tradesStream() >> Stream.of(PortfolioItemBuilder.irsPortfolioItem())

    XvaPortfolioCalculationData.builder()
      .calculationId(CALCULATION_ID)
      .counterPartyNames(Map.of())
      .calculationTrades(new XvaCalculationTrades(calculationTrades, []))
      .calculationPerspective(CalculationPerspective.COUNTERPARTY)
      .calculationLevel(CalculationLevel.COUNTERPARTY)
      .portfolioView(PortfolioViewBuilder.of(PortfolioBuilder.portfolio()))
      .calculationType(PortfolioCalculationType.XVA)
      .marketFile(new XvaMarketFile([:]))
      .settings(new XvaSettingsView())
      .build()
  }
}
