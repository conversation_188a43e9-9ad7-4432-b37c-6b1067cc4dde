package com.solum.xplain.xva.integration.csv

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.xva.calculation.entity.XvaPartyResult
import spock.lang.Specification

class XvaPartyResultCsvMapperTest extends Specification {

  def "should return empty csv row for empty XVA result"() {
    setup:
    def partyResult = new XvaPartyResult()

    when:
    def row = XvaPartyResultCsvMapper.toCsvRow(partyResult)
    def csv = new CsvOutputFile(XvaPartyResultCsvMapper.XVA_RESULTS_FIELDS, [row])
    def result = csv.write()

    then:
    result == """Counterparty,Trades,CVA,DVA,FCA,FBA,BilateralCVA,FVA
,,,,,,,
"""
  }

  def "should return full csv row for XVA result"() {
    setup:
    def partyResult = new XvaPartyResult(
      partyFullName: "PARTY",
      numTrades: 1,
      cva: 2,
      dva: 3,
      fca: 4,
      fba: 5,
      bilateralCva: 6,
      fva: 7
      )

    when:
    def row = XvaPartyResultCsvMapper.toCsvRow(partyResult)
    def csv = new CsvOutputFile(XvaPartyResultCsvMapper.XVA_RESULTS_FIELDS, [row])
    def result = csv.write()

    then:
    result == """Counterparty,Trades,CVA,DVA,FCA,FBA,BilateralCVA,FVA
PARTY,1,2,3,4,5,6,7
"""
  }
}
