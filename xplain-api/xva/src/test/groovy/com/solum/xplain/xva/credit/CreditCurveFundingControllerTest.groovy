package com.solum.xplain.xva.credit

import static com.solum.xplain.core.curvemarket.CurveMarketSample.MARKET_STATE_FORM
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static java.util.Collections.emptyList
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveExportService
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveService
import com.solum.xplain.core.curvegroup.curvecredit.value.MdkExportForm
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.xva.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@MockMvcConfiguration
@WebMvcTest(controllers = [CreditCurveFundingController])
class CreditCurveFundingControllerTest extends Specification {
  @SpringBean
  private CurveGroupCreditCurveService service = Mock()
  @SpringBean
  private CurveGroupCreditCurveExportService exportService = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper mapper

  @WithMockUser
  def "should get credit curve funding nodes"() {
    setup:
    1 * service.getCurveFundingNodes("groupId", "1", parse("2016-01-01"), MARKET_STATE_FORM) >> right(emptyList())

    when:
    def results = mockMvc.perform(get("/curve-group/groupId/credit-curves/1/2016-01-01/funding-nodes")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    results.response.status == 200
  }

  def "should get credit curve funding nodes CSV"() {
    setup:
    exportService.getCurveFundingNodesCsv(
      _ as String,
      _ as String,
      _ as LocalDate,
      _ as CurveConfigMarketStateForm,
      null
      ) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves/id/2016-01-01/funding-nodes/csv")
      .param("curveGroupStateDate", "2018-05-05")
      .param("marketDataGroupId", "id")
      .param("configurationId", "id")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    results.getResponse().getStatus() == 200
  }

  def "should get funding nodes market data definitions csv"() {
    setup:
    def exportForm = [
      "stateDate"      : LocalDate.now(),
      "curveIds"       : ["00000000"],
      "configurationId": "0000000"
    ]

    exportService.getFundingNodesMDKDefinitionsCsv(
      "curveGroupId",
      _ as MdkExportForm,
      ) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/mdk-definitions/funding-nodes/csv")
      .content(mapper.writeValueAsString(exportForm))
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    results.getResponse().getStatus() == 200
  }
}
