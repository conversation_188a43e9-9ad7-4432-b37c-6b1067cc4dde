package com.solum.xplain.xva.calculation

import static com.solum.xplain.core.common.EntityId.entityId
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.xva.calculation.value.PortfolioXvaCalculationForm
import com.solum.xplain.xva.correlation.CorrelationMatrixRepository
import com.solum.xplain.xva.correlation.value.CorrelationMatrixView
import com.solum.xplain.xva.helpers.MockMvcConfiguration
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.function.Supplier
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PortfolioXvaCalculationController])
class PortfolioXvaCalculationControllerTest extends Specification {

  static LocalDate STATE_DATE = LocalDate.ofEpochDay(0)
  static String COMPANY_ID = "000000000000000000000000"
  static String PORTFOLIO_ID = "000000000000000000000001"
  static String CONFIGURATION_ID = "000000000000000000000002"

  @SpringBean
  XvaCalculationService calculationService = Mock()
  @SpringBean
  ResourceValidationService resourceValidationService = Mock()
  @SpringBean
  CompanyMarketDataGroupValidator mdValidator = Mock()
  @SpringBean
  CurveConfigurationRepository configurationRepository = Mock()
  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  PortfolioRepository portfolioRepository = Mock()
  @SpringBean
  CorrelationMatrixRepository correlationMatrixRepository = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should perform portfolio calculation with form #form"() {
    setup:
    def stateDate = BitemporalDate.newOf(STATE_DATE)
    calculationService.calculateXVAResults(
      _ as Authentication,
      _ as String,
      _ as PortfolioXvaCalculationForm,
      { it.getActualDate() == stateDate.actualDate } as BitemporalDate) >> right(entityId("1"))
    portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(new PortfolioView(companyId: COMPANY_ID))
    mdValidator.isMdAllowedForCompany("marketDataId", COMPANY_ID) >> true
    mdValidator.isMdAllowedForCompany("badId", COMPANY_ID) >> false
    configurationRepository.validCurveConfigurationId(CONFIGURATION_ID) >> true
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> { locks, supplier ->
      right(supplier.get())
    }
    correlationMatrixRepository.correlation("000000000000000000000003", _ as BitemporalDate) >> right(new CorrelationMatrixView(domesticCcy: "EUR"))
    correlationMatrixRepository.correlation("000000000000000000000004", _ as BitemporalDate) >> right(new CorrelationMatrixView(domesticCcy: "USD"))
    correlationMatrixRepository.correlation(_ as String, _ as BitemporalDate) >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    when:
    def results = mockMvc.perform(post("/portfolio/{id}/calculation-xva", PORTFOLIO_ID)
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    results.getResponse().getStatus() == code
    with(results.getResponse()) {
      status == code
      contentAsString.contains(response)
    }
    where:
    form                                                                    | code | response
    form()                                                                  | 200  | "id"
    form({ c ->
      c.remove("valuationDate")
    })                                | 412  | "NotNull.portfolioXvaCalculationForm.valuationDate"
    form({ c ->
      c.remove("curveDate")
    })                                    | 412  | "NotNull.portfolioXvaCalculationForm.curveDate"
    form({ c ->
      c.remove("curveDate")
    })                                    | 412  | "NotNull.portfolioXvaCalculationForm.curveDate"
    form({ c ->
      c.remove("stateDate")
    })                                    | 412  | "NotNull.portfolioXvaCalculationForm.stateDate"
    form({ c ->
      c.remove("calculationCurrency")
    })                          | 412  | "NotEmpty.portfolioXvaCalculationForm.calculationCurrency"
    form({ c ->
      c.remove("discountingType")
    })                              | 412  | "NotEmpty.portfolioXvaCalculationForm.discountingType"
    form({ c ->
      c.put("discountingType", "LOCAL_CURRENCY")
    })               | 412  | "ValidStringSet.portfolioXvaCalculationForm.discountingType"
    form({ c ->
      c.put("discountingType", "LOCAL_CURRENCY")
    })               | 412  | "ValidStringSet.portfolioXvaCalculationForm.discountingType"
    form({ c ->
      c.put("curveConfiguration", calculationConfig("id"))
    })     | 412  | "ValidCurveConfigurationId.portfolioXvaCalculationForm.curveConfiguration.configurationId"
    form({ c ->
      c.remove("marketDataGroupId")
    })                            | 412  | "NotEmpty.portfolioXvaCalculationForm.marketDataGroupId"
    form({ c ->
      c.put("marketDataGroupId", "badId")
    })                      | 412  | "Calculation market data group is not valid"
    form({ c ->
      c.put("marketDataGroupId", "badId")
    })                      | 412  | "Calculation market data group is not valid"
    form({ c ->
      c.remove("calculationType")
    })                              | 412  | "NotEmpty.portfolioXvaCalculationForm.calculationType"
    form({ c ->
      c.put("calculationType", "whoops")
    })                       | 412  | "ValidStringSet.portfolioXvaCalculationForm.calculationType"
    form({ c ->
      c.remove("calculationPerspective")
    })                       | 412  | "NotEmpty.portfolioXvaCalculationForm.calculationPerspective"
    form({ c ->
      c.put("calculationPerspective", "whoops")
    })                | 412  | "ValidStringSet.portfolioXvaCalculationForm.calculationPerspective"
    form({ c ->
      c.put("correlationMatrixId", "whoops")
    })                   | 412  | "ValidObjectId.portfolioXvaCalculationForm.correlationMatrixId"
    form({ c ->
      c.put("correlationMatrixId", "000000000000000000000004")
    }) | 412  | "Correlation matrix currency must match discount currency"
    form({ c ->
      c.remove("marketDataSource")
    })                             | 412  | "NotEmpty.portfolioXvaCalculationForm.marketDataSource"
    form({ c ->
      c.put("marketDataSource", "whoops")
    })                      | 412  | "ValidStringSet.portfolioXvaCalculationForm.marketDataSource"
    form({ c ->
      c.put("tradeId", "whoops")
    })                               | 412  | "ValidObjectId.portfolioXvaCalculationForm.tradeId"
    form({ c ->
      c.put("tradeId", new ObjectId().toHexString())
    })           | 200  | "id"
  }

  def form(Closure c = { a -> a }) {
    [
      valuationDate         : LocalDate.now(),
      curveDate             : LocalDate.now().plusDays(1),
      stateDate             : LocalDate.ofEpochDay(0),
      calculationCurrency   : "EUR",
      discountingType       : "DISCOUNT_EUR",
      triangulationCcy      : "USD",
      curveConfiguration    : calculationConfig(CONFIGURATION_ID),
      marketDataGroupId     : "marketDataId",
      calculationType       : "XVA",
      calculationPerspective: "SELF",
      marketDataSource      : "RAW_PRIMARY",
      correlationMatrixId   : "000000000000000000000003",
    ].tap(c)
  }

  def calculationConfig(String id) {
    return [
      "configurationId": id
    ]
  }
}
