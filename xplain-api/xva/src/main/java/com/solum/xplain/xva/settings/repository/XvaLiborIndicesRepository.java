package com.solum.xplain.xva.settings.repository;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.settings.GenericVersionedSettingsRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.settings.XvaLiborIndicesMapper;
import com.solum.xplain.xva.settings.entity.XvaLiborIndices;
import com.solum.xplain.xva.settings.value.XvaLiborIndexForm;
import com.solum.xplain.xva.settings.value.XvaLiborIndicesView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

@Repository
public class XvaLiborIndicesRepository
    extends GenericVersionedSettingsRepository<XvaLiborIndices, XvaLiborIndicesView> {

  private final XvaLiborIndicesMapper mapper;

  public XvaLiborIndicesRepository(XvaLiborIndicesMapper mapper, MongoOperations mongoOperations) {
    super(mongoOperations, mapper, XvaLiborIndices::empty);
    this.mapper = mapper;
  }

  public XvaLiborIndices getXvaLiborIndices(BitemporalDate stateDate) {
    return activeEntityOrEmpty(stateDate);
  }

  public Either<ErrorItem, EntityId> saveLiborIndices(LocalDate stateDate, XvaLiborIndexForm form) {
    return getExactSettings(stateDate)
        .flatMap(settings -> update(settings, form.getVersionForm(), s -> mapper.from(form, s)));
  }

  public Either<ErrorItem, EntityId> deleteLiborIndiciesVersion(LocalDate stateDate) {
    return getExactSettings(stateDate).flatMap(this::checkFirstVersion).flatMap(this::delete);
  }
}
