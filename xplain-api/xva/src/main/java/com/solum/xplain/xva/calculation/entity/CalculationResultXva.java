package com.solum.xplain.xva.calculation.entity;

import com.solum.xplain.calculation.CalculationResultStatus;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xva.calculation.constants.CalculationLevel;
import com.solum.xplain.xva.calculation.constants.CalculationPerspective;
import com.solum.xplain.xva.proxy.messages.XvaResultsFile;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
@FieldNameConstants
public class CalculationResultXva {

  @Id private ObjectId id;

  private AuditUser createdBy;
  private LocalDateTime createdAt;

  private CalculationResultStatus resultStatus;
  private List<XvaPartyResult> partyResults;
  private List<XvaPartyExposure> partyExposures;
  private CalculationPerspective calculationPerspective;
  private CalculationLevel calculationLevel;

  public static CalculationResultXva of(
      ObjectId calculationResultId,
      CalculationPerspective perspective,
      CalculationLevel level,
      AuditUser createdBy) {
    var calcItem = new CalculationResultXva();
    calcItem.setId(calculationResultId);
    calcItem.setResultStatus(CalculationResultStatus.IN_PROGRESS);
    calcItem.setCalculationPerspective(perspective);
    calcItem.setCalculationLevel(level);
    calcItem.setCreatedBy(createdBy);
    calcItem.setCreatedAt(LocalDateTime.now());
    return calcItem;
  }

  public CalculationResultXva with(
      XvaResultsFile resultsFile, Map<String, String> counterpartyNames) {
    resultStatus = CalculationResultStatus.UNSAVED;
    partyResults =
        XvaPartyResult.ofFile(
            resultsFile.getPartyResults(),
            resultsFile.getXvaPartyExposures(),
            calculationPerspective,
            counterpartyNames);
    partyExposures =
        Optional.ofNullable(resultsFile.getXvaPartyExposures()).map(Map::entrySet).stream()
            .flatMap(Collection::stream)
            .map(
                e ->
                    XvaPartyExposure.from(
                        e.getKey(), counterpartyNames.get(e.getKey()), e.getValue()))
            .toList();
    return this;
  }
}
