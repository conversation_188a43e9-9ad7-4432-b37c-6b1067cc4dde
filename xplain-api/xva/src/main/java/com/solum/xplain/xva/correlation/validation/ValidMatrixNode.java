package com.solum.xplain.xva.correlation.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidMatrixNodeValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidMatrixNode {
  String message() default "{com.solum.xplain.api.correlation.validation.ValidMatrixNode.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
