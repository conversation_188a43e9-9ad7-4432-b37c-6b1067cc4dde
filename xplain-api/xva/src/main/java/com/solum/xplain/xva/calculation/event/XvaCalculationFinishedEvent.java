package com.solum.xplain.xva.calculation.event;

import com.solum.xplain.calculation.events.CalculationEvent;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.types.ObjectId;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class XvaCalculationFinishedEvent extends CalculationEvent {
  public XvaCalculationFinishedEvent(ObjectId calculationId) {
    super(calculationId);
  }
}
