package com.solum.xplain.xva.calculation.value;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.proxy.messages.XvaControlFile;
import java.util.List;
import lombok.Data;

@Data
public class XvaDataStoreResult {

  private final XvaControlFile controlFile;
  private final XvaPortfolioCalculationData calculationData;
  private final List<ErrorItem> log;

  public String calculationId() {
    return calculationData.getCalculationId().toHexString();
  }
}
