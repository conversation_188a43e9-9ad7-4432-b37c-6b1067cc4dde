package com.solum.xplain.xva.calculation.entity;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.google.common.collect.Iterables;
import com.solum.xplain.xva.calculation.constants.CalculationPerspective;
import com.solum.xplain.xva.calculation.utils.TransposeUtils;
import com.solum.xplain.xva.proxy.messages.XvaPartyExposureFile;
import com.solum.xplain.xva.proxy.messages.XvaPartyResultsFile;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
@FieldNameConstants
public class XvaPartyResult {

  private String resultId = ObjectId.get().toString();
  private String partyName;
  private String partyFullName;
  private Integer numTrades;
  private Integer numWhatIfTrades;
  private Double pv;
  private Double fundingPV;
  private Double pvWhatIf;
  private Double fundingPVWhatIf;
  private Double cva;
  private Double dva;
  private Double fcaCp;
  private Double fbaCp;
  private Double fcaSelf;
  private Double fbaSelf;
  private Double fca;
  private Double fba;
  private Double cvaWhatIf;
  private Double dvaWhatIf;
  private Double fcaWhatIfCp;
  private Double fbaWhatIfCp;
  private Double fcaWhatIfSelf;
  private Double fbaWhatIfSelf;
  private Double bilateralCva;
  private Double fva;
  private String pvStatus;
  private String fundingPVStatus;
  private String pvWhatIfStatus;
  private String fundingPVWhatIfStatus;
  private String cvaDvaStatus;

  public static List<XvaPartyResult> ofFile(
      XvaPartyResultsFile resultsTransposed,
      Map<String, XvaPartyExposureFile> xvaPartyExposures,
      CalculationPerspective calculationPerspective,
      Map<String, String> counterpartiesNames) {
    return TransposeUtils.transpose(resultsTransposed, XvaPartyResult.class)
        .map(r -> r.adjust(calculationPerspective))
        .map(r -> r.updateCounterPartyFullName(counterpartiesNames))
        .map(r -> r.numberOfTrades(xvaPartyExposures))
        .toList();
  }

  private XvaPartyResult updateCounterPartyFullName(Map<String, String> counterpartiesNames) {
    String resolvedName = counterpartiesNames.get(partyName);
    this.partyFullName = isEmpty(resolvedName) ? partyName : resolvedName;
    return this;
  }

  private XvaPartyResult numberOfTrades(Map<String, XvaPartyExposureFile> xvaPartyExposures) {
    if (xvaPartyExposures == null) {
      return this;
    }
    XvaPartyExposureFile partyExposure = xvaPartyExposures.get(partyName);
    if (partyExposure != null) {
      if (partyExposure.getNumTrades() != null) {
        this.numTrades = Iterables.getFirst(partyExposure.getNumTrades(), 0);
      }
      if (partyExposure.getNumWhatIfTrades() != null) {
        this.numWhatIfTrades = Iterables.getFirst(partyExposure.getNumWhatIfTrades(), 0);
      }
    }
    return this;
  }

  private XvaPartyResult adjust(CalculationPerspective calculationPerspective) {
    if (calculationPerspective == CalculationPerspective.COUNTERPARTY) {
      Double swapDva = dva;
      dva = cva;
      cva = swapDva == null ? null : -swapDva;
    } else {
      cva = cva == null ? null : -cva;
    }
    if (allNotNull(cva, dva)) {
      bilateralCva = cva + dva;
    }
    fca = calculationPerspective == CalculationPerspective.SELF ? fcaSelf : fcaCp;
    fba = calculationPerspective == CalculationPerspective.SELF ? fbaSelf : fbaCp;
    if (allNotNull(fca, fba)) {
      fva = fca + fba;
    }
    return this;
  }
}
