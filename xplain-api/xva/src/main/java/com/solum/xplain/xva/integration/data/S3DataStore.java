package com.solum.xplain.xva.integration.data;

import com.solum.xplain.xva.calculation.config.S3Properties;
import java.io.IOException;
import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@Slf4j
@Service
@AllArgsConstructor
public class S3DataStore {
  private final S3Client s3Client;
  private final S3Properties s3Properties;

  public void uploadFile(Path file) {
    log.debug("Uploading file: {}", file);
    s3Client.putObject(
        PutObjectRequest.builder().bucket(s3Properties.getBucket()).key(file.toString()).build(),
        file);
  }

  public byte[] downloadFile(String key) throws IOException {
    log.debug("Downloading file: {}", key);
    return s3Client
        .getObject(GetObjectRequest.builder().bucket(s3Properties.getBucket()).key(key).build())
        .readAllBytes();
  }
}
