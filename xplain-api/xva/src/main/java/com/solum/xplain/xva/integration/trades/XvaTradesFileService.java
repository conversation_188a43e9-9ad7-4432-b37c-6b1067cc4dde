package com.solum.xplain.xva.integration.trades;

import com.solum.xplain.calculation.trades.CalculationTrades;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.integration.csv.XvaTradesCsvMapper;
import io.atlassian.fugue.Either;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class XvaTradesFileService {
  private final XvaTradesValidator validator;
  private final XvaTradesCsvMapper tradesCsvMapper;

  public List<ErrorItem> writeTradesToFile(Path tradesFilePath, CalculationTrades trades)
      throws IOException {
    final List<ErrorItem> errors;
    try (Writer tradesWriter = new FileWriter(tradesFilePath.toFile());
        Stream<Either<ErrorItem, CsvRow>> rows =
            trades
                .tradesStream()
                .map(validator::validatePortfolioItem)
                .map(r -> r.map(tradesCsvMapper::toCsvFields))) {
      errors = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, rows).write(tradesWriter);
    }
    return errors;
  }
}
