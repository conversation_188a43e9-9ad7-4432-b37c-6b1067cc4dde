package com.solum.xplain.xva.correlation.validation;

import static com.solum.xplain.xva.correlation.CorrelationMatrixRepository.uniqueEntityCriteria;
import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.common.validation.UniqueEntitySupport;
import com.solum.xplain.xva.correlation.CorrelationMatrix;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;

public class UniqueCorrelationMatrixNameValidator
    implements ConstraintValidator<UniqueCorrelationMatrixName, CorrelationMatrixCreateForm> {

  private final UniqueEntitySupport uniqueSupport;

  public UniqueCorrelationMatrixNameValidator(UniqueEntitySupport uniqueNameSupport) {
    this.uniqueSupport = uniqueNameSupport;
  }

  @Override
  public boolean isValid(CorrelationMatrixCreateForm form, ConstraintValidatorContext context) {
    if (anyNull(form, form.getVersionForm())) {
      return true;
    }
    return isUnique(form.getVersionForm().getValidFrom(), form.getName());
  }

  private boolean isUnique(LocalDate stateDate, String name) {
    if (name == null || stateDate == null) {
      return true;
    }
    return !uniqueSupport.existsByCriteria(
        stateDate, uniqueEntityCriteria(name), CorrelationMatrix.class);
  }
}
