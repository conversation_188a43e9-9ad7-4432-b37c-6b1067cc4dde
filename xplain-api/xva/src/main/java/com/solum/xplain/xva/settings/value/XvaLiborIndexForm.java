package com.solum.xplain.xva.settings.value;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.xva.settings.entity.XvaLiborIndex;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class XvaLiborIndexForm {

  @Valid private final List<XvaLiborIndex> indices;

  @Valid @NotNull private final NewVersionFormV2 versionForm;
}
