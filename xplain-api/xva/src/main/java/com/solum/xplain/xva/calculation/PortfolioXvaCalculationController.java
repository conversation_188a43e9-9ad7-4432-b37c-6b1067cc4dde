package com.solum.xplain.xva.calculation;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.FIXINGS_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.MARKET_DATA_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.MDK_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.VALUATION_SETTINGS_LOCK_ID;
import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_RUN_XVA_CALCULATIONS;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.ResourceValidationService;
import com.solum.xplain.core.lock.XplainLock;
import com.solum.xplain.xva.calculation.value.PortfolioXvaCalculationForm;
import com.solum.xplain.xva.locks.XvaLocks;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/portfolio")
public class PortfolioXvaCalculationController {

  private final XvaCalculationService xvaCalculationService;
  private final ResourceValidationService resourceValidationService;

  public PortfolioXvaCalculationController(
      XvaCalculationService xvaCalculationService,
      ResourceValidationService resourceValidationService) {
    this.xvaCalculationService = xvaCalculationService;
    this.resourceValidationService = resourceValidationService;
  }

  @Operation(summary = "Perform portfolio XVA calculation")
  @PostMapping("/{portfolioId}/calculation-xva")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_XVA_CALCULATIONS)
  public ResponseEntity<EntityId> calculateResultsWithXva(
      @PathVariable("portfolioId") String id,
      @RequestBody @Valid PortfolioXvaCalculationForm form,
      Authentication auth) {
    return eitherErrorItemResponse(
        calculationBitemporalDate(form.getStateDate(), form)
            .flatMap(bd -> xvaCalculationService.calculateXVAResults(auth, id, form, bd)));
  }

  private Either<ErrorItem, BitemporalDate> calculationBitemporalDate(
      LocalDate stateDate, PortfolioXvaCalculationForm form) {
    return resourceValidationService.ifLocksAvailable(
        List.of(
            XplainLock.newOf(TRADES_LOCK_ID),
            XplainLock.newOf(VALUATION_SETTINGS_LOCK_ID),
            XplainLock.newOf(FIXINGS_LOCK_ID),
            XplainLock.newOf(MARKET_DATA_LOCK_ID, form.getMarketDataGroupId()),
            XplainLock.newOf(MDK_LOCK_ID),
            XplainLock.newOf(CURVE_CONFIGURATION_LOCK_ID),
            XplainLock.newOf(XvaLocks.CORRELATION_LOCK_ID, form.getCorrelationMatrixId()),
            XplainLock.newOf(XvaLocks.XVA_SETTINGS_LOCK_ID),
            XplainLock.newOf(XvaLocks.LIBOR_INDICES_LOCK_ID)),
        () -> BitemporalDate.newOf(stateDate));
  }
}
