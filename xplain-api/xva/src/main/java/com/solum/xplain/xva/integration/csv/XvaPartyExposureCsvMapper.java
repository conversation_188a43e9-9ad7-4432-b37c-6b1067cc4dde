package com.solum.xplain.xva.integration.csv;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.xva.calculation.entity.XvaPartyExposureItem;

public class XvaPartyExposureCsvMapper {

  private XvaPartyExposureCsvMapper() {}

  public static CsvRow toCsvFields(XvaPartyExposureItem metrics) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField("time", metrics.getTime()));
    builder.add(new CsvField("date", metrics.getDate()));
    builder.add(new CsvField("epe", metrics.getEpe()));
    builder.add(new CsvField("ene", metrics.getEne()));
    builder.add(new CsvField("pfe", metrics.getPfe()));
    builder.add(new CsvField("ee", metrics.getEe()));
    return new CsvRow(builder.build());
  }
}
