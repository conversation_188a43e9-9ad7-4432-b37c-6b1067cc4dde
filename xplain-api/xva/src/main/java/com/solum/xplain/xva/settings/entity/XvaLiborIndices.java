package com.solum.xplain.xva.settings.entity;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.settings.VersionedSettings;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class XvaLiborIndices extends VersionedSettings {

  private List<XvaLiborIndex> liborIndices;

  public static XvaLiborIndices empty() {
    XvaLiborIndices settings = new XvaLiborIndices();
    settings.setValidFrom(LocalDate.ofEpochDay(0));
    settings.setState(State.ACTIVE);
    settings.liborIndices = List.of();
    return settings;
  }

  @Override
  public boolean valueEquals(Object indices) {
    if (indices instanceof XvaLiborIndices item) {
      return super.valueEquals(indices)
          && CollectionUtils.isEqualCollection(this.liborIndices, item.liborIndices);
    }
    return false;
  }
}
