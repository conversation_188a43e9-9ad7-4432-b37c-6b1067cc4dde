package com.solum.xplain.xva.correlation.value;

import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.xva.correlation.validation.CorrelationTypeSupplier;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CorrelationMatrixRemoveValueForm {

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  private final String ccy;

  @NotEmpty
  @ValidStringSet(value = CorrelationTypeSupplier.class)
  private final String type;

  @Valid @NotNull private final NewVersionFormV2 versionForm;
}
