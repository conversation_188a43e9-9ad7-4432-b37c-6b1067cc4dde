package com.solum.xplain.xva.credit;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_VIEW_FUNDING_NODES;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveExportService;
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveService;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveFundingNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.MdkExportForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/curve-group/{groupId}/credit-curves")
public class CreditCurveFundingController {

  private final CurveGroupCreditCurveService service;
  private final CurveGroupCreditCurveExportService exportService;

  @Operation(summary = "Gets Credit curve funding nodes")
  @GetMapping("/{curveId}/{version}/funding-nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FUNDING_NODES)
  public ResponseEntity<List<CreditCurveFundingNodeCalculatedView>> getCurveFundingNodes(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(
        service.getCurveFundingNodes(groupId, curveId, version, stateForm));
  }

  @Operation(summary = "Gets Credit curve funding nodes CSV")
  @CommonErrors
  @GetMapping("/{curveId}/{version}/funding-nodes/csv")
  @PreAuthorize(AUTHORITY_VIEW_FUNDING_NODES)
  public ResponseEntity<ByteArrayResource> getCurveFundingNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getCurveFundingNodesCsv(
            groupId, curveId, version, stateForm, selectedColumns));
  }

  @Operation(summary = "Gets Credit curves funding nodes MDK definitions as CSV")
  @PostMapping("/mdk-definitions/funding-nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getMarketDataKeyDefinitionsFundingNodesCsv(
      @PathVariable("groupId") String groupId, @RequestBody MdkExportForm exportForm) {
    return eitherErrorItemFileResponse(
        exportService.getFundingNodesMDKDefinitionsCsv(groupId, exportForm));
  }
}
