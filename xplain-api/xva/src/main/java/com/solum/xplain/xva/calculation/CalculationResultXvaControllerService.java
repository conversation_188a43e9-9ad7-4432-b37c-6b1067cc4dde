package com.solum.xplain.xva.calculation;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PARTY_EXPOSURE_FIELDS;
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter;
import static org.springframework.data.domain.Sort.unsorted;

import com.solum.xplain.calculation.UserCalculationResult;
import com.solum.xplain.calculation.UserCalculationResultResolver;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xva.calculation.entity.XvaPartyExposureItem;
import com.solum.xplain.xva.calculation.entity.XvaPartyResult;
import com.solum.xplain.xva.calculation.value.XvaCounterparty;
import com.solum.xplain.xva.calculation.value.XvaPartyExposureCharts;
import com.solum.xplain.xva.integration.csv.XvaPartyExposureCsvMapper;
import com.solum.xplain.xva.integration.csv.XvaPartyResultCsvMapper;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class CalculationResultXvaControllerService {

  private final UserCalculationResultResolver userCalculationResultResolver;
  private final CalculationResultXvaRepository calculationResultXvaRepository;

  public CalculationResultXvaControllerService(
      UserCalculationResultResolver userCalculationResultResolver,
      CalculationResultXvaRepository calculationResultXvaRepository) {
    this.userCalculationResultResolver = userCalculationResultResolver;
    this.calculationResultXvaRepository = calculationResultXvaRepository;
  }

  public Either<ErrorItem, ScrollableEntry<XvaPartyResult>> getCalculationXvaPartyResults(
      Authentication authentication,
      String id,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultXvaRepository.xvaPartyResults(
                    p.getView().getId(), scrollRequest, tableFilter));
  }

  public Either<ErrorItem, FileResponseEntity> exportCalculationXvaPartyResults(
      Authentication authentication,
      LocalDate stateDate,
      String id,
      Sort sort,
      TableFilter tableFilter) {
    var csvFileName = nameWithTimeStamp("XVAPartyResults", stateDate);
    return getUserCalculationResult(authentication, id)
        .map(
            r -> {
              var results =
                  calculationResultXvaRepository.xvaPartyResultForExport(id, sort, tableFilter);
              var rows = results.stream().map(XvaPartyResultCsvMapper::toCsvRow).toList();
              var csvFile = new CsvOutputFile(XvaPartyResultCsvMapper.XVA_RESULTS_FIELDS, rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, List<XvaCounterparty>> getCalculationXvaPartyExposureCounterparties(
      Authentication authentication, String id) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultXvaRepository.xvaPartyExposureCounterparties(p.getView().getId()));
  }

  public Either<ErrorItem, ScrollableEntry<XvaPartyExposureItem>> getCalculationXvaPartyExposures(
      Authentication authentication,
      String id,
      String counterPartyName,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultXvaRepository.xvaPartyExposures(
                    p.getView().getId(), counterPartyName, scrollRequest, tableFilter));
  }

  public Either<ErrorItem, FileResponseEntity> exportXvaPartyExposures(
      Authentication authentication,
      LocalDate stateDate,
      String id,
      String partyName,
      Sort sort,
      TableFilter tableFilter) {
    var csvFileName = nameWithTimeStamp("XVAPartyExposures", stateDate);
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultXvaRepository.xvaPartyExposuresList(
                    p.getView().getId(), partyName, sort, tableFilter))
        .map(
            portfolioItems -> {
              var rows =
                  portfolioItems.stream().map(XvaPartyExposureCsvMapper::toCsvFields).toList();
              var csvFile = new CsvOutputFile(PARTY_EXPOSURE_FIELDS, rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, XvaPartyExposureCharts> xvaPartyExposureCharts(
      Authentication authentication, String id, String partyName) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultXvaRepository.xvaPartyExposuresList(
                    id, partyName, unsorted(), emptyTableFilter()))
        .map(XvaPartyExposureCharts::of);
  }

  private Either<ErrorItem, UserCalculationResult> getUserCalculationResult(
      Authentication auth, String calculationResultId) {
    return userCalculationResultResolver.userCalculationResult(auth, calculationResultId);
  }
}
