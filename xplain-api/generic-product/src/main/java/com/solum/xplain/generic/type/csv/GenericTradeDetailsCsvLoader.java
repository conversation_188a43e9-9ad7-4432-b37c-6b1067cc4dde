package com.solum.xplain.generic.type.csv;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.portfolio.PortfolioCSVFields;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GenericTradeDetailsCsvLoader {

  public static String parseGenericAssetType(CsvRow row) {
    return row.findValue(PortfolioCSVFields.GENERIC_ASSET_TYPE).orElse(null);
  }

  public static String parseGenericSubAssetType(CsvRow row) {
    return row.findValue(PortfolioCSVFields.GENERIC_SUB_ASSET_TYPE).orElse(null);
  }

  public static String parseAdditionalInfo(CsvRow row) {
    return row.findValue(PortfolioCSVFields.GENERIC_ADDITIONAL_INFO).orElse(null);
  }

  public static String parseUnderlying(CsvRow row) {
    return row.findValue(PortfolioCSVFields.GENERIC_UNDERLYING).orElse(null);
  }

  public static String parseOptionPosition(CsvRow row) {
    return row.findValue(PortfolioCSVFields.GENERIC_OPTION_POSITION).orElse(null);
  }

  public static String parseProtection(CsvRow row) {
    return row.findValue(PortfolioCSVFields.TRADE_CREDIT_PROTECTION).orElse(null);
  }

  public static String parsePaylegType(CsvRow row) {
    return row.findValue(PortfolioCSVFields.TRADE_RATES_PAYLEG_TYPE).orElse(null);
  }

  public static String parseReceivelegType(CsvRow row) {
    return row.findValue(PortfolioCSVFields.TRADE_RATES_RECEIVELEG_TYPE).orElse(null);
  }
}
