package com.solum.xplain.generic.type.controller.value;

import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.portfolio.form.DefaultTradeForm;
import com.solum.xplain.core.portfolio.trade.GenericTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.validation.ValidPaymentPeriod;
import com.solum.xplain.core.portfolio.value.HasPaymentPeriod;
import com.solum.xplain.generic.type.controller.validation.GenericTradeFromGroupProvider;
import com.solum.xplain.generic.type.controller.validation.ValidGenericProductType;
import com.solum.xplain.generic.type.controller.validation.ValidGenericProductType.ProductGroup;
import com.solum.xplain.generic.type.controller.validation.groups.FxTradeGroup;
import com.solum.xplain.generic.type.controller.validation.groups.NonFxTradeGroup;
import com.solum.xplain.generic.type.product.GenericProductType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ValidPaymentPeriod
@GroupSequenceProvider(GenericTradeFromGroupProvider.class)
public abstract class CustomTradeForm extends DefaultTradeForm implements HasPaymentPeriod {

  @NotNull
  @ValidGenericProductType(
      groups = {FxTradeGroup.class},
      productGroup = ProductGroup.FX)
  @ValidGenericProductType(
      groups = {NonFxTradeGroup.class},
      productGroup = ProductGroup.NON_FX)
  private GenericProductType productType;

  @Schema(description = "The start date of the trade")
  @NotNull
  private LocalDate startDate;

  @Schema(description = "The end date of the trade")
  @NotNull
  private LocalDate endDate;

  @Schema(description = "The date of the trade")
  private LocalDate tradeDate;

  @Schema(description = "The asset type of the trade")
  private String assetType;

  @Schema(description = "The sub asset type of the trade")
  private String subAssetType;

  @Schema(description = "The additional information of the trade")
  private String additionalInfo;

  @Schema(description = "Trade underlying")
  private String underlying;

  @Nullable
  @Schema(description = "The option position of the trade")
  private String optionPosition;

  @Schema(description = "Currency of the trade")
  @NotEmpty(groups = {NonFxTradeGroup.class})
  @ValidStringSet(CurrenciesSupplier.class)
  private String tradeCurrency;

  public void withGenericTradeFields(GenericTradeDetails genericTradeDetails) {
    setAssetType(genericTradeDetails.getAssetType());
    setSubAssetType(genericTradeDetails.getSubAssetType());
    setAdditionalInfo(genericTradeDetails.getAdditionalInfo());
    setUnderlying(genericTradeDetails.getUnderlying());
    setOptionPosition(genericTradeDetails.getOptionPosition());
  }

  public void withTradeInfo(TradeDetails tradeDetails) {
    setTradeDate(tradeDetails.getInfo().getTradeDate());
    setTradeCounterpartyType(tradeDetails.getInfo().getCounterPartyType());
    setTradeCounterparty(tradeDetails.getInfo().getCounterParty());
    setTradeCurrency(tradeDetails.getInfo().getTradeCurrency());
  }

  public TradeInfoDetails toTradeInfo(String fallbackTradeCurrency) {
    TradeInfoDetails tradeInfoDetails = new TradeInfoDetails();
    tradeInfoDetails.setCounterParty(getTradeCounterparty());
    tradeInfoDetails.setCounterPartyType(getTradeCounterpartyType());
    tradeInfoDetails.setTradeDate(tradeDate);
    tradeInfoDetails.setTradeCurrency(ofNullable(tradeCurrency).orElse(fallbackTradeCurrency));
    return tradeInfoDetails;
  }
}
