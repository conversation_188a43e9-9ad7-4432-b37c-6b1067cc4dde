package com.solum.xplain.generic.type.classifier;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifierUtils;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.core.classifiers.CoreClassifiersProvider;
import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.generic.type.product.GenericProductGroup;
import com.solum.xplain.generic.type.product.GenericProductType;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;

@Component
public class GenericProductClassifierProvider implements ClassifiersProvider {

  @Override
  public List<Classifier> classifiers() {
    return List.of(
        productType(), productTypeGroup(), genericProductType(), genericProductTypeGroup());
  }

  @Override
  public int sortOrder() {
    return 7;
  }

  private Classifier productType() {
    return new Classifier(
        CoreClassifiersProvider.PRODUCT_TYPE,
        null,
        genericProductType().getValues(),
        ProductType.class);
  }

  private Classifier productTypeGroup() {
    return new Classifier(
        CoreClassifiersProvider.PRODUCT_TYPE_GROUP,
        null,
        genericProductGroups(),
        ProductGroup.class);
  }

  private Classifier genericProductType() {
    return ClassifierUtils.enumClassifier(
        "customProductType",
        GenericProductType.class,
        GenericProductType::name,
        GenericProductType::label);
  }

  private Classifier genericProductTypeGroup() {
    return new Classifier(
        "customProductTypeGroup", null, genericProductGroups(), GenericProductGroup.class);
  }

  private List<Classifier> genericProductGroups() {
    return Stream.of(GenericProductGroup.values())
        .map(
            group ->
                new Classifier(
                    group.name(),
                    group.label(),
                    group.getProductTypes().stream()
                        .map(c -> new Classifier(c.name(), c.label()))
                        .toList()))
        .toList();
  }
}
