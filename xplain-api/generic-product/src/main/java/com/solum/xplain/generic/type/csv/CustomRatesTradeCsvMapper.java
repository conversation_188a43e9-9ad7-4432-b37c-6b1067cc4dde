package com.solum.xplain.generic.type.csv;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ADDITIONAL_INFO;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ASSET_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_OPTION_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_SUB_ASSET_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_UNDERLYING;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_RATES_PAYLEG_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_RATES_RECEIVELEG_TYPE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import com.solum.xplain.generic.type.product.GenericProductGroup;
import com.solum.xplain.generic.type.product.GenericProductType;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class CustomRatesTradeCsvMapper implements ProductCsvMapper {

  public static final List<ProductType> PRODUCT_TYPES =
      Arrays.<ProductType>stream(GenericProductType.values())
          .filter(t -> t.getGroup().equals(GenericProductGroup.CUSTOM_RATES))
          .toList();

  @Override
  public List<ProductType> productTypes() {
    return PRODUCT_TYPES;
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails details) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(
        new CsvField(TRADE_ACCRUAL_SCHEDULE_START_DATE, details.getStartDate()),
        new CsvField(TRADE_ACCRUAL_SCHEDULE_END_DATE, details.getEndDate()),
        new CsvField(GENERIC_ASSET_TYPE, details.getCustomTradeDetails().getAssetType()),
        new CsvField(GENERIC_SUB_ASSET_TYPE, details.getCustomTradeDetails().getSubAssetType()),
        new CsvField(GENERIC_ADDITIONAL_INFO, details.getCustomTradeDetails().getAdditionalInfo()),
        new CsvField(TRADE_CURRENCY, details.getInfo().getTradeCurrency()),
        new CsvField(GENERIC_NOTIONAL, details.getCustomTradeDetails().getNotional()),
        new CsvField(GENERIC_UNDERLYING, details.getCustomTradeDetails().getUnderlying()),
        new CsvField(GENERIC_OPTION_POSITION, details.getCustomTradeDetails().getOptionPosition()));

    Optional.ofNullable(details.getPayLeg())
        .map(TradeLegDetails::getType)
        .ifPresent(s -> builder.add(new CsvField(TRADE_RATES_PAYLEG_TYPE, s)));

    Optional.ofNullable(details.getReceiveLeg())
        .map(TradeLegDetails::getType)
        .ifPresent(s -> builder.add(new CsvField(TRADE_RATES_RECEIVELEG_TYPE, s)));

    builder.addAll(List.of());

    return builder.build();
  }
}
