package com.solum.xplain.generic.type.controller.validation

import static java.time.LocalDate.now

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.portfolio.form.ClientMetricsForm
import com.solum.xplain.generic.type.controller.validation.groups.CreditTradeGroup
import com.solum.xplain.generic.type.controller.validation.groups.NonCreditTradeGroup
import com.solum.xplain.generic.type.controller.value.NonFxCustomTradeForm
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class NonFxTradeFormGroupProviderTest extends Specification {

  def "should Validate CREDIT groups correclty"() {
    setup:
    def form = new NonFxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: GenericProductType.CUSTOM_CREDIT_1,
      startDate: now(),
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      underlying: "UNDERLYING",
      notionalValue: 10,
      payLegType : "FIXED",
      receiveLegType: "INFLATION",
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = new NonFxTradeFormGroupProvider().getValidationGroups(form)

    then:
    result.contains(CreditTradeGroup.class)
  }

  def "should Validate RATES groups correclty"() {
    setup:
    def form = new NonFxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: GenericProductType.CUSTOM_RATES_1,
      startDate: now(),
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      underlying: "UNDERLYING",
      notionalValue: 10,
      payLegType : "FIXED",
      receiveLegType: "INFLATION",
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = new NonFxTradeFormGroupProvider().getValidationGroups(form)

    then:
    result.contains(NonCreditTradeGroup.class)
  }
}
