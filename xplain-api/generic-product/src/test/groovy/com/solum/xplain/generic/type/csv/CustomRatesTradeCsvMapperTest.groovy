package com.solum.xplain.generic.type.csv

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ADDITIONAL_INFO
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ASSET_TYPE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_NOTIONAL
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_SUB_ASSET_TYPE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_UNDERLYING
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_RATES_PAYLEG_TYPE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_RATES_RECEIVELEG_TYPE
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_5
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_6
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_7
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_8
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_RATES_9

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.common.csv.CsvRow
import com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder
import org.apache.commons.collections4.IterableUtils
import spock.lang.Specification

class CustomRatesTradeCsvMapperTest extends Specification{

  def HEADER = [
    TRADE_ACCRUAL_SCHEDULE_START_DATE,
    TRADE_ACCRUAL_SCHEDULE_END_DATE,
    GENERIC_ASSET_TYPE,
    GENERIC_SUB_ASSET_TYPE,
    GENERIC_ADDITIONAL_INFO,
    TRADE_CURRENCY,
    GENERIC_NOTIONAL,
    GENERIC_UNDERLYING,
    TRADE_RATES_PAYLEG_TYPE,
    TRADE_RATES_RECEIVELEG_TYPE
  ]

  def MAPPER = new CustomRatesTradeCsvMapper()

  def "should return product types"() {
    expect:
    MAPPER.productTypes() == Arrays.asList(CUSTOM_RATES_1, CUSTOM_RATES_2, CUSTOM_RATES_3, CUSTOM_RATES_4, CUSTOM_RATES_5, CUSTOM_RATES_6, CUSTOM_RATES_7, CUSTOM_RATES_8, CUSTOM_RATES_9)
  }

  def "should correctly map Custom Rate trade"() {
    setup:
    def trade = GenericPortfolioItemBuilder.singleCreditCcyGenericTrade(CUSTOM_RATES_1)

    when:
    def columns = MAPPER.toCsvFields(trade.tradeDetails)
    def csv = new CsvOutputFile(HEADER, [new CsvRow(IterableUtils.toList(columns))])
    def result = csv.write()

    then:
    result == """Start Date,End Date,Asset Type,Sub-Asset Type,Additional Information,Trade Currency,Custom Product Notional,Custom Product Underlying,Custom Rates Pay Leg Type,Custom Rates Receive Leg Type
1970-01-01,2031-06-19,SINGLE_CCY,SUB_SINGLE,ADDITIONAL,EUR,10,UNDERLYING,,
"""
  }
}
