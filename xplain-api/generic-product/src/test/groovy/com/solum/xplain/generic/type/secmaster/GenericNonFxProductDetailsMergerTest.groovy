package com.solum.xplain.generic.type.secmaster

import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import com.solum.xplain.core.portfolio.trade.GenericTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class GenericNonFxProductDetailsMergerTest extends Specification {
  def merger = new GenericNonFxProductDetailsMerger()

  def "should return correct product type"() {
    expect:
    merger.productTypes().size() == 29
  }

  def "should correctly merge trade with Non FX trade security master when BUY alloc"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(allocationNotional: 10)
    def tradeDetails = new TradeDetails(customTradeDetails: new GenericTradeDetails())

    when:
    def result = merger.mergeDetails(GenericProductType.CUSTOM_CREDIT_1, tradeDetails, allocationDetails)

    then:
    result.customTradeDetails.notional == allocationDetails.getAllocationNotional()
  }
}
