package com.solum.xplain.generic.type.csv

import static com.google.common.io.ByteSource.wrap

import com.google.common.io.ByteStreams
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.io.CsvIterator
import com.opengamma.strata.collect.io.CsvRow
import com.opengamma.strata.collect.io.UnicodeBom
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.extensions.enums.CreditSector
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class NonFxGenericProductTypeCsvLoaderTest extends Specification {
  def LOADER = new NonFxGenericProductTypeCsvLoader()

  def "should return correct product types"() {
    expect:
    LOADER.productTypes().size() == 29
  }

  def "should parse Generic non Fx trade"() {
    setup:
    def rows = loadResource("CustomNonFxTrade.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 4
    parsedRows.stream().allMatch(Either::isRight)

    and:
    with (parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails())) {
      TradeDetails rates1 ->
      rates1.startDate == LocalDate.parse("2023-01-01")
      rates1.endDate == LocalDate.parse("2023-12-12")
      rates1.creditTradeDetails == null

      def rates1GenericDetails = rates1.customTradeDetails
      rates1GenericDetails.notional == 10.0
      rates1GenericDetails.assetType == "ASSET"
      rates1GenericDetails.subAssetType == "SUB"
      rates1GenericDetails.additionalInfo == "ADDITIONAL"
      rates1GenericDetails.optionPosition == "BUY"
      rates1.payLeg.type == CalculationType.FIXED
      rates1.receiveLeg.type == CalculationType.IBOR
    }
    with (parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails())) {
      TradeDetails customCredit1 ->
      customCredit1.customTradeDetails.notional == 11.0
      customCredit1.customTradeDetails.optionPosition == "XX"
      customCredit1.payLeg.type == CalculationType.INFLATION
      customCredit1.receiveLeg.type == CalculationType.FIXED
    }
    with (parsedRows[2].getOrNull().toTradeDetails(new TradeInfoDetails())) {
      TradeDetails customCredit1 ->
      customCredit1.creditTradeDetails.sector == CreditSector.UNDEFINED
      customCredit1.customTradeDetails.notional == 12.0
      customCredit1.customTradeDetails.optionPosition == "YY"
    }
    with (parsedRows[3].getOrNull().toTradeDetails(new TradeInfoDetails())) {
      TradeDetails customCredit2 ->
      customCredit2.creditTradeDetails.sector == CreditSector.INDUSTRIALS
      customCredit2.customTradeDetails.notional == 13.0
      customCredit2.customTradeDetails.optionPosition == "ZZ"
    }
  }

  def "should parse Generic RefSec non Fx trade"() {
    setup:
    def rows = loadResource("CustomNonFxTradeRefSec.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, true)
    }.toList()

    then:
    parsedRows.size() == 1
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails())
    result.startDate == LocalDate.parse("2023-01-01")
    result.endDate == LocalDate.parse("2023-12-12")
    result.creditTradeDetails == null

    def genericDetails = result.customTradeDetails
    genericDetails.notional == null
    genericDetails.assetType == null
    genericDetails.subAssetType == null
    genericDetails.additionalInfo == null
  }

  def "should parse trade currency"() {
    setup:
    def rows = loadResource("CustomNonFxTrade.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parseTradeCcy(it)
    }.toList()

    then:
    parsedRows.size() == 4

    and:
    parsedRows[0] == Currency.EUR
    parsedRows[1] == Currency.EUR
    parsedRows[2] == Currency.EUR
    parsedRows[3] == Currency.EUR
  }

  def "should return Generic NONFX trade parse errors"() {
    setup:
    def rows = loadResource("CustomNonFxTradeInvalid.csv")

    when:
    def parsedRow = LOADER.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: No value was found for 'Trade Type'"
    1   | "Error at line number 3. Error: No value was found for 'Start Date'"
    2   | "Error at line number 4. Error: No value was found for 'End Date'"
    4   | "Error at line number 6. Error: Unable to parse double from ''"
  }

  def "should return Generic NONFX trade parse errors when no currency"() {
    setup:
    def rows = loadResource("CustomNonFxTradeInvalid.csv")

    when:
    LOADER.parseTradeCcy(rows[3])

    then:
    IllegalArgumentException error = thrown()
    error.message == "Trade currency is mandatory"
  }

  List<CsvRow> loadResource(String fileName) {
    def content = ByteStreams.toByteArray(getClass().getResourceAsStream("/csv/" + fileName))
    return CsvIterator.of(UnicodeBom.toCharSource(wrap(content)), true)
    .asStream()
    .toList()
  }
}
