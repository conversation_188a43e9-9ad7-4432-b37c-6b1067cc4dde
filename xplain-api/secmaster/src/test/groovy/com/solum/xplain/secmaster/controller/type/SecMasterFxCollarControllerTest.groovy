package com.solum.xplain.secmaster.controller.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.refFxCollarForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.refFxCollarFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static com.solum.xplain.extensions.enums.PositionType.SELL
import static com.solum.xplain.secmaster.entity.SecMasterTradeReadEntityBuilder.fxCollar
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.ReferenceTradesProvider
import com.solum.xplain.core.portfolio.form.FxCollarTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.test.TestSecurityConfig
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService
import com.solum.xplain.secmaster.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@MockMvcConfiguration
@WebMvcTest(controllers = [SecMasterFxCollarController])
@Import([TestSecurityConfig])
class SecMasterFxCollarControllerTest extends Specification {

  @SpringBean
  private SecMasterTradeControllerService service = Mock()

  @SpringBean
  private ReferenceTradesProvider referenceTradesProvider = Mock()

  @SpringBean
  private PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = Mock()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def "should get fx collar"() {
    setup:
    1 * service.itemLatest("1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(fxCollar())

    when:
    def results = mockMvc.perform(get("/security-master/fx-collar/1")
      .with(userWithAuthority(VIEW_TRADE))
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should respond with #response given form #form"() {
    setup:
    referenceTradesProvider.existsActiveReferenceTrade("DUPLICATE", LocalDate.parse("2020-01-01"), null) >> true
    service.insert(_ as FxCollarTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/security-master/fx-collar")
      .with(csrf())
      .with(userWithAuthority(MODIFY_TRADE))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                                   | code | response
    refFxCollarForm()                                      | 200  | """{"id":"1"}"""
    refFxCollarFormWith("tradeDate", null)                 | 200  | """{"id":"1"}"""
    refFxCollarFormWith("callPutType", null)               | 200  | """{"id":"1"}"""
    refFxCollarFormWith("fxRate", null)                    | 412  | "NotNull.fxCollarTradeForm.fxRate"
    refFxCollarFormWith("otherOptionFxRate", null)         | 412  | "NotNull.fxCollarTradeForm.otherOptionFxRate"
    refFxCollarFormWith("foreignCurrencyAmount", null)     | 412  | "NotNull.fxCollarTradeForm.foreignCurrencyAmount"
    refFxCollarFormWith("domesticCurrencyAmount", null)    | 412  | "NotNull.fxCollarTradeForm.domesticCurrencyAmount"
    refFxCollarFormWith("businessDayConvention", null)     | 412  | "NotEmpty.fxCollarTradeForm.businessDayConvention"
    refFxCollarFormWith("businessDayConvention", "random") | 412  | "ValidStringSet.fxCollarTradeForm.businessDayConvention"
    refFxCollarFormWith("externalTradeId", null)           | 412  | "NotEmpty.fxCollarTradeForm.externalTradeId"
    refFxCollarFormWith("externalTradeId", "WITH SPACES")  | 412  | "ValidIdentifier.fxCollarTradeForm.externalTradeId"
    refFxCollarFormWith("externalTradeId", "lowercase")    | 412  | "ValidIdentifier.fxCollarTradeForm.externalTradeId"
    refFxCollarFormWith("expiryDate", now().plusDays(1))   | 412  | "Expiry date must be before payment date"
    refFxCollarFormWith("position", SELL.name())           | 412  | "ValidStringSet.fxCollarTradeForm.position"
  }

  @Unroll
  def "should insert FxCollar when role #role then #responseStatus"() {
    setup:
    def form = refFxCollarForm()
    service.insert(_ as FxCollarTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/security-master/fx-collar")
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == responseStatus

    where:
    role         | responseStatus
    MODIFY_TRADE | 200
    VIEW_TRADE   | 403
  }

  @Unroll
  def "should update FxCollar when role #role then #responseStatus"() {
    setup:
    def form = refFxCollarForm()
    service.update("1", LocalDate.of(1970, 1, 1), _ as FxCollarTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/security-master/fx-collar/1/1970-01-01")
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == responseStatus

    where:
    role         | responseStatus
    MODIFY_TRADE | 200
    VIEW_TRADE   | 403
  }
}
