package com.solum.xplain.secmaster.controller.type

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.refCreditIndexTrancheTradeForm
import static com.solum.xplain.extensions.enums.PositionType.SELL
import static com.solum.xplain.secmaster.entity.SecMasterTradeReadEntityBuilder.creditIndexTranche
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.basics.date.DayCounts
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.ReferenceTradesProvider
import com.solum.xplain.core.portfolio.form.CreditIndexTradeForm
import com.solum.xplain.core.portfolio.form.PaymentDateForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService
import com.solum.xplain.secmaster.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [SecMasterCreditIndexTrancheController])
class SecMasterCreditIndexTrancheControllerTest extends Specification {

  @SpringBean
  private SecMasterTradeControllerService service = Mock()

  @SpringBean
  private ReferenceTradesProvider referenceTradesProvider = Mock()

  @SpringBean
  private PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = Mock()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def "should get credit-index"() {
    setup:
    1 * service.itemLatest("1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(creditIndexTranche())

    when:
    def results = mockMvc.perform(get("/security-master/credit-index-tranche/1")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new credit-index-tranche with response #response"() {
    setup:
    referenceTradesProvider.existsActiveReferenceTrade("DUPLICATE", LocalDate.parse("2020-01-01"), null) >> true
    service.insert(_ as CreditIndexTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/security-master/credit-index-tranche")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                                                             | code | response
    refCreditIndexTrancheTradeForm()                                                 | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["upfrontFee": null, tradeDate: null])            | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["creditIndexVersion": null])                     | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["creditIndexSeries": null])                      | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["dayCount": DayCounts.ACT_360.name])             | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["sector": null])                                 | 412  | "NotEmpty.creditIndexTrancheTradeForm.sector"
    refCreditIndexTrancheTradeForm(["dayCount": DayCounts.ACT_364.name])             | 412  | "Pattern.creditIndexTrancheTradeForm.dayCount"
    refCreditIndexTrancheTradeForm(["entityLongName": "rand"])                       | 412  | "ValidStringSet.creditIndexTrancheTradeForm.entityLongName"
    refCreditIndexTrancheTradeForm(["externalTradeId": null])                        | 412  | "NotEmpty.creditIndexTrancheTradeForm.externalTradeId"
    refCreditIndexTrancheTradeForm(["reference": null])                              | 412  | "NotEmpty.creditIndexTrancheTradeForm.reference"
    refCreditIndexTrancheTradeForm(["externalTradeId": "WITH SPACES"])               | 412  | "ValidIdentifier.creditIndexTrancheTradeForm.externalTradeId"
    refCreditIndexTrancheTradeForm(["externalTradeId": "lowercaseS"])                | 412  | "ValidIdentifier.creditIndexTrancheTradeForm.externalTradeId"
    refCreditIndexTrancheTradeForm(["upfrontFee": new PaymentDateForm(amount: 10)])  | 412  | "NotNull.creditIndexTrancheTradeForm.upfrontFee.date"
    refCreditIndexTrancheTradeForm(["upfrontFee": new PaymentDateForm(date: now())]) | 412  | "NotNull.creditIndexTrancheTradeForm.upfrontFee.amount"
    refCreditIndexTrancheTradeForm(["tradeDate": null])                              | 200  | """{"id":"1"}"""
    refCreditIndexTrancheTradeForm(["position": null])                               | 412  | "NotEmpty.creditIndexTrancheTradeForm.position"
    refCreditIndexTrancheTradeForm(["position": "random"])                           | 412  | "ValidStringSet.creditIndexTrancheTradeForm.position"
    refCreditIndexTrancheTradeForm(["startDate": null])                              | 412  | "NotNull.creditIndexTrancheTradeForm.startDate"
    refCreditIndexTrancheTradeForm(["endDate": null])                                | 412  | "NotNull.creditIndexTrancheTradeForm.endDate"
    refCreditIndexTrancheTradeForm(["endDate": now()])                               | 412  | "ValidPaymentPeriod.creditIndexTrancheTradeForm.endDate"
    refCreditIndexTrancheTradeForm(["notionalValue": 1])                             | 412  | "Null.creditIndexTrancheTradeForm.notionalValue"
    refCreditIndexTrancheTradeForm(["notionalValue": Double.valueOf(-10)])           | 412  | "Positive.creditIndexTrancheTradeForm.notionalValue"
    refCreditIndexTrancheTradeForm(["frequency": null])                              | 412  | "NotEmpty.creditIndexTrancheTradeForm.frequency"
    refCreditIndexTrancheTradeForm(["frequency": "random"])                          | 412  | "ValidStringSet.creditIndexTrancheTradeForm.frequency"
    refCreditIndexTrancheTradeForm(["fixedRate": null])                              | 412  | "NotNull.creditIndexTrancheTradeForm.fixedRate"
    refCreditIndexTrancheTradeForm(["currency": null])                               | 412  | "NotEmpty.creditIndexTrancheTradeForm.currency"
    refCreditIndexTrancheTradeForm(["position": SELL.name()])                        | 412  | "ValidStringSet.creditIndexTrancheTradeForm.position"
    refCreditIndexTrancheTradeForm(["creditIndexTranche": "3-6"])                    | 412  | "Invalid tranche for credit index. Valid tranches are: [0-15, 15-25, 25-35, 35-100]."
  }
}
