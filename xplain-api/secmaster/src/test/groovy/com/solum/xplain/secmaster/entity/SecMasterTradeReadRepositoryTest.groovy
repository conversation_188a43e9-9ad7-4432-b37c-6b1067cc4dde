package com.solum.xplain.secmaster.entity

import static com.solum.xplain.core.common.GroupRequest.emptyGroupRequest
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.secmaster.entity.SecMasterTradeReadEntityBuilder.trade
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.util.Collections.emptyList
import static java.util.stream.Collectors.toList
import static org.springframework.data.domain.Sort.Direction.DESC

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.value.PortfolioItemRefDetailsView
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class SecMasterTradeReadRepositoryTest extends IntegrationSpecification {

  @Resource
  SecMasterTradeReadRepository repository
  @Resource
  MongoOperations operations

  XplainPrincipal creator = user("userId")

  def stateDate = new BitemporalDate(LocalDate.of(2022, 1, 1))

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), SecMasterTradeReadEntity)
  }

  def "should fetch zero items"() {
    when:
    def result = repository.items(stateDate, emptyTableFilter(), active(), ScrollRequest.of(0, 1), emptyGroupRequest())

    then:
    result.getContent() == []
  }

  def "should fetch items"() {
    setup:
    def item = trade()
    operations.save(item)

    when:
    def result = repository.items(
    stateDate,
    emptyTableFilter(),
    active(),
    ScrollRequest.of(0, 1, Sort.by(SecMasterTradeReadEntity.Fields.externalTradeId)),
    emptyGroupRequest())

    then:
    result.getContent() == [item]
    result.getContent()[0].toString().contains("externalTradeId")
  }

  def "should fetch items limited"() {
    setup:
    def item1 = trade("1")
    def item2 = trade("2")
    operations.save(item1)
    operations.save(item2)

    when:
    def result = repository.items(stateDate, emptyTableFilter(), active(), ScrollRequest.of(0, 1), emptyGroupRequest())

    then:
    result.getLastRow() == null
    result.getContent().size() == 1
  }

  def "should fetch items grouped"() {
    setup:
    def item1 = trade("1")
    def item2 = trade("2")
    operations.save(item1)
    operations.save(item2)

    when:
    def result = repository.items(stateDate, emptyTableFilter(), active(), ScrollRequest.of(0, 10), GroupRequest.of(['externalTradeId'], emptyList()))

    then:
    result.getLastRow() == 2
    result.getContent().size() == 2
  }

  def "should fetch items count"() {
    setup:
    operations.save(trade())

    when:
    def result = repository.count(stateDate)

    then:
    result == 1L
  }

  def "should fetch item latest"() {
    setup:
    def item = trade("1")
    operations.save(item)

    when:
    def result = repository.itemLatest(item.entityId, stateDate)

    then:
    result.isRight()
    result.getOrNull() == item

    when:
    def resultFail = repository.itemLatest(item.entityId, new BitemporalDate(item.getValidFrom().minusDays(1)))

    then:
    resultFail.isLeft()
  }

  def "should fetch item latest by semantic id"() {
    setup:
    def item = trade("1")
    operations.save(item)

    when:
    def result = repository.itemLatestByExternalId(item.externalTradeId, stateDate)

    then:
    result.isRight()
    result.getOrNull() == item

    when:
    def resultFail = repository.itemLatestByExternalId(item.externalTradeId, new BitemporalDate(item.getValidFrom().minusDays(1)))

    then:
    resultFail.isLeft()
  }

  def "should fetch item versions"() {
    setup:
    def item = trade("1")
    operations.save(item)

    when:
    def result = repository.versions(item.entityId)

    then:
    result == [item]
  }

  def "should fetch items external source ids"() {
    setup:
    def item1 = trade("1").tap(it -> it.externalIdentifiers = [new ExternalIdentifier("1", "SID1")])
    def item2 = trade("2").tap(it -> it.externalIdentifiers = [new ExternalIdentifier("2", "SID2")])
    operations.save(item1)
    operations.save(item2)

    when:
    def result = repository.allExternalSourceIds(stateDate)

    then:
    result.size() == 2
    result.contains("SID1")
    result.contains("SID2")
  }

  def "should fetch items stream"() {
    setup:
    def item1 = trade("1")
    def item2 = trade("2")
    operations.save(item1)
    operations.save(item2)

    when:
    def result = repository.allItemsStream(stateDate, Sort.by(DESC, "externalTradeId"), emptyTableFilter()).collect toList()

    then:
    result.size() == 2
    result[0].getExternalTradeId() == "2"
    result[1].getExternalTradeId() == "1"
  }

  def "should fetch active items ids"() {
    setup:
    def item1 = trade("1")
    def item2 = trade("2")
    operations.save(item1)
    operations.save(item2)

    when:
    def result = repository.allActiveTradesWithDetails(stateDate)

    then:
    result.size() == 2
    result.keySet().containsAll(["1", "2"])
    result.get("1") == new PortfolioItemRefDetailsView(
    productType: item1.getProductType(),
    entityId: item1.getEntityId(),
    externalTradeId: item1.getExternalTradeId(),
    tradeCounterparty: item1.getTradeDetails().getInfo().getCounterParty(),
    tradeCounterpartyType: item1.getTradeDetails().getInfo().getCounterPartyType(),
    clientMetrics: item1.getClientMetrics()
    )
  }

  def "should check future versions"() {
    setup:
    operations.save(trade("1"))

    when:
    def result = repository.hasFutureVersions(stateDate.getActualDate())

    then:
    !result
  }

  def "should fetch future versions"() {
    setup:
    def trade = trade("1")
    operations.save(trade)

    when:
    def result = repository.futureVersions("1", trade.getValidFrom().minusDays(1))

    then:
    result == new DateList([trade.getValidFrom()])
  }

  def "should fetch by semantic ids"() {
    setup:
    def trade = trade("1")
    operations.save(trade)

    when:
    def result = repository.entitiesBySemanticIds(Set.of("1"))

    then:
    result.toList() == [trade]
  }

  def "should return whether any trade has changed after some date"() {
    setup:
    def trade = trade("1")
    operations.save(trade)

    expect:
    !repository.hasChanges(trade.getModifiedAt().plusMinutes(1))
    repository.hasChanges(trade.getModifiedAt().minusMinutes(1))
  }
}
