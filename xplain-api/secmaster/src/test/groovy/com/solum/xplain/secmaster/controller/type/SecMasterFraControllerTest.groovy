package com.solum.xplain.secmaster.controller.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.SwapLegFormBuilder.withCurrency
import static com.solum.xplain.core.portfolio.SwapLegFormBuilder.withIborIndex
import static com.solum.xplain.core.portfolio.SwapLegFormBuilder.withNotionalCurrency
import static com.solum.xplain.core.portfolio.SwapLegFormBuilder.withNotionalValue
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fraTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fraTradeFormWith
import static com.solum.xplain.secmaster.entity.SecMasterTradeReadEntityBuilder.fra
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.ReferenceTradesProvider
import com.solum.xplain.core.portfolio.SwapLegFormBuilder
import com.solum.xplain.core.portfolio.form.FraTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.test.TestSecurityConfig
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService
import com.solum.xplain.secmaster.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [SecMasterFraController])
@Import([TestSecurityConfig])
class SecMasterFraControllerTest extends Specification {

  @SpringBean
  private SecMasterTradeControllerService service = Mock()

  @SpringBean
  private ReferenceTradesProvider referenceTradesProvider = Mock()

  @SpringBean
  private PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def "should get FRA with role #role"() {
    setup:
    service.itemLatest("1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(fra())

    when:
    def results = mockMvc.perform(get("/security-master/fra/1")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role | code | response
    MODIFY_TRADE | 403 | "OPERATION_NOT_ALLOWED"
    VIEW_TRADE | 200 | "externalTradeId"
  }

  @Unroll
  def "should create new FRA with role #role form #form and response #response"() {
    setup:
    referenceTradesProvider.existsActiveReferenceTrade("DUPLICATE", LocalDate.parse("2020-01-01"), null) >> true
    service.insert(_ as FraTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/security-master/fra")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .content(objectMapper.writeValueAsString(form.tap { if (it.notionalValue == 1.0) it.notionalValue = null }))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:

    role | form | code | response
    MODIFY_TRADE | fraTradeForm()                                                                                     | 200  | """{"id":"1"}"""
    VIEW_TRADE | fraTradeForm()                                                                                       | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "123")                                                         | 412  | "Value must start with REF_"
    MODIFY_TRADE | fraTradeFormWith("tradeDate", null)                                                                | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fraTradeFormWith("startDate", null)                                                                | 412  | "NotNull.fraTradeForm.startDate"
    MODIFY_TRADE | fraTradeFormWith("endDate", null)                                                                  | 412  | "NotNull.fraTradeForm.endDate"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", null)                                                          | 412  | "NotEmpty.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "WITH SPACES")                                                 | 412  | "ValidIdentifier.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "lowercase")                                                   | 412  | "ValidIdentifier.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "DUPLICATE")                                                   | 412  | "UniqueReferenceTradeId"
    MODIFY_TRADE | fraTradeFormWith("notionalCurrency", null)                 | 412  | "NotEmpty.fraTradeForm.notionalCurrency"
    MODIFY_TRADE | fraTradeFormWith("notionalValue", Double.valueOf(10))      | 412  | "Null.fraTradeForm.notionalValue"
    MODIFY_TRADE | fraTradeFormWith("calculationIborIndex", null)             | 412  | "NotEmpty.fraTradeForm.calculationIborIndex"
    MODIFY_TRADE | fraTradeFormWith("calculationFixedRateInitialValue", null) | 412  | "NotNull.fraTradeForm.calculationFixedRateInitialValue"
    MODIFY_TRADE | fraTradeFormWith("csaDiscountingGroup", "XAU")             | 412  | "ValidStringSet.fraTradeForm.csaDiscountingGroup"
    MODIFY_TRADE | fraTradeFormWith("calculationIborIndex", "CHF-LIBOR-6M")   | 200  | """{"id":"1"}"""
  }

  @Unroll
  def "should update FRA with role #role form #form and response #response"() {
    setup:
    referenceTradesProvider.existsActiveReferenceTrade("REF_TRADEID", LocalDate.parse("2020-01-01"), "1") >> false
    referenceTradesProvider.existsActiveReferenceTrade("REF_DUPLICATE", LocalDate.parse("2020-01-01"), "1") >> true
    service.update("1", LocalDate.of(1970, 1, 1), _ as FraTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/security-master/fra/1/1970-01-01")
      .with(user("user").authorities(new SimpleGrantedAuthority(role)))
      .with(csrf())
      .content(objectMapper.writeValueAsString(form.tap { it.notionalValue = null }))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role | form | code | response
    MODIFY_TRADE | fraTradeForm()                                       | 200  | """{"id":"1"}"""
    VIEW_TRADE | fraTradeForm()                                         | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "REF_DUPLICATE") | 412  | "UniqueReferenceTradeId"
  }
}
