package com.solum.xplain.secmaster.trademerge.product

import static com.solum.xplain.core.portfolio.CoreProductType.FXCOLLAR
import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.portfolio.CoreProductType.FXSWAP
import static com.solum.xplain.core.portfolio.value.CounterpartyType.CLEARED

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.extensions.enums.CallPutType
import com.solum.xplain.extensions.enums.PositionType
import java.time.LocalDate
import spock.lang.Specification

class FxProductDetailsMergerTest extends Specification {
  def merger = new FxProductDetailsMerger()

  def "should return correct product type"() {
    expect:
    merger.productTypes() == [FXFWD, FXOPT, FXSWAP, FXCOLLAR]
  }

  def "should correctly merge trade with FXFWD security master when positive alloc notional"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxfwd()

    when:
    def result = merger.mergeDetails(FXFWD, tradeDetails, allocationDetails)

    then:
    result.getPayLeg().getPayReceive() == PayReceive.PAY
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -12
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 10
  }

  def "should correctly merge trade with FXSWAP security master when trade ccy is receive ccy"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxswap(false)

    when:
    def result = merger.mergeDetails(FXSWAP, tradeDetails, allocationDetails)

    then:
    result.getPayLeg().getPayReceive() == PayReceive.PAY
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -12
    result.getPayLeg().getNearNotional() == 13
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 10
    result.getReceiveLeg().getNearNotional() == -10
  }

  def "should correctly merge trade with FXSWAP security master when trade ccy is pay ccy"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxswap(true)

    when:
    def result = merger.mergeDetails(FXSWAP, tradeDetails, allocationDetails)

    then:
    result.getPayLeg().getPayReceive() == PayReceive.PAY
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -10
    result.getPayLeg().getNearNotional() == 10
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 12
    result.getReceiveLeg().getNearNotional() == -13
  }

  def "should correctly merge trade with FXOPT security master when BUY CALL"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", positionType: PositionType.BUY, tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxopt(CallPutType.CALL)

    when:
    def result = merger.mergeDetails(FXOPT, tradeDetails, allocationDetails)

    then:
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -12
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 10
  }

  def "should correctly merge trade with FXOPT security master when BUY PUT"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", positionType: PositionType.BUY, tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxopt(CallPutType.PUT)

    when:
    def result = merger.mergeDetails(FXOPT, tradeDetails, allocationDetails)

    then:
    result.getPayLeg().getPayReceive() == PayReceive.PAY
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -10
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 12
  }

  def "should correctly merge trade with FXCOLLAR security master when BUY and callPutType is null"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", positionType: PositionType.BUY, tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = fxcollar()

    when:
    def result = merger.mergeDetails(FXCOLLAR, tradeDetails, allocationDetails)

    then:
    result.getOptionTradeDetails().strike == 1.2d
    result.getOptionTradeDetails().otherOptionStrike == 1.3d
    result.getPayLeg().getPayReceive() == PayReceive.PAY
    result.getPayLeg().getCurrency() == "USD"
    result.getPayLeg().getNotional() == -12
    result.getReceiveLeg().getPayReceive() == PayReceive.RECEIVE
    result.getReceiveLeg().getCurrency() == "EUR"
    result.getReceiveLeg().getNotional() == 10
    result.getOptionTradeDetails().getCallPutType() == CallPutType.CALL

    result.getOptionTradeDetails().getOtherOptionCounterNotional() == 13
    result.getPositionType() == PositionType.BUY
  }

  def static fxfwd() {
    new TradeDetails(
      info: new TradeInfoDetails(tradeCurrency: "EUR"),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      currency: "USD",
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      currency: "EUR"
      ),
      fxRate: 1.2
      )
  }

  def static fxswap(boolean tradeIsPayCcy) {
    new TradeDetails(
      info: new TradeInfoDetails(tradeCurrency: tradeIsPayCcy ? "USD" : "EUR"),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      currency: "USD",
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      currency: "EUR"
      ),
      fxRate: 1.2,
      nearDateFxRate: 1.3
      )
  }

  def static fxopt(CallPutType callPutType) {
    new TradeDetails(
      info: new TradeInfoDetails(tradeCurrency: "EUR"),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      currency: "USD",
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      currency: "EUR",
      ),
      optionTradeDetails: new OptionTradeDetails(
      strike: 1.2,
      callPutType: callPutType
      )
      )
  }

  def static fxcollar() {
    new TradeDetails(
      info: new TradeInfoDetails(tradeCurrency: "EUR"),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      currency: "USD",
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      currency: "EUR"
      ),
      optionTradeDetails: new OptionTradeDetails(
      strike: 1.2,
      otherOptionStrike: 1.3,
      callPutType: null
      )
      )
  }
}
