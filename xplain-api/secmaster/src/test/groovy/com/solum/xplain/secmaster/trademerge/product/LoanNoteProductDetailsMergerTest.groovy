package com.solum.xplain.secmaster.trademerge.product

import static com.solum.xplain.core.portfolio.CoreProductType.LOAN_NOTE
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.loanNoteTrade
import static com.solum.xplain.core.portfolio.value.CounterpartyType.CLEARED

import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import java.time.LocalDate
import spock.lang.Specification

class LoanNoteProductDetailsMergerTest extends Specification {
  def merger = new LoanNoteProductDetailsMerger()

  def "should return correct product type"() {
    expect:
    merger.productTypes() == [LOAN_NOTE]
  }

  def "should merge loan note details"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 10, counterPartyType: CLEARED, counterParty: "CP", tradeDate: LocalDate.of(2022, 10, 10), description: "allocation comments")
    def tradeDetails = loanNoteTrade(LocalDate.of(2022, 10, 10))

    when:
    def result = merger.mergeDetails(LOAN_NOTE, tradeDetails, allocationDetails)

    then:
    result.getReceiveLeg().getNotional() == 10
  }
}
