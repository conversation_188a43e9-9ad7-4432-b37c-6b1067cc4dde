package com.solum.xplain.secmaster.controller.type;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.form.InflationTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.InflationTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.InflationTradeView;
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService;
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity;
import io.atlassian.fugue.Either;
import jakarta.validation.groups.Default;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/security-master/inflation")
@AllArgsConstructor
@Getter
public class SecMasterInflationController
    implements SecMasterTypedController<InflationTradeForm, InflationTradeView> {

  private final SecMasterTradeControllerService service;

  @Override
  public Either<ErrorItem, InflationTradeView> toViewFunction(SecMasterTradeReadEntity e) {
    return InflationTradeView.of(e);
  }

  @Override
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> insert(
      @Validated({InflationTradeGroup.class, ReferenceTradeGroup.class, Default.class}) @RequestBody
          InflationTradeForm form) {
    return eitherErrorItemResponse(getService().insert(form));
  }

  @Override
  @PutMapping("{tradeEntityId}/{version}")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> update(
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @Validated(value = {InflationTradeGroup.class, ReferenceTradeGroup.class, Default.class})
          @RequestBody
          InflationTradeForm form) {
    return eitherErrorItemResponse(getService().update(tradeEntityId, version, form));
  }
}
