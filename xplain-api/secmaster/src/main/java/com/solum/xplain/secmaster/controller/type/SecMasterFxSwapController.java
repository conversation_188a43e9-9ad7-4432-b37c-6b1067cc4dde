package com.solum.xplain.secmaster.controller.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import com.solum.xplain.core.portfolio.value.FxSwapTradeView;
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService;
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/security-master/fxswap")
@AllArgsConstructor
@Getter
public class SecMasterFxSwapController
    implements SecMasterTypedController<FxSwapTradeForm, FxSwapTradeView> {

  private final SecMasterTradeControllerService service;

  @Override
  public Either<ErrorItem, FxSwapTradeView> toViewFunction(SecMasterTradeReadEntity e) {
    return FxSwapTradeView.of(e);
  }
}
