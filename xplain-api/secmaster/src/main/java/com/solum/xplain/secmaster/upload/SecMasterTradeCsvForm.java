package com.solum.xplain.secmaster.upload;

import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SecMasterTradeCsvForm implements ResolvableEmbeddedVersionValue<TradeValue, String> {

  private final String entityId;
  private final TradeValue item;

  public static SecMasterTradeCsvForm newOf(String externalTradeId, TradeValue value) {
    return new SecMasterTradeCsvForm(externalTradeId, value);
  }

  @Override
  public TradeValue toVersionValue() {
    return item;
  }
}
