package com.solum.xplain.workflow.service.command;

import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.repository.CacheSettingDataModificationCommand;
import com.solum.xplain.workflow.value.WorkflowStatus;
import jakarta.annotation.Nonnull;
import java.time.LocalDateTime;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

/**
 * Command to update the {@link ProcessExecution#getStatus() status} on a process execution. A
 * {@link BulkOperations#updateOne(Query, Update)} issued to reflect this in the database.
 *
 * <p>If the process was previously inactive then this will also set the start time to the current
 * time.
 *
 * @param processExecution the execution to be updated
 * @param newStatus the new status to apply to the process execution
 */
public record ProcessSetStatus(ProcessExecution<?, ?> processExecution, WorkflowStatus newStatus)
    implements CacheSettingDataModificationCommand<ProcessExecution<?, ?>> {
  @Override
  public Class<ProcessExecution> getEntity() {
    return ProcessExecution.class;
  }

  @Nonnull
  @Override
  public ProcessExecution<?, ?> apply(BulkOperations bulkOps) {
    WorkflowStatus oldStatus = processExecution.getStatus();
    processExecution.setStatus(newStatus);
    processExecution.setModifiedAt(LocalDateTime.now());
    Update update =
        Update.update(ProcessExecution.Fields.status, newStatus)
            .set(ProcessExecution.Fields.modifiedAt, processExecution.getModifiedAt());
    if (oldStatus == WorkflowStatus.INACTIVE) {
      processExecution.setStartedAt(processExecution.getModifiedAt());
      update.set(ProcessExecution.Fields.startedAt, processExecution.getStartedAt());
    }
    bulkOps.updateOne(
        Query.query(Criteria.where(ProcessExecution.Fields.id).is(processExecution.getId())),
        update);

    return processExecution;
  }
}
