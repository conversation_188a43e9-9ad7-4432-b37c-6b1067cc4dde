package com.solum.xplain.workflow.entity;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.workflow.value.WorkflowStatus;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Execution of a step definition. The {@link #processId} and {@link #businessKey} are copied across
 * from the parent {@link ProcessExecution} and this adds a {@link #stepId} which references the
 * step definition within that process. {@link #executionId} is a foreign key to the {@code
 * ProcessExecution}. The {@link #initialState} is set when the step is created from the current
 * process state, and the step can add properties to the {@link #outcome} to mutate the current
 * process state which will then carry to the next step. These can be nested properties using
 * standard dotted path syntax.
 *
 * @param <T> type representing the attached process state (must be serializable)
 */
@Data
@NoArgsConstructor
@Document(collection = StepInstance.STEP_INSTANCE_COLLECTION)
@FieldNameConstants
public class StepInstance<T extends Serializable> implements CacheableWorkflowEntity, Serializable {
  public static final String STEP_INSTANCE_COLLECTION = "wfStepInstance";

  @Id private ObjectId id;
  @Nonnull private ObjectId executionId;
  @Nonnull private String processId;
  @Nonnull private String businessKey;
  @Nullable private ObjectId rootExecutionId;
  @Nonnull private String rootBusinessKey;
  @Nonnull private String stepId;
  private boolean reportable;
  @Nonnull private WorkflowStatus status;
  @Nonnull private T initialState;
  @Nonnull private MutablePropertyValues outcome;
  @Nullable private String holdReason;
  @CreatedDate private LocalDateTime startedAt;
  @LastModifiedDate private LocalDateTime modifiedAt;
  @Nullable private AuditUser assignee;
  @Nullable private AssignmentRule candidate;

  public boolean isAssignedTo(XplainPrincipal user) {
    return assignee != null && assignee.getUserId().equals(user.getId());
  }

  @Override
  public boolean cacheable() {
    return reportable;
  }
}
