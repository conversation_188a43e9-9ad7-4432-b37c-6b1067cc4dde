package com.solum.xplain.workflow.service.command;

import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.repository.CacheSettingDataModificationCommand;
import com.solum.xplain.workflow.value.StepDefinition;
import com.solum.xplain.workflow.value.WorkflowStatus;
import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.bson.types.ObjectId;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.data.mongodb.core.BulkOperations;

public record StepCreate<T extends Serializable, C extends Serializable>(
    StepDefinition<T, C> def, ProcessExecution<T, C> processExecution, T state)
    implements CacheSettingDataModificationCommand<StepInstance<T>> {
  @SuppressWarnings("rawtypes")
  @Override
  public Class<StepInstance> getEntity() {
    return StepInstance.class;
  }

  @Nonnull
  @Override
  public StepInstance<T> apply(BulkOperations bulkOps) {
    StepInstance<T> stepInstance = new StepInstance<>();
    stepInstance.setId(ObjectId.get());
    stepInstance.setStartedAt(LocalDateTime.now());
    stepInstance.setModifiedAt(stepInstance.getStartedAt());
    stepInstance.setBusinessKey(processExecution.getBusinessKey());
    stepInstance.setExecutionId(processExecution.getId());
    stepInstance.setProcessId(processExecution.getProcessId());
    stepInstance.setRootExecutionId(
        processExecution.getRootExecutionId() != null
            ? processExecution.getRootExecutionId()
            : processExecution.getId());
    stepInstance.setRootBusinessKey(processExecution.getRootBusinessKey());
    stepInstance.setStepId(def.id());
    stepInstance.setReportable(def.reportable());
    stepInstance.setStatus(WorkflowStatus.ACTIVE);
    stepInstance.setInitialState(state);
    stepInstance.setOutcome(new MutablePropertyValues());
    bulkOps.insert(stepInstance);

    return stepInstance;
  }
}
