package com.solum.xplain.workflow;

import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.validation.annotation.Validated;

@Validated
@ConfigurationProperties(prefix = "app.workflow")
public record WorkflowProperties(
    @NotNull Duration flushFrequency,
    @NotNull String cleanupCron,
    @NotNull @DefaultValue Map<String, WorkflowDataCacheProperties> cache) {
  public static final WorkflowDataCacheProperties DEFAULT_CACHE_PROPERTIES =
      new WorkflowDataCacheProperties(Duration.ofMinutes(5), Duration.ofMinutes(5), 10_000);

  public WorkflowDataCacheProperties cacheProperties(String collectionName) {
    return cache.getOrDefault(collectionName, DEFAULT_CACHE_PROPERTIES);
  }

  public record WorkflowDataCacheProperties(
      Duration sharedCacheTtl, Duration nearCacheTtl, int maxEntries) {}
}
