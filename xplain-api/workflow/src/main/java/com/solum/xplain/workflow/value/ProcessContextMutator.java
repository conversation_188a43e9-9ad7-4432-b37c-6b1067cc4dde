package com.solum.xplain.workflow.value;

import com.solum.xplain.workflow.entity.ProcessExecution;
import java.io.Serializable;
import org.springframework.lang.Nullable;

public interface ProcessContextMutator<T extends Serializable, C extends Serializable> {

  /**
   * Mutate the process execution context. Return non-empty MutablePropertyValues if the process
   * execution was mutated.
   *
   * @param processExecution the process execution to be mutated
   * @return Optional<MutablePropertyValues> not empty if the process execution was mutated
   */
  ProcessExecutionContextMutation<C> mutate(ProcessExecution<T, C> processExecution);

  record ProcessExecutionContextMutation<C extends Serializable>(
      boolean updated, @Nullable C context) {}
}
