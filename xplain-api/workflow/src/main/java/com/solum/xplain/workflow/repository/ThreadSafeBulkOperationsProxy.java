package com.solum.xplain.workflow.repository;

import com.mongodb.bulk.BulkWriteResult;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.util.Pair;

/**
 * This class wraps a MongoDB bulk operations proxy and makes it safe to add new operations from
 * multiple threads. Initial implementation is just to synchronize all access but for performance we
 * could later implement a fast version using ConcurrentLinkedQueue to replace
 * DefaultBulkOperations' models list and no event firing.
 */
@RequiredArgsConstructor
@Slf4j
public class ThreadSafeBulkOperationsProxy implements BulkOperations {

  public static final int MAX_OPERATIONS = 100;
  private final MongoOperations mongoOperations;
  private final Class<?> entityClass;

  private volatile BulkOperations wrappedBulkOps;
  private int operationsCount = 0;

  @Nonnull
  @Override
  public synchronized BulkOperations insert(@Nonnull Object documents) {
    beforeOperation();
    return wrappedBulkOps.insert(documents);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations insert(@Nonnull List<?> documents) {
    beforeOperation();
    return wrappedBulkOps.insert(documents);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations updateOne(
      @Nonnull Query query, @Nonnull UpdateDefinition update) {
    beforeOperation();
    return wrappedBulkOps.updateOne(query, update);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations updateOne(
      @Nonnull List<Pair<Query, UpdateDefinition>> updates) {
    beforeOperation();
    return wrappedBulkOps.updateOne(updates);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations updateMulti(
      @Nonnull Query query, @Nonnull UpdateDefinition update) {
    beforeOperation();
    return wrappedBulkOps.updateMulti(query, update);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations updateMulti(
      @Nonnull List<Pair<Query, UpdateDefinition>> updates) {
    beforeOperation();
    return wrappedBulkOps.updateMulti(updates);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations upsert(
      @Nonnull Query query, @Nonnull UpdateDefinition update) {
    beforeOperation();
    return wrappedBulkOps.upsert(query, update);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations upsert(@Nonnull List<Pair<Query, Update>> updates) {
    beforeOperation();
    return wrappedBulkOps.upsert(updates);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations remove(@Nonnull Query remove) {
    beforeOperation();
    return wrappedBulkOps.remove(remove);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations remove(@Nonnull List<Query> removes) {
    beforeOperation();
    return wrappedBulkOps.remove(removes);
  }

  @Nonnull
  @Override
  public synchronized BulkOperations replaceOne(
      @Nonnull Query query, @Nonnull Object replacement, @Nonnull FindAndReplaceOptions options) {
    beforeOperation();
    return wrappedBulkOps.replaceOne(query, replacement, options);
  }

  @SuppressWarnings("NullableProblems") // We return null, breaking @NonNullApi on package
  @Nullable
  @Override
  public synchronized BulkWriteResult execute() {
    if (wrappedBulkOps == null) {
      return null;
    }
    log.trace("Flushing data modification command queue for {}", entityClass.getSimpleName());
    var result = wrappedBulkOps.execute();
    wrappedBulkOps = null;
    operationsCount = 0;
    log.trace("Flushed data modification command queue for {}", entityClass.getSimpleName());
    return result;
  }

  /**
   * Ensures that the wrapped bulk operations proxy is initialized before any operation is, and if
   * we our threshold of operations exceeds a certain limit, we first flush the operations
   */
  private void beforeOperation() {
    if (++operationsCount >= MAX_OPERATIONS) {
      log.debug("Forcing flush due to high volume for: {}", entityClass.getSimpleName());
      execute();
    }
    if (wrappedBulkOps == null) {
      wrappedBulkOps = mongoOperations.bulkOps(BulkOperations.BulkMode.ORDERED, entityClass);
    }
  }
}
