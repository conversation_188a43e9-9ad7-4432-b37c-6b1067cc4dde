package com.solum.xplain.workflow.repository;

import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class WorkflowDataCacheService {
  private final MongoOperations mongoOperations;
  private final CacheManager cacheManager;

  private final ConcurrentHashMap<Class<?>, Cache> cacheCache = new ConcurrentHashMap<>();

  public <T> Cache getCache(Class<? super T> entityClass) {
    return cacheCache.computeIfAbsent(entityClass, this::ensureCache);
  }

  private <T> Cache ensureCache(Class<T> entity) {
    String collectionName = mongoOperations.getCollectionName(entity);
    return cacheManager.getCache(collectionName);
  }
}
