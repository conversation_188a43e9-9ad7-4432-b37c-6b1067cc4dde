package com.solum.xplain.workflow.event;

import com.solum.xplain.workflow.entity.ProcessExecution;
import java.io.Serializable;
import org.springframework.beans.PropertyValues;

/**
 * This event is fired when a user task form is submitted. It is primarily used internally to
 * complete the user task.
 *
 * @param processExecution process execution with details of the process which has had a form
 *     submission
 * @param stepId the step for which the form was submitted
 * @param formData the property values resulting from the form submission
 * @param <T> type representing the attached process state (must be serializable)
 * @param <C> type representing the attached process context (must be serializable)
 */
public record UserTaskSubmittedEvent<T extends Serializable, C extends Serializable>(
    ProcessExecution<T, C> processExecution, String stepId, PropertyValues formData)
    implements Serializable {}
