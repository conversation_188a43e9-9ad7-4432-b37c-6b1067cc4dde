package com.solum.xplain.workflow.value;

import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.service.StepStateOps;
import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Objects;
import java.util.Queue;
import java.util.function.Consumer;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Definition of a step which calls subprocesses and does not complete until they are finished. A
 * {@link SubprocessContextCreator} is passed in which creates a stream of initial contexts for
 * subprocesses using the initial state & context of this step (which is the state & context of the
 * parent process). For each state in that stream, a new process is created using {@link
 * StepStateOps#createSubprocess(String, String, Serializable, Class, boolean)}.
 *
 * <p>Call activities are always reportable.
 *
 * @param id ID of the step
 * @param calledProcessId ID of the subprocess to call
 * @param multiInstance type of multi-instance behavior (only PARALLEL is supported)
 * @param subprocessContextCreator creates subprocess contexts from the parent state and context
 * @param dataAssociations list of data associations to copy data between the parent and subprocess
 * @param <T> type representing the attached process state (must be serializable)
 * @param <C> type representing the immutable process context (must be serializable)
 * @param <S> type representing the initial subprocess state (must be serializable)
 * @param <D> type representing the immutable subprocess context (must be serializable)
 */
@Slf4j
public record CallActivityDefinitionView<
        T extends Serializable,
        C extends Serializable,
        S extends Serializable,
        D extends Serializable>(
    String id,
    String calledProcessId,
    MultiInstance multiInstance,
    SubprocessContextCreator<T, C, S, D> subprocessContextCreator,
    DataAssociationView... dataAssociations)
    implements StepDefinition<T, C> {

  public boolean executable() {
    return true;
  }

  @Override
  public boolean reportable() {
    return true;
  }

  @Override
  public boolean autoComplete() {
    return false;
  }

  @Override
  public void runStep(StepStateOps<T, C> ops) {
    Class<S> subprocessStateType = subprocessContextCreator.subprocessStateType();
    Stream<D> subprocessContexts =
        subprocessContextCreator.subprocessContext(ops.getInitialState(), ops.getContext());

    log.trace(
        "WF: {} ({}) - {} - run subprocesses {}",
        ops.getProcessId(),
        ops.getBusinessKey(),
        ops.getStepId(),
        multiInstance);

    // In processing the stream, this holds the most recent ACTIVE subprocess created by mapMulti().
    Queue<ProcessExecution<S, D>> deferredSubprocess = new LinkedList<>();

    Stream<ProcessExecution<S, D>> runAfterCreation =
        subprocessContexts
            .sequential() // deferredSubprocess is not thread-safe
            .mapMulti(
                (D context, Consumer<ProcessExecution<S, D>> runAfter) -> {
                  final String businessKey =
                      subprocessContextCreator.subprocessBusinessKey(ops.getBusinessKey(), context);
                  ProcessExecution<S, D> subprocess =
                      ops.createSubprocess(
                          calledProcessId,
                          businessKey,
                          context,
                          subprocessStateType,
                          multiInstance == MultiInstance.PARALLEL);

                  // We can now safely allow the previously-created subprocess downstream because
                  // the tracker knows about this one. Even if it completes immediately, the parent
                  // will not be treated as complete.
                  ProcessExecution<S, D> previousSubprocess = deferredSubprocess.poll();
                  if (previousSubprocess != null) {
                    runAfter.accept(previousSubprocess);
                  }
                  if (subprocess.getStatus() == WorkflowStatus.ACTIVE) {
                    deferredSubprocess.offer(subprocess); // To be picked up next time around.
                  }
                });
    runAfterCreation.forEach(ops::runSubprocess);

    // The last ACTIVE subprocess created didn't get passed on into runAfterCreation, so we'll run
    // it now.
    ProcessExecution<S, D> finalSubprocess = deferredSubprocess.poll();
    if (finalSubprocess != null) {
      ops.runSubprocess(finalSubprocess);
    }
  }

  /**
   * Overridden to use the data association array contents rather than using the array reference.
   * {@inheritDoc}
   */
  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    CallActivityDefinitionView<?, ?, ?, ?> that = (CallActivityDefinitionView<?, ?, ?, ?>) o;
    return Objects.equals(id, that.id)
        && Objects.equals(calledProcessId, that.calledProcessId)
        && multiInstance == that.multiInstance
        && Arrays.equals(dataAssociations, that.dataAssociations);
  }

  /**
   * Overridden to use the data association array contents rather than using the array reference.
   * {@inheritDoc}
   */
  @Override
  public int hashCode() {
    int result = Objects.hash(id, calledProcessId, multiInstance);
    result = 31 * result + Arrays.hashCode(dataAssociations);
    return result;
  }

  /**
   * Fluent interface for creating a new input data association using a builder.
   *
   * <p>See Activiti <a
   * href='https://www.activiti.org/userguide/#bpmnCallActivityPassVariables'>Passing Variables</a>
   * documentation for more information.
   *
   * @param propertyPath property path on this process's state
   * @return builder to allow the association to be defined
   */
  public static DataAssociationBuilder withInput(String propertyPath) {
    return new DataAssociationBuilder(propertyPath, DataAssociationDirection.INPUT);
  }

  /**
   * Fluent interface for creating a new output data association using a builder.
   *
   * <p>See Activiti <a
   * href='https://www.activiti.org/userguide/#bpmnCallActivityPassVariables'>Passing Variables</a>
   * documentation for more information.
   *
   * @param propertyPath property path on the subprocess state
   * @return builder to allow the association to be defined
   */
  public static DataAssociationBuilder withOutput(String propertyPath) {
    return new DataAssociationBuilder(propertyPath, DataAssociationDirection.OUTPUT);
  }

  @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
  public static class DataAssociationBuilder {
    private final String sourcePropertyPath;

    /**
     * whether the property is input (parent is source, child is target) or output (child is source,
     * parent is target)
     */
    private final DataAssociationDirection direction;

    public DataAssociationView asProperty(String targetPropertyPath) {
      return new DataAssociationView(sourcePropertyPath, targetPropertyPath, direction);
    }
  }
}
