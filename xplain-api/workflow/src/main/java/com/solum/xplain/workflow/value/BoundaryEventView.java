package com.solum.xplain.workflow.value;

import jakarta.annotation.Nonnull;
import java.util.Set;
import org.springframework.util.Assert;

public record BoundaryEventView(
    String eventId, EventType eventType, CatchingEffect catchingEffect, Set<String> attachedSteps)
    implements CatchingEvent {
  public static BoundaryEventViewBuilder boundaryEvent(
      @Nonnull String eventId,
      @Nonnull EventType eventType,
      @Nonnull CatchingEffect catchingEffect) {
    Assert.isTrue(eventType == EventType.ERROR, "Only ERROR boundary events are supported");
    Assert.isTrue(
        CatchingEffect.INTERRUPTING == catchingEffect,
        "ERROR boundary events must be interrupting");

    return new BoundaryEventViewBuilder(eventId, eventType, catchingEffect);
  }

  public static class BoundaryEventViewBuilder {
    private final String eventId;
    private final EventType eventType;
    private final CatchingEffect catchingEffect;

    public BoundaryEventViewBuilder(
        String eventId, EventType eventType, CatchingEffect catchingEffect) {
      this.eventId = eventId;
      this.eventType = eventType;
      this.catchingEffect = catchingEffect;
    }

    public BoundaryEventView attachedTo(String... stepIds) {
      return new BoundaryEventView(eventId, eventType, catchingEffect, Set.of(stepIds));
    }
  }
}
