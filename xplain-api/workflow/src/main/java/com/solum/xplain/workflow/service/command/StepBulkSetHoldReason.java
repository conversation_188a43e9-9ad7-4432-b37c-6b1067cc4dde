package com.solum.xplain.workflow.service.command;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;
import static org.springframework.data.mongodb.core.query.Update.update;

import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.provider.WorkflowProvider;
import com.solum.xplain.workflow.repository.CacheClearingDataModificationCommand;
import com.solum.xplain.workflow.value.StepDefinition;
import com.solum.xplain.workflow.value.WorkflowStatus;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

/**
 * Command to bulk set the @link StepInstance#getHoldReason() hold reason} on step instances. The
 * status will also be updated to {@link WorkflowStatus#HELD}. In-memory objects will be unaffected
 * but a {@link BulkOperations#updateMulti(Query, Update)} issued to reflect this in the database.
 *
 * <p>The hold reason is a string but this would typically be a key into a set of predefined reasons
 * managed by the calling application.
 *
 * @param processId the ID of the process definition as supplied by a {@link WorkflowProvider}.
 * @param businessKeys the unique keys to identify specific executions of the process
 * @param definition the step definition of the step to update across executions
 * @param holdReason the string to set as the hold reason
 */
public record StepBulkSetHoldReason(
    String processId, List<String> businessKeys, StepDefinition<?, ?> definition, String holdReason)
    implements CacheClearingDataModificationCommand<StepInstance<?>> {
  @Override
  public Class<? super StepInstance<?>> getEntity() {
    return StepInstance.class;
  }

  @Override
  public void apply(BulkOperations bulkOps) {
    bulkOps.updateMulti(
        query(
            where(StepInstance.Fields.processId)
                .is(processId)
                .and(StepInstance.Fields.businessKey)
                .in(businessKeys)
                .and(StepInstance.Fields.reportable)
                .is(true)
                .and(StepInstance.Fields.stepId)
                .is(definition.id())
                .and(StepInstance.Fields.status)
                .is(WorkflowStatus.ACTIVE)),
        update(StepInstance.Fields.holdReason, holdReason)
            .set(StepInstance.Fields.status, WorkflowStatus.HELD)
            .set(StepInstance.Fields.modifiedAt, LocalDateTime.now()));
  }
}
