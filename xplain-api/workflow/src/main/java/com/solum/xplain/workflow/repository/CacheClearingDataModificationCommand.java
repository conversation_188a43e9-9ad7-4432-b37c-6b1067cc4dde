package com.solum.xplain.workflow.repository;

import com.solum.xplain.workflow.entity.CacheableByObjectId;
import org.springframework.cache.Cache;
import org.springframework.data.mongodb.core.BulkOperations;

/**
 * Convenient subinterface which evicts all data in the cache using {@link Cache#clear()}. This
 * naive approach should be used when a large bulk update makes the previously-cached data invalid.
 *
 * @param <T> the entity type being affected by this command
 */
public interface CacheClearingDataModificationCommand<T extends CacheableByObjectId>
    extends DataModificationCommand<T> {
  @Override
  default T apply(BulkOperations bulkOps, Cache cache) {
    apply(bulkOps);
    cache.clear();
    return null;
  }

  void apply(BulkOperations bulkOps);
}
