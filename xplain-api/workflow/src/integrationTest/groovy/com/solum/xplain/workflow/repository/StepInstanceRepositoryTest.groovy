package com.solum.xplain.workflow.repository

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class StepInstanceRepositoryTest extends IntegrationSpecification {

  @Resource
  StepInstanceRepository repository
  @Resource
  MongoOperations mongoOperations

  def cleanup() {
    mongoOperations.remove(new Query(), ProcessExecution)
    mongoOperations.remove(new Query(), StepInstance)
  }

  @Unroll
  def "should find completable parent steps (parent=#parentStatus, children=#child1Status,#child2Status, parallel=#parallel)"() {
    given:
    def parent = new StepInstance(
      processId: "processId",
      businessKey: "businessKey",
      status: parentStatus,
      )
    def nonParent = new StepInstance(
      processId: "processId",
      businessKey: "businessKey",
      status: WorkflowStatus.ACTIVE,
      )
    mongoOperations.insertAll([parent, nonParent])
    def child1 = new ProcessExecution(
      processId: "subprocessId",
      businessKey: "businessKey1",
      status: child1Status,
      parentStepInstanceId: parent.id,
      sequence: parallel ? null : 1,
      )
    def child2 = new ProcessExecution(
      processId: "subprocessId",
      businessKey: "businessKey2",
      status: child2Status,
      parentStepInstanceId: parent.id,
      sequence: parallel ? null : 2,
      )
    mongoOperations.insertAll([child1, child2])

    when:
    def result = repository.findActiveStepInstancesWithCompletedParallelSubProcesses()

    then:
    result.size() == results
    if (results > 0) {
      assert result[0].id == parent.id
    }

    where:
    parentStatus          | child1Status          | child2Status          | parallel || results
    WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | WorkflowStatus.DONE   | true     || 1
    WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | true     || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | true     || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | true     || 0
    WorkflowStatus.DONE   | WorkflowStatus.DONE   | WorkflowStatus.DONE   | true     || 0
    WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | true     || 0
    WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | true     || 0
    WorkflowStatus.DONE   | WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | true     || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | WorkflowStatus.DONE   | false    || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | false    || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | false    || 0
    WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | false    || 0
    WorkflowStatus.DONE   | WorkflowStatus.DONE   | WorkflowStatus.DONE   | false    || 0
    WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | WorkflowStatus.ACTIVE | false    || 0
    WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | WorkflowStatus.DONE   | false    || 0
    WorkflowStatus.DONE   | WorkflowStatus.DONE   | WorkflowStatus.ACTIVE | false    || 0
  }
}
