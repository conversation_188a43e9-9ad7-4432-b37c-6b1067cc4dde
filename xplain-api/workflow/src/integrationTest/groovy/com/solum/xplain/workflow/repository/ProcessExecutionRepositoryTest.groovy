package com.solum.xplain.workflow.repository

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import jakarta.annotation.Resource
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ProcessExecutionRepositoryTest extends IntegrationSpecification {

  @Resource
  ProcessExecutionRepository repository
  @Resource
  MongoOperations mongoOperations

  def cleanup() {
    mongoOperations.remove(new Query(), ProcessExecution)
    mongoOperations.remove(new Query(), StepInstance)
  }

  def "should save record"() {
    given:
    def record = new ProcessExecution<String, String>(
      processId: "processId",
      businessKey: "businessKey",
      status: WorkflowStatus.ACTIVE,
      currentState: "Hello"
      )

    when:
    repository.save(record)
    def readBack = mongoOperations.findOne(new Query(), ProcessExecution)

    then:
    readBack.id == record.id
    readBack.processId == record.processId
    readBack.businessKey == record.businessKey
    readBack.status == record.status
    readBack.currentState == record.currentState
    readBack.modifiedAt != null
    readBack.startedAt != null
  }

  def "should delete all descendants"() {
    given:
    def grampy = new ProcessExecution<String, String>(
      id: new ObjectId(),
      processId: "grampyProcessId",
      businessKey: "grampyBusinessKey",
      rootBusinessKey: "grampyBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Pardon?"
      )
    def parent = new ProcessExecution<String, String>(
      id: new ObjectId(),
      processId: "parentProcessId",
      businessKey: "parentBusinessKey",
      rootBusinessKey: "grampyBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "World",
      parentExecutionId: grampy.id,
      )
    def record = new ProcessExecution<String, String>(
      processId: "processId",
      businessKey: "businessKey",
      rootBusinessKey: "grampyBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Hello",
      parentExecutionId: parent.id,
      )
    def unrelated = new ProcessExecution<String, String>(
      id: new ObjectId(),
      processId: "unrelatedProcessId",
      businessKey: "unrelatedBusinessKey",
      rootBusinessKey: "unrelatedBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Ignored",
      parentExecutionId: new ObjectId(),
      )
    def parentStep = new StepInstance<String>(
      executionId: parent.id,
      processId: parent.processId,
      businessKey: parent.businessKey,
      rootBusinessKey: "grampyBusinessKey",
      stepId: "stepId",
      status: WorkflowStatus.ACTIVE
      )
    def unrelatedStep = new StepInstance<String>(
      executionId: unrelated.id,
      processId: unrelated.processId,
      businessKey: unrelated.businessKey,
      rootBusinessKey: "unrelatedBusinessKey",
      stepId: "stepId",
      status: WorkflowStatus.ACTIVE
      )
    mongoOperations.insert([grampy, parent, record, unrelated], ProcessExecution)
    mongoOperations.insert([parentStep, unrelatedStep], StepInstance)

    when:
    def count = repository.deleteProcessByRootBusinessKey(grampy.businessKey)
    def remaining = mongoOperations.find(new Query(), ProcessExecution)
    def steps = mongoOperations.find(new Query(), StepInstance)

    then:
    count == 3
    remaining.size() == 1
    steps.size() == 1
  }

  def "should get distinct roots for same, parent and grandparent"() {
    given:
    def grampy = new ProcessExecution<String, String>(
      id: new ObjectId(),
      processId: "grampyProcessId",
      businessKey: "grampyBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Pardon?"
      )
    def parent = new ProcessExecution<String, String>(
      id: new ObjectId(),
      processId: "parentProcessId",
      businessKey: "parentBusinessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "World",
      rootExecutionId: grampy.id,
      rootBusinessKey: grampy.businessKey,
      parentExecutionId: grampy.id,
      parentBusinessKey: grampy.businessKey
      )
    def record = new ProcessExecution<String, String>(
      processId: "processId",
      businessKey: "businessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Hello",
      rootExecutionId: grampy.id,
      rootBusinessKey: grampy.businessKey,
      parentExecutionId: parent.id,
      parentBusinessKey: parent.businessKey
      )
    mongoOperations.insert([grampy, parent, record], ProcessExecution)

    when:
    def roots = repository.getRootProcessExecutions(List.of(record.businessKey, parent.businessKey, grampy.businessKey))

    then:
    roots.size() == 1
    roots[0].id == grampy.id
  }

  def "exists with matching process #processId and business key #businessKey"() {
    given:
    def record = new ProcessExecution<String, String>(
      processId: "processId",
      businessKey: "businessKey",
      status: WorkflowStatus.ACTIVE,
      context: "Greetings",
      currentState: "Hello"
      )
    mongoOperations.insert(record)

    when:
    def result = repository.existsByProcessIdAndBusinessKey(processId, businessKey)

    then:
    result == exists

    where:
    processId   | businessKey   || exists
    "proces5Id" | "bus1nessKey" || false
    "pr0cessId" | "businessKey" || false
    "processId" | "bu3inessKey" || false
    "processId" | "businessKey" || true
  }
}
