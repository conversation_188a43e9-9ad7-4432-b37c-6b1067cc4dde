package com.solum.xplain.app.version


import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpStatus
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@WebMvcTest(controllers = [VersionController])
@WithMockUser
@ActiveProfiles("test")
class VersionControllerTest extends Specification {

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper objectMapper

  def "should fetch version correctly"() {
    when:
    def response = mockMvc.perform(get('/version').with(csrf())).andReturn().response

    then:
    response.status == HttpStatus.OK.value()
    objectMapper.readValue(response.getContentAsString(), VersionInfo.class) == new VersionInfo("1.0.0")
  }
}
