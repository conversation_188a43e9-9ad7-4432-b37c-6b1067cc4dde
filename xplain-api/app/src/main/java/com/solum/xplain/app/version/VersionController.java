package com.solum.xplain.app.version;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/version")
public class VersionController {

  private final String version;

  public VersionController(@Value("${app.version}") String version) {
    this.version = version;
  }

  @Operation(summary = "Get app version")
  @GetMapping
  public VersionInfo version() {
    return new VersionInfo(version);
  }
}
