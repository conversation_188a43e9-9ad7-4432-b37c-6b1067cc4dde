package com.solum.xplain.trs.portfolio.value;

import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class NonMtmPortfolioItemUniqueKey {

  private final String nonMtmPortfolioId;
  private final String externalTrsTradeId;

  public static NonMtmPortfolioItemUniqueKey newOf(
      String nonMtmPortfolioId, String externalTrsTradeId) {
    return new NonMtmPortfolioItemUniqueKey(nonMtmPortfolioId, externalTrsTradeId);
  }

  public static NonMtmPortfolioItemUniqueKey newOf(NonMtmPortfolioItem item) {
    return new NonMtmPortfolioItemUniqueKey(item.getPortfolioId(), item.getExternalTradeId());
  }

  public String uniqueId() {
    return getNonMtmPortfolioId() + getExternalTrsTradeId();
  }
}
