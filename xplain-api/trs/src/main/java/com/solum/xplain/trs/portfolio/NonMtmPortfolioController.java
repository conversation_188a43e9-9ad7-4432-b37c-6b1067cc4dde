package com.solum.xplain.trs.portfolio;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static com.solum.xplain.core.lock.RequireLock.Type.PATH_VARIABLE;
import static com.solum.xplain.core.lock.XplainLock.COMPANY_SETTINGS_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.FIXINGS_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.MARKET_DATA_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.MDK_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.NON_MTM_TRADES_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.TRS_MARKET_DATA_LOCK_ID;
import static com.solum.xplain.core.lock.XplainLock.TRS_VALUATION_LOCK_ID;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_MODIFY_TRS_TRADE;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_RUN_NON_MTM_VALUATION;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_VIEW_NON_MTM_PORTFOLIOS;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_VIEW_TRS_TRADE;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.ScrolledGroupedFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.BitemporalDateLocker;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.lock.XplainLock;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioExportService;
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioItemUploadService;
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioUploadService;
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioCreateForm;
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioUpdateForm;
import com.solum.xplain.trs.portfolio.form.SearchNonMtmPortfolioItemForm;
import com.solum.xplain.trs.portfolio.trade.form.CreateTrsBondTradeForm;
import com.solum.xplain.trs.portfolio.trade.form.CreateTrsShareTradeForm;
import com.solum.xplain.trs.portfolio.trade.form.UpdateTrsBondTradeForm;
import com.solum.xplain.trs.portfolio.trade.form.UpdateTrsShareTradeForm;
import com.solum.xplain.trs.portfolio.trade.value.TrsBondTradeView;
import com.solum.xplain.trs.portfolio.trade.value.TrsShareTradeView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCondensedView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCountedView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioItemWithKeyView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioView;
import com.solum.xplain.trs.valuation.NonMtmValuationsService;
import com.solum.xplain.trs.valuation.value.NonMtmValuationForm;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RestController
@RequestMapping("/non-mtm-portfolios")
@AllArgsConstructor
@NullMarked
public class NonMtmPortfolioController {

  private final NonMtmPortfolioService service;

  private final BitemporalDateLocker bitemporalDateLocker;
  private final NonMtmValuationsService valuationsService;
  private final NonMtmPortfolioExportService exportService;
  private final NonMtmPortfolioUploadService uploadService;
  private final NonMtmPortfolioItemUploadService uploadItemService;

  @Operation(summary = "Get all non-mtm portfolios")
  @ScrolledFiltered
  @CommonErrors
  @Sorted
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_NON_MTM_PORTFOLIOS)
  public ScrollableEntry<NonMtmPortfolioCountedView> getAll(
      @RequestParam LocalDate stateDate,
      @ParameterObject
          @SortDefault(
              sort = {
                NonMtmPortfolioView.Fields.companyName,
                NonMtmPortfolioView.Fields.entityName,
                NonMtmPortfolioCondensedView.Fields.externalPortfolioId
              })
          ScrollRequest scrollRequest,
      @ParameterObject TableFilter tableFilter,
      @ParameterObject NonMtmPortfolioFilter nonMtmPortfolioFilter,
      Authentication auth) {
    return service.getAll(
        scrollRequest, tableFilter, nonMtmPortfolioFilter, newOf(stateDate), auth);
  }

  @Operation(summary = "Get portfolios for company legal entity")
  @CommonErrors
  @GetMapping("/entities")
  @PreAuthorize(AUTHORITY_VIEW_NON_MTM_PORTFOLIOS)
  public List<EntityNameView> getAllForCompanyLegalEntity(
      @RequestParam("entityId") String entityId, Authentication auth) {
    return service.legalEntityPortfolioNames(auth, entityId);
  }

  @Operation(summary = "Create new non-mtm portfolio")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> newPortfolio(
      @Valid @RequestBody NonMtmPortfolioCreateForm newForm, Authentication authentication) {
    return eitherErrorItemResponse(service.create(authentication, newForm));
  }

  @Operation(summary = "Get all non-mtm portfolios in csv format")
  @SortedFiltered
  @CommonErrors
  @GetMapping("/portfolios-csv")
  @PreAuthorize(AUTHORITY_VIEW_NON_MTM_PORTFOLIOS)
  public ResponseEntity<ByteArrayResource> exportPortfolios(
      @SortDefault(
              sort = {
                NonMtmPortfolioView.Fields.companyName,
                NonMtmPortfolioView.Fields.entityName,
                NonMtmPortfolioCondensedView.Fields.externalPortfolioId
              })
          Sort sort,
      TableFilter tableFilter,
      NonMtmPortfolioFilter portfolioFilter,
      Authentication authentication,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.exportPortfolios(
            authentication, sort, tableFilter, portfolioFilter, stateDate));
  }

  @Operation(summary = "Get all non-mtm portfolio trade items in csv format")
  @GetMapping("/trades-csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<ByteArrayResource> getAllItemsCsv(
      @RequestParam LocalDate stateDate, Authentication authentication) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemFileResponse(
        exportService.exportAllPortfolioItems(authentication, bitemporalStateDate));
  }

  @Operation(summary = "Upload non-mtm portfolio csv file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadPortfolios(
      @RequestPart MultipartFile file,
      @RequestParam("duplicates") DuplicateAction action,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode,
      Authentication authentication)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadService.uploadPortfolios(authentication, parsingMode, action, file.getBytes()));
  }

  @Operation(summary = "Upload TRS trades csv file")
  @PostMapping(value = "/trades/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadTrades(
      @RequestPart MultipartFile file,
      @Valid @ParameterObject ImportOptions importOptions,
      Authentication user)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadItemService.uploadTrades(user, file.getBytes(), importOptions));
  }

  @Operation(summary = "Validate TRS trades csv file")
  @PostMapping(value = "/trades/upload/validate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<ValidationResponse> validateImportFile(
      @RequestPart MultipartFile file,
      @Valid @ParameterObject ImportOptions importOptions,
      Authentication user)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadItemService.validateTradesImport(user, file.getBytes(), importOptions));
  }

  @Operation(summary = "Upload TRS trades for non-mtm porfolio csv file")
  @PostMapping(
      value = "/{portfolioId}/trades/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadTrsTradesForNonMtmPortfolio(
      @RequestPart MultipartFile file,
      @PathVariable String portfolioId,
      @Valid @ParameterObject ImportOptions importOptions,
      Authentication user)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadItemService.uploadTradesForPortfolio(
            user, portfolioId, file.getBytes(), importOptions));
  }

  @Operation(summary = "Validate TRS trades csv file")
  @PostMapping(
      value = "/{portfolioId}/trades/upload/validate",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<ValidationResponse> validateImportForNonMtmPortfolio(
      @RequestPart MultipartFile file,
      @PathVariable String portfolioId,
      @Valid @ParameterObject ImportOptions importOptions,
      Authentication user)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadItemService.validateUploadForPortfolio(
            user, portfolioId, file.getBytes(), importOptions));
  }

  @Operation(summary = "Get non-mtm portfolio trade items in csv format")
  @GetMapping("/{portfolioId}/trades-csv")
  @CommonErrors
  @Sorted
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<ByteArrayResource> getNonMtmPortfolioItemsCsv(
      @PathVariable String portfolioId,
      @RequestParam LocalDate stateDate,
      @SortDefault(
              sort = {
                NonMtmPortfolioItemWithKeyView.Fields.trsType,
                NonMtmPortfolioItemWithKeyView.Fields.externalTradeId
              })
          Sort sort,
      TableFilter tableFilter,
      Authentication authentication) {
    var bitemporalDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemFileResponse(
        exportService.exportPortfolioItems(
            authentication, portfolioId, bitemporalDate, sort, tableFilter));
  }

  @Operation(summary = "Update non-mtm portfolio")
  @CommonErrors
  @Sorted
  @PutMapping("/{portfolioId}")
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> editPortfolio(
      @PathVariable String portfolioId,
      @Valid @RequestBody NonMtmPortfolioUpdateForm edit,
      Authentication authentication) {
    return eitherErrorItemResponse(service.updateItem(authentication, portfolioId, edit));
  }

  @Operation(summary = "Get non-mtm portfolio data")
  @GetMapping("/{portfolioId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_NON_MTM_PORTFOLIOS)
  public ResponseEntity<NonMtmPortfolioCountedView> get(
      @RequestParam LocalDate stateDate,
      @PathVariable String portfolioId,
      Authentication authentication) {
    return eitherErrorItemResponse(service.get(authentication, portfolioId, newOf(stateDate)));
  }

  @Operation(summary = "Archive portfolio")
  @PutMapping("/{portfolioId}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_NON_MTM_PORTFOLIOS)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> archivePortfolio(
      @PathVariable String portfolioId, Authentication authentication) {
    return eitherErrorItemResponse(service.archivePortfolio(authentication, portfolioId));
  }

  @Operation(summary = "Get non-mtm portfolio items")
  @GetMapping("/{portfolioId}/trades")
  @ScrolledGroupedFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<ScrollableEntry<NonMtmPortfolioItemWithKeyView>> getItems(
      @PathVariable String portfolioId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) boolean withArchived,
      TableFilter tableFilter,
      @SortDefault(
              sort = {
                NonMtmPortfolioItemWithKeyView.Fields.trsType,
                NonMtmPortfolioItemWithKeyView.Fields.externalTradeId
              })
          ScrollRequest scrollRequest,
      GroupRequest groupRequest,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.getItems(
            authentication,
            portfolioId,
            newOf(stateDate),
            withArchived,
            tableFilter,
            scrollRequest,
            groupRequest));
  }

  @Operation(summary = "Get single non-mtm portfolio item")
  @GetMapping("/{portfolioId}/trades/{tradeEntityId}")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<NonMtmPortfolioItemWithKeyView> getItem(
      Authentication auth,
      @PathVariable("portfolioId") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @RequestParam("stateDate") LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getItem(auth, portfolioId, tradeEntityId, newOf(stateDate)));
  }

  @Operation(summary = "Get TRS Bond portfolio item")
  @GetMapping("/{portfolioId}/trades/trs-bond/{tradeEntityId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<TrsBondTradeView> getTrsBondItem(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) LocalDateTime recordDate,
      Authentication authentication) {
    var bitemporalDate = BitemporalDate.newOfNullable(stateDate, recordDate);
    return eitherErrorItemResponse(
        service.getTrsBondTradeView(authentication, portfolioId, tradeEntityId, bitemporalDate));
  }

  @Operation(summary = "Get TRS Share portfolio item")
  @GetMapping("/{portfolioId}/trades/trs-share/{tradeEntityId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<TrsShareTradeView> getTrsShareItem(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) LocalDateTime recordDate,
      Authentication authentication) {
    var bitemporalDate = BitemporalDate.newOfNullable(stateDate, recordDate);

    return eitherErrorItemResponse(
        service.getTrsShareTradeView(authentication, portfolioId, tradeEntityId, bitemporalDate));
  }

  @Operation(summary = "Create new TRS Bond portfolio item")
  @CommonErrors
  @PostMapping("/{portfolioId}/trades/trs-bond")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> newTrsBondPortfolioItem(
      @PathVariable String portfolioId,
      @Valid @RequestBody CreateTrsBondTradeForm form,
      Authentication authentication) {
    return eitherErrorItemResponse(service.insertItem(authentication, portfolioId, form));
  }

  @Operation(summary = "Create new TRS Share portfolio item")
  @CommonErrors
  @PostMapping("/{portfolioId}/trades/trs-share")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> newTrsSharePortfolioItem(
      @PathVariable String portfolioId,
      @Valid @RequestBody CreateTrsShareTradeForm form,
      Authentication authentication) {
    return eitherErrorItemResponse(service.insertItem(authentication, portfolioId, form));
  }

  @Operation(summary = "Update TRS Bond portfolio item")
  @CommonErrors
  @PutMapping("/{portfolioId}/trades/trs-bond/{tradeEntityId}/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> updateTrsBondTrade(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @PathVariable LocalDate version,
      @Valid @RequestBody UpdateTrsBondTradeForm form,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.updateItem(authentication, portfolioId, version, form, tradeEntityId));
  }

  @Operation(summary = "Update TRS Share portfolio item")
  @CommonErrors
  @PutMapping("/{portfolioId}/trades/trs-share/{tradeEntityId}/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> updateTrsShareTrade(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @PathVariable LocalDate version,
      @Valid @RequestBody UpdateTrsShareTradeForm form,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.updateItem(authentication, portfolioId, version, form, tradeEntityId));
  }

  @Operation(summary = "Archives (sets status to ARCHIVED) non-mtm portfolio item")
  @PutMapping("/{portfolioId}/trades/{tradeEntityId}/{version}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> archivePortfolioItem(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @PathVariable LocalDate version,
      @RequestBody @Valid ArchiveEntityForm archiveEntityForm,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.archivePortfolioItem(
            authentication, portfolioId, tradeEntityId, version, archiveEntityForm));
  }

  @Operation(summary = "Deletes (sets status to DELETED) non-mtm portfolio item")
  @PutMapping("/{portfolioId}/trades/{tradeEntityId}/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  @RequireLock(name = NON_MTM_TRADES_LOCK_ID)
  public ResponseEntity<EntityId> deletePortfolioItem(
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId,
      @PathVariable LocalDate version,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.deletePortfolioItem(authentication, portfolioId, tradeEntityId, version));
  }

  @Operation(summary = "Get non-mtm portfolio item future versions")
  @GetMapping("/{portfolioId}/trades/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_TRADE)
  public DateList getFutureTradeVersions(
      @PathVariable String portfolioId,
      @ParameterObject @Valid SearchNonMtmPortfolioItemForm form) {
    return service.futureVersions(portfolioId, form);
  }

  @Operation(summary = "Get non-mtm portfolio item versions")
  @GetMapping("/{portfolioId}/trades/{tradeEntityId}/versions")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_TRADE)
  public ResponseEntity<List<NonMtmPortfolioItemWithKeyView>> getVersions(
      Authentication authentication,
      @PathVariable String portfolioId,
      @PathVariable String tradeEntityId) {
    return eitherErrorItemResponse(service.getVersions(authentication, portfolioId, tradeEntityId));
  }

  @Operation(summary = "Start non-mtm portfolio valuation")
  @PostMapping("/{portfolioId}/valuate")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_NON_MTM_VALUATION)
  @RequireLock(type = PATH_VARIABLE, name = "portfolioId", prefix = TRS_VALUATION_LOCK_ID)
  public ResponseEntity<EntityId> valuate(
      @PathVariable String portfolioId, @RequestBody @Valid NonMtmValuationForm form) {
    return eitherErrorItemResponse(
        calculationBitemporalDate(form)
            .flatMap(d -> valuationsService.performValuation(portfolioId, form, d)));
  }

  private Either<ErrorItem, BitemporalDate> calculationBitemporalDate(NonMtmValuationForm form) {
    return bitemporalDateLocker.lock(
        List.of(
            XplainLock.newOf(CURVE_CONFIGURATION_LOCK_ID),
            XplainLock.newOf(FIXINGS_LOCK_ID),
            XplainLock.newOf(MARKET_DATA_LOCK_ID, form.getMarketDataGroupId()),
            XplainLock.newOf(TRS_MARKET_DATA_LOCK_ID, form.getTrsMarketDataGroupId()),
            XplainLock.newOf(MDK_LOCK_ID),
            XplainLock.newOf(COMPANY_SETTINGS_LOCK_ID),
            XplainLock.newOf(NON_MTM_TRADES_LOCK_ID)),
        form.getStateDate());
  }
}
