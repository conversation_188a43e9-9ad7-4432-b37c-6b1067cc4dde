package com.solum.xplain.trs.company.entity;

import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.company.CompanySettingsType;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyDataProvidersSettings extends DataProvidersSettings {
  public static CompanyDataProvidersSettings newOf(String companyId) {
    CompanyDataProvidersSettings providerSettings = new CompanyDataProvidersSettings();
    providerSettings.setEntityId(companyId);
    providerSettings.setValidFrom(ROOT_DATE);
    providerSettings.setRecordDate(LocalDateTime.now());
    providerSettings.setState(State.ACTIVE);
    return providerSettings;
  }

  public LegalEntityDataProvidersSettings toDefaultLegalEntitySettings(String legalEntityId) {
    var settings = LegalEntityDataProvidersSettings.newOf(getEntityId(), legalEntityId);
    settings.setSettingsType(CompanySettingsType.DEFAULT);
    settings.setMarketDataGroup(getMarketDataGroup());
    settings.setInstruments(getInstruments());
    return settings;
  }
}
