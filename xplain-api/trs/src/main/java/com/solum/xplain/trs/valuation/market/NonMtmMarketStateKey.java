package com.solum.xplain.trs.valuation.market;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import lombok.Data;

@Data
public class NonMtmMarketStateKey {
  private final String trsMarketDataGroupId;
  private final BitemporalDate stateDate;
  private final String companyId;
  private final String entityId;
  private final InstrumentPriceType instrumentPriceType;
}
