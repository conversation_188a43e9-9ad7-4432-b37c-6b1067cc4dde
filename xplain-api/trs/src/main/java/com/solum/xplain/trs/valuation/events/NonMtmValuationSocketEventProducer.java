package com.solum.xplain.trs.valuation.events;

import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.global;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class NonMtmValuationSocketEventProducer {
  private static final String NON_MTM_VALUATION_UPDATED = "NON_MTM_VALUATION_UPDATED";
  private final ApplicationEventPublisher eventPublisher;

  public NonMtmValuationSocketEventProducer(ApplicationEventPublisher eventPublisher) {
    this.eventPublisher = eventPublisher;
  }

  @EventListener(NonMtmValuationEvent.class)
  public void onValuationEvent(NonMtmValuationEvent event) {
    eventPublisher.publishEvent(global(NON_MTM_VALUATION_UPDATED, event.getValuationId()));
  }
}
