package com.solum.xplain.trs.portfolio.trade.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = PaymentOffsetDaysGreaterThanAccrualOffsetDaysValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface PaymentOffsetDaysGreaterThanAccrualOffsetDays {

  String message() default
      "{com.solum.xplain.trs.portfolio.trade.validation.PaymentOffsetDaysGreaterThanAccrualOffsetDays.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
