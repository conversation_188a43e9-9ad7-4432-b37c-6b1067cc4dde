package com.solum.xplain.trs.index.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.trs.index.csv.TrsIndexCsvMapper.CURRENCY;
import static com.solum.xplain.trs.index.csv.TrsIndexCsvMapper.DESCRIPTION;
import static com.solum.xplain.trs.index.csv.TrsIndexCsvMapper.INDEX_NAME;
import static com.solum.xplain.trs.index.csv.TrsIndexCsvMapper.INDEX_TYPE;
import static com.solum.xplain.trs.value.NonMtmClassifiersProvider.TRS_CURRENCIES;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.trs.classifiers.TrsClassifierSupplier;
import com.solum.xplain.trs.index.view.CreateTrsIndexForm;
import com.solum.xplain.trs.value.TrsType;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

@Component
@EqualsAndHashCode
public class TrsIndexCsvLoader extends GenericCsvLoader<CreateTrsIndexForm, String> {
  private final Supplier<Collection<String>> trsCurrencies =
      new TrsClassifierSupplier(TRS_CURRENCIES);

  @Override
  protected CsvParserResultBuilder<CreateTrsIndexForm, String> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CreateTrsIndexForm::getName, Function.identity(), parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return TrsIndexCsvMapper.header();
  }

  @Override
  protected Either<ErrorItem, CreateTrsIndexForm> parseLine(CsvRow csvRow) {
    try {
      var form = new CreateTrsIndexForm();
      form.setName(CsvLoaderUtils.parseIdentifier(csvRow, INDEX_NAME));
      var indexTypeLabel =
          validateValue(csvRow.getValue(INDEX_TYPE), INDEX_TYPE, TrsType::indexLabels);
      form.setType(TrsType.byIndexLabel(indexTypeLabel));
      form.setCurrency(validateValue(csvRow.getValue(CURRENCY), CURRENCY, trsCurrencies));
      csvRow.findValue(DESCRIPTION).ifPresent(form::setDescription);
      return right(form);
    } catch (IllegalArgumentException e) {
      return left(rowParsingError(csvRow, e));
    }
  }
}
