package com.solum.xplain.trs.portfolio.value;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetailsView;
import com.solum.xplain.trs.value.TrsType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class NonMtmPortfolioItemWithKeyView {
  private String key;
  private String entityId;
  private String externalTradeId;
  private LocalDate validFrom;
  private String comment;
  private LocalDateTime recordFrom;
  private State state;
  private String modifiedBy;
  private LocalDateTime modifiedAt;

  private String portfolioId;
  private String description;
  private TrsType trsType;
  private TrsTradeDetailsView tradeDetails;
}
