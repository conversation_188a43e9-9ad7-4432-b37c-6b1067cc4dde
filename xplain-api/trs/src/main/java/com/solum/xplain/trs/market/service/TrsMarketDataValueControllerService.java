package com.solum.xplain.trs.market.service;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.DataExportForm;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.ArchiveForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.mdvalue.MarketDataValueRepository;
import com.solum.xplain.core.mdvalue.csv.MarketDataValueExportService;
import com.solum.xplain.core.mdvalue.csv.MarketDataValueImportOptions;
import com.solum.xplain.core.mdvalue.csv.MarketDataValueUploadService;
import com.solum.xplain.core.mdvalue.entity.MarketDataValue;
import com.solum.xplain.core.mdvalue.entity.MarketDataValueUniqueKey;
import com.solum.xplain.core.mdvalue.value.MarketDataValueCreateForm;
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import com.solum.xplain.core.mdvalue.value.MarketDataValueUpdateForm;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueCombinedView;
import com.solum.xplain.trs.market.TrsAssetGroupExportForm;
import com.solum.xplain.trs.value.TrsAssetClassGroup;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TrsMarketDataValueControllerService {

  private final MarketDataValueRepository repository;
  private final MarketDataKeyRepository mdkRepository;
  private final TrsMarketDataGroupValueService groupValueService;
  private final AuditEntryService auditEntryService;
  private final MarketDataValueUploadService uploadService;
  private final MarketDataValueExportService exportService;

  public TrsMarketDataValueControllerService(
      MarketDataValueRepository repository,
      MarketDataKeyRepository mdkRepository,
      TrsMarketDataGroupValueService groupValueService,
      AuditEntryService auditEntryService,
      MarketDataValueUploadService uploadService,
      MarketDataValueExportService exportService) {
    this.repository = repository;
    this.mdkRepository = mdkRepository;
    this.groupValueService = groupValueService;
    this.auditEntryService = auditEntryService;
    this.uploadService = uploadService;
    this.exportService = exportService;
  }

  @Transactional
  public Either<ErrorItem, EntityId> createValue(
      String groupId, MarketDataValueCreateForm form, BitemporalDate stateDate) {
    return groupValueService.performWithGroupUpdate(
        groupId, () -> createAndLog(groupId, form, stateDate));
  }

  private Either<ErrorItem, EntityId> createAndLog(
      String groupId, MarketDataValueCreateForm form, BitemporalDate stateDate) {
    logUnresolvable(MarketDataValueUniqueKey.from(form), stateDate);
    return Either.right(repository.createValue(groupId, form));
  }

  public Either<ErrorItem, List<MarketDataValueFlatView>> getValues(
      String groupId, BitemporalDate stateDate, LocalDate curveDate, boolean archived) {
    return groupValueService
        .userTrsGroup(groupId)
        .map(
            g ->
                repository.getValueViews(
                    g.getId(),
                    BitemporalDate.newOf(curveDate, stateDate.getRecordDate()),
                    archived));
  }

  public Either<ErrorItem, List<ResolvedMarketDataValueCombinedView>> getResolvedValues(
      String groupId, BitemporalDate stateDate, LocalDate curveDate) {
    var resolver = mdkRepository.keyResolver(stateDate, TrsAssetClassGroup.TRS);
    return getValues(groupId, stateDate, curveDate, false)
        .map(values -> resolver.resolvedCombinedValues(values).toList());
  }

  public Either<ErrorItem, List<MarketDataValueFlatView>> getUnresolvedValues(
      String groupId, BitemporalDate stateDate, LocalDate curveDate) {
    var resolver = mdkRepository.keyResolver(stateDate, TrsAssetClassGroup.TRS);
    return getValues(groupId, stateDate, curveDate, false).map(resolver::unresolvedValues);
  }

  public Either<ErrorItem, MarketDataValueFlatView> getValue(
      String groupId, String valueId, BitemporalDate stateDate) {
    return groupValueService
        .userTrsGroup(groupId)
        .flatMap(g -> repository.getValueView(g.getId(), valueId, stateDate));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateValue(
      String groupId, String valueId, MarketDataValueUpdateForm form, BitemporalDate stateDate) {
    return groupValueService.performWithGroupUpdate(
        groupId, () -> updateGroupValue(groupId, valueId, form, stateDate));
  }

  private Either<ErrorItem, EntityId> updateGroupValue(
      String groupId, String valueId, MarketDataValueUpdateForm form, BitemporalDate stateDate) {
    return repository
        .getValueView(groupId, valueId, stateDate)
        .flatMap(
            v -> {
              logUnresolvable(MarketDataValueUniqueKey.from(v), stateDate);
              return repository.updateValue(valueId, form);
            });
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveValue(
      String groupId, String valueId, ArchiveForm form) {
    return groupValueService.performWithGroupUpdate(
        groupId, () -> archiveGroupValue(valueId, form));
  }

  private Either<ErrorItem, EntityId> archiveGroupValue(String valueId, ArchiveForm form) {
    return repository.archiveValue(valueId, form);
  }

  public Either<List<ErrorItem>, EntityId> upload(
      String groupId,
      byte[] csvBytes,
      MarketDataValueImportOptions importOptions,
      BitemporalDate stateDate) {
    return groupValueService.performWithGroupUpdate(
        groupId,
        g ->
            uploadService.upload(
                g, csvBytes, importOptions, stateDate, TrsAssetClassGroup.values()));
  }

  public Either<List<ErrorItem>, ValidationResponse> validateFile(
      String groupId, byte[] csvBytes, ParsingMode parsingMode) {
    return groupValueService
        .userTrsGroup(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> uploadService.validateFile(g, csvBytes, parsingMode));
  }

  public Either<ErrorItem, FileResponseEntity> getUnresolvedValuesCsv(
      String groupId, BitemporalDate stateDate, DataExportForm exportForm) {
    return groupValueService
        .userTrsGroup(groupId)
        .map(
            g ->
                exportService.getUnresolvedValuesCsv(
                    g, stateDate, toMdExportForm(exportForm), TrsAssetClassGroup.values()));
  }

  public Either<ErrorItem, FileResponseEntity> getResolvedValuesCsv(
      String groupId, BitemporalDate stateDate, DataExportForm exportForm) {
    return groupValueService
        .userTrsGroup(groupId)
        .map(g -> exportService.getResolvedValuesCsv(g, stateDate, toMdExportForm(exportForm)));
  }

  private TrsAssetGroupExportForm toMdExportForm(DataExportForm exportForm) {
    var mdExportForm = new TrsAssetGroupExportForm();
    mdExportForm.setCurveDate(exportForm.getCurveDate());
    mdExportForm.setStateDate(exportForm.getStateDate());
    return mdExportForm;
  }

  private void logUnresolvable(MarketDataValueUniqueKey key, BitemporalDate stateDate) {
    var resolver = mdkRepository.keyResolver(stateDate, TrsAssetClassGroup.values());
    resolver
        .unresolvableError(key)
        .ifPresent(
            v ->
                auditEntryService.newEntryWithLogs(
                    AuditEntry.of(MarketDataValue.class.getSimpleName(), "Trs Market Value edited"),
                    List.of(v)));
  }
}
