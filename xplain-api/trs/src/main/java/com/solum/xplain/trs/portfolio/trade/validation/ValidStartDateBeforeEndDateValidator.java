package com.solum.xplain.trs.portfolio.trade.validation;

import com.solum.xplain.trs.portfolio.trade.form.TrsLegForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

public class ValidStartDateBeforeEndDateValidator
    implements ConstraintValidator<ValidStartDateBeforeEndDate, TrsLegForm> {

  public boolean isValid(TrsLegForm form, ConstraintValidatorContext context) {
    if (ObjectUtils.allNotNull(form.getStartDate(), form.getEndDate())
        && form.getEndDate().isBefore(form.getStartDate())) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("endDate")
          .addConstraintViolation();
      return false;
    }
    return true;
  }
}
