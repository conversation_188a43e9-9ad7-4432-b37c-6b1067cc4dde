package com.solum.xplain.trs.portfolio.trade.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.portfolio.validation.TradeIdValidator;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemRepository;
import com.solum.xplain.trs.portfolio.trade.form.TrsTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;

public class UniqueNonMtmTradeIdValidator extends TradeIdValidator
    implements ConstraintValidator<UniqueNonMtmTradeId, TrsTradeForm> {

  private static final String TRADE_ID_PATH = "tradeEntityId";
  private static final String PORTFOLIO_ID_PATH = "portfolioId";
  private final NonMtmPortfolioItemRepository nonMtmItemRepository;

  public UniqueNonMtmTradeIdValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      NonMtmPortfolioItemRepository nonMtmItemrepository) {
    super(requestPathVariablesSupport, TRADE_ID_PATH, PORTFOLIO_ID_PATH);
    this.nonMtmItemRepository = nonMtmItemrepository;
  }

  @Override
  @SuppressWarnings("java:S2589")
  public boolean isValid(TrsTradeForm value, ConstraintValidatorContext context) {
    if (value != null
        && value.getVersionForm() != null
        && value.getVersionForm().getValidFrom() != null) {
      var validFrom = value.getVersionForm().getValidFrom();

      return isValid(value.getExternalTradeId(), validFrom, context);
    }
    return true;
  }

  @Override
  protected boolean isUniqueTrade(
      LocalDate stateDate, String portfolioId, String externalId, String internalId) {
    return !nonMtmItemRepository.hasPortfolioItemByExternalTradeId(
        BitemporalDate.newOf(stateDate), portfolioId, externalId, internalId);
  }
}
