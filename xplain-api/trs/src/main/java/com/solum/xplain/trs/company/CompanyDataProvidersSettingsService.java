package com.solum.xplain.trs.company;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.UserCompanyService;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.trs.company.form.CompanyDataProvidersSettingsForm;
import com.solum.xplain.trs.company.repository.CompanyDataProvidersSettingsRepository;
import com.solum.xplain.trs.company.view.CompanyDataProvidersSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CompanyDataProvidersSettingsService {
  private final CompanyDataProvidersSettingsRepository repository;
  private final UserCompanyService userCompanyService;

  public Either<ErrorItem, CompanyDataProvidersSettingsView> companySettings(
      Authentication auth, String companyId, BitemporalDate stateDate) {
    return userCompanyService
        .userCompany(auth, companyId)
        .flatMap(u -> repository.companySettings(companyId, stateDate));
  }

  public Either<ErrorItem, List<CompanyDataProvidersSettingsView>> companySettingsVersions(
      Authentication auth, String companyId) {
    return userCompanyService
        .userCompany(auth, companyId)
        .map(u -> repository.companySettingsVersions(companyId));
  }

  public Either<ErrorItem, DateList> companySettingsFutureVersions(
      Authentication auth, String companyId, LocalDate stateDate) {
    return userCompanyService
        .userCompany(auth, companyId)
        .map(u -> repository.companySettingsFutureVersions(companyId, stateDate));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateCompanySettings(
      Authentication auth,
      String companyId,
      LocalDate versionDate,
      CompanyDataProvidersSettingsForm form) {
    return userCompanyService
        .userCompany(auth, companyId)
        .flatMap(u -> repository.updateCompanySettings(companyId, versionDate, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteCompanySettingsVersion(
      Authentication auth, String companyId, LocalDate version) {
    return userCompanyService
        .userCompany(auth, companyId)
        .flatMap(g -> repository.deleteCompanySettingsVersion(companyId, version));
  }
}
