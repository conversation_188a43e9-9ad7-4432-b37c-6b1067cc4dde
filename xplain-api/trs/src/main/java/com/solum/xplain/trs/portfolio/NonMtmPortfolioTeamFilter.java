package com.solum.xplain.trs.portfolio;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import java.util.List;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.mongodb.core.query.Criteria;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NullMarked
public class NonMtmPortfolioTeamFilter extends EntityTeamFilter {
  @Getter(AccessLevel.PACKAGE)
  private final List<String> excludedEntityIds;

  @Getter(AccessLevel.PACKAGE)
  private final List<String> excludedCompanyIds;

  public NonMtmPortfolioTeamFilter(
      List<ObjectId> teamIds, List<String> excludedCompanyIds, List<String> excludedEntityIds) {
    super(teamIds);
    this.excludedEntityIds = excludedEntityIds;
    this.excludedCompanyIds = excludedCompanyIds;
  }

  public static NonMtmPortfolioTeamFilter emptyFilter() {
    return new NonMtmPortfolioTeamFilter(List.of(), List.of(), List.of());
  }

  public static NonMtmPortfolioTeamFilter portfolioTeamFilter(
      XplainPrincipal user, List<String> excludedCompanyIds, List<String> excludedEntityIds) {
    return new NonMtmPortfolioTeamFilter(user.getTeams(), excludedCompanyIds, excludedEntityIds);
  }

  @Override
  public Criteria criteria() {
    Criteria criteria = super.criteria();
    if (!excludedCompanyIds.isEmpty()) {
      criteria =
          criteria
              .and(propertyName(NonMtmPortfolio.Fields.company, EntityReference.Fields.entityId))
              .nin(excludedCompanyIds);
    }
    if (!excludedEntityIds.isEmpty()) {
      criteria =
          criteria
              .and(propertyName(NonMtmPortfolio.Fields.entity, EntityReference.Fields.entityId))
              .nin(excludedEntityIds);
    }
    return criteria;
  }
}
