package com.solum.xplain.trs.valuation.csv;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;

import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider;
import com.solum.xplain.trs.valuation.csv.accrued.NonMtmAccruedPeriodCsvMapperFactory;
import com.solum.xplain.trs.valuation.repository.NonMtmValuationPortfolioItemRepository;
import com.solum.xplain.trs.valuation.repository.NonMtmValuationRepository;
import com.solum.xplain.trs.valuation.value.NonMtmLegAccruedMetricsView;
import com.solum.xplain.trs.valuation.value.NonMtmValuationView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
public class NonMtmValuationExportService {

  private final NonMtmValuationPortfolioItemRepository itemRepository;
  private final NonMtmValuationRepository valuationRepository;
  private final NonMtmPortfolioTeamFilterProvider filterProvider;
  private final AuthenticationContext authenticationContext;
  private final NonMtmValuationCsvMapper valuationsCsvMapper;
  private final NonMtmAccruedPeriodCsvMapperFactory accruedCsvFactory;

  public NonMtmValuationExportService(
      NonMtmValuationPortfolioItemRepository itemRepository,
      NonMtmValuationRepository valuationRepository,
      NonMtmPortfolioTeamFilterProvider filterProvider,
      AuthenticationContext authenticationContext,
      NonMtmValuationCsvMapper valuationsCsvMapper,
      NonMtmAccruedPeriodCsvMapperFactory accruedCsvFactory) {
    this.itemRepository = itemRepository;
    this.valuationRepository = valuationRepository;
    this.filterProvider = filterProvider;
    this.authenticationContext = authenticationContext;
    this.valuationsCsvMapper = valuationsCsvMapper;
    this.accruedCsvFactory = accruedCsvFactory;
  }

  public Either<ErrorItem, FileResponseEntity> exportValuationTrades(
      LocalDate stateDate, String id, Sort sort, TableFilter tableFilter) {
    return valuation(id)
        .map(
            result -> {
              var rows = csvRows(result, sort, tableFilter);
              var csvFileName = resolveFilename(result, stateDate);
              return toFileResponseEntity(csvFileName, valuationsCsvMapper.fileHeader(), rows);
            });
  }

  public Either<ErrorItem, FileResponseEntity> exportLegAccruedMetrics(
      String resultId, String tradeId, PayReceive payReceive, LocalDate stateDate) {
    return valuation(resultId).flatMap(res -> accruedCsv(res, tradeId, payReceive, stateDate));
  }

  private Either<ErrorItem, FileResponseEntity> accruedCsv(
      NonMtmValuationView view, String tradeId, PayReceive payReceive, LocalDate stateDate) {
    var fileName = resolveAccruedFilename(view, payReceive, stateDate);
    return itemRepository
        .getTradeAccruedMetrics(view.getId(), tradeId)
        .map(m -> m.legMetrics(payReceive))
        .map(results -> toAccruedResponseEntity(fileName, results));
  }

  private FileResponseEntity toAccruedResponseEntity(
      String fileName, NonMtmLegAccruedMetricsView metricsView) {
    var mapper = accruedCsvFactory.getMapper(metricsView.getType());
    var rows =
        metricsView.getPeriods().stream()
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return toFileResponseEntity(fileName, mapper.fileHeader(), rows);
  }

  private FileResponseEntity toFileResponseEntity(
      String fileName, List<String> headers, Stream<Either<ErrorItem, CsvRow>> rows) {
    var csvFile = new CsvOutputFile(headers, rows).writeToByteArray();
    return FileResponseEntity.csvFile(csvFile, fileName);
  }

  private String resolveFilename(NonMtmValuationView valuationView, LocalDate stateDate) {
    var name =
        String.format(
            "%s_%s_NonMtmCalculationResults",
            valuationView.getValuationDate(), valuationView.getExternalPortfolioId());
    return nameWithTimeStamp(name, stateDate);
  }

  private String resolveAccruedFilename(
      NonMtmValuationView valuationView, PayReceive payReceive, LocalDate stateDate) {
    var name =
        String.format(
            "%s_%s_NonMtm%sLegAccruedPeriods",
            valuationView.getValuationDate(), valuationView.getExternalPortfolioId(), payReceive);
    return nameWithTimeStamp(name, stateDate);
  }

  private Either<ErrorItem, NonMtmValuationView> valuation(String id) {
    var filter = filterProvider.valuationTeamFilter(authenticationContext.currentUser());
    return valuationRepository.getValuation(filter, id);
  }

  private Stream<Either<ErrorItem, CsvRow>> csvRows(
      NonMtmValuationView valuationView, Sort sort, TableFilter filter) {
    return itemRepository
        .valuationItemsStream(valuationView.getId(), sort, filter)
        .map(r -> valuationsCsvMapper.toCsvRow(valuationView, r))
        .map(Either::right);
  }
}
