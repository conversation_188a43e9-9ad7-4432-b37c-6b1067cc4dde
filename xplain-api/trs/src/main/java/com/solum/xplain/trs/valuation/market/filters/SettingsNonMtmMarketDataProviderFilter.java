package com.solum.xplain.trs.valuation.market.filters;

import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.trs.company.view.MarketDataProvidersView;
import com.solum.xplain.trs.value.NonMtmInstrumentType;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SettingsNonMtmMarketDataProviderFilter implements NonMtmMarketDataProviderFilter {
  private final Map<String, String> resolvedProvidersMap;

  public static SettingsNonMtmMarketDataProviderFilter resolveForType(
      MarketDataSourceType sourceType,
      Map<NonMtmInstrumentType, MarketDataProvidersView> providersMap) {
    var providerFn = providerFn(sourceType);
    var resolvedMap =
        providersMap.entrySet().stream()
            .filter(v -> Objects.nonNull(v.getValue()))
            .filter(v -> Objects.nonNull(providerFn.apply(v.getValue())))
            .collect(
                Collectors.toMap(v -> v.getKey().toString(), v -> providerFn.apply(v.getValue())));
    return new SettingsNonMtmMarketDataProviderFilter(resolvedMap);
  }

  private static Function<MarketDataProvidersView, String> providerFn(MarketDataSourceType type) {
    return switch (type) {
      case RAW_PRIMARY, PRELIMINARY_PRIMARY, OVERLAY -> MarketDataProvidersView::getPrimary;
      case RAW_SECONDARY, PRELIMINARY_SECONDARY -> MarketDataProvidersView::getSecondary;
    };
  }

  @Override
  public boolean isRequiredProvider(String nonMtmInstrumentType, String provider) {
    return Optional.ofNullable(resolvedProvidersMap.get(nonMtmInstrumentType))
        .filter(v -> StringUtils.equals(v, provider))
        .isPresent();
  }
}
