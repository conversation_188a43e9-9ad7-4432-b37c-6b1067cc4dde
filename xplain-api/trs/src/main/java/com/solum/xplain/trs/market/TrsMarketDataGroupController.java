package com.solum.xplain.trs.market;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_MODIFY_TRS_MARKET_DATA_GROUP;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_VIEW_TRS_MARKET_DATA_GROUP;
import static org.springframework.data.domain.Sort.Direction.ASC;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.market.service.TrsMarketDataGroupControllerService;
import com.solum.xplain.trs.market.value.TrsMarketDataGroupCountedView;
import com.solum.xplain.trs.market.value.TrsMarketDataGroupForm;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/trs/market-data-group")
public class TrsMarketDataGroupController {

  private final TrsMarketDataGroupControllerService groupService;

  public TrsMarketDataGroupController(TrsMarketDataGroupControllerService groupService) {
    this.groupService = groupService;
  }

  @Operation(summary = "Get trs market data groups list")
  @ScrolledFiltered
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_TRS_MARKET_DATA_GROUP)
  public ScrollableEntry<TrsMarketDataGroupCountedView> getAllTrsDataGroups(
      @SortDefault(sort = "id", direction = ASC) ScrollRequest scrollRequest,
      TrsMarketDataGroupFilter filter,
      TableFilter tableFilter,
      @RequestParam LocalDate stateDate) {
    return groupService.countedGroupsScrollable(
        scrollRequest, filter, tableFilter, BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Get trs market data groups names list")
  @ScrolledFiltered
  @CommonErrors
  @GetMapping("/all")
  @PreAuthorize(AUTHORITY_VIEW_TRS_MARKET_DATA_GROUP)
  public List<EntityNameView> list(@RequestParam(required = false) List<String> companyIds) {
    return groupService.namesList(companyIds);
  }

  @Operation(summary = "Get trs market data group")
  @GetMapping("/{groupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRS_MARKET_DATA_GROUP)
  public ResponseEntity<TrsMarketDataGroupCountedView> get(
      @PathVariable("groupId") String groupId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        groupService.countedGroup(groupId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Create new trs market data group")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_TRS_MARKET_DATA_GROUP)
  public ResponseEntity<EntityId> newTrsMarketDataGroup(
      @Valid @RequestBody TrsMarketDataGroupForm newForm) {
    return eitherErrorItemResponse(groupService.create(newForm));
  }

  @Operation(summary = "Archive trs market data group")
  @PutMapping("/{groupId}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_MARKET_DATA_GROUP)
  public ResponseEntity<EntityId> archive(@PathVariable("groupId") String groupId) {
    return eitherErrorItemResponse(groupService.archiveGroup(groupId));
  }

  @Operation(summary = "Update market data group")
  @PutMapping("/{groupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_MARKET_DATA_GROUP)
  public ResponseEntity<EntityId> update(
      @PathVariable("groupId") String groupId,
      @Valid @RequestBody TrsMarketDataGroupForm updateForm) {
    return eitherErrorItemResponse(groupService.updateGroup(groupId, updateForm));
  }
}
