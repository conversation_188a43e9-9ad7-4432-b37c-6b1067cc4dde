package com.solum.xplain.trs.portfolio.validation;

import static com.solum.xplain.trs.value.TrsClassifiersConstants.TRS_TRADE_FREQUENCIES;

import com.solum.xplain.core.utils.FrequencyUtils;
import java.util.Collection;
import java.util.function.Supplier;

public class TrsTradeFrequencySupplier implements Supplier<Collection<String>> {

  @Override
  public Collection<String> get() {
    return TRS_TRADE_FREQUENCIES.stream().map(FrequencyUtils::toStringNoPrefix).toList();
  }
}
