package com.solum.xplain.trs.market.events;

import com.solum.xplain.core.common.EntityEvent;
import com.solum.xplain.trs.market.value.TrsMarketDataGroupForm;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TrsMarketDataGroupUpdated extends EntityEvent {

  private final TrsMarketDataGroupForm form;

  public TrsMarketDataGroupUpdated(String entityId, TrsMarketDataGroupForm form) {
    super(entityId);
    this.form = form;
  }

  public TrsMarketDataGroupForm getForm() {
    return form;
  }
}
