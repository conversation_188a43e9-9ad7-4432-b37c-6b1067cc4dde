package com.solum.xplain.trs.valuation.market.source;

import static java.util.Optional.empty;

import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class TrsPriceTypeTimeSeriesResolver {

  /**
   * Creates a LocalDateDoubleTimeSeries object by grouping all elements in a list of
   * TrsBidAskDateValue by date, and for each date, extracting the corresponding bid/mid/ask value
   * for the input price type. All dates and resolved values are then added to the time series and
   * returned.
   *
   * @param values - the list of TrsBidAskDateValue
   * @param priceType - the required price type
   * @return an optional with the time series if the value exists, empty otherwise
   */
  public static Optional<LocalDateDoubleTimeSeries> timeSeriesForPriceType(
      List<TrsBidAskDateValue> values, InstrumentPriceType priceType) {

    if (values.isEmpty()) {
      return empty();
    }

    var valuesByDate = values.stream().collect(Collectors.groupingBy(TrsBidAskDateValue::getDate));

    Map<LocalDate, Double> dateResolvedValueMap = new java.util.HashMap<>();

    valuesByDate.forEach(
        (date, trsMdValues) -> {
          var trsMdValuesByPriceType =
              trsMdValues.stream()
                  .collect(
                      Collectors.toMap(
                          TrsBidAskDateValue::getBidAskType, TrsBidAskDateValue::getValue));
          var resolvedBidAskValue = getPriceTypeResolvedValue(trsMdValuesByPriceType, priceType);

          resolvedBidAskValue.ifPresent(r -> dateResolvedValueMap.put(date, r));
        });

    if (!dateResolvedValueMap.entrySet().isEmpty()) {
      return Optional.of(LocalDateDoubleTimeSeries.builder().putAll(dateResolvedValueMap).build());
    }
    return empty();
  }

  /**
   * For instruments grouped by date, key, and price type (bid/mid/ask), extract value that
   * corresponds to price type.
   *
   * @param instrumentKeyValuesByPriceType - map with price type as key and value as value
   * @param priceType - the required price type
   * @return an optional with the time series if the value exists, empty otherwise
   */
  private static Optional<Double> getPriceTypeResolvedValue(
      Map<ValueBidAskType, BigDecimal> instrumentKeyValuesByPriceType,
      InstrumentPriceType priceType) {
    var bidValue = instrumentKeyValuesByPriceType.get(ValueBidAskType.BID);
    var askValue = instrumentKeyValuesByPriceType.get(ValueBidAskType.ASK);
    var midValue = instrumentKeyValuesByPriceType.get(ValueBidAskType.MID);

    return priceType
        .calculateRequiredPrice(askValue, midValue, bidValue)
        .map(BigDecimal::doubleValue);
  }
}
