package com.solum.xplain.trs.index.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueTrsIndexValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface UniqueTrsIndex {

  String message() default
      "{com.solum.xplain.trs.index.validation.UniqueTrsIndexValidator.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
