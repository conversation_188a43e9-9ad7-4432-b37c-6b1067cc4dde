package com.solum.xplain.trs.valuation.repository;

import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrollableEntrySupport;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.PathUtils;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.valuation.entity.NonMtmLegMetrics;
import com.solum.xplain.trs.valuation.entity.NonMtmMetrics;
import com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolioItem;
import com.solum.xplain.trs.valuation.value.NonMtmAccruedMetricsView;
import com.solum.xplain.trs.valuation.value.NonMtmValuationPortfolioItemView;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.List;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations.BulkMode;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class NonMtmValuationPortfolioItemRepository {
  private static final Integer BATCH_SIZE = 10000;

  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final ScrollableEntrySupport scrollableEntrySupport;

  public NonMtmValuationPortfolioItemRepository(
      MongoOperations mongoOperations,
      ConversionService conversionService,
      ScrollableEntrySupport scrollableEntrySupport) {
    this.mongoOperations = mongoOperations;
    this.conversionService = conversionService;
    this.scrollableEntrySupport = scrollableEntrySupport;
  }

  public void saveResults(List<NonMtmValuationPortfolioItem> resultItems) {
    Iterables.partition(resultItems, BATCH_SIZE)
        .forEach(
            items ->
                mongoOperations
                    .bulkOps(BulkMode.UNORDERED, NonMtmValuationPortfolioItem.class)
                    .insert(items)
                    .execute());
  }

  public long countResults(String resultId) {
    return mongoOperations.count(query(idCriteria(resultId)), NonMtmValuationPortfolioItem.class);
  }

  public ScrollableEntry<NonMtmValuationPortfolioItemView> getValuationItems(
      String resultId, ScrollRequest scrollRequest, TableFilter tableFilter) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(idCriteria(resultId)),
                match(
                    tableFilter.criteria(
                        NonMtmValuationPortfolioItemView.class, conversionService)))
            .build();

    return scrollableEntrySupport.getScrollableEntry(
        baseOperations,
        scrollRequest,
        NonMtmValuationPortfolioItem.class,
        NonMtmValuationPortfolioItemView.class);
  }

  public Either<ErrorItem, NonMtmAccruedMetricsView> getTradeAccruedMetrics(
      String resultId, String tradeId) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(idCriteria(resultId)),
                match(Criteria.where(NonMtmValuationPortfolioItem.Fields.tradeId).is(tradeId)),
                project()
                    .and(
                        PathUtils.joinPaths(
                            NonMtmValuationPortfolioItem.Fields.metrics,
                            NonMtmMetrics.Fields.payLegMetrics,
                            NonMtmLegMetrics.Fields.accrued))
                    .as(NonMtmAccruedMetricsView.Fields.payLegMetrics)
                    .and(
                        PathUtils.joinPaths(
                            NonMtmValuationPortfolioItem.Fields.metrics,
                            NonMtmMetrics.Fields.receiveLegMetrics,
                            NonMtmLegMetrics.Fields.accrued))
                    .as(NonMtmAccruedMetricsView.Fields.receiveLegMetrics))
            .build();
    var result =
        mongoOperations
            .aggregateAndReturn(NonMtmAccruedMetricsView.class)
            .by(Aggregation.newAggregation(NonMtmValuationPortfolioItem.class, baseOperations))
            .all()
            .getUniqueMappedResult();
    return Eithers.cond(
        result != null, Error.OBJECT_NOT_FOUND.entity("Trade metrics not found"), result);
  }

  public Stream<NonMtmValuationPortfolioItemView> valuationItemsStream(
      String resultId, Sort sort, TableFilter tableFilter) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(idCriteria(resultId)),
                match(
                    tableFilter.criteria(
                        NonMtmValuationPortfolioItemView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    }
    return mongoOperations
        .aggregateAndReturn(NonMtmValuationPortfolioItemView.class)
        .by(newAggregation(NonMtmValuationPortfolioItem.class, operations.build()))
        .stream();
  }

  public void removeAllByCalculationResultId(String calculationResultId) {
    mongoOperations.remove(
        query(idCriteria(calculationResultId)), NonMtmValuationPortfolioItem.class);
  }

  public void removeAllByCalculationResultIdIn(List<ObjectId> ids) {
    mongoOperations.remove(
        query(Criteria.where(NonMtmValuationPortfolioItem.Fields.calculationResultId).in(ids)),
        NonMtmValuationPortfolioItem.class);
  }

  private Criteria idCriteria(String resultId) {
    return Criteria.where(NonMtmValuationPortfolioItem.Fields.calculationResultId)
        .is(new ObjectId(resultId));
  }
}
