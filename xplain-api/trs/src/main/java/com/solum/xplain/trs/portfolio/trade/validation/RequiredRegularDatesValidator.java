package com.solum.xplain.trs.portfolio.trade.validation;

import com.opengamma.strata.basics.schedule.StubConvention;
import com.solum.xplain.trs.portfolio.trade.form.TrsLegForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredRegularDatesValidator
    implements ConstraintValidator<RequiredRegularDates, TrsLegForm> {

  private static final String NOT_NULL = "NotNull";

  @Override
  public boolean isValid(TrsLegForm form, ConstraintValidatorContext context) {
    var result = Boolean.TRUE;
    if (StubConvention.BOTH.name().equalsIgnoreCase(form.getStubConvention())) {
      if (form.getRegularStartDate() == null) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(NOT_NULL)
            .addPropertyNode("regularStartDate")
            .addConstraintViolation();
        result = Boolean.FALSE;
      }
      if (form.getRegularEndDate() == null) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(NOT_NULL)
            .addPropertyNode("regularEndDate")
            .addConstraintViolation();
        result = Boolean.FALSE;
      }
    }
    return result;
  }
}
