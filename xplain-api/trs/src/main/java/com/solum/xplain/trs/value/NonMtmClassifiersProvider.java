package com.solum.xplain.trs.value;

import static com.solum.xplain.core.classifiers.ClassifierUtils.enumClassifier;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.ASSET_CLASSES;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.INSTRUMENT_TYPES;
import static com.solum.xplain.trs.value.TrsClassifiersConstants.TRS_FIXING_OFFSET_DAYS_CALENDARS;
import static com.solum.xplain.trs.value.TrsClassifiersConstants.TRS_TRADE_FREQUENCIES;
import static java.util.Comparator.comparing;

import com.google.common.collect.ImmutableCollection;
import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.core.classifiers.CoreClassifiersProvider;
import com.solum.xplain.core.classifiers.CurrencyComparator;
import com.solum.xplain.core.classifiers.conventions.ConventionMapper;
import com.solum.xplain.core.classifiers.type.SupportedDayCount;
import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.trs.valuation.value.PeriodStatus;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;

@Component
public class NonMtmClassifiersProvider implements ClassifiersProvider {

  public static final NonMtmClassifiersProvider VALIDATION_INSTANCE =
      new NonMtmClassifiersProvider();
  public static final String TRS_ROLL_CONVENTIONS = "trsTradeRollConvention";
  public static final String TRS_CURRENCIES = "trsCurrencies";

  @Override
  public List<Classifier> classifiers() {
    return List.of(
        groupedAssetClasses(),
        assetClasses(),
        assetClassGroups(),
        allMdkInstruments(),
        instrumentTypes(),
        trsTypes(),
        trsFixingOffsetDaysCalendar(),
        trsTradeSupportedDayCounts(),
        trsTradeCalculationType(),
        trsTradeFrequency(),
        trsTradeRollConvention(),
        trsTradeCurrencies(),
        nonMtmInstruments(),
        trsIndexType(),
        trsPeriodStatus());
  }

  @Override
  public int sortOrder() {
    return 3;
  }

  private Classifier groupedAssetClasses() {
    return new Classifier(
        CoreClassifiersProvider.ALL_ASSET_CLASSES,
        null,
        Stream.of(TrsAssetClassGroup.values()).map(this::assetClasses).toList(),
        AssetGroup.class);
  }

  private Classifier assetClasses(TrsAssetClassGroup group) {
    return new Classifier(
        group.name(),
        group.getLabel(),
        group.getAssetClasses().stream().map(c -> new Classifier(c.name(), c.getLabel())).toList());
  }

  private Classifier assetClasses() {
    return new Classifier(
        ASSET_CLASSES,
        null,
        Stream.of(TrsAssetClass.values()).map(v -> new Classifier(v.name(), v.getLabel())).toList(),
        AssetClass.class);
  }

  private Classifier allMdkInstruments() {
    return new Classifier(
        CoreClassifiersProvider.ALL_INSTRUMENTS,
        Arrays.stream(TrsAssetClassGroup.values())
            .map(
                group -> {
                  var values =
                      Arrays.stream(NonMtmInstrumentType.values())
                          .filter(v -> v.getGroup() == group)
                          .map(i -> new Classifier(i.name(), i.getLabel()))
                          .sorted(comparing((Classifier::getId)))
                          .toList();
                  return new Classifier(group.name(), values);
                })
            .toList());
  }

  private Classifier instrumentTypes() {
    return new Classifier(
        INSTRUMENT_TYPES,
        null,
        Arrays.stream(NonMtmInstrumentType.values())
            .map(c -> new Classifier(c.name(), c.getLabel()))
            .toList(),
        InstrumentType.class);
  }

  private Classifier assetClassGroups() {
    return new Classifier(
        CoreClassifiersProvider.ALL_ASSET_CLASS_GROUPS,
        null,
        Arrays.stream(TrsAssetClassGroup.values())
            .map(v -> new Classifier(v.name(), v.getLabel()))
            .toList(),
        AssetGroup.class);
  }

  private Classifier nonMtmInstruments() {
    return enumClassifier(
        "nonMtmInstruments",
        NonMtmInstrumentType.class,
        NonMtmInstrumentType::toString,
        NonMtmInstrumentType::getLabel);
  }

  private Classifier trsIndexType() {
    return enumClassifier("trsIndexType", TrsType.class, TrsType::toString, TrsType::indexLabel);
  }

  private static Classifier trsTypes() {
    return new Classifier("trsTypes", TrsClassifiersConstants.trsTypes());
  }

  private static Classifier trsFixingOffsetDaysCalendar() {
    var calendars =
        TRS_FIXING_OFFSET_DAYS_CALENDARS.entrySet().stream()
            .map(
                v ->
                    new Classifier(
                        v.getKey().getCode(), List.of(new Classifier(v.getValue().getName()))))
            .toList();

    return new Classifier("trsFixingOffsetDaysCalendar", calendars);
  }

  private static Classifier trsTradeSupportedDayCounts() {
    var dayCounts =
        SupportedDayCount.supportedDayCounts().stream()
            .map(v -> new Classifier(v.label()))
            .toList();
    return new Classifier("trsTradeSupportedDayCounts", dayCounts);
  }

  private static Classifier trsTradeCalculationType() {
    return enumClassifier(
        "trsTradeCalculationType",
        TrsTradeCalculationType.class,
        TrsTradeCalculationType::name,
        TrsTradeCalculationType::getLabel);
  }

  private static Classifier trsTradeFrequency() {
    var frequencies =
        TRS_TRADE_FREQUENCIES.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList();
    return new Classifier("trsTradeFrequency", frequencies);
  }

  private Classifier trsTradeRollConvention() {
    return new Classifier(
        TRS_ROLL_CONVENTIONS,
        null,
        valuesStream(
                TrsClassifiersConstants.trsTradeRollConventions(),
                v -> new Classifier(ConventionMapper.INSTANCE.map(v)))
            .toList(),
        RollConvention.class);
  }

  private Classifier trsTradeCurrencies() {
    return new Classifier(
        TRS_CURRENCIES,
        TrsClassifiersConstants.trsTradeCurrencies().stream()
            .sorted(CurrencyComparator.INSTANCE)
            .map(v -> new Classifier(v.toString()))
            .toList());
  }

  private Classifier trsPeriodStatus() {
    return enumClassifier(
        "trsPeriodStatus", PeriodStatus.class, PeriodStatus::toString, PeriodStatus::getLabel);
  }

  private static <T extends Named> Stream<Classifier> valuesStream(
      ExtendedEnum<T> extendedEnum, Function<T, Classifier> mapper) {
    ImmutableCollection<T> values = extendedEnum.lookupAllNormalized().values();
    return values.stream().map(mapper).sorted(comparing(Classifier::getId));
  }
}
