package com.solum.xplain.trs.value;

import java.util.Arrays;

public enum TrsTradeCalculationType {
  FIXED("Fixed"),
  IBOR("Ibor"),
  OVERNIGHT("Overnight"),
  PERFORMANCE("Performance");

  private final String label;

  TrsTradeCalculationType(String label) {
    this.label = label;
  }

  public String getLabel() {
    return label;
  }

  public static TrsTradeCalculationType getByLabel(String label) {
    return Arrays.stream(values())
        .filter(it -> it.label.equalsIgnoreCase(label))
        .findFirst()
        .orElseThrow(
            () -> new IllegalArgumentException("Unknown Trade Calculation Type: " + label));
  }
}
