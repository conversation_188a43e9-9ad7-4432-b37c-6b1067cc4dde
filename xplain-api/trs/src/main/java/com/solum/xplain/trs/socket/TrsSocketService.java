package com.solum.xplain.trs.socket;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.sockets.SocketService;
import com.solum.xplain.core.sockets.WrappedEmitter;
import com.solum.xplain.trs.socket.type.TrsValuationSocketEvent;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Service
@AllArgsConstructor
public class TrsSocketService {
  private final SocketService socketService;
  private final AuthenticationContext authenticationContext;

  public Either<ErrorItem, SseEmitter> subscribeValuation(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(TrsValuationSocketEvent::trsValuationReference)
        .map(WrappedEmitter::defaultEmitter)
        .map(socketService::registerEmitter);
  }
}
