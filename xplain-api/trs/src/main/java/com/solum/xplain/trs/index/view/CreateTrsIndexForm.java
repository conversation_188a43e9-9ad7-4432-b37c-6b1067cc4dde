package com.solum.xplain.trs.index.view;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.trs.index.validation.UniqueTrsIndex;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@UniqueTrsIndex
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CreateTrsIndexForm extends TrsIndexForm {
  @NotEmpty @ValidIdentifier private String name;
}
