package com.solum.xplain.trs.portfolio.trade.validation;

import com.solum.xplain.trs.portfolio.trade.form.TrsLegForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

public class PaymentOffsetDaysGreaterThanAccrualOffsetDaysValidator
    implements ConstraintValidator<PaymentOffsetDaysGreaterThanAccrualOffsetDays, TrsLegForm> {

  @Override
  public boolean isValid(TrsLegForm value, ConstraintValidatorContext context) {
    if (ObjectUtils.allNotNull(value, value.getPaymentOffsetDays(), value.getAccrualOffsetDays())
        && value.getPaymentOffsetDays() < value.getAccrualOffsetDays()) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("paymentOffsetDays")
          .addConstraintViolation();
      return false;
    }
    return true;
  }
}
