package com.solum.xplain.trs.portfolio.csv;

import static com.solum.xplain.trs.portfolio.csv.TrsTradeRowFilterUtils.portfolioRowsFilter;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.customfield.CustomFieldNameRepository;
import com.solum.xplain.core.externalsource.IdentifierSourceRepository;
import com.solum.xplain.core.portfolio.csv.RowFilter;
import com.solum.xplain.core.portfolio.csv.loader.CustomFieldsCsvLoader;
import com.solum.xplain.core.portfolio.csv.loader.ExternalTradeIdsCsvLoader;
import com.solum.xplain.trs.index.validation.ValidTrsIndexValidator;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCondensedView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioNamesUniqueKey;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioView;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TrsTradeCsvLoaderFactory {
  private final ValidTrsIndexValidator validTrsIndexValidator;
  private final IdentifierSourceRepository identifierSourceRepository;
  private final CustomFieldNameRepository customFieldNameRepository;

  public TrsTradeCsvLoader standard(List<NonMtmPortfolioCondensedView> views) {
    var externalTradeIdsCsvLoader = externalTradeIdsCsvLoader();
    var customFieldCsvLoader = customFieldNameCsvLoader();
    var trsTradeValueCsvLoader =
        new TrsTradeValueCsvLoader(
            validTrsIndexValidator, externalTradeIdsCsvLoader, customFieldCsvLoader);
    return new TrsTradeCsvLoader(
        trsTradeValueCsvLoader,
        externalTradeIdsCsvLoader,
        portfolioNames(views),
        RowFilter.EMPTY_FILTER);
  }

  public TrsTradeCsvLoader forPortfolio(NonMtmPortfolioView view) {
    var key = NonMtmPortfolioNamesUniqueKey.fromView(view);
    var externalTradeIdsCsvLoader = externalTradeIdsCsvLoader();
    var customFieldCsvLoader = customFieldNameCsvLoader();

    var trsTradeValueCsvLoader =
        new TrsTradeValueCsvLoader(
            validTrsIndexValidator, externalTradeIdsCsvLoader, customFieldCsvLoader);
    return new TrsTradeCsvLoader(
        trsTradeValueCsvLoader,
        externalTradeIdsCsvLoader,
        Collections.singletonMap(key, view),
        portfolioRowsFilter(key));
  }

  private static Map<NonMtmPortfolioNamesUniqueKey, NonMtmPortfolioCondensedView> portfolioNames(
      List<NonMtmPortfolioCondensedView> views) {
    return views.stream()
        .filter(v -> isNotEmpty(v.getExternalPortfolioId()))
        .collect(
            Collectors.toMap(
                NonMtmPortfolioNamesUniqueKey::fromView, Function.identity(), (a, b) -> a));
  }

  private ExternalTradeIdsCsvLoader externalTradeIdsCsvLoader() {
    return new ExternalTradeIdsCsvLoader(
        identifierSourceRepository.activeIdentifierSourcesExtIds());
  }

  private CustomFieldsCsvLoader customFieldNameCsvLoader() {
    return new CustomFieldsCsvLoader(customFieldNameRepository.activeFieldExternalIds());
  }
}
