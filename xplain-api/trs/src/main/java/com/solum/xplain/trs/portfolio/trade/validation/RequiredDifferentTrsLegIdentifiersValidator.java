package com.solum.xplain.trs.portfolio.trade.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.trs.portfolio.trade.form.TrsTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentTrsLegIdentifiersValidator
    implements ConstraintValidator<RequiredDifferentTrsLegIdentifiers, TrsTradeForm> {

  public boolean isValid(TrsTradeForm form, ConstraintValidatorContext context) {
    if (form != null && allNotNull(form.getLeg1(), form.getLeg2())) {
      var leg1 = form.getLeg1();
      var leg2 = form.getLeg2();
      return leg1.getExtLegIdentifier() == null
          || leg2.getExtLegIdentifier() == null
          || !leg1.getExtLegIdentifier().equalsIgnoreCase(leg2.getExtLegIdentifier());
    }
    return true;
  }
}
