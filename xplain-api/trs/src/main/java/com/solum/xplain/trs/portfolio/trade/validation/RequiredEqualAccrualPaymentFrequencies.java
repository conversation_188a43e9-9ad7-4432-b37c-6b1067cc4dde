package com.solum.xplain.trs.portfolio.trade.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = RequiredEqualAccrualPaymentFrequenciesValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RequiredEqualAccrualPaymentFrequencies {
  String MESSAGE_TEMPLATE =
      "com.solum.xplain.trs.portfolio.trade.validation.RequiredEqualAccrualPaymentFrequencies.message";

  String message() default "{" + MESSAGE_TEMPLATE + "}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
