package com.solum.xplain.trs.portfolio.csv;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.portfolio.event.PortfolioAdded;
import com.solum.xplain.trs.portfolio.NonMtmPortfolio;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider;
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioCreateForm;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository;
import com.solum.xplain.trs.portfolio.value.ImportNonMtmPortfolio;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioUniqueKey;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@NullMarked
public class NonMtmPortfolioUploadService extends LoggingImportService {

  private static final String UNAUTHORIZED_NON_MTM_PORTFOLIO =
      "Unable to modify non-mtm portfolio without access";

  private final NonMtmPortfolioRepository nonMtmPortfolioRepository;
  private final AuthenticationContext authenticationContext;
  private final NonMtmPortfolioTeamFilterProvider nonMtmPortfolioTeamFilterProvider;
  private final NonMtmPortfolioCsvLoaderFactory nonMtmPortfolioCsvLoaderFactory;
  private final ApplicationEventPublisher eventPublisher;

  public NonMtmPortfolioUploadService(
      AuditEntryService auditEntryService,
      NonMtmPortfolioRepository nonMtmPortfolioRepository,
      AuthenticationContext authenticationContext,
      NonMtmPortfolioTeamFilterProvider nonMtmPortfolioTeamFilterProvider,
      NonMtmPortfolioCsvLoaderFactory nonMtmPortfolioCsvLoaderFactory,
      ApplicationEventPublisher eventPublisher) {
    super(auditEntryService);
    this.nonMtmPortfolioRepository = nonMtmPortfolioRepository;
    this.authenticationContext = authenticationContext;
    this.nonMtmPortfolioTeamFilterProvider = nonMtmPortfolioTeamFilterProvider;
    this.nonMtmPortfolioCsvLoaderFactory = nonMtmPortfolioCsvLoaderFactory;
    this.eventPublisher = eventPublisher;
  }

  @Override
  protected String getCollection() {
    return NonMtmPortfolio.class.getName();
  }

  @Override
  protected String getObjectName() {
    return NonMtmPortfolio.class.getName();
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadPortfolios(
      Authentication user, ParsingMode parsingMode, DuplicateAction duplicateAction, byte[] bytes) {
    return authenticationContext
        .userEither(user)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(u -> upload(u, parsingMode, duplicateAction, bytes))
        .fold(
            err -> toErrorReturn(duplicateAction, err),
            result -> toReturn(duplicateAction, result));
  }

  private Either<List<ErrorItem>, ImportResult> upload(
      XplainPrincipal user,
      ParsingMode parsingMode,
      DuplicateAction duplicateAction,
      byte[] bytes) {
    var loader = nonMtmPortfolioCsvLoaderFactory.getLoader(user);
    return loader
        .parse(bytes, parsingMode)
        .map(parserResult -> importNonMtmPortfolios(user, duplicateAction, parserResult));
  }

  private ImportResult importNonMtmPortfolios(
      XplainPrincipal xplainPrincipal,
      DuplicateAction duplicateAction,
      CsvParserResult<NonMtmPortfolioCreateForm> parserResult) {
    var importLogs =
        importNonMtmPortfolios(xplainPrincipal, duplicateAction, parserResult.getParsedLines());
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private List<LogItem> importNonMtmPortfolios(
      XplainPrincipal user,
      DuplicateAction duplicateAction,
      List<NonMtmPortfolioCreateForm> nonMtmPortfolioForms) {
    var existingNonMtmPortfolios = nonMtmPortfolioRepository.activeImportPortfolios();

    var importItems =
        ImportItems
            .<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>builder()
            .existingActiveItems(existingNonMtmPortfolios)
            .existingItemToKeyFn(NonMtmPortfolioUniqueKey::fromImportNonMtmPortfolio)
            .importItems(nonMtmPortfolioForms)
            .importItemToKeyFn(NonMtmPortfolioUniqueKey::fromForm)
            .build();

    return switch (duplicateAction) {
      case ERROR -> onError(importItems);
      case REPLACE_DELETE ->
          modifyWithAccessCheck(
              user, importItems.getImportKeys(), () -> onReplaceDelete(importItems));
      case REPLACE ->
          modifyWithAccessCheck(user, importItems.getImportKeys(), () -> onReplace(importItems));
      case APPEND_DELETE ->
          modifyWithAccessCheck(
              user, importItems.getImportKeys(), () -> onAppendDelete(importItems));
      case APPEND ->
          modifyWithAccessCheck(user, importItems.getImportKeys(), () -> onAppend(importItems));
    };
  }

  private List<LogItem> modifyWithAccessCheck(
      XplainPrincipal user,
      Set<NonMtmPortfolioUniqueKey> importKeys,
      Supplier<List<LogItem>> resultSupplier) {
    return validateImportKeys(user, importKeys)
        .fold(items -> asLogItems(List.of(items)), r -> resultSupplier.get());
  }

  private Either<ErrorItem, Set<NonMtmPortfolioUniqueKey>> validateImportKeys(
      XplainPrincipal user, Set<NonMtmPortfolioUniqueKey> importKeys) {
    var portfolioTeamFilter = nonMtmPortfolioTeamFilterProvider.provideFilter(user);
    int accessibleDuplicateCount =
        nonMtmPortfolioRepository.duplicatePortfoliosCount(importKeys, portfolioTeamFilter);
    int allDuplicatesCount =
        nonMtmPortfolioRepository.duplicatePortfoliosCount(
            importKeys, NonMtmPortfolioTeamFilter.emptyFilter());

    return Eithers.cond(
        accessibleDuplicateCount == allDuplicatesCount,
        Error.OPERATION_NOT_ALLOWED.entity(UNAUTHORIZED_NON_MTM_PORTFOLIO),
        importKeys);
  }

  private List<LogItem> onReplaceDelete(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    var archiveResult = archive(importItems);
    return CollectionUtils.join(appendResult, replaceResult, archiveResult);
  }

  private List<LogItem> onReplace(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    return CollectionUtils.join(appendResult, replaceResult);
  }

  private List<LogItem> onAppendDelete(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var appendResult = append(importItems);
    var archiveResult = archive(importItems);
    return CollectionUtils.join(appendResult, archiveResult);
  }

  private List<LogItem> onAppend(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    return append(importItems);
  }

  private List<LogItem> onError(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var errors =
        Stream.of(
                errorStream(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem),
                errorStream(importItems.getSpareKeys(), ImportErrorUtils::missingItem))
            .flatMap(Function.identity())
            .toList();

    return asLogItems(errors);
  }

  private Stream<ErrorItem> errorStream(
      Set<NonMtmPortfolioUniqueKey> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(k -> function.apply(k.getExternalPortfolioId()));
  }

  private List<LogItem> append(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    // TODO: SXSD-10151 ideally this would work the same as PortfolioUploadService rather than
    //  collecting company IDs here.
    Set<String> companyIds = new HashSet<>();
    List<LogItem> logItems =
        importItems.getNewKeys().stream()
            .map(
                k -> {
                  var i = importItems.importItem(k);
                  companyIds.add(i.getCompanyId());
                  return appendItem(k, i);
                })
            .toList();
    if (!companyIds.isEmpty()) {
      eventPublisher.publishEvent(new PortfolioAdded(companyIds));
    }
    return logItems;
  }

  private List<LogItem> replace(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    return importItems.getDuplicateKeys().stream().map(k -> replaceItem(k, importItems)).toList();
  }

  private List<LogItem> archive(
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    return importItems.getSpareKeys().stream().map(k -> archiveItem(k, importItems)).toList();
  }

  private LogItem appendItem(NonMtmPortfolioUniqueKey key, NonMtmPortfolioCreateForm i) {
    var eitherId = insert(i);
    return createInsertLogItem(getIdentifier(key), eitherId);
  }

  private LogItem replaceItem(
      NonMtmPortfolioUniqueKey key,
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var e = importItems.existingItem(key);
    var i = importItems.importItem(key);
    var eitherId = update(e, i);
    return createUpdateLogItem(getIdentifier(key), eitherId);
  }

  private LogItem archiveItem(
      NonMtmPortfolioUniqueKey key,
      ImportItems<NonMtmPortfolioCreateForm, NonMtmPortfolioUniqueKey, ImportNonMtmPortfolio>
          importItems) {
    var e = importItems.existingItem(key);
    var eitherId = archive(e.getId());
    return createArchiveLogItem(getIdentifier(key), eitherId);
  }

  private String getIdentifier(NonMtmPortfolioUniqueKey key) {
    return key.getExternalPortfolioId();
  }

  private Either<ErrorItem, EntityId> insert(NonMtmPortfolioCreateForm form) {
    return nonMtmPortfolioRepository.insert(form, false);
  }

  private Either<ErrorItem, EntityId> archive(String id) {
    return nonMtmPortfolioRepository.archive(id);
  }

  private Either<ErrorItem, EntityId> update(
      ImportNonMtmPortfolio existingPortfolio, NonMtmPortfolioCreateForm form) {
    return nonMtmPortfolioRepository.update(existingPortfolio.getId(), form);
  }
}
