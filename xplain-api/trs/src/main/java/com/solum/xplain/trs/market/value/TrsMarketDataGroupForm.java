package com.solum.xplain.trs.market.value;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.common.NamedObjectForm;
import com.solum.xplain.core.common.value.AllowedCompaniesForm;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.trs.market.validation.UniqueTrsMarketDataGroupName;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@UniqueTrsMarketDataGroupName
public class TrsMarketDataGroupForm implements NamedObjectForm {
  @NotEmpty private final String name;

  private final PricingSlot pricingSlot;

  @Valid @NotNull private final AllowedCompaniesForm allowedCompaniesForm;

  @Valid @NotNull private final AllowedTeamsForm allowedTeamsForm;
}
