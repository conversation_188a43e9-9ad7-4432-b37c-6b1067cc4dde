package com.solum.xplain.trs.valuation.market.source;

import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** Lightweight representation of a TRS market data value, with date and bid/ask type and value. */
@AllArgsConstructor
@Getter
public class TrsBidAskDateValue {

  private final LocalDate date;
  private final ValueBidAskType bidAskType;
  private final BigDecimal value;

  public static TrsBidAskDateValue ofMarketDataValueFlatView(
      MarketDataValueFlatView marketDataValueFlatView) {
    return new TrsBidAskDateValue(
        marketDataValueFlatView.getDate(),
        marketDataValueFlatView.getBidAsk(),
        marketDataValueFlatView.getValue());
  }
}
