package com.solum.xplain.trs.valuation.value;

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.trs.valuation.market.NonMtmValuationMarketDataSettings;
import com.solum.xplain.trs.valuation.validation.ValidTrsValuationMarketDataGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;

@Data
@ValidTrsValuationMarketDataGroup
public class NonMtmValuationForm {

  @NotNull
  @Schema(description = "Curve date (market data date)", example = "2023-01-31")
  private LocalDate curveDate;

  @NotNull
  @Schema(description = "Valuation date", example = "2023-01-31")
  private LocalDate valuationDate;

  @NotNull
  @Schema(description = "State date", example = "2023-01-31")
  private LocalDate stateDate;

  @ValidStringSet(CurrenciesSupplier.class)
  @Schema(description = "Calculation currency", example = "EUR")
  private String calculationCcy;

  @ValidObjectId
  @Schema(description = "Curve configuration id", nullable = true)
  private String curveConfigurationId;

  @ValidObjectId
  @Schema(description = "Market data group id", nullable = true)
  private String marketDataGroupId;

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "marketDataSourceType")
  @Schema(
      description = "Market data type",
      nullable = true,
      allowableValues = {
        "RAW_PRIMARY",
        "RAW_SECONDARY",
        "PRELIMINARY_PRIMARY",
        "PRELIMINARY_SECONDARY",
        "OVERLAY"
      })
  private String marketDataSource;

  @Schema(
      description = "Price type to use during valuation for market data",
      nullable = true,
      defaultValue = "BID_PRICE")
  private InstrumentPriceType marketDataPriceType;

  @NotEmpty
  @ValidObjectId
  @Schema(description = "TRS market data group id")
  private String trsMarketDataGroupId;

  @Schema(
      description = "Price type to use during valuation for TRS market data",
      nullable = true,
      defaultValue = "BID_PRICE")
  private InstrumentPriceType trsMarketDataPriceType;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "marketDataSourceType")
  @Schema(
      description = "TRS market data type",
      allowableValues = {
        "RAW_PRIMARY",
        "RAW_SECONDARY",
        "PRELIMINARY_PRIMARY",
        "PRELIMINARY_SECONDARY",
        "OVERLAY"
      })
  private String trsMarketDataSource;

  @ValidObjectId
  @Schema(description = "Trade id to valuate, all trades if not provided", nullable = true)
  private String tradeId;

  public NonMtmValuationMarketDataSettings marketDataSetup(BitemporalDate stateLockDate) {
    return new NonMtmValuationMarketDataSettings(
        trsMarketDataGroupId,
        MarketDataSourceType.valueOf(trsMarketDataSource),
        marketDataGroupId,
        marketDataSource == null ? null : MarketDataSourceType.valueOf(marketDataSource),
        curveConfigurationId,
        stateLockDate,
        curveDate,
        valuationDate,
        ofNullable(marketDataPriceType).orElse(BID_PRICE),
        ofNullable(trsMarketDataPriceType).orElse(BID_PRICE));
  }
}
