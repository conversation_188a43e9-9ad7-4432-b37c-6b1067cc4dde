package com.solum.xplain.trs.valuation.csv.accrued;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NonMtmAccruedPeriodFields {
  static final String FIXING_DATE = "Fixing Date";
  static final String START_DATE = "Start Date";
  static final String END_DATE = "End Date";
  static final String PAYMENT_DATE = "Payment Date";
  static final String FIXING_RATE = "Fixing Rate";
  static final String HISTORICAL_ACCRUAL = "Historical Accrual";
  static final String STATUS = "Status";
  static final String PAID = "Paid";
  static final String ACCRUAL_VALUE = "Accrual Value";
  static final String IA_ACCRUAL_VALUE = "IA Accrual Value";

  static final String FIXING_DATE_SD = "Fixing Date (SD)";
  static final String FIXING_DATE_ED = "Fixing Date (ED)";
  static final String BASE_INDEX = "Base Index";
  static final String REFERENCE_INDEX = "Reference Index";
  static final String HISTORICAL_MNGT_FEE = "Historical Mngt Fees";
  static final String NET_ACCRUAL_VALUE = "Net Accrual Value";
  static final String NET_IA_ACCRUAL_VALUE = "Net IA Accrual Value";
}
