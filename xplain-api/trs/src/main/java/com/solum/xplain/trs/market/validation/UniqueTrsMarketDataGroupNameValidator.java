package com.solum.xplain.trs.market.validation;

import static com.solum.xplain.core.datagroup.DataGroupNameValidatorUtils.isUniqueGroupName;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.trs.market.TrsMarketDataGroupRepository;
import com.solum.xplain.trs.market.value.TrsMarketDataGroupForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class UniqueTrsMarketDataGroupNameValidator
    implements ConstraintValidator<UniqueTrsMarketDataGroupName, TrsMarketDataGroupForm> {

  private final TrsMarketDataGroupRepository repository;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  public UniqueTrsMarketDataGroupNameValidator(
      TrsMarketDataGroupRepository repository,
      RequestPathVariablesSupport requestPathVariablesSupport) {
    this.repository = repository;
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  public boolean isValid(TrsMarketDataGroupForm form, ConstraintValidatorContext context) {
    String groupId = requestPathVariablesSupport.getPathVariable("groupId");
    return isUniqueGroupName(form, context, name -> repository.existsByName(name, groupId));
  }
}
