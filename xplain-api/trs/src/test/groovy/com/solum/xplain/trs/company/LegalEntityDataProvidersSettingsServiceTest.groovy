package com.solum.xplain.trs.company

import static com.solum.xplain.core.common.EntityId.entityId
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.common.team.UserTeamEntity
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.UserCompanyService
import com.solum.xplain.trs.company.entity.CompanyDataProvidersSettings
import com.solum.xplain.trs.company.form.LegalEntityDataProvidersSettingsForm
import com.solum.xplain.trs.company.repository.CompanyDataProvidersSettingsRepository
import com.solum.xplain.trs.company.repository.LegalEntityDataProvidersSettingsRepository
import com.solum.xplain.trs.company.view.LegalEntityDataProvidersSettingsView
import java.time.LocalDate
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.Authentication
import spock.lang.Specification

class LegalEntityDataProvidersSettingsServiceTest extends Specification {
  Authentication AUTH = new TestingAuthenticationToken("user", "password")
  String COMPANY_ID = "companyId"
  String ENTITY_ID = "entityId"
  LocalDate STATE_DATE = LocalDate.now()
  BitemporalDate BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  CompanyDataProvidersSettingsRepository companySettingsRepository = Mock()
  LegalEntityDataProvidersSettingsRepository repository = Mock()
  UserCompanyService userCompanyService = Mock()

  LegalEntityDataProvidersSettingsService service =
  new LegalEntityDataProvidersSettingsService(companySettingsRepository, repository, userCompanyService)


  def "should return entity settings view"() {
    setup:
    def settingsView = new LegalEntityDataProvidersSettingsView()
    def defaultSettings = Mock(CompanyDataProvidersSettings)
    1 * userCompanyService.userLegalEntity(AUTH, COMPANY_ID, ENTITY_ID) >> right(Mock(UserTeamEntity))
    1 * companySettingsRepository.companySettingsEntity(COMPANY_ID, BITEMPORAL_STATE_DATE) >> right(defaultSettings)
    1 * repository.legalEntitySettings(ENTITY_ID, { it.getActualDate() == STATE_DATE }, defaultSettings) >> settingsView

    when:
    def result = service.entitySettings(AUTH, COMPANY_ID, ENTITY_ID, BITEMPORAL_STATE_DATE)

    then:
    result.isRight()
    result.getOrNull() == settingsView
  }

  def "should return entity settings versions"() {
    setup:
    1 * userCompanyService.userLegalEntity(AUTH, COMPANY_ID, ENTITY_ID) >> right(Mock(UserTeamEntity))
    1 * repository.legalEntitySettingsVersions(ENTITY_ID) >> []

    when:
    def result = service.entitySettingsVersions(AUTH, COMPANY_ID, ENTITY_ID)

    then:
    result.isRight()
    result.getOrNull() == []
  }

  def "should return entity settings future versions"() {
    setup:
    1 * userCompanyService.userLegalEntity(AUTH, COMPANY_ID, ENTITY_ID) >> right(Mock(UserTeamEntity))
    1 * repository.entitySettingsFutureVersions(ENTITY_ID, STATE_DATE) >> DateList.uniqueSorted([])

    when:
    def result = service.entitySettingsFutureVersions(AUTH, COMPANY_ID, ENTITY_ID, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull().getDates() == []
  }

  def "should update entity settings"() {
    setup:
    def form = Mock(LegalEntityDataProvidersSettingsForm)
    1 * userCompanyService.userLegalEntity(AUTH, COMPANY_ID, ENTITY_ID) >> right(Mock(UserTeamEntity))
    1 * repository.updateLegalEntitySettings(ENTITY_ID, STATE_DATE, form) >> right(entityId(ENTITY_ID))

    when:
    def result = service.updateEntitySettings(AUTH, COMPANY_ID, ENTITY_ID, STATE_DATE, form)

    then:
    result.isRight()
  }

  def "should delete entity settings version"() {
    setup:
    1 * userCompanyService.userLegalEntity(AUTH, COMPANY_ID, ENTITY_ID) >> right(Mock(UserTeamEntity))
    1 * repository.deleteEntitySettingsVersion(ENTITY_ID, STATE_DATE) >> right(entityId(ENTITY_ID))

    when:
    def result = service.deleteEntitySettingsVersion(AUTH, COMPANY_ID, ENTITY_ID, STATE_DATE)

    then:
    result.isRight()
  }
}
