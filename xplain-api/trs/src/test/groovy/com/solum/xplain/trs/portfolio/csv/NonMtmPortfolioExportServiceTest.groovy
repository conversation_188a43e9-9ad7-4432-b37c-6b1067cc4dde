package com.solum.xplain.trs.portfolio.csv

import static com.solum.xplain.core.common.team.UserTeamEntity.userEntity
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.data.domain.Sort.by

import com.google.common.io.ByteStreams
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.CompanyLegalEntityView
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.csv.CustomTradeCsvHeaders
import com.solum.xplain.core.portfolio.csv.PortfolioItemCustomHeadersService
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamListView
import com.solum.xplain.trs.index.sampledata.TrsIndexSample
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemRepository
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioBuilder
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioItemBuilder
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioViewBuilder
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.springframework.data.domain.Sort
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class NonMtmPortfolioExportServiceTest extends Specification implements TrsIndexSample {

  NonMtmPortfolioExportService testObject
  XplainPrincipal authUser
  TestingAuthenticationToken authToken
  CompanyLegalEntityView companyLegalEntityView
  def userRepository = Mock(AuthenticationContext)
  def repository = Mock(NonMtmPortfolioRepository)
  def portfolioItemRepository = Mock(NonMtmPortfolioItemRepository)
  def filterProvider = Mock(NonMtmPortfolioTeamFilterProvider)
  def versionedTrsTradeMapper = new VersionedTrsTradeCsvMapper()
  def customHeadersService = Mock(PortfolioItemCustomHeadersService)
  def teamRepository = Mock(TeamRepository)
  def nonMtmPortfolioItemCsvMapper = new NonMtmPortfolioItemCsvMapper(versionedTrsTradeMapper, customHeadersService)

  def setup() {
    testObject = new NonMtmPortfolioExportService(userRepository, filterProvider, repository, portfolioItemRepository, nonMtmPortfolioItemCsvMapper, teamRepository)
    authUser = user("userId")
    authToken = new TestingAuthenticationToken(authUser, null)
    companyLegalEntityView = new CompanyLegalEntityView()
    companyLegalEntityView.id = "id"
    companyLegalEntityView.name = "name"
  }

  def "should fail to export non-mtm portfolio when user is not found"() {
    setup:
    def sort = by("name")
    def tableFilter = emptyTableFilter()
    def portfolioTeamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def portfolioFilter = new NonMtmPortfolioFilter()
    def stateDate = now()

    when:
    def result = testObject.exportPortfolios(authToken, sort, tableFilter, portfolioFilter, stateDate)

    then:
    1 * userRepository.userEither(authToken) >> left(Error.OBJECT_NOT_FOUND.entity())
    0 * filterProvider.provideFilter(authUser)
    0 * repository.portfolioViewStream(tableFilter, sort, portfolioFilter, portfolioTeamFilter)

    and:
    result.isLeft()
    def err = result.left().getOrNull() as ErrorItem
    err.reason == Error.OBJECT_NOT_FOUND
  }

  def "should successfully export non-mtm portfolio"() {
    setup:
    def sort = by("name")
    def tableFilter = emptyTableFilter()
    def portfolioTeamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def portfolioFilter = new NonMtmPortfolioFilter()
    def stateDate = now()
    def portfolio = NonMtmPortfolioBuilder.nonMtmPortfolio()
    def teamId1 = new ObjectId()
    def teamId2 = new ObjectId()
    def restrictedPortfolio = new NonMtmPortfolioBuilder()
      .externalPortfolioId("PORTFOLIO_ID_2")
      .name("Restricted Portfolio")
      .allowAllTeams(false)
      .teamIds([teamId1, teamId2])
      .build()
    def item = NonMtmPortfolioViewBuilder.of(portfolio)
    def item2 = NonMtmPortfolioViewBuilder.of(restrictedPortfolio)

    when:
    def result = testObject.exportPortfolios(authToken, sort, tableFilter, portfolioFilter, stateDate)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * filterProvider.provideFilter(authUser) >> portfolioTeamFilter
    1 * repository.portfolioViewStream(tableFilter, sort, portfolioFilter, portfolioTeamFilter) >> Stream.of(item, item2)
    1 * teamRepository.getTeamsById() >> [
      (teamId1.toHexString()): new TeamListView(id: teamId1.toHexString(), name: "Team 1"),
      (teamId2.toHexString()): new TeamListView(id: teamId2.toHexString(), name: "Team 2")
    ]

    and:
    result.isRight()
    def content = result.getOrNull()
    new String(content.getBytes().getByteArray(), "utf-8") == """\
      Portfolio ID,Portfolio Name,Company ID,Entity ID,Team names,Allow all teams,Description
      extPortfolioId,Non-mtm Portfolio name,externalCompanyId,externalEntityId,,true,interesting description
      PORTFOLIO_ID_2,Restricted Portfolio,externalCompanyId,externalEntityId,Team 1|Team 2,false,interesting description
    """.stripIndent(true)
  }

  def "should fail to export non-mtm portfolio items when user is not found"() {
    setup:
    def portfolioTeamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def portfolioFilter = new NonMtmPortfolioFilter()
    def stateDate = BitemporalDate.newOfNow()

    when:
    def result = testObject.exportAllPortfolioItems(authToken, stateDate)

    then:
    1 * userRepository.userEither(authToken) >> left(Error.OBJECT_NOT_FOUND.entity())
    0 * filterProvider.provideFilter(authUser)
    0 * repository.portfolioViewList(portfolioFilter, portfolioTeamFilter)

    and:
    result.isLeft()
    def err = result.left().getOrNull() as ErrorItem
    err.reason == Error.OBJECT_NOT_FOUND
  }

  def "should successfully export all non-mtm portfolio items"() {
    setup:
    def expectedCsv = loadResource("all-non-mtm-portfolio-items.csv")
    def portfolioTeamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def portfolioFilter = new NonMtmPortfolioFilter(false)
    def stateDate = BitemporalDate.newOfNow()
    def portfolio = new NonMtmPortfolioBuilder().id("00000000000000000000006").allowAllTeams(true).build()
    def portfolioView = NonMtmPortfolioViewBuilder.of(portfolio)
    def item = NonMtmPortfolioItemBuilder.trsTradeWithOvernightAndPerformanceLegs(portfolioId: portfolio.id)
    def item2 = NonMtmPortfolioItemBuilder.trsTradeWithFixedAndPerformanceLegs(portfolioId: portfolio.id, customFields: [new CustomTradeField("F1", "V1")])
    def item3 = NonMtmPortfolioItemBuilder.trsTradeWithIborAndPerformanceLegs(portfolioId: portfolio.id, externalIdentifiers: [new ExternalIdentifier("ID1", "SOURCE1")])

    when:
    def result = testObject.exportAllPortfolioItems(authToken, stateDate)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * filterProvider.provideFilter(authUser) >> portfolioTeamFilter
    1 * repository.portfolioViewList(portfolioFilter, portfolioTeamFilter) >> [portfolioView]
    1 * portfolioItemRepository.portfolioItemsStream([portfolioView.id], stateDate) >> Stream.of(item, item2, item3)
    1 * customHeadersService.customHeaders() >> new CustomTradeCsvHeaders(["Trade ID.SOURCE1"], ["Custom Field.F1"])

    and:
    result.isRight()
    def content = result.getOrNull()
    new String(content.getBytes().getByteArray(), "utf-8") == new String(expectedCsv, "utf-8")
  }

  def "should successfully export non-mtm portfolio items"() {
    setup:
    def expectedCsv = loadResource("non-mtm-portfolio-items.csv")
    def sort = Sort.unsorted()
    def tableFilter = emptyTableFilter()
    def stateDate = BitemporalDate.newOfNow()
    def portfolio = new NonMtmPortfolioBuilder().id("00000000000000000000006").allowAllTeams(true).build()
    def portfolioView = NonMtmPortfolioViewBuilder.of(portfolio)
    def item = NonMtmPortfolioItemBuilder.trsTradeWithOvernightAndPerformanceLegs(portfolioId: portfolio.id)
    def item2 = NonMtmPortfolioItemBuilder.trsTradeWithFixedAndPerformanceLegs(portfolioId: portfolio.id, externalIdentifiers: [new ExternalIdentifier("ID1", "SOURCE1")])
    def item3 = NonMtmPortfolioItemBuilder.trsTradeWithIborAndPerformanceLegs(
      portfolioId: portfolio.id,
      externalIdentifiers: [new ExternalIdentifier("ID2", "SOURCE2")],
      customFields: [new CustomTradeField("F2", "V11")]
      )

    when:
    def result = testObject.exportPortfolioItems(authToken, portfolio.id, stateDate, sort, tableFilter)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * repository.getUserPortfolioView(authUser, portfolio.id) >> right(userEntity(authUser, portfolioView))
    1 * portfolioItemRepository.portfolioItemStream(portfolio.id, stateDate, sort, tableFilter) >> Stream.of(item, item2, item3)
    1 * customHeadersService.customHeaders() >> new CustomTradeCsvHeaders(["Trade ID.SOURCE1", "Trade ID.SOURCE2"], ["Custom Field.F1", "Custom Field.F2"])

    and:
    result.isRight()
    def content = result.getOrNull()
    new String(content.getBytes().getByteArray(), "utf-8") == new String(expectedCsv, "utf-8")
  }


  byte[] loadResource(String fileName) {
    return ByteStreams.toByteArray(getClass().getResourceAsStream("/non-mtm-portfolio/csv/" + fileName))
  }
}
