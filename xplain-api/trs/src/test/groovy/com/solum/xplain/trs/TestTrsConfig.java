package com.solum.xplain.trs;

import com.solum.xplain.trs.valuation.market.source.NonMtmOverlayMarketDataSource;
import com.solum.xplain.trs.valuation.market.source.NonMtmPreliminaryMarketDataSource;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@EnableAutoConfiguration
@SpringBootConfiguration
@ImportAutoConfiguration
public class TestTrsConfig extends TrsConfig {

  @Bean
  @ConditionalOnMissingBean(NonMtmPreliminaryMarketDataSource.class)
  NonMtmPreliminaryMarketDataSource mockPreliminarySource() {
    return (stateKey, valueFilter) -> {
      throw new UnsupportedOperationException(
          "Mock provider should be overridden in TestTrsConfig");
    };
  }

  @Bean
  @ConditionalOnMissingBean(NonMtmOverlayMarketDataSource.class)
  NonMtmOverlayMarketDataSource mockOverlaySource() {
    return (stateKey, valueFilter) -> {
      throw new UnsupportedOperationException(
          "Mock provider should be overridden in TestTrsConfig");
    };
  }
}
