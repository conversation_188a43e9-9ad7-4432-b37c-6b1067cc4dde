package com.solum.xplain.trs.portfolio.trade

import static com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter.activeNonMtmPortfolios

import com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverter
import com.solum.xplain.trs.portfolio.NonMtmPortfolioMapper
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import spock.lang.Specification

class NonMtmPortfolioItemEntityToViewConverterProviderTest extends Specification {
  NonMtmPortfolioMapper mapper = Mock()
  NonMtmPortfolioRepository portfolioRepository = Mock()

  NonMtmPortfolioItemEntityToViewConverterProvider provider = new NonMtmPortfolioItemEntityToViewConverterProvider(mapper, portfolioRepository)

  def "should get converter"() {
    setup:
    1 * portfolioRepository.portfolioCondensedViews(activeNonMtmPortfolios(), NonMtmPortfolioTeamFilter.emptyFilter()) >> []
    def converter = new NonMtmPortfolioItemToViewConverter([], mapper)

    expect:
    provider.provideForValues([]) == new DefaultEmbeddedVersionEntityToViewConverter(converter)
  }
}
