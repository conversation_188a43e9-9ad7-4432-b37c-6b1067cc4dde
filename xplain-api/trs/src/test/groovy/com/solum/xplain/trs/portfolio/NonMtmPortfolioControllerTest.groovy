package com.solum.xplain.trs.portfolio

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter.activeNonMtmPortfolios
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static java.time.LocalDate.parse
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.company.CompanyTeamValidationService
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.csv.ValidationResponse
import com.solum.xplain.core.customfield.CustomFieldNameRepository
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.trs.helper.MockMvcConfiguration
import com.solum.xplain.trs.index.TrsIndexRepository
import com.solum.xplain.trs.index.value.TrsIndex
import com.solum.xplain.trs.market.TrsMarketDataGroup
import com.solum.xplain.trs.market.TrsMarketDataGroupRepository
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioExportService
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioItemUploadService
import com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioUploadService
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioCreateForm
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioUpdateForm
import com.solum.xplain.trs.portfolio.form.SearchNonMtmPortfolioItemForm
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemRepository
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioBuilder
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioFormSample
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioItemBuilder
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioViewBuilder
import com.solum.xplain.trs.portfolio.trade.value.TrsBondTradeView
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCondensedView
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioItemWithKeyView
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioView
import com.solum.xplain.trs.trade.sampledata.TrsTradeSample
import com.solum.xplain.trs.valuation.NonMtmValuationsService
import com.solum.xplain.trs.valuation.sampledata.NonMtmValuationFormSample
import com.solum.xplain.trs.valuation.value.NonMtmValuationForm
import com.solum.xplain.trs.value.TrsType
import java.time.LocalDate
import java.util.function.Supplier
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@MockMvcConfiguration
@WebMvcTest(controllers = [NonMtmPortfolioController])
class NonMtmPortfolioControllerTest extends Specification implements NonMtmPortfolioFormSample, TrsTradeSample, NonMtmValuationFormSample {

  private static EXPORTED_CSV = FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")

  private final String NON_MTM_PORTFOLIO_API = '/non-mtm-portfolios'

  @Autowired
  MockMvc mockMvc

  @Autowired
  ObjectMapper mapper

  @SpringBean
  NonMtmPortfolioService service = Mock()

  @SpringBean
  NonMtmPortfolioExportService exportService = Mock()

  @SpringBean
  NonMtmPortfolioUploadService uploadService = Mock()

  @SpringBean
  NonMtmPortfolioItemUploadService uploadItemService = Mock()

  @SpringBean
  CompanyTeamValidationService companyTeamValidationService = Mock()

  @SpringBean
  CompanyRepository companyRepository = Mock()

  @SpringBean
  NonMtmPortfolioRepository nonMtmPortfolioRepository = Mock()

  @SpringBean
  MarketDataGroupRepository mdDataGroupRepository = Mock()

  @SpringBean
  TrsMarketDataGroupRepository trsDataGroupRepository = Mock()

  @SpringBean
  TrsIndexRepository trsIndexRepository = Mock()

  @SpringBean
  NonMtmPortfolioItemRepository itemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport pathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  ResourceValidationService resourceValidationService = Mock()

  @SpringBean
  NonMtmValuationsService valuationsService = Mock()

  @SpringBean
  CustomFieldNameRepository fieldNameRepository = Mock()

  def "should successfully get non-mtm portfolios when stateDate is given"() {
    setup:
    def stateDate = parse("2022-06-01")
    def scrollRequest = ScrollRequest.of(0, 15, Sort.by(NonMtmPortfolioView.Fields.companyName,
      NonMtmPortfolioView.Fields.entityName,
      NonMtmPortfolioCondensedView.Fields.externalPortfolioId))
    def tableFilter = TableFilter.emptyTableFilter()
    def portfolioFilter = activeNonMtmPortfolios()

    when:
    def results = mockMvc.perform(get(NON_MTM_PORTFOLIO_API)
      .param("stateDate", stateDate.toString())
      .param("archived", "false")
      .param("startRow", "0")
      .param("endRow", "15")
      .contentType(MediaType.APPLICATION_JSON))

    then:
    results.andExpect(status().is2xxSuccessful())

    and:
    1 * service.getAll(scrollRequest,
      tableFilter,
      portfolioFilter,
      { it.getActualDate() == stateDate },
      _ as Authentication) >> ScrollableEntry.empty()
  }

  def "should successfully get legal entity non-mtm portfolios when entityId is given"() {
    when:
    def results = mockMvc.perform(get(NON_MTM_PORTFOLIO_API + "/entities")
      .param("entityId", "ID")
      .contentType(MediaType.APPLICATION_JSON))

    then:
    results.andExpect(status().is2xxSuccessful())

    and:
    1 * service.legalEntityPortfolioNames(_ as Authentication, "ID") >> []
  }

  def "should create successfully portfolio with form #nonMtmPortfolioForm"() {
    setup:
    def validCompanyId = nonMtmPortfolioForm.companyId as String
    def externalId = nonMtmPortfolioForm.externalPortfolioId as String
    def legalEntityId = nonMtmPortfolioForm.entityId as String

    when:
    def results = mockMvc.perform(post(NON_MTM_PORTFOLIO_API)
      .with(csrf())
      .content(toJson(nonMtmPortfolioForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * companyTeamValidationService.validCompanyEntityTeams(legalEntityId,
      validCompanyId,
      _ as AllowedTeamsForm) >> true
    2 * companyRepository.companyEntity(validCompanyId) >> right(new Company())
    2 * nonMtmPortfolioRepository.existsByExternalIdExcludingSelf(validCompanyId,
      legalEntityId,
      externalId,
      null) >> false
    1 * service.create(_ as Authentication, _ as NonMtmPortfolioCreateForm) >> right(entityId("1"))

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(response))


    where:
    nonMtmPortfolioForm                                | response
    sampleNonMtmPortfolioCreateForm()                  | """{"id":"1"}"""
    sampleNonMtmPortfolioCreateForm(description: null) | """{"id":"1"}"""
  }

  def "when creating a portfolio with invalid form #nonMtmPortfolioForm is given then validation error is returned"() {
    setup:
    def companyId = nonMtmPortfolioForm.companyId as String
    def externalId = nonMtmPortfolioForm.externalPortfolioId as String
    def legalEntityId = nonMtmPortfolioForm.entityId as String
    companyTeamValidationService.validCompanyEntityTeams(legalEntityId, companyId, _ as AllowedTeamsForm) >> true
    companyRepository.companyEntity(companyId) >> { "000000000000000000000009" == companyId ? left(OBJECT_NOT_FOUND.entity()) : right(new Company()) }
    nonMtmPortfolioRepository.existsByExternalIdExcludingSelf(companyId,
      legalEntityId,
      externalId,
      null) >> { "EXISTS" == externalId }

    when:
    def results = mockMvc.perform(post(NON_MTM_PORTFOLIO_API)
      .with(csrf())
      .content(toJson(nonMtmPortfolioForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.create(_ as Authentication, _ as NonMtmPortfolioCreateForm)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    nonMtmPortfolioForm                                                    | response
    sampleNonMtmPortfolioCreateForm(name: null)                            | "NotEmpty.nonMtmPortfolioCreateForm.name"
    sampleNonMtmPortfolioCreateForm(externalPortfolioId: null)             | "NotEmpty.nonMtmPortfolioCreateForm.externalPortfolioId"
    sampleNonMtmPortfolioCreateForm(externalPortfolioId: "WITH SPACES")    | "ValidIdentifier.nonMtmPortfolioCreateForm.externalPortfolioId"
    sampleNonMtmPortfolioCreateForm(externalPortfolioId: "lowercase")      | "ValidIdentifier.nonMtmPortfolioCreateForm.externalPortfolioId"
    sampleNonMtmPortfolioCreateForm(externalPortfolioId: "EXISTS")         | "UniqueNonMtmPortfolioExtId.nonMtmPortfolioCreateForm.externalPortfolioId"
    sampleNonMtmPortfolioCreateForm(externalPortfolioId: "EXISTS")         | "NON-MTM Portfolio ID must be unique"
    sampleNonMtmPortfolioCreateForm(allowedTeamsForm: null)                | "NotNull.nonMtmPortfolioCreateForm.allowedTeamsForm"
    sampleNonMtmPortfolioCreateForm(companyId: null)                       | "NotEmpty.nonMtmPortfolioCreateForm.companyId"
    sampleNonMtmPortfolioCreateForm(companyId: "")                         | "NotEmpty.nonMtmPortfolioCreateForm.companyId"
    sampleNonMtmPortfolioCreateForm(companyId: "INCORRECT_COMPANY_ID")     | "ValidObjectId.nonMtmPortfolioCreateForm.companyId"
    sampleNonMtmPortfolioCreateForm(companyId: "000000000000000000000009") | "ValidCompanyId.nonMtmPortfolioCreateForm.companyId"
    sampleNonMtmPortfolioCreateForm(companyId: "000000000000000000000009") | "ValidCompanyId.nonMtmPortfolioCreateForm.companyId"
  }

  def "should update successfully portfolio with form #nonMtmPortfolioForm"() {
    setup:
    def validCompanyId = nonMtmPortfolioForm.companyId as String
    def legalEntityId = nonMtmPortfolioForm.entityId as String
    def portfolioId = "000000000000000000000008"

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolioId}")
      .with(csrf())
      .content(toJson(nonMtmPortfolioForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * companyTeamValidationService.validCompanyEntityTeams(legalEntityId,
      validCompanyId,
      _ as AllowedTeamsForm) >> true
    2 * companyRepository.companyEntity(validCompanyId) >> right(new Company())
    1 * service.updateItem(_ as Authentication, portfolioId, _ as NonMtmPortfolioUpdateForm) >> right(entityId(portfolioId))

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(response))


    where:
    nonMtmPortfolioForm                                | response
    sampleNonMtmPortfolioUpdateForm()                  | """{"id":"000000000000000000000008"}"""
    sampleNonMtmPortfolioUpdateForm(description: null) | """{"id":"000000000000000000000008"}"""
  }

  def "when updating a portfolio with invalid form #nonMtmPortfolioForm is given then validation error is returned"() {
    setup:
    def companyId = nonMtmPortfolioForm.companyId as String
    def legalEntityId = nonMtmPortfolioForm.entityId as String
    def portfolioId = "000000000000000000000008"
    companyTeamValidationService.validCompanyEntityTeams(legalEntityId, companyId, _ as AllowedTeamsForm) >> true
    companyRepository.companyEntity(companyId) >> { "000000000000000000000009" == companyId ? left(OBJECT_NOT_FOUND.entity()) : right(new Company()) }

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolioId}")
      .with(csrf())
      .content(toJson(nonMtmPortfolioForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.updateItem(_ as Authentication, portfolioId, _ as NonMtmPortfolioUpdateForm)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    nonMtmPortfolioForm                                                    | response
    sampleNonMtmPortfolioUpdateForm(name: null)                            | "NotEmpty.nonMtmPortfolioUpdateForm.name"
    sampleNonMtmPortfolioUpdateForm(allowedTeamsForm: null)                | "NotNull.nonMtmPortfolioUpdateForm.allowedTeamsForm"
    sampleNonMtmPortfolioUpdateForm(companyId: null)                       | "NotEmpty.nonMtmPortfolioUpdateForm.companyId"
    sampleNonMtmPortfolioUpdateForm(companyId: "")                         | "NotEmpty.nonMtmPortfolioUpdateForm.companyId"
    sampleNonMtmPortfolioUpdateForm(companyId: "INCORRECT_COMPANY_ID")     | "ValidObjectId.nonMtmPortfolioUpdateForm.companyId"
    sampleNonMtmPortfolioUpdateForm(companyId: "000000000000000000000009") | "ValidCompanyId.nonMtmPortfolioUpdateForm.companyId"
  }

  def "should get existing non-mtm portfolio view"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def date = portfolio.valuationDate
    def view = NonMtmPortfolioViewBuilder.of(portfolio, 3)

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}")
      .param("stateDate", date.toString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.get(_ as Authentication, portfolio.id, { it.getActualDate() == date }) >> right(view)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(view)))
  }

  def "should archive non-mtm portfolio view"() {
    setup:
    def portfolio = new EntityId("000000000000000000000009")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/archive")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.archivePortfolio(_ as Authentication, portfolio.getId()) >> right(portfolio)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(portfolio)))
  }

  def "Should get non-mtm portfolio items"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tableFilter = TableFilter.emptyTableFilter()
    def scrollRequest = ScrollRequest.of(0, 15, Sort.by(NonMtmPortfolioItemWithKeyView.Fields.trsType,
      NonMtmPortfolioItemWithKeyView.Fields.externalTradeId))
    def groupRequest = GroupRequest.emptyGroupRequest()

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades")
      .param("stateDate", portfolio.valuationDate.toString())
      .param("withArchived", Boolean.TRUE.toString())
      .param("startRow", "0")
      .param("endRow", "15")
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.getItems(_ as Authentication,
      portfolio.id,
      { it.getActualDate() == portfolio.valuationDate },
      Boolean.TRUE,
      tableFilter,
      scrollRequest,
      groupRequest) >> right(ScrollableEntry.empty())

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  def "should get single portfolio item"() {
    setup:
    1 * service.getItem(
      _ as Authentication,
      "portfolioId",
      "tradeId", {
        it.getActualDate() == LocalDate.parse("2020-01-01")
      }
      ) >> right(new NonMtmPortfolioItemWithKeyView(externalTradeId: "EXTID"))

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/portfolioId/trades/tradeId")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString().contains("EXTID")
  }

  def "Should get TRS Bond non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def trade = NonMtmPortfolioItemBuilder.trsTrade(trsType: TrsType.BOND)

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond/${trade.entityId}")
      .param("stateDate", portfolio.valuationDate.toString())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.getTrsBondTradeView(_ as Authentication, portfolio.id, trade.entityId, { it.getActualDate() == portfolio.valuationDate }) >> right(new TrsBondTradeView())

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  @Unroll
  def "when invalid create non-mtm portfolio item (BOND) form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    itemRepository.hasPortfolioItemByExternalTradeId({ it.getActualDate() == NewVersionFormV2.ROOT_DATE }, portfolio.id, "EXISTING", null) >> true
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                   | response
    createTrsBondTradeFormSample(externalTradeId: "")                      | "NotEmpty.createTrsBondTradeForm.externalTradeId"
    createTrsBondTradeFormSample(externalTradeId: "INCORRECT EXTERNAL_ID") | "ValidIdentifier.createTrsBondTradeForm.externalTradeId"
    createTrsBondTradeFormSample(externalTradeId: "EXISTING")              | "Trade external id must be unique"
    createTrsBondTradeFormSample(tradeCounterpartyType: "INCORRECT")       | "Pattern.createTrsBondTradeForm.tradeCounterpartyType"
    createTrsBondTradeFormSample(managementFeesDayCount: "Act360")         | "ValidStringSet.createTrsBondTradeForm.managementFeesDayCount"
    createTrsBondTradeFormSample(leg1: null)                               | "NotNull.createTrsBondTradeForm.leg1"
    createTrsBondTradeFormSample(leg2: null)                               | "NotNull.createTrsBondTradeForm.leg2"
    createTrsBondTradeFormSample(currency: "LT")                           | "ValidStringSet.createTrsBondTradeForm.currency"
    createTrsBondTradeFormSample(currency: "")                             | "NotEmpty.createTrsBondTradeForm.currency"
    createTrsBondTradeFormSample(versionForm: null)                        | "NotNull.createTrsBondTradeForm.versionForm"
  }

  @Unroll
  def "when invalid create non-mtm portfolio item (BOND) leg form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                                                                                          | response
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegIborFormSample())                                                      | "PerformanceLegRequired.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegFixedFormSample())                                                     | "PerformanceLegRequired.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegOvernightFormSample())                                                 | "PerformanceLegRequired.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(extLegIdentifier: "ABC"), leg2: trsLegPerformanceFormSample(extLegIdentifier: "ABC")) | "RequiredDifferentTrsLegIdentifiers.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(extLegIdentifier: "invalid leg identifier"))                                          | "ValidIdentifier.createTrsBondTradeForm.leg1.extLegIdentifier"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(payReceive: ""))                                                                      | "NotEmpty.createTrsBondTradeForm.leg1.payReceive"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: "AED-EIBOR-0M"))                                                | "ValidStringSet.createTrsBondTradeForm.leg1.calculationIborIndex"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(accrualFrequency: "P2W"))                                                             | "ValidStringSet.createTrsBondTradeForm.leg1.accrualFrequency"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(paymentFrequency: "1T/0M"))                                                           | "ValidStringSet.createTrsBondTradeForm.leg1.paymentFrequency"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(compoundingMethod: "Fake"))                                                           | "Compounding must be None"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(startDate: null))                                                                     | "NotNull.createTrsBondTradeForm.leg1.startDate"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(endDate: null))                                                                       | "NotNull.createTrsBondTradeForm.leg1.endDate"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(startDate: parse("2020-10-11"), endDate: parse("2020-10-10")))                        | "ValidStartDateBeforeEndDate.createTrsBondTradeForm.leg1.endDate"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborDayCount: "Fake"))                                                     | "ValidStringSet.createTrsBondTradeForm.leg1.calculationIborDayCount"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))                                          | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.createTrsBondTradeForm.leg1.paymentOffsetDays"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(iborFixingCalendars: ["LT"]))                                                         | "ValidCollectionStringSet.createTrsBondTradeForm.leg1.iborFixingCalendars"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborDayCount: null))                                                       | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationIborDayCount"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: null))                                                          | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationIborIndex"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborFixingDateOffsetDays: null))                                           | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationIborFixingDateOffsetDays"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(iborFixingCalendars: null))                                                           | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.iborFixingCalendars"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborSpreadInitialValue: null))                                             | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationIborSpreadInitialValue"
    createTrsBondTradeFormSample(leg1: trsLegFixedFormSample(), leg2: trsLegFixedFormSample())                                                    | "PerformanceLegRequired.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedDayCount: "Fake"))                                                   | "ValidStringSet.createTrsBondTradeForm.leg1.calculationFixedDayCount"
    createTrsBondTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedDayCount: null))                                                     | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationFixedDayCount"
    createTrsBondTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedRateInitialValue: null))                                             | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationFixedRateInitialValue"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalValue: null))                                                            | "NotNull.createTrsBondTradeForm.leg1.notionalValue"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalValue: 0))                                                               | "Positive.leg1.notionalValue"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalValue: -1))                                                              | "Positive.leg1.notionalValue"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalCurrency: null))                                                         | "NotEmpty.createTrsBondTradeForm.leg1.notionalCurrency"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(), leg2: trsLegOvernightFormSample())                                            | "PerformanceLegRequired.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalCurrency: "LT"))                                                         | "ValidStringSet.createTrsBondTradeForm.leg1.notionalCurrency"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightDayCount: "Fake"))                                           | "ValidStringSet.createTrsBondTradeForm.leg1.calculationOvernightDayCount"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightDayCount: null))                                             | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationOvernightDayCount"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightIndex: null))                                                | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationOvernightIndex"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightSpreadInitialValue: null))                                   | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationOvernightSpreadInitialValue"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightRateCutOffDays: null))                                       | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationOvernightRateCutOffDays"
    createTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightAccrualMethod: null))                                        | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg1.calculationOvernightAccrualMethod"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))                                   | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.createTrsBondTradeForm.leg2.paymentOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingCalendars: ["LT"]))                                              | "ValidCollectionStringSet.createTrsBondTradeForm.leg2.trsIndexFixingCalendars"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingCalendars: ["LT"]))                                              | "ValidCollectionStringSet.createTrsBondTradeForm.leg2.trsIndexFixingCalendars"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingCalendars: ["LT"]))                                              | "ValidCollectionStringSet.createTrsBondTradeForm.leg2.trsIndexFixingCalendars"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(payReceive: ""))                                                               | "NotEmpty.createTrsBondTradeForm.leg2.payReceive"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(calculationType: null))                                                        | "NotNull.createTrsBondTradeForm.leg2.calculationType"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(accrualFrequency: ""))                                                         | "NotEmpty.createTrsBondTradeForm.leg2.accrualFrequency"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(paymentFrequency: ""))                                                         | "NotEmpty.createTrsBondTradeForm.leg2.paymentFrequency"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(compoundingMethod: "Flat", accrualFrequency: "1M", paymentFrequency: "1M"))    | "Compounding must be None"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndex: null))                                                               | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg2.trsIndex"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexType: null))                                                           | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg2.trsIndexType"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingOffsetDays: null))                                               | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg2.trsIndexFixingOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingCalendars: null))                                                | "RequiredTrsLegCalculation.createTrsBondTradeForm.leg2.trsIndexFixingCalendars"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: null))                                                      | "NotNull.createTrsBondTradeForm.leg2.accrualOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: -1))                                                        | "PositiveOrZero.createTrsBondTradeForm.leg2.accrualOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(paymentOffsetDays: null))                                                      | "NotNull.createTrsBondTradeForm.leg2.paymentOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(paymentOffsetDays: -1))                                                        | "PositiveOrZero.createTrsBondTradeForm.leg2.paymentOffsetDays"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(paymentFrequency: "6M"))                                                       | "Leg must have equal payment and accrual frequencies"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(notionalValue: 0))                                                             | "Positive.leg2.notionalValue"
    createTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(notionalValue: -1))                                                            | "Positive.leg2.notionalValue"

    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(businessDayConvention: ""))                                                           | "NotEmpty.createTrsBondTradeForm.leg1.businessDayConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(businessDayConvention: "Act360"))                                                     | "ValidStringSet.createTrsBondTradeForm.leg1.businessDayConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(rollConvention: "Fake"))                                                              | "ValidStringSet.createTrsBondTradeForm.leg1.rollConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(rollConvention: ""))                                                                  | "NotEmpty.createTrsBondTradeForm.leg1.rollConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(stubConvention: ""))                                                                  | "NotEmpty.createTrsBondTradeForm.leg1.stubConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Fake"))                                                              | "ValidStringSet.createTrsBondTradeForm.leg1.stubConvention"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: null, regularEndDate: parse("2020-10-10"))) | "RequiredRegularDates.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: parse("2020-10-10"), regularEndDate: null)) | "RequiredRegularDates.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: null, regularEndDate: null))                | "RequiredRegularDates.createTrsBondTradeForm"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(regularStartDate: parse("2020-10-11"), regularEndDate: parse("2020-10-10")))          | "ValidRegularStartDateBeforeRegularEndDate.createTrsBondTradeForm.leg1.regularEndDate"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calendars: null))                                                                     | "NotNull.createTrsBondTradeForm.leg1.calendars"
    createTrsBondTradeFormSample(leg1: trsLegIborFormSample(calendars: ["FAKE_CALENDAR"]))                                                        | "ValidCollectionStringSet.createTrsBondTradeForm.leg1.calendars"
  }

  @Unroll
  def "when invalid create non-mtm portfolio item (SHARE) leg form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                                                                | response
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegIborFormSample())                           | "PerformanceLegRequired.createTrsShareTradeForm"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegFixedFormSample())                          | "PerformanceLegRequired.createTrsShareTradeForm"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegOvernightFormSample())                      | "PerformanceLegRequired.createTrsShareTradeForm"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: "AED-EIBOR-0M"))                     | "ValidStringSet.createTrsShareTradeForm.leg1.calculationIborIndex"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(accrualFrequency: "P2W"))                                  | "ValidStringSet.createTrsShareTradeForm.leg1.accrualFrequency"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(paymentFrequency: "1T/0M"))                                | "ValidStringSet.createTrsShareTradeForm.leg1.paymentFrequency"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(compoundingMethod: "Fake"))                                | "Compounding must be None"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(calculationIborDayCount: "Fake"))                          | "ValidStringSet.createTrsShareTradeForm.leg1.calculationIborDayCount"
    createTrsShareTradeFormSample(leg1: trsLegIborFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))               | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.createTrsShareTradeForm.leg1.paymentOffsetDays"
    createTrsShareTradeFormSample(leg1: trsLegFixedFormSample(), leg2: trsLegFixedFormSample())                         | "PerformanceLegRequired.createTrsShareTradeForm"
    createTrsShareTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedDayCount: "Fake"))                        | "ValidStringSet.createTrsShareTradeForm.leg1.calculationFixedDayCount"
    createTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(), leg2: trsLegOvernightFormSample())                 | "PerformanceLegRequired.createTrsShareTradeForm"
    createTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(notionalCurrency: "LT"))                              | "ValidStringSet.createTrsShareTradeForm.leg1.notionalCurrency"
    createTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightDayCount: "Fake"))                | "ValidStringSet.createTrsShareTradeForm.leg1.calculationOvernightDayCount"
    createTrsShareTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))        | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.createTrsShareTradeForm.leg2.paymentOffsetDays"
    createTrsShareTradeFormSample(leg2: trsLegPerformanceFormSample(dividendPayout: null, trsIndexType: TrsType.SHARE)) | "RequiredTrsLegCalculation.createTrsShareTradeForm.leg2.dividendPayout"
  }

  def "Should create successfully new TRS Bond non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def form = createTrsBondTradeFormSample()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    1 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(tradeEntityId)))
  }

  def "Should create successfully new TRS Share non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def form = createTrsShareTradeFormSample()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    1 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(tradeEntityId)))
  }


  def "when invalid create non-mtm portfolio item (SHARE) form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.insertItem(_ as Authentication, portfolio.id, form) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                                | response
    createTrsShareTradeFormSample(externalTradeId: "")                                  | "NotEmpty.createTrsShareTradeForm.externalTradeId"
    createTrsShareTradeFormSample(externalTradeId: "INCORRECT EXTERNAL_ID")             | "ValidIdentifier.createTrsShareTradeForm.externalTradeId"
    createTrsShareTradeFormSample(tradeCounterpartyType: "INCORRECT")                   | "Pattern.createTrsShareTradeForm.tradeCounterpartyType"
    createTrsShareTradeFormSample(managementFeesDayCount: "Act360")                     | "ValidStringSet.createTrsShareTradeForm.managementFeesDayCount"
    createTrsShareTradeFormSample(leg1: null)                                           | "NotNull.createTrsShareTradeForm.leg1"
    createTrsShareTradeFormSample(leg2: null)                                           | "NotNull.createTrsShareTradeForm.leg2"
    createTrsShareTradeFormSample(currency: "LT")                                       | "ValidStringSet.createTrsShareTradeForm.currency"
    createTrsShareTradeFormSample(versionForm: null)                                    | "NotNull.createTrsShareTradeForm.versionForm"
    createTrsShareTradeFormSample(customFields: [new CustomTradeFieldForm("F2", "V1")]) | "Custom Field Name does not exist!"
  }

  def "when invalid update non-mtm portfolio item (BOND) form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                   | response
    updateTrsBondTradeFormSample(externalTradeId: "")                      | "NotEmpty.updateTrsBondTradeForm.externalTradeId"
    updateTrsBondTradeFormSample(externalTradeId: "INCORRECT EXTERNAL_ID") | "ValidIdentifier.updateTrsBondTradeForm.externalTradeId"
    updateTrsBondTradeFormSample(tradeCounterpartyType: "INCORRECT")       | "Pattern.updateTrsBondTradeForm.tradeCounterpartyType"
    updateTrsBondTradeFormSample(managementFeesDayCount: "Act360")         | "ValidStringSet.updateTrsBondTradeForm.managementFeesDayCount"
    updateTrsBondTradeFormSample(leg1: null)                               | "NotNull.updateTrsBondTradeForm.leg1"
    updateTrsBondTradeFormSample(leg2: null)                               | "NotNull.updateTrsBondTradeForm.leg2"
    updateTrsBondTradeFormSample(currency: "LT")                           | "ValidStringSet.updateTrsBondTradeForm.currency"
    updateTrsBondTradeFormSample(versionForm: null)                        | "NotNull.updateTrsBondTradeForm.versionForm"
  }

  def "when invalid update non-mtm portfolio item (BOND) leg form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                                                        | response
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegIborFormSample())                    | "PerformanceLegRequired.updateTrsBondTradeForm"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegFixedFormSample())                   | "PerformanceLegRequired.updateTrsBondTradeForm"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegOvernightFormSample())               | "PerformanceLegRequired.updateTrsBondTradeForm"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: "AED-EIBOR-0M"))              | "ValidStringSet.updateTrsBondTradeForm.leg1.calculationIborIndex"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(accrualFrequency: "P2W"))                           | "ValidStringSet.updateTrsBondTradeForm.leg1.accrualFrequency"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(paymentFrequency: "1T/0M"))                         | "ValidStringSet.updateTrsBondTradeForm.leg1.paymentFrequency"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(compoundingMethod: "Fake"))                         | "Compounding must be None"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborDayCount: "Fake"))                   | "ValidStringSet.updateTrsBondTradeForm.leg1.calculationIborDayCount"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))        | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.updateTrsBondTradeForm.leg1.paymentOffsetDays"
    updateTrsBondTradeFormSample(leg1: trsLegFixedFormSample(), leg2: trsLegFixedFormSample())                  | "PerformanceLegRequired.updateTrsBondTradeForm"
    updateTrsBondTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedDayCount: "Fake"))                 | "ValidStringSet.updateTrsBondTradeForm.leg1.calculationFixedDayCount"
    updateTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(), leg2: trsLegOvernightFormSample())          | "PerformanceLegRequired.updateTrsBondTradeForm"
    updateTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(notionalCurrency: "LT"))                       | "ValidStringSet.updateTrsBondTradeForm.leg1.notionalCurrency"
    updateTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightDayCount: "Fake"))         | "ValidStringSet.updateTrsBondTradeForm.leg1.calculationOvernightDayCount"
    updateTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1)) | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.updateTrsBondTradeForm.leg2.paymentOffsetDays"
    updateTrsBondTradeFormSample(leg2: trsLegPerformanceFormSample(trsIndexFixingOffsetDays: 2))                | "NegativeOrZero.updateTrsBondTradeForm.leg2.trsIndexFixingOffsetDays"
    updateTrsBondTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightFixingOffsetDays: 2))      | "NegativeOrZero.updateTrsBondTradeForm.leg1.calculationOvernightFixingOffsetDays"
    updateTrsBondTradeFormSample(leg1: trsLegIborFormSample(calculationIborFixingDateOffsetDays: 2))            | "NegativeOrZero.updateTrsBondTradeForm.leg1.calculationIborFixingDateOffsetDays"
  }

  def "when invalid update non-mtm portfolio item (SHARE) form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                    | response
    updateTrsShareTradeFormSample(externalTradeId: "")                      | "NotEmpty.updateTrsShareTradeForm.externalTradeId"
    updateTrsShareTradeFormSample(externalTradeId: "INCORRECT EXTERNAL_ID") | "ValidIdentifier.updateTrsShareTradeForm.externalTradeId"
    updateTrsShareTradeFormSample(tradeCounterpartyType: "INCORRECT")       | "Pattern.updateTrsShareTradeForm.tradeCounterpartyType"
    updateTrsShareTradeFormSample(managementFeesDayCount: "Act360")         | "ValidStringSet.updateTrsShareTradeForm.managementFeesDayCount"
    updateTrsShareTradeFormSample(leg1: null)                               | "NotNull.updateTrsShareTradeForm.leg1"
    updateTrsShareTradeFormSample(leg2: null)                               | "NotNull.updateTrsShareTradeForm.leg2"
    updateTrsShareTradeFormSample(currency: "LT")                           | "ValidStringSet.updateTrsShareTradeForm.currency"
    updateTrsShareTradeFormSample(versionForm: null)                        | "NotNull.updateTrsShareTradeForm.versionForm"
  }

  def "when invalid update non-mtm portfolio item (SHARE) leg form is given then a validation error is returned #response"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    0 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    form                                                                                                                                           | response
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegIborFormSample())                                                      | "PerformanceLegRequired.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegFixedFormSample())                                                     | "PerformanceLegRequired.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(), leg2: trsLegOvernightFormSample())                                                 | "PerformanceLegRequired.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: "AED-EIBOR-0M"))                                                | "ValidStringSet.updateTrsShareTradeForm.leg1.calculationIborIndex"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(calculationIborIndex: "USD-LIBOR-6M"))                                                | "Accrual Frequency must come from IBOR index attributes."
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(accrualFrequency: "P2W"))                                                             | "ValidStringSet.updateTrsShareTradeForm.leg1.accrualFrequency"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(paymentFrequency: "1T/0M"))                                                           | "ValidStringSet.updateTrsShareTradeForm.leg1.paymentFrequency"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(compoundingMethod: "Fake"))                                                           | "Compounding must be None"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(calculationIborDayCount: "Fake"))                                                     | "ValidStringSet.updateTrsShareTradeForm.leg1.calculationIborDayCount"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))                                          | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.updateTrsShareTradeForm.leg1.paymentOffsetDays"
    updateTrsShareTradeFormSample(leg1: trsLegFixedFormSample(), leg2: trsLegFixedFormSample())                                                    | "PerformanceLegRequired.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegFixedFormSample(calculationFixedDayCount: "Fake"))                                                   | "ValidStringSet.updateTrsShareTradeForm.leg1.calculationFixedDayCount"
    updateTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(), leg2: trsLegOvernightFormSample())                                            | "PerformanceLegRequired.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(notionalCurrency: "LT"))                                                         | "ValidStringSet.updateTrsShareTradeForm.leg1.notionalCurrency"
    updateTrsShareTradeFormSample(leg1: trsLegOvernightFormSample(calculationOvernightDayCount: "Fake"))                                           | "ValidStringSet.updateTrsShareTradeForm.leg1.calculationOvernightDayCount"
    updateTrsShareTradeFormSample(leg2: trsLegPerformanceFormSample(accrualOffsetDays: 2, paymentOffsetDays: 1))                                   | "PaymentOffsetDaysGreaterThanAccrualOffsetDays.updateTrsShareTradeForm.leg2.paymentOffsetDays"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(businessDayConvention: ""))                                                           | "NotEmpty.updateTrsShareTradeForm.leg1.businessDayConvention"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(businessDayConvention: "Act360"))                                                     | "ValidStringSet.updateTrsShareTradeForm.leg1.businessDayConvention"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(rollConvention: "Fake"))                                                              | "ValidStringSet.updateTrsShareTradeForm.leg1.rollConvention"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(stubConvention: ""))                                                                  | "NotEmpty.updateTrsShareTradeForm.leg1.stubConvention"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Fake"))                                                              | "ValidStringSet.updateTrsShareTradeForm.leg1.stubConvention"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: null, regularEndDate: parse("2020-10-10"))) | "RequiredRegularDates.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: parse("2020-10-10"), regularEndDate: null)) | "RequiredRegularDates.updateTrsShareTradeForm"
    updateTrsShareTradeFormSample(leg1: trsLegIborFormSample(stubConvention: "Both", regularStartDate: null, regularEndDate: null))                | "RequiredRegularDates.updateTrsShareTradeForm"
  }

  def "should update successfully existing TRS Bond non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def form = updateTrsBondTradeFormSample()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-bond/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    1 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(tradeEntityId)))
  }

  def "should update successfully existing TRS Share non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000009")
      .valuationDate(now())
      .build()
    def form = updateTrsShareTradeFormSample()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share/${tradeEntityId.id}/${portfolio.valuationDate}")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    2 * trsIndexRepository.findByName(_ as String) >> right(new TrsIndex())
    1 * service.updateItem(_ as Authentication, portfolio.id, portfolio.valuationDate, form, tradeEntityId.id) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(tradeEntityId)))
  }

  def "should get TRS Share non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()
    def trade = NonMtmPortfolioItemBuilder.trsTrade(trsType: TrsType.SHARE)

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/trs-share/${trade.entityId}")
      .param("stateDate", portfolio.valuationDate.toString())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.getTrsShareTradeView(_ as Authentication, portfolio.id, trade.entityId, { it.getActualDate() == portfolio.valuationDate }) >> right(new TrsBondTradeView())

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  def "should archive non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")
    def versionForm = new ArchiveEntityForm(updateTrsBondTradeFormSample().getVersionForm())

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/${tradeEntityId.id}/${portfolio.valuationDate}/archive")
      .with(csrf())
      .content(mapper.writeValueAsString(versionForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.archivePortfolioItem(_ as Authentication, portfolio.id, tradeEntityId.id, portfolio.valuationDate, versionForm) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  def "should delete non-mtm portfolio item"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000002")

    when:
    def results = mockMvc.perform(put("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/${tradeEntityId.id}/${portfolio.valuationDate}/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.deletePortfolioItem(_ as Authentication, portfolio.id, tradeEntityId.id, portfolio.valuationDate) >> right(tradeEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  def "should fail to get future non-mtm portfolio items dates when request params are missing"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()


    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/future-versions/search")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.futureVersions(portfolio.id, _ as SearchNonMtmPortfolioItemForm)

    and:
    results.andExpect(status().is4xxClientError())
  }

  def "should get future non-mtm portfolio items dates"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()
    def externalTradeId = "RANDOM EXTERNAL ID"
    def stateDate = now()
    def searchForm = new SearchNonMtmPortfolioItemForm(externalTradeId, stateDate)


    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/future-versions/search")
      .with(csrf())
      .param('stateDate', portfolio.valuationDate.toString())
      .param('externalTradeId', externalTradeId)
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.futureVersions(portfolio.id, searchForm) >> new DateList([])

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  def "should get non-mtm portfolio item versions"() {
    setup:
    def portfolio = new NonMtmPortfolioBuilder()
      .id("000000000000000000000008")
      .valuationDate(now())
      .build()
    def tradeEntityId = entityId("000000000000000000000001")


    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolio.id}/trades/${tradeEntityId.id}/versions")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.getVersions(_ as Authentication, portfolio.id, tradeEntityId.id) >> right([])

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  @Unroll
  def "should invoke non-mtm portfolio csv export and get #code response code"() {
    setup:
    def sort = Sort.by(NonMtmPortfolioView.Fields.companyName,
      NonMtmPortfolioView.Fields.entityName,
      NonMtmPortfolioCondensedView.Fields.externalPortfolioId)

    def tableFilter = TableFilter.emptyTableFilter()
    def portfolioFilter = new NonMtmPortfolioFilter(false)
    def stateDate = now()

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/portfolios-csv")
      .queryParam("archived", "false")
      .queryParam("stateDate", stateDate.toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))

    then:
    1 * exportService.exportPortfolios(_ as Authentication, sort, tableFilter, portfolioFilter, stateDate) >> exportResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    exportResult                    | code | responseBody
    right(EXPORTED_CSV)             | 200  | ""
    left(OBJECT_NOT_FOUND.entity()) | 422  | OBJECT_NOT_FOUND.name()
  }

  @Unroll
  def "should invoke all non-mtm portfolio items csv export and get #code response code"() {
    setup:
    def stateDate = now()

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/trades-csv")
      .queryParam("stateDate", stateDate.toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))

    then:
    1 * exportService.exportAllPortfolioItems(_ as Authentication, { it.getActualDate() == stateDate }) >> exportResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    exportResult                    | code | responseBody
    right(EXPORTED_CSV)             | 200  | ""
    left(OBJECT_NOT_FOUND.entity()) | 422  | OBJECT_NOT_FOUND.name()
  }

  @Unroll
  def "should invoke non-mtm portfolio items csv export and get #code response code"() {
    setup:
    def stateDate = now()
    def portfolioId = "000000000000000000000001"
    def sort = Sort.by(NonMtmPortfolioItemWithKeyView.Fields.trsType,
      NonMtmPortfolioItemWithKeyView.Fields.externalTradeId)
    def tableFilter = TableFilter.emptyTableFilter()

    when:
    def results = mockMvc.perform(get("${NON_MTM_PORTFOLIO_API}/${portfolioId}/trades-csv")
      .queryParam("stateDate", stateDate.toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))

    then:
    1 * exportService.exportPortfolioItems(_ as Authentication, portfolioId, { it.getActualDate() == stateDate }, sort, tableFilter) >> exportResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    exportResult                    | code | responseBody
    right(EXPORTED_CSV)             | 200  | ""
    left(OBJECT_NOT_FOUND.entity()) | 422  | OBJECT_NOT_FOUND.name()
  }

  @Unroll
  def "should invoke non-mtm portfolio csv upload and get #code response code"() {
    setup:
    def action = DuplicateAction.REPLACE
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${NON_MTM_PORTFOLIO_API}/upload")
      .file("file", csv)
      .param("duplicates", action.name())
      .with(csrf()))

    then:
    1 * uploadService.uploadPortfolios(_ as Authentication, STRICT, action, csv) >> uploadResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    uploadResult                                  | code
    right([entityId("000000000000000000000001")]) | 200
    left([OBJECT_NOT_FOUND.entity()])             | 422
  }

  @Unroll
  def "should invoke TRS trades upload and get #code response code"() {
    setup:
    def action = DuplicateAction.REPLACE
    def stateDate = now()
    def importOptions = new ImportOptions(stateDate, action, null, null, null, null, null, null)
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${NON_MTM_PORTFOLIO_API}/trades/upload")
      .file("file", csv)
      .param("stateDate", stateDate.toString())
      .param("duplicateAction", action.name())
      .with(csrf()))

    then:
    1 * uploadItemService.uploadTrades(_ as Authentication, csv, importOptions) >> uploadResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    uploadResult                                  | code
    right([entityId("000000000000000000000001")]) | 200
    left([OBJECT_NOT_FOUND.entity()])             | 422
  }

  @Unroll
  def "should invoke TRS Trades csv validation and get #code response code"() {
    setup:
    def action = DuplicateAction.REPLACE
    def stateDate = now()
    def importOptions = new ImportOptions(stateDate, action, null, null, null, null, null, null)
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${NON_MTM_PORTFOLIO_API}/trades/upload/validate")
      .file("file", csv)
      .param("stateDate", stateDate.toString())
      .param("duplicateAction", action.name())
      .with(csrf()))

    then:
    1 * uploadItemService.validateTradesImport(_ as Authentication, csv, importOptions) >> validationResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    validationResult                  | code
    right(new ValidationResponse([])) | 200
    left([OBJECT_NOT_FOUND.entity()]) | 422
  }

  @Unroll
  def "should invoke non-mtm portfolio TRS trades upload and get #code response code"() {
    setup:
    def portfolioId = new ObjectId().toHexString()
    def action = DuplicateAction.REPLACE
    def stateDate = now()
    def importOptions = new ImportOptions(stateDate, action, null, null, null, null, null, null)
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${NON_MTM_PORTFOLIO_API}/${portfolioId}/trades/upload")
      .file("file", csv)
      .param("stateDate", stateDate.toString())
      .param("duplicateAction", action.name())
      .with(csrf()))

    then:
    1 * uploadItemService.uploadTradesForPortfolio(_ as Authentication, portfolioId, csv, importOptions) >> uploadResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    uploadResult                                  | code
    right([entityId("000000000000000000000001")]) | 200
    left([OBJECT_NOT_FOUND.entity()])             | 422
  }

  @Unroll
  def "should invoke non-mtm portfolio TRS Trades csv validation and get #code response code"() {
    setup:
    def portfolioId = new ObjectId().toHexString()
    def action = DuplicateAction.REPLACE
    def stateDate = now()
    def importOptions = new ImportOptions(stateDate, action, null, null, null, null, null, null)
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${NON_MTM_PORTFOLIO_API}/${portfolioId}/trades/upload/validate")
      .file("file", csv)
      .param("stateDate", stateDate.toString())
      .param("duplicateAction", action.name())
      .with(csrf()))

    then:
    1 * uploadItemService.validateUploadForPortfolio(_ as Authentication, portfolioId, csv, importOptions) >> validationResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    validationResult                  | code
    right(new ValidationResponse([])) | 200
    left([OBJECT_NOT_FOUND.entity()]) | 422
  }

  def "should start valuation with response #response"() {
    setup:
    def portfolioId = "000000000000000000000008"
    nonMtmPortfolioRepository.portfolio(portfolioId) >> Optional.of(new NonMtmPortfolioView(companyId: "companyId"))
    nonMtmPortfolioRepository.portfolio(_ as String) >> Optional.empty()

    mdDataGroupRepository.validEntity("000000000000000000000002") >> right(new MarketDataGroup(allowAllCompanies: true))
    mdDataGroupRepository.validEntity("000000000000000000000007") >> right(new MarketDataGroup(allowAllCompanies: false, companies: []))
    trsDataGroupRepository.validEntity("000000000000000000000003") >> right(new TrsMarketDataGroup(allowAllCompanies: true))
    trsDataGroupRepository.validEntity("000000000000000000000007") >> right(new TrsMarketDataGroup(allowAllCompanies: false, companies: []))

    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> { locks, supplier ->
      right(SAMPLE_STATE_LOCK_DATE)
    }
    valuationsService.performValuation(portfolioId, _ as NonMtmValuationForm, SAMPLE_STATE_LOCK_DATE) >> right(entityId("id"))

    when:
    def results = mockMvc.perform(post("${NON_MTM_PORTFOLIO_API}/${portfolioId}/valuate")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                                                        | code | response
    SAMPLE_VALUATION_FORM                                                       | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(marketDataPriceType: null)                        | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(trsMarketDataPriceType: null)                     | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(marketDataSource: null)                           | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(marketDataGroupId: null)                          | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(curveConfigurationId: null)                       | 200  | "\"id\":\"id\""
    sampleNonMtmValuationForm(calculationCcy: null)                             | 200  | "\"id\":\"id\""

    sampleNonMtmValuationForm(curveDate: null)                                  | 412  | "NotNull.nonMtmValuationForm.curveDate"
    sampleNonMtmValuationForm(valuationDate: null)                              | 412  | "NotNull.nonMtmValuationForm.valuationDate"
    sampleNonMtmValuationForm(stateDate: null)                                  | 412  | "NotNull.nonMtmValuationForm.stateDate"
    sampleNonMtmValuationForm(calculationCcy: "OOO")                            | 412  | "ValidStringSet.nonMtmValuationForm.calculationCcy"
    sampleNonMtmValuationForm(marketDataGroupId: "000000000000000000000007")    | 412  | "Valuation market data group is not valid"
    sampleNonMtmValuationForm(marketDataGroupId: "abcd")                        | 412  | "ValidObjectId.nonMtmValuationForm.marketDataGroupId"
    sampleNonMtmValuationForm(curveConfigurationId: "abcd")                     | 412  | "ValidObjectId.nonMtmValuationForm.curveConfigurationId"
    sampleNonMtmValuationForm(marketDataSource: "abcd")                         | 412  | "ValidStringSet.nonMtmValuationForm.marketDataSource"
    sampleNonMtmValuationForm(trsMarketDataSource: null)                        | 412  | "NotEmpty.nonMtmValuationForm.trsMarketDataSource"
    sampleNonMtmValuationForm(trsMarketDataGroupId: null)                       | 412  | "NotEmpty.nonMtmValuationForm.trsMarketDataGroupId"
    sampleNonMtmValuationForm(trsMarketDataGroupId: "000000000000000000000007") | 412  | "Valuation market data group is not valid"
    sampleNonMtmValuationForm(tradeId: "abcd")                                  | 412  | "ValidObjectId.nonMtmValuationForm.tradeId"
  }
}
