package com.solum.xplain.trs.portfolio

import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE_TIME
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioBuilder.nonMtmPortfolio
import static java.time.LocalDate.now
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.customfield.CustomFieldName
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataSample
import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm
import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.trs.portfolio.event.NonMtmPortfolioItemsStatesUpdated
import com.solum.xplain.trs.portfolio.event.NonMtmPortfolioUpdated
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemWriteRepository
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioItemBuilder
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue
import com.solum.xplain.trs.trade.sampledata.TrsTradeSample
import com.solum.xplain.trs.value.TrsType
import java.time.LocalDateTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.mock.DetachedMockFactory

@SpringBootTest
@ActiveProfiles("test")
class NonMtmPortfolioItemWriteRepositoryTest extends IntegrationSpecification implements TrsTradeSample {

  @Autowired
  NonMtmPortfolioItemWriteRepository testObject

  @Autowired
  MongoOperations operations

  @Autowired
  ApplicationEventPublisher publisher

  XplainPrincipal creator

  def setup() {
    creator = user("userId")
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), NonMtmPortfolioItemEntity)
    operations.remove(new Query(), NonMtmPortfolioItem)
    operations.remove(new Query(), NonMtmPortfolio)
  }

  def "should create non-mtm portfolio item"() {
    setup:
    def nonMtmPortfolio = nonMtmPortfolio()
    operations.insert(nonMtmPortfolio)
    def externalTradeId = "CUSTOM_ID"
    def form = createTrsBondTradeFormSample(
    externalTradeId: externalTradeId,
    externalIdentifiers: [new ExternalIdentifierForm("CUSTOM_ID", "CUSTOM_SOURCE")]
    )

    when:
    def result = testObject.insert(nonMtmPortfolio.id, form)

    then:
    result.isRight()
    def allItems = operations.query(NonMtmPortfolioItem).all()
    allItems.size() == 1
    def item = allItems[0]

    and: "Non MTM Portfolio Item"
    verifyAll(item) {
      entityId != null
      trsType == TrsType.BOND
      modifiedBy == AuditUser.of(creator)
      modifiedAt != null
      validFrom == ofEpochDay(0)
      validTo == null
      recordFrom != null
      recordTo == null
      validities.size() == 1
      validities[0].getValidTo() == MAX_DATE
      validities[0].getRecordFrom() != null
      validities[0].getRecordTo() == MAX_DATE_TIME
      description == form.description
      externalTradeId == form.externalTradeId
      tradeDetails != null
      externalIdentifiers == [new ExternalIdentifier("CUSTOM_ID", "CUSTOM_SOURCE")]
    }

    and: "TRS trade details"
    verifyAll(item.tradeDetails) {
      info.tradeDate == form.tradeDate
      info.counterParty == form.tradeCounterparty
      info.counterPartyType == form.tradeCounterpartyType
      managementFees == form.managementFees
      managementFeesDayCount == form.managementFeesDayCount
      performanceLegLastPaymentWithheld == form.performanceLegLastPaymentWithheld
      otherLegLastPaymentWithheld == form.otherLegLastPaymentWithheld
      currency == form.currency
    }

    and: "PayLeg"
    def expectedPayLeg = form.getLeg1()
    verifyAll(item.tradeDetails.payLeg) {
      notional == expectedPayLeg.notionalValue
      index == expectedPayLeg.calculationIborIndex
      accrualFrequency == expectedPayLeg.accrualFrequency
      accrualOffsetDays == expectedPayLeg.accrualOffsetDays
      paymentFrequency == expectedPayLeg.paymentFrequency
      paymentOffsetDays == expectedPayLeg.paymentOffsetDays
      paymentCompounding == expectedPayLeg.compoundingMethod
      dayCount == expectedPayLeg.calculationIborDayCount
      initialValue == expectedPayLeg.calculationIborSpreadInitialValue
      fixingDateOffsetDays == expectedPayLeg.calculationIborFixingDateOffsetDays
      fixingCalendars == expectedPayLeg.iborFixingCalendars
      dividendPayout == expectedPayLeg.dividendPayout
      overnightAccrualMethod == expectedPayLeg.calculationOvernightAccrualMethod
      overnightRateCutOffDays == expectedPayLeg.calculationOvernightRateCutOffDays
      initialCoupon == expectedPayLeg.calculationIborInitialCoupon
      startDate == expectedPayLeg.startDate
      endDate == expectedPayLeg.endDate
      businessDayConvention == expectedPayLeg.businessDayConvention
      rollConvention == expectedPayLeg.rollConvention
      stubConvention == expectedPayLeg.stubConvention
      calendars == expectedPayLeg.calendars
      regularStartDate == expectedPayLeg.regularStartDate
      regularEndDate == expectedPayLeg.regularEndDate
    }

    and: "ReceiveLeg"
    def expectedReceivedLeg = form.getLeg2()
    verifyAll(item.tradeDetails.receiveLeg) {
      notional == expectedReceivedLeg.notionalValue
      index == expectedReceivedLeg.trsIndex
      trsIndexType == expectedReceivedLeg.trsIndexType
      accrualFrequency == expectedReceivedLeg.accrualFrequency
      accrualOffsetDays == expectedReceivedLeg.accrualOffsetDays
      paymentFrequency == expectedReceivedLeg.paymentFrequency
      paymentOffsetDays == expectedReceivedLeg.paymentOffsetDays
      paymentCompounding == expectedReceivedLeg.compoundingMethod
      fixingDateOffsetDays == expectedReceivedLeg.trsIndexFixingOffsetDays
      fixingCalendars == expectedReceivedLeg.trsIndexFixingCalendars
      dividendPayout == expectedReceivedLeg.dividendPayout
      initialValue == expectedReceivedLeg.calculationFixedRateInitialValue
      overnightAccrualMethod == expectedReceivedLeg.calculationOvernightAccrualMethod
      overnightRateCutOffDays == expectedReceivedLeg.calculationOvernightRateCutOffDays
      initialCoupon == expectedReceivedLeg.trsInitialIndex
      startDate == expectedReceivedLeg.startDate
      endDate == expectedReceivedLeg.endDate
      businessDayConvention == expectedReceivedLeg.businessDayConvention
      rollConvention == expectedReceivedLeg.rollConvention
      stubConvention == expectedReceivedLeg.stubConvention
      calendars == expectedReceivedLeg.calendars
      regularStartDate == expectedReceivedLeg.regularStartDate
      regularEndDate == expectedReceivedLeg.regularEndDate
    }

    and:
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioItemsStatesUpdated
      && ((NonMtmPortfolioItemsStatesUpdated) it).getNonMtmPortfolioId() == nonMtmPortfolio.id
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges().size() == 1
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges()[0].getExternalTradeId() == externalTradeId
    })
  }

  def "should update non-mtm portfolio item"() {
    setup:
    def nonMtmPortfolio = nonMtmPortfolio()
    operations.insert(nonMtmPortfolio)
    def form = createTrsBondTradeFormSample()
    def tradeEntityId = testObject.insert(nonMtmPortfolio.id, form).right().getOrNull().id
    def updateForm = updateTrsBondTradeFormSample(
    versionForm: form.getVersionForm(),
    description: "Updated description",
    externalIdentifiers: [new ExternalIdentifierForm("UPDATE_ID", "UPDATE_SOURCE")],
    customFields: [new CustomTradeFieldForm("FIELD2", "VAL2")]
    )

    when:
    def result = testObject.update(nonMtmPortfolio.id, tradeEntityId, form.stateDate(), updateForm)

    then:
    result.isRight()
    def items = operations.query(NonMtmPortfolioItem)
    .all().sort({
      it.getRecordFrom()
    })

    and:
    items.size() == 2

    and: "Non MTM Portfolio Item"
    verifyAll(items[1]) {
      description == updateForm.description
      entityId != null
      trsType == TrsType.BOND
      modifiedBy == AuditUser.of(creator)
      modifiedAt != null
      validFrom == ofEpochDay(0)
      validTo == null
      recordFrom != null
      recordTo == null
      validities.size() == 1
      validities[0].getValidTo() == MAX_DATE
      validities[0].getRecordFrom() != null
      validities[0].getRecordTo() == MAX_DATE_TIME
      externalTradeId == form.externalTradeId
      tradeDetails != null
      externalIdentifiers == [new ExternalIdentifier("UPDATE_ID", "UPDATE_SOURCE")]
      customFields == [new CustomTradeField("FIELD2", "VAL2")]
    }

    and:
    0 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioItemsStatesUpdated
    })
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioUpdated
      && ((NonMtmPortfolioUpdated) it).getEntityId() == nonMtmPortfolio.id
    })
  }

  def "should not update non-mtm portfolio item when no changes"() {
    setup:
    def nonMtmPortfolio = nonMtmPortfolio()
    operations.insert(nonMtmPortfolio)
    def form = createTrsBondTradeFormSample()
    def tradeEntityId = testObject.insert(nonMtmPortfolio.id, form).right().getOrNull().id
    def updateForm = updateTrsBondTradeFormSample(
    versionForm: form.getVersionForm()
    )

    when:
    def result = testObject.update(nonMtmPortfolio.id, tradeEntityId, form.stateDate(), updateForm)

    then:
    result.isRight()
    def items = operations.query(NonMtmPortfolioItem).all()

    and:
    items.size() == 1
  }

  def "should archive non-mtm portfolio item"() {
    setup:
    def nonMtmPortfolio = nonMtmPortfolio()
    operations.insert(nonMtmPortfolio)
    def form = createTrsShareTradeFormSample()
    def itemId = testObject.insert(nonMtmPortfolio.id, form).right().getOrNull().id
    def archiveEntityForm = new ArchiveEntityForm(new NewVersionFormV2("Comment", now(), now(), FutureVersionsAction.DELETE))

    when:
    def result = testObject.archiveItem(nonMtmPortfolio.id, itemId, form.stateDate(), archiveEntityForm)

    then:
    result.isRight()

    and:
    def items = operations.findAll(NonMtmPortfolioItem)
    items.size() == 2
    items.stream()
    .filter({
      v -> v.getId() != itemId
    })
    .filter({
      v -> v.state == State.ARCHIVED
    })
    .findFirst()
    .isPresent()

    and:
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioItemsStatesUpdated
      && ((NonMtmPortfolioItemsStatesUpdated) it).getNonMtmPortfolioId() == nonMtmPortfolio.id
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges().size() == 3
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges()[0].getExternalTradeId() == form.getExternalTradeId()
    })
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioUpdated
      && ((NonMtmPortfolioUpdated) it).getEntityId() == nonMtmPortfolio.id
    })
  }

  def "should delete non-mtm portfolio item version"() {
    setup:
    def nonMtmPortfolio = nonMtmPortfolio()
    operations.insert(nonMtmPortfolio)
    def item1_1 = NonMtmPortfolioItemBuilder.trsTrade(portfolioId: nonMtmPortfolio.id)
    def item2_1 = NonMtmPortfolioItemBuilder.trsTrade(portfolioId: nonMtmPortfolio.id, tradeId: "anotherTradeId")

    and:
    def data1 = NonMtmPortfolioItemBuilder.toData(item1_1)
    def data1Value = data1.getVersions()[0].getValue()
    data1.getVersions().add(new EmbeddedVersion<TrsTradeValue>(MarketDataSample.VAL_DT, LocalDateTime.now(), item1_1.getState(), item1_1.getComment(), null, data1Value))
    data1.getVersions().add(new EmbeddedVersion<TrsTradeValue>(MarketDataSample.VAL_DT, LocalDateTime.now(), item1_1.getState(), item1_1.getComment(), null, data1Value))
    def data2 = NonMtmPortfolioItemBuilder.toData(item2_1, MarketDataSample.VAL_DT)
    operations.insertAll([data1, data2])

    when:
    def result = testObject.deleteItem(nonMtmPortfolio.id, data1.id, MarketDataSample.VAL_DT)

    then:
    result.isRight()
    result.getOrNull().id == data1.id

    and:
    def items = operations.query(NonMtmPortfolioItem).all()
    items.size() == 4
    items.stream()
    .filter({
      p -> p.state == State.DELETED
    })
    .filter({
      p -> p.validFrom == MarketDataSample.VAL_DT
    })
    .count() == 1

    items.stream()
    .filter({
      p -> p.state == State.ACTIVE
    })
    .count() == 3

    and:
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioItemsStatesUpdated
      && ((NonMtmPortfolioItemsStatesUpdated) it).getNonMtmPortfolioId() == nonMtmPortfolio.id
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges().size() == 3
      && ((NonMtmPortfolioItemsStatesUpdated) it).getChanges()[0].getExternalTradeId() == data1.getExternalTrsTradeId()
    })
    1 * publisher.publishEvent({
      it ->
      it instanceof NonMtmPortfolioUpdated
      && ((NonMtmPortfolioUpdated) it).getEntityId() == nonMtmPortfolio.id
    })
  }

  @TestConfiguration
  static class NonMtmPortfolioItemWriteRepositoryTestConfig {

    private DetachedMockFactory factory = new DetachedMockFactory()

    @Bean
    @Primary
    ApplicationEventPublisher publisher() {
      factory.Mock(ApplicationEventPublisher)
    }
  }
}
