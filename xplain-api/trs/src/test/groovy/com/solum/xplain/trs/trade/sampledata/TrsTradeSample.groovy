package com.solum.xplain.trs.trade.sampledata

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm
import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import com.solum.xplain.trs.portfolio.trade.form.CreateTrsBondTradeForm
import com.solum.xplain.trs.portfolio.trade.form.CreateTrsShareTradeForm
import com.solum.xplain.trs.portfolio.trade.form.TrsLegForm
import com.solum.xplain.trs.portfolio.trade.form.TrsTradeForm
import com.solum.xplain.trs.portfolio.trade.form.UpdateTrsBondTradeForm
import com.solum.xplain.trs.portfolio.trade.form.UpdateTrsShareTradeForm
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetails
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeInfo
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeLegDetails
import com.solum.xplain.trs.value.TrsTradeCalculationType
import com.solum.xplain.trs.value.TrsType
import java.time.LocalDate

trait TrsTradeSample {

  static Map SAMPLE_TRS_LEG_IBOR_FORM = ['extLegIdentifier'                   : 'IBOR_LEG',
    'payReceive'                         : 'Pay',
    'calculationType'                    : TrsTradeCalculationType.IBOR,
    'notionalValue'                      : 10000000,
    'startDate'                          : LocalDate.of(2023, 1, 1),
    'endDate'                            : LocalDate.of(2024, 1, 1),
    'businessDayConvention'              : 'Following',
    'businessDayAdjustment'              : BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    'rollConvention'                     : 'None',
    'stubConvention'                     : 'Both',
    'calendars'                          : ['USNY'],
    'regularStartDate'                   : LocalDate.parse('2020-02-10'),
    'regularEndDate'                     : LocalDate.parse('2025-02-11'),
    'calculationIborIndex'               : "USD-LIBOR-3M",
    'accrualFrequency'                   : '3M',
    'accrualOffsetDays'                  : 1,
    'paymentFrequency'                   : '3M',
    'paymentOffsetDays'                  : 2,
    'compoundingMethod'                  : 'None',
    'calculationIborDayCount'            : 'Act/360',
    'calculationIborSpreadInitialValue'  : 0,
    'iborFixingCalendars'                : ['USNY'],
    'calculationIborFixingDateOffsetDays': -1,
    'calculationIborInitialCoupon'       : 1,
    'notionalCurrency'                   : 'USD',]

  static Map SAMPLE_TRS_LEG_PERFORMANCE_FORM = ['extLegIdentifier'        : 'PERFORMANCE_LEG',
    'payReceive'              : 'Receive',
    'calculationType'         : TrsTradeCalculationType.PERFORMANCE,
    'notionalValue'           : 10000000,
    'startDate'               : LocalDate.of(2023, 1, 1),
    'endDate'                 : LocalDate.of(2024, 1, 1),
    'businessDayConvention'   : 'Following',
    'businessDayAdjustment'   : BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    'rollConvention'          : 'None',
    'stubConvention'          : 'Both',
    'calendars'               : ['USNY'],
    'regularStartDate'        : LocalDate.parse('2020-02-10'),
    'regularEndDate'          : LocalDate.parse('2025-02-11'),
    'trsIndex'                : "EUR_INDEX",
    'trsIndexType'            : TrsType.BOND,
    'accrualFrequency'        : '3M',
    'accrualOffsetDays'       : 1,
    'paymentFrequency'        : '3M',
    'paymentOffsetDays'       : 2,
    'compoundingMethod'       : 'None',
    'trsIndexFixingOffsetDays': -2,
    'trsIndexFixingCalendars' : ['USNY'],
    'dividendPayout'          : 10,
    'trsInitialIndex'         : 2,
    'notionalCurrency'        : 'USD',]

  static Map SAMPLE_TRS_LEG_FIXED_FORM = ['extLegIdentifier'                : 'FIXED_LEG',
    'payReceive'                      : 'Pay',
    'calculationType'                 : TrsTradeCalculationType.FIXED,
    'notionalValue'                   : 10000000,
    'startDate'                       : LocalDate.of(2023, 1, 1),
    'endDate'                         : LocalDate.of(2024, 1, 1),
    'businessDayConvention'           : 'Following',
    'businessDayAdjustment'           : BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    'rollConvention'                  : 'None',
    'stubConvention'                  : 'Both',
    'calendars'                       : ['USNY'],
    'regularStartDate'                : LocalDate.parse('2020-02-10'),
    'regularEndDate'                  : LocalDate.parse('2025-02-11'),
    'calculationFixedRateInitialValue': 0,
    'accrualFrequency'                : '3M',
    'accrualOffsetDays'               : 0,
    'paymentFrequency'                : '3M',
    'paymentOffsetDays'               : 2,
    'compoundingMethod'               : 'None',
    'calculationFixedDayCount'        : 'Act/360',
    'notionalCurrency'                : 'USD',]

  static Map SAMPLE_TRS_LEG_OVERNIGHT_FORM = ['extLegIdentifier'                      : 'OVERNIGHT_LEG',
    'payReceive'                            : 'Pay',
    'calculationType'                       : TrsTradeCalculationType.OVERNIGHT,
    'calculationOvernightIndex'             : 'AUD-AONIA',
    'notionalCurrency'                      : 'USD',
    'notionalValue'                         : 10000000,
    'startDate'                             : LocalDate.of(2023, 1, 1),
    'endDate'                               : LocalDate.of(2024, 1, 1),
    'businessDayConvention'                 : 'Following',
    'businessDayAdjustment'                 : BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    'rollConvention'                        : 'None',
    'stubConvention'                        : 'Both',
    'calendars'                             : ['USNY'],
    'regularStartDate'                      : LocalDate.parse('2020-02-10'),
    'regularEndDate'                        : LocalDate.parse('2025-02-11'),
    'accrualFrequency'                      : '3M',
    'accrualOffsetDays'                     : 1,
    'paymentFrequency'                      : '3M',
    'paymentOffsetDays'                     : 2,
    'compoundingMethod'                     : 'None',
    'calculationOvernightDayCount'          : 'Act/360',
    'calculationOvernightAccrualMethod'     : 'COMPOUNDED',
    'initialCoupon'                         : 4,
    'calculationOvernightFixingOffsetDays'  : -2,
    'calculationOvernightSpreadInitialValue': 0,
    'calculationOvernightRateCutOffDays'    : 0]

  static Map SAMPLE_TRS_TRADE_FORM = ['externalTradeId'                  : '12PM_OIS_DUAL',
    'tradeDate'                        : LocalDate.parse('2022-06-30'),
    'tradeSettlementDate'              : LocalDate.parse('2022-07-01'),
    'tradeCounterparty'                : 'free text',
    'tradeCounterpartyType'            : 'BILATERAL',
    'description'                      : 'free text description',
    'managementFees'                   : BigDecimal.valueOf(10000),
    'managementFeesDayCount'           : 'Act/360',
    'performanceLegLastPaymentWithheld': true,
    'otherLegLastPaymentWithheld'      : true,
    'leg1'                             : trsLegIborFormSample(),
    'leg2'                             : trsLegPerformanceFormSample(),
    'currency'                         : 'USD',
    'versionForm'                      : NewVersionFormV2.newDefault()]

  static Map SAMPLE_TRS_TRADE = ['externalTradeId'                  : '12PM_OIS_DUAL',
    'tradeDate'                        : LocalDate.parse('2022-06-30'),
    'tradeSettlementDate'              : LocalDate.parse('2022-07-01'),
    'tradeCounterparty'                : 'free text',
    'tradeCounterpartyType'            : 'BILATERAL',
    'description'                      : 'free text description',
    'calendars'                        : ['USNY'],
    'managementFees'                   : BigDecimal.valueOf(10000.0),
    'managementFeesDayCount'           : 'Act/360',
    'performanceLegLastPaymentWithheld': true,
    'otherLegLastPaymentWithheld'      : true,
    'leg1'                             : iborLegDetailsSample(),
    'leg2'                             : performanceLegDetailsSample(),
    'currency'                         : 'USD',
    'versionForm'                      : NewVersionFormV2.newDefault()]

  private static TrsTradeLegDetails trsLegDetailsSample(TrsTradeLegDetails details, Map args = [:]) {
    details.payReceive = PayReceive.of((args.payReceive as String).toUpperCase())
    details.type = args.calculationType as TrsTradeCalculationType
    details.notional = args.notionalValue as Double
    details.currency = args.notionalCurrency as String
    details.accrualFrequency = args.accrualFrequency as String
    details.accrualOffsetDays = args.accrualOffsetDays as Integer
    details.paymentFrequency = args.paymentFrequency as String
    details.paymentOffsetDays = args.paymentOffsetDays as Integer
    details.paymentCompounding = args.compoundingMethod as String
    details.dividendPayout = args.dividendPayout as Double
    details.startDate = args.startDate as LocalDate
    details.endDate = args.endDate as LocalDate
    details.businessDayConvention = args.businessDayConvention as String
    details.businessDayAdjustment = args.businessDayAdjustment as String
    details.rollConvention = args.rollConvention as String
    details.stubConvention = args.stubConvention as String
    details.calendars = args.calendars as List<String>
    details.regularStartDate = args.regularStartDate as LocalDate
    details.regularEndDate = args.regularEndDate as LocalDate
    details.extLegIdentifier = args.extLegIdentifier as String
    details
  }

  static TrsTradeLegDetails overnightLegDetailsSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_OVERNIGHT_FORM + args
    def details = new TrsTradeLegDetails()
    details.index = args.calculationOvernightIndex as String
    details.dayCount = args.calculationOvernightDayCount as String
    details.initialValue = args.calculationOvernightSpreadInitialValue as Double
    details.overnightAccrualMethod = args.calculationOvernightAccrualMethod as String
    details.overnightRateCutOffDays = args.calculationOvernightRateCutOffDays as String
    details.fixingDateOffsetDays = args.calculationOvernightFixingOffsetDays as Integer
    trsLegDetailsSample(details, args)
  }

  static TrsTradeLegDetails iborLegDetailsSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_IBOR_FORM + args
    def details = new TrsTradeLegDetails()
    details.index = args.calculationIborIndex as String
    details.dayCount = args.calculationIborDayCount as String
    details.initialValue = args.calculationIborSpreadInitialValue as Double
    details.fixingCalendars = args.iborFixingCalendars as List<String>
    details.fixingDateOffsetDays = args.calculationIborFixingDateOffsetDays as Integer
    details.initialCoupon = args.calculationIborInitialCoupon as Double
    trsLegDetailsSample(details, args)
  }

  static TrsTradeLegDetails fixedLegDetailsSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_FIXED_FORM + args
    def details = new TrsTradeLegDetails()
    details.dayCount = args.calculationFixedDayCount as String
    details.initialValue = args.calculationFixedRateInitialValue as Double
    trsLegDetailsSample(details, args)
  }

  static TrsTradeLegDetails performanceLegDetailsSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_PERFORMANCE_FORM + args
    def details = new TrsTradeLegDetails()
    details.index = args.trsIndex as String
    details.trsIndexType = args.trsIndexType as TrsType
    details.fixingCalendars = args.trsIndexFixingCalendars as List<String>
    details.fixingDateOffsetDays = args.trsIndexFixingOffsetDays as Integer
    details.initialCoupon = args.trsInitialIndex as Double
    trsLegDetailsSample(details, args)
  }

  static TrsTradeForm createTrsTradeFormSample(TrsTradeForm form, Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    form.externalTradeId = args.externalTradeId as String
    form.tradeDate = args.tradeDate as LocalDate
    form.tradeCounterparty = args.tradeCounterparty as String
    form.tradeCounterpartyType = args.tradeCounterpartyType as String
    form.description = args.description as String
    form.managementFees = args.managementFees as BigDecimal
    form.managementFeesDayCount = args.managementFeesDayCount as String
    form.performanceLegLastPaymentWithheld = args.performanceLegLastPaymentWithheld as Boolean
    form.otherLegLastPaymentWithheld = args.otherLegLastPaymentWithheld as Boolean
    form.leg1 = args.leg1 as TrsLegForm
    form.leg2 = args.leg2 as TrsLegForm
    form.currency = args.currency as String
    form.versionForm = args.versionForm as NewVersionFormV2
    form.externalIdentifiers = args.externalIdentifiers as List<ExternalIdentifierForm>
    form.customFields = args.customFields as List<CustomTradeFieldForm>
    form
  }

  static CreateTrsBondTradeForm createTrsBondTradeFormSample(Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    def form = new CreateTrsBondTradeForm()
    createTrsTradeFormSample(form, args)
    form
  }

  static CreateTrsShareTradeForm createTrsShareTradeFormSample(Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    def form = new CreateTrsShareTradeForm()
    createTrsTradeFormSample(form, args)
    form
  }

  static TrsTradeForm updateTrsTradeFormSample(TrsTradeForm form, Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    form.externalTradeId = args.externalTradeId as String
    form.tradeDate = args.tradeDate as LocalDate
    form.tradeCounterparty = args.tradeCounterparty as String
    form.tradeCounterpartyType = args.tradeCounterpartyType as String
    form.description = args.description as String
    form.managementFees = args.managementFees as BigDecimal
    form.managementFeesDayCount = args.managementFeesDayCount as String
    form.performanceLegLastPaymentWithheld = args.performanceLegLastPaymentWithheld as Boolean
    form.otherLegLastPaymentWithheld = args.otherLegLastPaymentWithheld as Boolean
    form.leg1 = args.leg1 as TrsLegForm
    form.leg2 = args.leg2 as TrsLegForm
    form.currency = args.currency as String
    form.versionForm = args.versionForm as NewVersionFormV2
    form.externalIdentifiers = args.externalIdentifiers as List<ExternalIdentifierForm>
    form.customFields = args.customFields as List<CustomTradeFieldForm>
    form
  }

  static UpdateTrsBondTradeForm updateTrsBondTradeFormSample(Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    def form = new UpdateTrsBondTradeForm()
    updateTrsTradeFormSample(form, args)
    form
  }

  static UpdateTrsShareTradeForm updateTrsShareTradeFormSample(Map args = [:]) {
    args = SAMPLE_TRS_TRADE_FORM + args
    def form = new UpdateTrsShareTradeForm()
    updateTrsTradeFormSample(form, args)
    form
  }

  static TrsLegForm trsLegFormSample(Map args = [:]) {
    def form = new TrsLegForm()
    form.extLegIdentifier = args.extLegIdentifier as String
    form.payReceive = args.payReceive as String
    form.calculationType = args.calculationType as TrsTradeCalculationType
    form.notionalValue = args.notionalValue as Double
    form.notionalCurrency = args.notionalCurrency as String
    form.trsIndex = args.trsIndex as String
    form.trsIndexType = args.trsIndexType as TrsType
    form.calculationIborIndex = args.calculationIborIndex as String
    form.calculationOvernightIndex = args.calculationOvernightIndex as String
    form.accrualFrequency = args.accrualFrequency as String
    form.accrualOffsetDays = args.accrualOffsetDays as Integer
    form.paymentFrequency = args.paymentFrequency as String
    form.paymentOffsetDays = args.paymentOffsetDays as Integer
    form.compoundingMethod = args.compoundingMethod as String
    form.calculationFixedDayCount = args.calculationFixedDayCount as String
    form.calculationIborDayCount = args.calculationIborDayCount as String
    form.calculationOvernightDayCount = args.calculationOvernightDayCount as String
    form.calculationOvernightSpreadInitialValue = args.calculationOvernightSpreadInitialValue as Double
    form.calculationIborSpreadInitialValue = args.calculationIborSpreadInitialValue as Double
    form.calculationIborFixingDateOffsetDays = args.calculationIborFixingDateOffsetDays as Integer
    form.iborFixingCalendars = args.iborFixingCalendars as List<String>
    form.trsIndexFixingOffsetDays = args.trsIndexFixingOffsetDays as Integer
    form.trsIndexFixingCalendars = args.trsIndexFixingCalendars as List<String>
    form.dividendPayout = args.dividendPayout as Double
    form.calculationFixedRateInitialValue = args.calculationFixedRateInitialValue as Integer
    form.calculationOvernightAccrualMethod = args.calculationOvernightAccrualMethod as String
    form.calculationOvernightRateCutOffDays = args.calculationOvernightRateCutOffDays as String
    form.calculationIborInitialCoupon = args.calculationIborInitialCoupon as Double
    form.trsInitialIndex = args.trsInitialIndex as Double
    form.startDate = args.startDate as LocalDate
    form.endDate = args.endDate as LocalDate
    form.businessDayConvention = args.businessDayConvention as String
    form.businessDayAdjustment = args.businessDayAdjustment as String
    form.rollConvention = args.rollConvention as String
    form.stubConvention = args.stubConvention as String
    form.calendars = args.calendars as List<String>
    form.regularStartDate = args.regularStartDate as LocalDate
    form.regularEndDate = args.regularEndDate as LocalDate
    form.calculationOvernightFixingOffsetDays = args.calculationOvernightFixingOffsetDays as Integer
    form
  }

  static TrsLegForm trsLegIborFormSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_IBOR_FORM + args
    trsLegFormSample(args)
  }

  static TrsLegForm trsLegFixedFormSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_FIXED_FORM + args
    trsLegFormSample(args)
  }

  static TrsLegForm trsLegOvernightFormSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_OVERNIGHT_FORM + args
    trsLegFormSample(args)
  }

  static TrsLegForm trsLegPerformanceFormSample(Map args = [:]) {
    args = SAMPLE_TRS_LEG_PERFORMANCE_FORM + args
    trsLegFormSample(args)
  }

  static TrsTradeDetails trsTradeDetailsSample(Map args = [:]) {
    args = SAMPLE_TRS_TRADE + args
    def trade = new TrsTradeDetails()
    def info = new TrsTradeInfo()
    info.tradeDate = args.tradeDate as LocalDate
    info.counterParty = args.tradeCounterparty as String
    info.counterPartyType = args.tradeCounterpartyType as String
    trade.info = info
    trade.managementFees = args.managementFees as BigDecimal
    trade.managementFeesDayCount = args.managementFeesDayCount as String
    trade.performanceLegLastPaymentWithheld = args.performanceLegLastPaymentWithheld as Boolean
    trade.otherLegLastPaymentWithheld = args.otherLegLastPaymentWithheld as Boolean
    trade.payLeg = args.leg1 as TrsTradeLegDetails
    trade.receiveLeg = args.leg2 as TrsTradeLegDetails
    trade.currency = args.currency as String
    trade
  }
}
