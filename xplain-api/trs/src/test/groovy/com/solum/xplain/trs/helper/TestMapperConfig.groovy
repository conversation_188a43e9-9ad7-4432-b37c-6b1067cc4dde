package com.solum.xplain.trs.helper

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.solum.xplain.core.common.RequestPathVariablesSupport
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean

@TestConfiguration
class TestMapperConfig {

  @Bean
  RequestPathVariablesSupport requestPathVariablesSupport() {
    new RequestPathVariablesSupport()
  }

  @Bean
  ObjectMapper mapper() {
    ObjectMapper objectMapper = new ObjectMapper()
    objectMapper.registerModule(new JavaTimeModule())
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
    objectMapper
  }
}
