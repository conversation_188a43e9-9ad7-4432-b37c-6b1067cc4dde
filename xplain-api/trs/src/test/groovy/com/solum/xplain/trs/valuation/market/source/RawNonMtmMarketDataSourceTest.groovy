package com.solum.xplain.trs.valuation.market.source

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static java.time.LocalDate.now

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.service.MarketDataKeyResolver
import com.solum.xplain.core.mdvalue.MarketDataValueRepository
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.trs.valuation.market.NonMtmMarketStateKey
import com.solum.xplain.trs.valuation.market.filters.NonMtmMarketDataProviderFilter
import com.solum.xplain.trs.value.TrsAssetClassGroup
import spock.lang.Specification

class RawNonMtmMarketDataSourceTest extends Specification {
  static GROUP_ID = "ID"

  MarketDataKeyRepository keyRepository = Mock()
  MarketDataValueRepository valueRepository = Mock()

  RawNonMtmMarketDataSource source = new RawNonMtmMarketDataSource(keyRepository, valueRepository)

  def "should provide raw market data"() {
    setup:
    def stateDate = new BitemporalDate(now())
    def marketDataValueFilter = Mock(NonMtmMarketDataProviderFilter)
    5 * marketDataValueFilter.isRequiredProvider(_ as String, "P1") >> true
    1 * marketDataValueFilter.isRequiredProvider(_ as String, "P2") >> false

    def keyResolver = Mock(MarketDataKeyResolver)
    def stateKey = new NonMtmMarketStateKey(GROUP_ID, stateDate, "companyId", "entityId", ASK_PRICE)
    1 * valueRepository.getLatestValueViews(GROUP_ID, stateDate) >> []
    1 * keyRepository.keyResolver(stateDate, TrsAssetClassGroup.TRS) >> keyResolver
    1 * keyResolver.filterUnresolvableValues([]) >> []
    1 * keyResolver.marketDataKeysHasAnyProvider() >> [:]
    1 * keyResolver.resolvedValues([]) >> resolvedValues()

    def result = source.provideCalculationMarketData(stateKey, marketDataValueFilter)
    expect:

    result != null
    def t1 = result.trsValues["TRS_INDEX_VALUE"].get(now())
    t1.isPresent()
    t1.getAsDouble() == 1.0d

    def t2 = result.trsValues["TRS2_INDEX_VALUE"].get(now())
    t2.isPresent()
    t2.getAsDouble() == 1.1d

    def dividends = result.dividends["TRS_DIVIDEND"]
    dividends != null
    dividends.get(now()).asDouble == 1.0d
    dividends.get(now().minusDays(1)).asDouble == 2.0d
    dividends.get(now().plusDays(1)).asDouble == 3.0d
  }

  def resolvedValues() {
    [
      new ResolvedMarketDataValueView(
      key: "TRS_INDEX_VALUE",
      assetGroup: "TRS",
      instrumentType: "TRS_BOND",
      values: [new MarketDataValueFlatView(provider: "P1", date: now(), value: 1.0, bidAsk: ValueBidAskType.ASK)]
      ),
      new ResolvedMarketDataValueView(
      key: "TRS2_INDEX_VALUE",
      assetGroup: "TRS",
      instrumentType: "TRS_SHARE",
      values: [
        new MarketDataValueFlatView(provider: "P1", date: now(), value: 1.1, bidAsk: ValueBidAskType.ASK),
        new MarketDataValueFlatView(provider: "P2", date: now(), value: 3.0, bidAsk: ValueBidAskType.ASK)
      ]
      ),
      new ResolvedMarketDataValueView(
      key: "TRS_DIVIDEND",
      assetGroup: "TRS",
      instrumentType: "TRS_DIVIDEND",
      values: [
        new MarketDataValueFlatView(provider: "P1", date: now(), value: 1.0, bidAsk: ValueBidAskType.ASK),
        new MarketDataValueFlatView(provider: "P1", date: now().minusDays(1), value: 2.0, bidAsk: ValueBidAskType.ASK),
        new MarketDataValueFlatView(provider: "P1", date: now().plusDays(1), value: 3.0, bidAsk: ValueBidAskType.ASK)
      ]
      ),
    ]
  }
}
