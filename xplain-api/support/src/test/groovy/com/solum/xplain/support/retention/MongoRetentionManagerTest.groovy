package com.solum.xplain.support.retention

import com.solum.xplain.support.retention.value.DocumentValidities
import com.solum.xplain.support.retention.value.RemovalSummary
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.MongoMappingContext
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity
import spock.lang.Specification

class MongoRetentionManagerTest extends Specification {
  MongoMappingContext mappingContext = Mock()
  DocumentRetentionManagerProvider managerProvider = Mock()


  MongoRetentionManager mongoManager = new MongoRetentionManager(mappingContext, managerProvider)

  def "should clean old data"() {
    setup:
    def validities = Mock(DocumentValidities)
    def entity = Mock(MongoPersistentEntity)
    def collectionManager = Mock(CollectionRetentionManager)


    when:
    mongoManager.cleanOldData(validities)

    then:
    1 * entity.isAnnotationPresent(Document) >> true
    1 * mappingContext.getPersistentEntities() >> [entity]

    1 * collectionManager.clean(validities) >> new RemovalSummary(1, "test")
    1 * managerProvider.retentionManager(entity) >> Optional.of(collectionManager)
  }

  def "should ignore not annotated class"() {
    setup:
    def validities = Mock(DocumentValidities)
    def entity = Mock(MongoPersistentEntity)

    when:
    mongoManager.cleanOldData(validities)

    then:
    1 * entity.isAnnotationPresent(Document) >> false
    1 * mappingContext.getPersistentEntities() >> [entity]
    0 * managerProvider.retentionManager(entity)
  }
}
