package com.solum.xplain.support.retention.explicit.secmaster

import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity
import com.solum.xplain.secmaster.entity.SecMasterTradeWriteEntity
import com.solum.xplain.support.retention.value.DocumentValidities
import java.util.stream.Stream
import spock.lang.Specification

class SecMasterRetentionManagerTest extends Specification {
  def supportRepository = Mock(SecMasterSupportRepository)
  def manager = new SecMasterRetentionManager(supportRepository)

  def "should invoke date range item retention manager"() {
    setup:
    def validities = Mock(DocumentValidities)

    when:
    manager.clean(validities)

    then:
    1 * supportRepository.entitiesStream() >> Stream.empty()
  }

  def "should match portfolio item write class"() {
    expect:
    manager.matchesCollection(SecMasterTradeWriteEntity)
    !manager.matchesCollection(SecMasterTradeReadEntity)
  }

  def "should match ignore portfolio item class"() {
    expect:
    !manager.matchesCollectionToIgnore(SecMasterTradeWriteEntity)
    manager.matchesCollectionToIgnore(SecMasterTradeReadEntity)
  }
}
