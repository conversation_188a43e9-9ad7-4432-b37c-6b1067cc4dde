package com.solum.xplain.support.helpers

import com.solum.xplain.core.config.converters.instrument.InstrumentTypeJacksonDeserializer
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentTypeProvider
import com.solum.xplain.core.instrument.InstrumentTypeResolver
import com.solum.xplain.core.lock.LockingInterceptor
import jakarta.inject.Provider
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean

@TestConfiguration
class TestMvcConfig {

  @Bean
  @ConditionalOnMissingBean(LockingInterceptor.class)
  LockingInterceptor mockInterceptor() {
    return new LockingInterceptor() {
      }
  }

  @Bean
  @ConditionalOnMissingBean(InstrumentTypeResolver.class)
  InstrumentTypeResolver typeProvider() {
    return new InstrumentTypeResolver([new CoreInstrumentTypeProvider()])
  }

  @Bean
  @ConditionalOnMissingBean(InstrumentTypeJacksonDeserializer.class)
  InstrumentTypeJacksonDeserializer deserializer() {
    return new InstrumentTypeJacksonDeserializer(new Provider<InstrumentTypeResolver>() {
        @Override
        InstrumentTypeResolver get() {
          return typeProvider()
        }
      })
  }
}
