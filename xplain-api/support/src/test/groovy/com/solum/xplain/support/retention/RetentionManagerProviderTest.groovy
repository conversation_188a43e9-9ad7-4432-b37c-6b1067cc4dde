package com.solum.xplain.support.retention

import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity
import com.solum.xplain.secmaster.entity.SecMasterTradeWriteEntity
import com.solum.xplain.support.retention.datavalues.DataValuesRetentionManager
import com.solum.xplain.support.retention.datavalues.TestDataHolder
import com.solum.xplain.support.retention.explicit.ExplicitCollectionRetentionManager
import com.solum.xplain.support.retention.majoversioned.MajorVersionedRetentionManager
import com.solum.xplain.support.retention.majoversioned.TestMajorVersionedEntity
import com.solum.xplain.support.retention.minorversioned.MinorVersionedEntityRetentionManager
import com.solum.xplain.support.retention.minorversioned.TestMinorVersionedEntity
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity
import spock.lang.Shared
import spock.lang.Specification

class RetentionManagerProviderTest extends Specification {
  private static final String COLLECTION = "TEST"

  @Shared
  def operations = Mock(MongoOperations)
  @Shared
  def explicitManager = mockExplicitManager()
  def provider = new DocumentRetentionManagerProvider(operations, [explicitManager])

  def "should provider correct retention function"() {
    expect:
    provider.retentionManager(entity) == expectedFuntion

    where:
    entity                                     | expectedFuntion
    mockMongoEntity(TestDataHolder)            | Optional.of(new DataValuesRetentionManager(operations, COLLECTION))
    mockMongoEntity(TestMajorVersionedEntity)  | Optional.of(new MajorVersionedRetentionManager(operations, COLLECTION))
    mockMongoEntity(TestMinorVersionedEntity)  | Optional.of(new MinorVersionedEntityRetentionManager(operations, COLLECTION))
    mockMongoEntity(SecMasterTradeWriteEntity) | Optional.of(explicitManager)
    mockMongoEntity(SecMasterTradeReadEntity)  | Optional.empty()
    mockMongoEntity(String)                    | Optional.empty()
  }

  def mockMongoEntity(Class<?> classToWrap) {
    def entity = Mock(MongoPersistentEntity)
    entity.getType() >> classToWrap
    entity.getCollection() >> COLLECTION
    return entity
  }

  def mockExplicitManager() {
    def manager = Mock(ExplicitCollectionRetentionManager)
    manager.matchesCollection(SecMasterTradeWriteEntity) >> true
    manager.matchesCollectionToIgnore(SecMasterTradeReadEntity) >> true
    return manager
  }
}
