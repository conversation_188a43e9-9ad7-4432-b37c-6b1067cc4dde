package com.solum.xplain.support.migration.changeunits.v_1_11_0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CU01AddNewAllowAllCompaniesTest extends IntegrationSpecification{

  @Resource
  MongoOperations mongoOperations

  def "should execute on empty db"() {
    when:
    def changeUnit = new CU01AddNewAllowAllCompaniesField(mongoOperations)
    changeUnit.execution()

    then:
    mongoOperations.find(new Query(), Document.class, "IpvDataGroup").isEmpty()
  }

  def "should execute" () {
    given:
    Document ipvDataGroup = new Document()
    ipvDataGroup.put("companyIds", Arrays.asList("company1", "company2"))
    mongoOperations.save(ipvDataGroup, "IpvDataGroup")

    and:
    CU01AddNewAllowAllCompaniesField migration = new CU01AddNewAllowAllCompaniesField(mongoOperations)

    when:
    migration.execution()

    then:
    Document updatedIpvDataGroup = mongoOperations.findOne(Query.query(Criteria.where("_id").is(ipvDataGroup.get("_id"))), Document.class, "IpvDataGroup")
    boolean allowAllCompanies = (Boolean) updatedIpvDataGroup.get("allowAllCompanies")
    List<String> companyIds = (List<String>) updatedIpvDataGroup.get("companyIds")

    updatedIpvDataGroup.containsKey("allowAllCompanies")
    !allowAllCompanies
    companyIds == Arrays.asList("company1", "company2")
  }
}
