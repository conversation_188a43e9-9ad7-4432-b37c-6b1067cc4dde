package com.solum.xplain.support.migration.changeunits.v_2_00_0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.index.Index
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CU01AddCalculationPortfolioItemUniqueIndexTest extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection("calculationPortfolioItem")
  }

  def "should create new index if no equivalent exists"() {
    setup:
    def changeUnit = new CU01AddCalculationPortfolioItemUniqueIndex(mongoTemplate)

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps("calculationPortfolioItem").getIndexInfo()
    peInfo.find { it.getName() == "calculationResultId_tradeId_unique" } != null
  }

  def "should skip index creation if an equivalent index exists"() {
    setup:
    def changeUnit = new CU01AddCalculationPortfolioItemUniqueIndex(mongoTemplate)
    mongoTemplate.indexOps("calculationPortfolioItem").ensureIndex(
      new Index()
      .named("existing_index")
      .on("calculationResultId", Direction.ASC)
      .on("tradeId", Direction.ASC)
      .unique())

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps("calculationPortfolioItem").getIndexInfo()
    peInfo.find { it.getName() == "calculationResultId_tradeId_unique" } == null
    peInfo.find { it.getName() == "existing_index" } != null
  }
}
