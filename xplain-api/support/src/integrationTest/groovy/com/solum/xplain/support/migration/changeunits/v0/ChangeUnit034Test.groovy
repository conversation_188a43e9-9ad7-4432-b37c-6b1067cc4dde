
package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.bson.Document
import org.bson.json.JsonObject
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit034Test extends IntegrationSpecification {
  @Resource
  MongoOperations mongoOperations

  def COMPANY_IPV_SETTINGS = "companyIpvSettings"
  def COMPANY_LEGAL_ENTITY_IPV_SETTINGS = "companyLegalEntityIpvSettings"

  def cleanup() {
    if (mongoOperations.collectionExists(COMPANY_IPV_SETTINGS)) {
      mongoOperations.dropCollection(COMPANY_IPV_SETTINGS)
    }

    if (mongoOperations.collectionExists(COMPANY_LEGAL_ENTITY_IPV_SETTINGS)) {
      mongoOperations.dropCollection(COMPANY_LEGAL_ENTITY_IPV_SETTINGS)
    }
  }

  def "should execute on empty db"() {
    when:
    new ChangeUnit034(mongoOperations).execution()

    then:
    mongoOperations.findAll(JsonObject.class, COMPANY_IPV_SETTINGS).isEmpty()
    mongoOperations.findAll(JsonObject.class, COMPANY_LEGAL_ENTITY_IPV_SETTINGS).isEmpty()
  }

  def "should execute for company settings"() {
    setup:
    def doc1 = Document.parse("""
      {
        "settingsType": "BESPOKE",
        "slaDeadline": "OTHER",
        "entityId": "65c111b616463810e70ec483",
        "comment": "new",
        "modifiedBy": {
          "name": "DevUser",
          "userId": "DevUser",
          "username": "DevUser"
        },
        "ipvDataGroup": {
          "name": "ipvDataGroup1",
          "entityId": "65c111b616463810e70ec484"
        },
        "products": {
          "FXOPT": {
            "primary": "fxoptP1",
            "primaryWithGreeks": true,
            "secondary": "fxoptP2",
            "secondaryWithGreeks": true,
            "tertiaryWithGreeks": false,
            "quaternaryWithGreeks": false
          },
          "CDS": {
            "primary": "cdsFloorP1",
            "primaryWithGreeks": false,
            "secondary": "cdsFloorP2",
            "secondaryWithGreeks": true,
            "tertiaryWithGreeks": false,
            "quaternaryWithGreeks": false
          },
          "CAP_FLOOR": {
            "primary": "capFloorP1",
            "primaryWithGreeks": true,
            "secondary": "capFloorP2",
            "secondaryWithGreeks": true,
            "tertiaryWithGreeks": false,
            "quaternaryWithGreeks": false
          }
        }
      }
    """)
    def doc2 = Document.parse("""
      {
        "ipvDataGroup": {
          "name": "ipvDataGroup2",
          "entityId": "65c111b616463810e70ec485"
        },
        "products": {
          "CDS": {
            "primary": "cdsFloorP1",
            "primaryWithGreeks": false,
            "secondary": "cdsFloorP2",
            "secondaryWithGreeks": true,
            "tertiaryWithGreeks": false,
            "quaternaryWithGreeks": false
          }
        }
      }
    """)

    mongoOperations.insert(doc1, "companyIpvSettings")
    mongoOperations.insert(doc2, "companyIpvSettings")

    when:
    new ChangeUnit034(mongoOperations).execution()
    def compSettingsFetched = mongoOperations.findAll(JsonObject.class, COMPANY_IPV_SETTINGS)

    then:
    compSettingsFetched != null
    compSettingsFetched.size() == 2

    compSettingsFetched[0].settingsType == "BESPOKE"
    compSettingsFetched[0].slaDeadline == "OTHER"
    compSettingsFetched[0].entityId == "65c111b616463810e70ec483"
    compSettingsFetched[0].comment == "new"

    compSettingsFetched[0].modifiedBy.userId == "DevUser"
    compSettingsFetched[0].modifiedBy.username == "DevUser"
    compSettingsFetched[0].modifiedBy.name == "DevUser"

    compSettingsFetched[0].products.size() == 3
    compSettingsFetched[0].products[0].productType == "FXOPT"
    compSettingsFetched[0].products[0].primary == "fxoptP1"
    compSettingsFetched[0].products[0].secondary == "fxoptP2"
    compSettingsFetched[0].products[0].tertiary == null
    compSettingsFetched[0].products[0].quaternary == null
    compSettingsFetched[0].products[0].ipvDataGroup.entityId == "65c111b616463810e70ec484"
    compSettingsFetched[0].products[0].ipvDataGroup.name == "ipvDataGroup1"

    compSettingsFetched[0].products[1].productType == "CDS"
    compSettingsFetched[0].products[1].primary == "cdsFloorP1"
    compSettingsFetched[0].products[1].secondary == "cdsFloorP2"
    compSettingsFetched[0].products[1].tertiary == null
    compSettingsFetched[0].products[1].quaternary == null
    compSettingsFetched[0].products[1].ipvDataGroup.entityId == "65c111b616463810e70ec484"
    compSettingsFetched[0].products[1].ipvDataGroup.name == "ipvDataGroup1"

    compSettingsFetched[0].products[2].productType == "CAP_FLOOR"
    compSettingsFetched[0].products[2].primary == "capFloorP1"
    compSettingsFetched[0].products[2].secondary == "capFloorP2"
    compSettingsFetched[0].products[2].tertiary == null
    compSettingsFetched[0].products[2].quaternary == null
    compSettingsFetched[0].products[2].ipvDataGroup.entityId == "65c111b616463810e70ec484"
    compSettingsFetched[0].products[2].ipvDataGroup.name == "ipvDataGroup1"

    // second result
    compSettingsFetched[1].products.size() == 1
    compSettingsFetched[1].products[0].productType == "CDS"
    compSettingsFetched[1].products[0].primary == "cdsFloorP1"
    compSettingsFetched[1].products[0].secondary == "cdsFloorP2"
    compSettingsFetched[1].products[0].tertiary == null
    compSettingsFetched[1].products[0].quaternary == null
    compSettingsFetched[1].products[0].ipvDataGroup.entityId == "65c111b616463810e70ec485"
    compSettingsFetched[1].products[0].ipvDataGroup.name == "ipvDataGroup2"
    compSettingsFetched[1].products[0].ipvDataGroup.name != "ipvDataGroup1"
  }

  def "should execute for DEFAULT company settings"() {
    setup:
    Document document = Document.parse("""
      {
        "settingsType": "DEFAULT",
        "slaDeadline": "OTHER",
        "entityId": "65c1256b68794536313ef421",
        "comment": "new",
        "modifiedBy": {
          "name": "DevUser",
          "userId": "DevUser",
          "username": "DevUser"
        },
        "ipvDataGroup": {
          "name": "ipvDataGroup1",
          "entityId": "65c1256b68794536313ef422"
        }
      }
    """)

    mongoOperations.insert(document, "companyIpvSettings")

    when:
    new ChangeUnit034(mongoOperations).execution()
    def compSettingsFetched = mongoOperations.findAll(JsonObject.class, COMPANY_IPV_SETTINGS)

    then:
    compSettingsFetched != null
    compSettingsFetched.size() == 1

    compSettingsFetched[0].settingsType == "DEFAULT"
    compSettingsFetched[0].slaDeadline == "OTHER"
    compSettingsFetched[0].entityId == "65c1256b68794536313ef421"
    compSettingsFetched[0].comment == "new"

    compSettingsFetched[0].modifiedBy.userId == "DevUser"
    compSettingsFetched[0].modifiedBy.username == "DevUser"
    compSettingsFetched[0].modifiedBy.name == "DevUser"

    compSettingsFetched[0].products == null
  }

  def "should execute for entity settings"() {
    setup:
    Document document = Document.parse("""
      {
        "companyId": "65c126dfd21c734430168e29",
        "settingsType": "BESPOKE",
        "slaDeadline": "OTHER",
        "entityId": "65c126dfd21c734430168e2b",
        "comment": "new",
        "modifiedBy": {
          "name": "DevUser",
          "userId": "DevUser",
          "username": "DevUser"
        },
        "ipvDataGroup": {
          "name": "ipvDataGroup1",
          "entityId": "65c126dfd21c734430168e2c"
        },
        "products": {
          "FXOPT": {
            "secondary": "fxoptP2",
            "primaryWithGreeks": true,
            "secondaryWithGreeks": true,
            "quaternaryWithGreeks": false,
            "tertiaryWithGreeks": false,
            "primary": "fxoptP1"
          },
          "CDS": {
            "secondary": "cdsFloorP2",
            "primaryWithGreeks": false,
            "secondaryWithGreeks": true,
            "quaternaryWithGreeks": false,
            "tertiaryWithGreeks": false,
            "primary": "cdsFloorP1"
          },
          "CAP_FLOOR": {
            "secondary": "capFloorP2",
            "primaryWithGreeks": true,
            "secondaryWithGreeks": true,
            "quaternaryWithGreeks": false,
            "tertiaryWithGreeks": false,
            "primary": "capFloorP1"
          }
        }
      }
    """)
    Document document2 = Document.parse("""
      {
        "companyId": "65c126dfd21c734430168e2a",
        "ipvDataGroup": {
          "name": "ipvDataGroup2",
          "entityId": "65c126dfd21c734430168e2d"
        },
        "products": {
          "CDS": {
            "secondary": "cdsFloorP2",
            "primaryWithGreeks": false,
            "secondaryWithGreeks": true,
            "quaternaryWithGreeks": false,
            "tertiaryWithGreeks": false,
            "primary": "cdsFloorP1"
          }
        }
      }
    """)

    mongoOperations.insert(document, "companyLegalEntityIpvSettings")
    mongoOperations.insert(document2, "companyLegalEntityIpvSettings")

    when:
    new ChangeUnit034(mongoOperations).execution()
    def compEntitySettingsFetched = mongoOperations.findAll(JsonObject.class, COMPANY_LEGAL_ENTITY_IPV_SETTINGS)

    then:
    compEntitySettingsFetched != null
    compEntitySettingsFetched.size() == 2

    compEntitySettingsFetched[0].companyId == "65c126dfd21c734430168e29"
    compEntitySettingsFetched[0].settingsType == "BESPOKE"
    compEntitySettingsFetched[0].slaDeadline == "OTHER"
    compEntitySettingsFetched[0].entityId == "65c126dfd21c734430168e2b"
    compEntitySettingsFetched[0].comment == "new"

    compEntitySettingsFetched[0].modifiedBy.userId == "DevUser"
    compEntitySettingsFetched[0].modifiedBy.username == "DevUser"
    compEntitySettingsFetched[0].modifiedBy.name == "DevUser"

    compEntitySettingsFetched[0].products.size() == 3
    compEntitySettingsFetched[0].products[0].productType == "FXOPT"
    compEntitySettingsFetched[0].products[0].primary == "fxoptP1"
    compEntitySettingsFetched[0].products[0].secondary == "fxoptP2"
    compEntitySettingsFetched[0].products[0].tertiary == null
    compEntitySettingsFetched[0].products[0].quaternary == null
    compEntitySettingsFetched[0].products[0].ipvDataGroup.entityId == "65c126dfd21c734430168e2c"
    compEntitySettingsFetched[0].products[0].ipvDataGroup.name == "ipvDataGroup1"
    compEntitySettingsFetched[0].products[1].productType == "CDS"
    compEntitySettingsFetched[0].products[1].primary == "cdsFloorP1"
    compEntitySettingsFetched[0].products[1].secondary == "cdsFloorP2"
    compEntitySettingsFetched[0].products[1].tertiary == null
    compEntitySettingsFetched[0].products[1].quaternary == null
    compEntitySettingsFetched[0].products[1].ipvDataGroup.entityId == "65c126dfd21c734430168e2c"
    compEntitySettingsFetched[0].products[1].ipvDataGroup.name == "ipvDataGroup1"

    compEntitySettingsFetched[0].products[2].productType == "CAP_FLOOR"
    compEntitySettingsFetched[0].products[2].primary == "capFloorP1"
    compEntitySettingsFetched[0].products[2].secondary == "capFloorP2"
    compEntitySettingsFetched[0].products[2].tertiary == null
    compEntitySettingsFetched[0].products[2].quaternary == null
    compEntitySettingsFetched[0].products[2].ipvDataGroup.entityId == "65c126dfd21c734430168e2c"
    compEntitySettingsFetched[0].products[2].ipvDataGroup.name == "ipvDataGroup1"

    // second result
    compEntitySettingsFetched[1].companyId == "65c126dfd21c734430168e2a"
    compEntitySettingsFetched[1].products.size() == 1
    compEntitySettingsFetched[1].products[0].productType == "CDS"
    compEntitySettingsFetched[1].products[0].primary == "cdsFloorP1"
    compEntitySettingsFetched[1].products[0].secondary == "cdsFloorP2"
    compEntitySettingsFetched[1].products[0].tertiary == null
    compEntitySettingsFetched[1].products[0].quaternary == null
    compEntitySettingsFetched[1].products[0].ipvDataGroup.entityId == "65c126dfd21c734430168e2d"
    compEntitySettingsFetched[1].products[0].ipvDataGroup.name == "ipvDataGroup2"
    compEntitySettingsFetched[1].products[0].ipvDataGroup.name != "ipvDataGroup1"
  }

  def "should execute for DEFAULT entity settings"() {
    setup:
    Document document = Document.parse("""
      {
        "companyId": "65c12ca88e4c0600873c557e",
        "settingsType": "DEFAULT",
        "slaDeadline": "OTHER",
        "entityId": "65c12ca88e4c0600873c557f",
        "comment": "new",
        "modifiedBy": {
          "name": "DevUser",
          "userId": "DevUser",
          "username": "DevUser"
        },
        "ipvDataGroup": {
          "name": "ipvDataGroup1",
          "entityId": "65c12ca88e4c0600873c5580"
        }
      }
    """)
    mongoOperations.insert(document, "companyLegalEntityIpvSettings")

    when:
    new ChangeUnit034(mongoOperations).execution()
    def compEntitySettingsFetched = mongoOperations.findAll(JsonObject.class, COMPANY_LEGAL_ENTITY_IPV_SETTINGS)

    then:
    compEntitySettingsFetched != null
    compEntitySettingsFetched.size() == 1

    compEntitySettingsFetched[0].companyId == "65c12ca88e4c0600873c557e"
    compEntitySettingsFetched[0].settingsType == "DEFAULT"
    compEntitySettingsFetched[0].slaDeadline == "OTHER"
    compEntitySettingsFetched[0].entityId == "65c12ca88e4c0600873c557f"
    compEntitySettingsFetched[0].comment == "new"

    compEntitySettingsFetched[0].modifiedBy.userId == "DevUser"
    compEntitySettingsFetched[0].modifiedBy.username == "DevUser"
    compEntitySettingsFetched[0].modifiedBy.name == "DevUser"

    compEntitySettingsFetched[0].products == null
  }
}
