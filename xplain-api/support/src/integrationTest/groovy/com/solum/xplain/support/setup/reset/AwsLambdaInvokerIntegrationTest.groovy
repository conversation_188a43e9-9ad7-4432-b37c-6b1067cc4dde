package com.solum.xplain.support.setup.reset

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.config.JacksonConfig
import com.solum.xplain.support.setup.SetupProperties
import com.solum.xplain.support.setup.reset.dto.DataSetupRequestFactory
import com.solum.xplain.support.setup.reset.dto.DataSetupResponse
import io.atlassian.fugue.Try
import jakarta.annotation.Resource
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [ResetConfig, Aws<PERSON><PERSON>bdaInvoker, JacksonConfig])
@ImportAutoConfiguration(JacksonAutoConfiguration)
@EnableConfigurationProperties([SetupProperties])
@ActiveProfiles("test")
@Ignore("This test is disabled because it requires a running AWS account - need to use use localstack to mock this out")
// https://docs.localstack.cloud/user-guide/integrations/testcontainers/
// https://docs.localstack.cloud/user-guide/aws/lambda/
class AwsLambdaInvokerIntegrationTest extends Specification {
  @Resource
  AwsLambdaInvoker setupLambdaInvoker
  @Resource
  ObjectMapper objectMapper
  @Resource
  DataSetupRequestFactory dataSetupRequestFactory

  def "should return value from our test lambda function"() {
    given:
    def request = dataSetupRequestFactory.createDataSetupRequest("SANDBOX", "billy bob")
    when:
    Try<DataSetupResponse> result = setupLambdaInvoker.invokeLambda(request)

    then:
    result.isSuccess()
    def response = result.toOptional().get()
    response.message() == "Load test into SANDBOX using token billy bob"
  }
}
