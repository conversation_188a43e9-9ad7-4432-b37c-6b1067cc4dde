package com.solum.xplain.support.retention.datavalues

import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.support.retention.value.DocumentValidities
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class DataValuesRetentionManagerTest extends IntegrationSpecification {
  def OLD_DATE = LocalDateTime.now().minusYears(5)
  def NOW = LocalDateTime.now()
  def COLLECTION_NAME = "testDataHolder"

  @Resource
  MongoOperations operations

  def cleanup() {
    operations.dropCollection(COLLECTION_NAME)
  }

  def "should clean old minor data"() {
    setup:
    def timestamps = new DocumentValidities(ofEpochDay(0), NOW)

    def value1 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE)
    def value2 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE.plusDays(1))
    def value3 = new TestDataValue(value: BigDecimal.ONE, recordDate: NOW)
    def holder1 = new TestDataHolder(date: OLD_DATE.toLocalDate(), values: [value1, value2, value3])
    operations.insert(holder1)

    when:
    new DataValuesRetentionManager(operations, COLLECTION_NAME).clean(timestamps)

    then:
    def loaded = operations.findAll(TestDataHolder)
    loaded.size() == 1
    with(loaded[0]) {
      values.size() == 1
      values[0].recordDate.toLocalDate() == NOW.toLocalDate()
    }
  }

  def "should clean old major data"() {
    setup:
    def timestamps = new DocumentValidities(LocalDate.now(), LocalDateTime.now())

    def value1 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE)
    def value2 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE.plusDays(1))
    def value3 = new TestDataValue(value: BigDecimal.ONE, recordDate: NOW)
    def holder1 = new TestDataHolder(date: OLD_DATE.toLocalDate(), values: [value1, value2, value3])
    operations.insert(holder1)

    when:
    new DataValuesRetentionManager(operations, COLLECTION_NAME).clean(timestamps)

    then:
    operations.findAll(TestDataHolder).isEmpty()
  }

  def "should leave latest old minor data"() {
    setup:
    def timestamps = new DocumentValidities(ofEpochDay(0), NOW)

    def value1 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE)
    def holder1 = new TestDataHolder(date: OLD_DATE.toLocalDate(), values: [value1])
    operations.insert(holder1)

    when:
    new DataValuesRetentionManager(operations, COLLECTION_NAME).clean(timestamps)

    then:
    def loaded = operations.findAll(TestDataHolder)
    loaded.size() == 1
    with(loaded[0]) {
      values.size() == 1
      values[0].recordDate.toLocalDate() == OLD_DATE.toLocalDate()
    }
  }

  def "should remove temp field"() {
    setup:
    def timestamps = new DocumentValidities(ofEpochDay(0), NOW)

    def value1 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE)
    def value2 = new TestDataValue(value: BigDecimal.ONE, recordDate: OLD_DATE.plusDays(1))
    def value3 = new TestDataValue(value: BigDecimal.ONE, recordDate: NOW)
    def holder1 = new TestDataHolder(date: OLD_DATE.toLocalDate(), values: [value1, value2, value3])
    operations.insert(holder1)

    when:
    new DataValuesRetentionManager(operations, COLLECTION_NAME).clean(timestamps)

    then:
    def loaded = operations.query(Document).inCollection(COLLECTION_NAME).oneValue()
    loaded.keySet().stream().noneMatch(c -> c.contains("latestValue"))
  }
}
