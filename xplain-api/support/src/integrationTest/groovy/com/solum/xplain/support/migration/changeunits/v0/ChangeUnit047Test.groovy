package com.solum.xplain.support.migration.changeunits.v0

import static com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings.COMPANY_ENTITY_VALUATION_SETTINGS_COLLECTION
import static com.solum.xplain.core.company.entity.CompanyValuationSettings.COMPANY_VALUATION_SETTINGS_COLLECTION
import static com.solum.xplain.core.settings.entity.GlobalValuationSettings.GLOBAL_VALUATION_SETTINGS_COLLECTION

import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings
import com.solum.xplain.core.company.entity.CompanyValuationSettings
import com.solum.xplain.core.company.entity.ValuationSettings
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import jakarta.annotation.Resource
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit047Test extends IntegrationSpecification {

  private static final String STRIPPING_TYPE = "Single"
  private static final String DISCOUNTING_TYPE = "USD"

  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(GLOBAL_VALUATION_SETTINGS_COLLECTION)
    mongoTemplate.dropCollection(COMPANY_VALUATION_SETTINGS_COLLECTION)
    mongoTemplate.dropCollection(COMPANY_ENTITY_VALUATION_SETTINGS_COLLECTION)
  }

  def "should rename genericTradeDetails to customTradeDetails for trade read entities"() {
    setup:
    mongoTemplate.insert(new GlobalValuationSettings())
    mongoTemplate.insert(new GlobalValuationSettings(strippingType: STRIPPING_TYPE))
    mongoTemplate.insert(new Document()
      .append(ChangeUnit047.OLD_FIELD_NAME, STRIPPING_TYPE)
      .append(GlobalValuationSettings.Fields.discountingType, DISCOUNTING_TYPE), GLOBAL_VALUATION_SETTINGS_COLLECTION)

    mongoTemplate.insert(new CompanyValuationSettings())
    mongoTemplate.insert(new CompanyValuationSettings(strippingType: STRIPPING_TYPE))
    mongoTemplate.insert(new Document()
      .append(ChangeUnit047.OLD_FIELD_NAME, STRIPPING_TYPE)
      .append(ValuationSettings.Fields.discountingType, DISCOUNTING_TYPE), COMPANY_VALUATION_SETTINGS_COLLECTION)

    mongoTemplate.insert(new CompanyLegalEntityValuationSettings())
    mongoTemplate.insert(new CompanyLegalEntityValuationSettings(strippingType: STRIPPING_TYPE))
    mongoTemplate.insert(new Document()
      .append(ChangeUnit047.OLD_FIELD_NAME, STRIPPING_TYPE)
      .append(ValuationSettings.Fields.discountingType, DISCOUNTING_TYPE), COMPANY_ENTITY_VALUATION_SETTINGS_COLLECTION)

    when:
    new ChangeUnit047(mongoTemplate).beforeExecution()

    then:
    var globalResults = mongoTemplate.findAll(GlobalValuationSettings)
    globalResults.size() == 3
    globalResults[0].strippingType == null
    globalResults[0].discountingType == null
    globalResults[1].strippingType == STRIPPING_TYPE
    globalResults[1].discountingType == null
    globalResults[2].strippingType == STRIPPING_TYPE
    globalResults[2].discountingType == DISCOUNTING_TYPE

    var companyResults = mongoTemplate.findAll(CompanyValuationSettings)
    companyResults.size() == 3
    companyResults[0].strippingType == null
    companyResults[0].discountingType == null
    companyResults[1].strippingType == STRIPPING_TYPE
    companyResults[1].discountingType == null
    companyResults[2].strippingType == STRIPPING_TYPE
    companyResults[2].discountingType == DISCOUNTING_TYPE

    var entityResults = mongoTemplate.findAll(CompanyLegalEntityValuationSettings)
    entityResults.size() == 3
    entityResults[0].strippingType == null
    entityResults[0].discountingType == null
    entityResults[1].strippingType == STRIPPING_TYPE
    entityResults[1].discountingType == null
    entityResults[2].strippingType == STRIPPING_TYPE
    entityResults[2].discountingType == DISCOUNTING_TYPE
  }
}
