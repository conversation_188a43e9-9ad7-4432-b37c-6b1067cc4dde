package com.solum.xplain.support.migration.changeunits.v_1_12_0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CU03AddSequenceToBreakTestsTest extends IntegrationSpecification {
  static def SINGLE_VERSION = """
  {
    "_id": ObjectId("66ece7dcda0b915af8b7b905"),
    "_class": "com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest",
    "archived": false,
    "createdAt": ISODate("2024-09-20T03:11:24.519Z"),
    "enabled": true,
    "entityId": "66ece7dcda0b915af8b7b904",
    "name": "NULL",
    "recordDate": ISODate("2024-09-20T03:11:24.516Z"),
    "type": "NULL_VALUE"
  }"""
  static def SUPERSEDED_VERSION = """
  {
    "_id": ObjectId("66ed529020ab9d70ab1ce9a6"),
    "_class": "com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest",
    "archived": false,
    "createdAt": ISODate("2024-09-20T10:46:40.545Z"),
    "enabled": true,
    "entityId": "66ed529020ab9d70ab1ce9a5",
    "name": "Dod_GreeksOverlay I Old",
    "recordDate": ISODate("2024-09-20T10:46:40.544Z"),
    "type": "DAY_TO_DAY",
  }"""
  static def SUPERSEDING_VERSION = """
  {
    "_id": ObjectId("6717b421c4ba0616a842075e"),
    "_class": "com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest",
    "archived": false,
    "createdAt": ISODate("2024-10-22T14:18:09.613Z"),
    "enabled": true,
    "entityId": "66ed529020ab9d70ab1ce9a5",
    "name": "Dod_GreeksOverlay I New",
    "recordDate": ISODate("2024-10-22T14:18:09.607Z"),
    "scope": "OVERLAY_1",
    "type": "DAY_TO_DAY",
  }"""
  static def ANOTHER_SINGLE_VERSION = """
  {
    "_id": ObjectId("66ed529d20ab9d70ab1ce9ac"),  
    "_class": "com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest",
    "archived": false,
    "createdAt": ISODate("2024-09-20T10:46:53.163Z"),
    "enabled": true,
    "entityId": "66ed529d20ab9d70ab1ce9ab",
    "name": "DoD_SignOverlay I",
    "recordDate": ISODate("2024-09-20T10:46:53.162Z"),
    "scope": "OVERLAY_1",
    "type": "DAY_TO_DAY_SIGN",
  }
"""

  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection("ipvBreakTest")
    mongoTemplate.dropCollection("breakTest")
    mongoTemplate.dropCollection("onboardingBreakTest")
  }

  @Unroll
  def "should add sequence to break tests in #collectionName"() {
    setup:
    [SINGLE_VERSION, SUPERSEDED_VERSION, SUPERSEDING_VERSION, ANOTHER_SINGLE_VERSION].forEach {
      mongoTemplate.insert(it, collectionName)
    }
    def changeUnit = new CU03AddSequenceToBreakTests(mongoTemplate)

    when:
    changeUnit.execute()
    def singleVersion = mongoTemplate.findById("66ece7dcda0b915af8b7b905", Document, collectionName)
    def supersededVersion = mongoTemplate.findById("66ed529020ab9d70ab1ce9a6", Document, collectionName)
    def supersedingVersion = mongoTemplate.findById("6717b421c4ba0616a842075e", Document, collectionName)
    def anotherSingleVersion = mongoTemplate.findById("66ed529d20ab9d70ab1ce9ac", Document, collectionName)

    then:
    singleVersion.containsKey("sequence")
    singleVersion.getInteger("sequence") == 1
    !supersededVersion.containsKey("sequence")
    supersedingVersion.containsKey("sequence")
    supersedingVersion.getInteger("sequence") == 3
    anotherSingleVersion.containsKey("sequence")
    anotherSingleVersion.getInteger("sequence") == 2

    where:
    collectionName << ["ipvBreakTest", "breakTest", "onboardingBreakTest"]
  }

  def "should run with empty collection"() {
    def changeUnit = new CU03AddSequenceToBreakTests(mongoTemplate)

    expect:
    changeUnit.execute()
  }

  def "should run empty rollback"() {
    def changeUnit = new CU03AddSequenceToBreakTests(mongoTemplate)

    expect:
    changeUnit.rollback()
  }
}
