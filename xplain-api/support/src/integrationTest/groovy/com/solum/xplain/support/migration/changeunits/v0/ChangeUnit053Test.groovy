package com.solum.xplain.support.migration.changeunits.v0

import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.data.entity.IpvDataValue
import com.solum.xplain.core.providers.DataProvider
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit053Test extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(DataProvider)
    mongoTemplate.dropCollection(IpvDataValue)
  }

  def "should delete NOTIONAL provider"() {
    setup:
    mongoTemplate.insert(new DataProvider(externalId: "A"))
    mongoTemplate.insert(new DataProvider(externalId: NAV_PROVIDER_CODE))
    mongoTemplate.insert(new DataProvider(externalId: "NOTIONAL"))

    mongoTemplate.insert(new IpvDataValue(provider: "A"))
    mongoTemplate.insert(new IpvDataValue(provider: "NOTIONAL"))
    mongoTemplate.insert(new IpvDataValue(provider: NAV_PROVIDER_CODE))

    when:
    new ChangeUnit053(mongoTemplate).execute()

    then:
    def loadedProvider = mongoTemplate.findAll(DataProvider.class)
    loadedProvider.size() == 2
    loadedProvider[0].getExternalId() == "A"
    loadedProvider[1].getExternalId() == NAV_PROVIDER_CODE

    def loadedData = mongoTemplate.findAll(IpvDataValue.class)
    loadedData.size() == 2
    loadedData[0].getProvider() == "A"
    loadedData[1].getProvider() == NAV_PROVIDER_CODE
  }
}
