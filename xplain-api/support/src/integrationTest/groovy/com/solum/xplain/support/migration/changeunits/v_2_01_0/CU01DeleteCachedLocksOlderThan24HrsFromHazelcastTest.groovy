package com.solum.xplain.support.migration.changeunits.v_2_01_0

import com.solum.xplain.core.lock.CachedLockState
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.KeyValueCache
import java.util.function.Predicate
import spock.lang.Specification

@Deprecated(forRemoval = true) // Should remove in 2.2.x as this will be applied in 2.1.x release
class CU01DeleteCachedLocksOlderThan24HrsFromHazelcastTest extends Specification {

  def dataGrid = Mock(DataGrid)
  def keyValueCache = Mock(KeyValueCache)
  def migrationScript = new CU01DeleteCachedLocksOlderThan24HrsFromHazelcast(dataGrid)

  def setup() {
    dataGrid.getKeyValueCache("LOCK_STATE") >> keyValueCache
  }

  def "should call removeAll with correct predicate"() {
    given:
    def currentTime = System.currentTimeMillis()

    when:
    migrationScript.beforeExecution()

    then:
    1 * keyValueCache.removeAll(_) >> { Predicate<CachedLockState> predicate ->
      def oldEntry = Stub(CachedLockState) { obtained() >> currentTime - (25 * 60 * 60 * 1000) }
      def recentEntry = Stub(CachedLockState) { obtained() >> currentTime - (20 * 60 * 60 * 1000) }
      def nullEntry = Stub(CachedLockState) { obtained() >> null }

      assert predicate.test(oldEntry)
      assert !predicate.test(recentEntry)
      assert !predicate.test(nullEntry)
    }
  }
}
