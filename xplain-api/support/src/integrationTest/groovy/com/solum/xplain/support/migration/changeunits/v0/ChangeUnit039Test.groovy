package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit039Test extends IntegrationSpecification {
  @Resource
  MongoOperations mongoOperations

  def cleanup() {
    mongoOperations.dropCollection(IpvTasksDefinition)
  }

  def "should execute on empty db"() {
    when:
    def changeUnit = new ChangeUnit039(mongoOperations)
    changeUnit.execution()

    then:
    mongoOperations.findAll(IpvTasksDefinition).isEmpty()
  }

  def "should execute"() {
    setup:
    mongoOperations.insert(new IpvTasksDefinition())

    when:
    new ChangeUnit039(mongoOperations).execution()

    then:
    var definitions = mongoOperations.findAll(IpvTasksDefinition)
    definitions[0].type == IpvExceptionManagementPhase.OVERLAY_1
  }

  def "should create phase 2 definition"() {
    setup:
    mongoOperations.insert(new IpvTasksDefinition(type: IpvExceptionManagementPhase.OVERLAY_1, granularityByFxCcyPairType: TaskGranularityByFxCcyPairType.DOMESTIC_CCY))

    when:
    new ChangeUnit039(mongoOperations).execution()

    then:
    var definitions = mongoOperations.findAll(IpvTasksDefinition)
    definitions[0].type == IpvExceptionManagementPhase.OVERLAY_1
    definitions[1].type == IpvExceptionManagementPhase.OVERLAY_2
    definitions[0].granularityByFxCcyPairType == definitions[1].granularityByFxCcyPairType
  }
}
