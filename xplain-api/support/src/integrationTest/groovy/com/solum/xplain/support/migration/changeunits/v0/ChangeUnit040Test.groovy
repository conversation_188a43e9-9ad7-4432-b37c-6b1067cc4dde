package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit040Test extends IntegrationSpecification {

  @Resource MongoOperations mongoOperations

  def cleanup() {
    mongoOperations.dropCollection("curveStrippingDiscountSettings")
    mongoOperations.dropCollection("curveGroupInflationVolatility")
    mongoOperations.dropCollection("cleanMarketData")
    mongoOperations.dropCollection("instrumentResult")
    mongoOperations.dropCollection("ipvExceptionManagementResult")
    mongoOperations.dropCollection("leaveThisOneAlone")
  }

  def "should execute on empty db"() {
    given:
    def initCollections = mongoOperations.getCollectionNames()

    when:
    new ChangeUnit040(mongoOperations).beforeExecution()

    then:
    mongoOperations.getCollectionNames() == initCollections
  }

  def "should remove specific collections that exist"() {
    given:
    def initCollections = mongoOperations.collectionNames
    mongoOperations.createCollection("curveStrippingDiscountSettings")
    mongoOperations.createCollection("curveGroupInflationVolatility")
    mongoOperations.createCollection("cleanMarketData")
    mongoOperations.createCollection("instrumentResult")
    mongoOperations.createCollection("ipvExceptionManagementResult")
    mongoOperations.createCollection("leaveThisOneAlone")

    when:
    new ChangeUnit040(mongoOperations).beforeExecution()
    def collections = mongoOperations.collectionNames

    then:
    collections.size() == initCollections.size() + 1
    collections.containsAll(initCollections)
    collections.contains("leaveThisOneAlone")
  }
}
