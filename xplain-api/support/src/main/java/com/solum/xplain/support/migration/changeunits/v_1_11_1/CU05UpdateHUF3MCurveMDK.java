package com.solum.xplain.support.migration.changeunits.v_1_11_1;

import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationUpdate;
import org.springframework.data.mongodb.core.aggregation.SetOperation;
import org.springframework.data.mongodb.core.query.Query;

@AllArgsConstructor
@ChangeUnit(order = "v01.11.01_05", id = "v1.11.1_05", author = "xplain")
public class CU05UpdateHUF3MCurveMDK {

  private final MongoOperations mongoTemplate;

  @Execution
  public void execution() {
    // Execution logic goes here
  }

  @BeforeExecution
  public void beforeExecution() {
    updateField("marketDataKey", "key");
    updateField("marketDataKeyEntity", "semanticId");
  }

  public void updateField(String collectionName, String fieldName) {

    SetOperation setOperation =
        new SetOperation(
            fieldName,
            new Document(
                "$replaceAll",
                new Document("input", "$" + fieldName)
                    .append("find", "HUF_FIXED_3M_BUBOR_3M")
                    .append("replacement", "HUF-FIXED-3M-BUBOR-3M")));

    mongoTemplate.updateMulti(
        new Query(), AggregationUpdate.update().set(setOperation), collectionName);
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback logic goes here
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }
}
