package com.solum.xplain.support.setup.reset;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solum.xplain.support.setup.SetupProperties;
import com.solum.xplain.support.setup.reset.dto.DataSetupRequestFactory;
import io.mongock.runner.springboot.RunnerSpringbootBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.services.lambda.LambdaClient;

/**
 * Configures beans for the reset functionality. If reset is disabled then none of these beans will
 * be configured, and if an {@link SetupProperties.ResetProperties#getAwsEmulatorProxyUrl() AWS
 * Emulator Proxy URL} is set then no AWS {@link LambdaClient} bean will be configured, which will
 * then in turn mean we end up with a {@link LocalLambdaInvoker} instead of a {@link
 * AwsLambdaInvoker} in the context.
 */
@Configuration
@ConditionalOnProperty(prefix = "app.setup.reset", name = "enabled", havingValue = "true")
public class ResetConfig {

  @Bean
  ResetPermissionsProvider resetPermissionsProvider() {
    return new ResetPermissionsProvider();
  }

  @Bean
  @ConditionalOnMissingBean(RestTemplate.class)
  RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
    return restTemplateBuilder.build();
  }

  @Bean
  @ConditionalOnProperty(
      prefix = "app.setup.reset",
      name = "aws-emulator-proxy-url",
      havingValue = "missing", // Will never match, but will matchIfMissing
      matchIfMissing = true)
  LambdaClient awsLambda(SetupProperties setup) {
    return LambdaClient.builder()
        .credentialsProvider(DefaultCredentialsProvider.create())
        .region(setup.getReset().getAwsRegion())
        .build();
  }

  @Bean
  @ConditionalOnProperty(
      prefix = "app.setup.reset",
      name = "aws-emulator-proxy-url",
      havingValue = "missing", // Will never match, but will matchIfMissing
      matchIfMissing = true)
  AwsLambdaInvoker awsLambdaInvoker(
      SetupProperties setupProperties, LambdaClient awsLamba, ObjectMapper objectMapper) {
    return new AwsLambdaInvoker(setupProperties, awsLamba, objectMapper);
  }

  @Bean
  @ConditionalOnProperty(prefix = "app.setup.reset", name = "aws-emulator-proxy-url")
  LocalLambdaInvoker localLambdaInvoker(
      SetupProperties setupProperties, RestTemplate restTemplate) {
    return new LocalLambdaInvoker(setupProperties, restTemplate);
  }

  @Bean
  ResetControllerService resetControllerService(
      SetupLambdaInvoker setupLambdaInvoker,
      MongoCleardownService mongoCleardownService,
      DataSetupRequestFactory dataSetupRequestFactory,
      RunnerSpringbootBuilder mongockRunnerBuilder) {
    return new ResetControllerService(
        setupLambdaInvoker, mongoCleardownService, dataSetupRequestFactory, mongockRunnerBuilder);
  }
}
