package com.solum.xplain.support.permissions;

import static com.solum.xplain.core.permissions.UserType.ADMIN_USER;
import static com.solum.xplain.core.permissions.UserType.BASIC_USER;
import static com.solum.xplain.support.permissions.SupportPermissionCategory.CLEAN_UP;
import static com.solum.xplain.support.permissions.SupportPermissionCategory.MONITOR;

import com.solum.xplain.core.permissions.extension.XplainPermission;
import com.solum.xplain.core.permissions.provider.PermissionsProvider;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class SupportPermissionProvider implements PermissionsProvider {

  private static final XplainPermission RUN_DATABASE_CLEAN_UP =
      new XplainPermission(
          "RUN_DATABASE_CLEAN_UP", SupportAuthorities.RUN_DB_CLEANUP, ADMIN_USER, CLEAN_UP);
  private static final XplainPermission VIEW_LOCK_STATE =
      new XplainPermission(
          "VIEW_LOCK_STATE", SupportAuthorities.VIEW_LOCK_STATE, BASIC_USER, MONITOR);

  @Override
  public List<XplainPermission> availablePermissions() {
    return List.of(VIEW_LOCK_STATE, RUN_DATABASE_CLEAN_UP);
  }
}
