package com.solum.xplain.support.migration.changeunits;

import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

/** Utility methods for change units. */
@UtilityClass
public class ChangeUnitSupport {
  public static String indexName(String... fields) {
    return String.join("_", fields);
  }

  public static String uniqueIndexName(String... fields) {
    return String.join("_", indexName(fields), "unique");
  }

  public static boolean indexExists(
      MongoTemplate mongoTemplate, String collectionName, String indexName, String[] indexFields) {
    return mongoTemplate.indexOps(collectionName).getIndexInfo().stream()
        .anyMatch(
            indexInfo ->
                indexInfo.getName().equals(indexName)
                    || indexInfo.isIndexForFields(List.of(indexFields)));
  }

  public static void createIndex(
      MongoTemplate mongoTemplate,
      String collectionName,
      String indexName,
      Map<String, Sort.Direction> fields) {
    Index index = new Index().named(indexName);
    for (Map.Entry<String, Sort.Direction> field : fields.entrySet()) {
      index = index.on(field.getKey(), field.getValue());
    }

    mongoTemplate.indexOps(collectionName).ensureIndex(index);
  }

  public static void dropIfExists(
      MongoTemplate mongoTemplate, String collectionName, String indexName) {
    if (mongoTemplate.indexOps(collectionName).getIndexInfo().stream()
        .anyMatch(index -> index.getName().equals(indexName))) {
      mongoTemplate.indexOps(collectionName).dropIndex(indexName);
    }
  }
}
