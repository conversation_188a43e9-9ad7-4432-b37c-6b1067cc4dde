package com.solum.xplain.support.retention.explicit.mdk;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityMongoOperations;
import com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverterProvider;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateOperations;
import com.solum.xplain.core.market.MarketDataKey;
import com.solum.xplain.core.market.MarketDataKeyEntity;
import com.solum.xplain.core.market.MarketDataKeyValue;
import com.solum.xplain.core.market.mapping.MarketDataKeyMapper;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.support.retention.daterange.DateRangeEntitySupportRepository;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class MarketDataKeySupportRepository
    implements DateRangeEntitySupportRepository<MarketDataKeyValue, MarketDataKeyEntity> {

  private final EmbeddedVersionEntityMongoOperations<
          MarketDataKeyValue, MarketDataKeyEntity, MarketDataKey>
      operations;
  private final MongoOperations mongoOperations;

  public MarketDataKeySupportRepository(
      MongoOperations mongoOperations,
      MarketDataKeyMapper marketDataKeyMapper,
      AuditorAware<AuditUser> auditUserAuditorAware) {
    this.mongoOperations = mongoOperations;
    this.operations =
        new EmbeddedVersionEntityMongoOperations<>(
            MarketDataKeyEntity.class,
            MarketDataKey.class,
            mongoOperations,
            new DefaultEmbeddedVersionEntityToViewConverterProvider<>(
                marketDataKeyMapper::generateRead),
            new EntityUpdateOperations<>(auditUserAuditorAware),
            c -> {});
  }

  @Override
  public Stream<MarketDataKeyEntity> entitiesStream() {
    return mongoOperations.stream(new Query(), MarketDataKeyEntity.class);
  }

  @Override
  public void updateEntity(
      MarketDataKeyEntity entity, List<EmbeddedVersion<MarketDataKeyValue>> embeddedVersions) {
    operations.storeSingle(entity, embeddedVersions);
  }

  @Override
  public void deleteEntity(MarketDataKeyEntity entity) {
    mongoOperations.remove(entity);
    mongoOperations.remove(
        query(where(DateRangeVersionedEntity.Fields.entityId).is(entity.getId())),
        MarketDataKey.class);
  }
}
