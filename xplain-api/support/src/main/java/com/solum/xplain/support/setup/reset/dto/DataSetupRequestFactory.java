package com.solum.xplain.support.setup.reset.dto;

import com.solum.xplain.support.setup.SetupProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DataSetupRequestFactory {
  private final SetupProperties setupProperties;

  public DataSetupRequest createDataSetupRequest(
      String dataset, String authorization, String activitySession) {
    return new DataSetupRequest(
        dataset, setupProperties.getEnvironmentName(), authorization, activitySession);
  }
}
