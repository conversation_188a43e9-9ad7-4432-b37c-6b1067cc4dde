package com.solum.xplain.support.migration.changeunits.v_1_11_2;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;

import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@RequiredArgsConstructor
@ChangeUnit(order = "v01.11.02_01", id = "v1.11.2_01", author = "xplain")
public class CU01UpdateWorkflowIndexes {
  public static final String STEP_INSTANCE = "wfStepInstance";
  public static final String ROOT_BUSINESS_KEY = "rootBusinessKey";
  public static final String PROCESS_ID = "processId";
  public static final String STEP_ID = "stepId";
  public static final String REPORTABLE = "reportable";
  public static final String STATUS = "status";
  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    mongoTemplate
        .indexOps(STEP_INSTANCE)
        .ensureIndex(
            new Index()
                .named(indexName(ROOT_BUSINESS_KEY, PROCESS_ID, STEP_ID, REPORTABLE, STATUS))
                .on(ROOT_BUSINESS_KEY, Sort.Direction.ASC)
                .on(PROCESS_ID, Sort.Direction.ASC)
                .on(STEP_ID, Sort.Direction.ASC)
                .on(REPORTABLE, Sort.Direction.ASC)
                .on(STATUS, Sort.Direction.ASC));
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
