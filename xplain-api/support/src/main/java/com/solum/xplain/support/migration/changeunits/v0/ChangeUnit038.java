package com.solum.xplain.support.migration.changeunits.v0;

import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

@AllArgsConstructor
@ChangeUnit(order = "038", id = "38", author = "donatasz")
public class ChangeUnit038 {
  private final MongoOperations mongoOperations;

  @Execution
  public void execution() {
    mongoOperations.updateMulti(
        new Query(), Update.update(IpvBreakTest.Fields.scope, "OVERLAY_1"), IpvBreakTest.class);
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }
}
