package com.solum.xplain.support.retention.utils;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.groupingBy;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.support.retention.majoversioned.MajorVersionedEntityAggregateView;
import com.solum.xplain.support.retention.majoversioned.MajorVersionedEntityView;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class VersionValiditiesFactory {

  public static <V, T extends EmbeddedVersionEntity<V>>
      List<MajorVersion<EmbeddedVersion<V>>> forEntity(T entity) {
    return buildVersions(
        entity.getVersions().stream().collect(groupingBy(EmbeddedVersion::getValidFrom)),
        EmbeddedVersion::getRecordFrom,
        (v, t) -> new MinorVersion<>(v, v.getRecordFrom(), t, v.getState()));
  }

  public static List<MajorVersion<String>> forView(MajorVersionedEntityAggregateView view) {
    return buildVersions(
        view.getVersions().stream().collect(groupingBy(MajorVersionedEntityView::getValidFrom)),
        MajorVersionedEntityView::getRecordDate,
        (v, t) -> new MinorVersion<>(v.getId(), v.getRecordDate(), t, v.getState()));
  }

  private static <T, V> List<MajorVersion<T>> buildVersions(
      Map<LocalDate, List<V>> groupedByValidFrom,
      Function<V, LocalDateTime> toRecordDateF,
      BiFunction<V, LocalDateTime, MinorVersion<T>> toMinorVerF) {
    var allValidFromDatesSorted = groupedByValidFrom.keySet().stream().sorted().toList();
    var majors = ImmutableList.<MajorVersion<T>>builder();
    var currValidTo = LocalDate.MAX;
    for (var i = allValidFromDatesSorted.size() - 1; i >= 0; i--) {
      var currVersion = allValidFromDatesSorted.get(i);
      var allMinorsSorted =
          groupedByValidFrom.get(currVersion).stream().sorted(comparing(toRecordDateF)).toList();
      var minors = ImmutableList.<MinorVersion<T>>builder();
      var currRecordTo = LocalDateTime.MAX;
      for (var j = allMinorsSorted.size() - 1; j >= 0; j--) {
        var v = allMinorsSorted.get(j);
        minors.add(toMinorVerF.apply(v, currRecordTo));
        currRecordTo = toRecordDateF.apply(v);
      }
      majors.add(new MajorVersion<>(currVersion, currValidTo, minors.build()));
      currValidTo = currVersion;
    }
    return majors.build();
  }
}
