package com.solum.xplain.support.retention;

import com.solum.xplain.support.retention.value.DocumentValidities;
import com.solum.xplain.support.retention.value.RetentionProperties;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DatabaseCleanUpControllerService {
  private final MongoRetentionManager retentionManager;
  private final RetentionProperties properties;

  public DocumentValidities startCleanUp() {
    var validities = properties.resolveValidities(LocalDateTime.now());
    retentionManager.cleanOldData(validities);
    return validities;
  }
}
