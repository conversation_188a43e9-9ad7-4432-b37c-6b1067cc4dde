package com.solum.xplain.support.migration.changeunits.v_1_11_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;

import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@AllArgsConstructor
@ChangeUnit(order = "v01.11.00_05", id = "v1.11.0_05", author = "xplain")
public class CU05AddDealExceptionIndexes {

  public static final String SEMANTIC_ID = "semanticId";
  private final MongoTemplate mongoTemplate;

  public static final String DEAL_EXCEPTION = "dealException";
  public static final String DEAL_EXCEPTION_ENTITY = "dealExceptionEntity";
  public static final String STATE = "state";
  public static final String VALID_FROM = "validFrom";
  public static final String VALIDITIES_VALID_TO = "validities.validTo";
  public static final String VALIDITIES_RECORD_FROM = "validities.recordFrom";
  public static final String VALIDITIES_RECORD_TO = "validities.recordTo";
  public static final String EXTERNAL_COMPANY_ID = "externalCompanyId";
  public static final String EXTERNAL_ENTITY_ID = "externalEntityId";
  public static final String EXTERNAL_PORTFOLIO_ID = "externalPortfolioId";
  public static final String EXTERNAL_PORTFOLIO_ITEM_ID = "externalPortfolioItemId";

  @BeforeExecution
  public void beforeExecution() {
    // TradeLevelOverride (read repo)
    addDateRangeVersionedEntityIndex(DEAL_EXCEPTION);
    addGetTradeLevelOverridesCsvIndex();
    addExternalPortfolioItemIndex();

    // TradeLevelOverrideEntity (write repo)
    addEmbeddedVersionEntityMongoOperationsInsertIndex(DEAL_EXCEPTION_ENTITY);
  }

  private void addEmbeddedVersionEntityMongoOperationsInsertIndex(String collection) {
    mongoTemplate
        .indexOps(collection)
        .ensureIndex(new Index().named(indexName(SEMANTIC_ID)).on(SEMANTIC_ID, Direction.ASC));
  }

  private void addExternalPortfolioItemIndex() {
    mongoTemplate
        .indexOps(DEAL_EXCEPTION)
        .ensureIndex(
            new Index()
                .named(indexName(EXTERNAL_PORTFOLIO_ITEM_ID))
                .on(EXTERNAL_PORTFOLIO_ITEM_ID, Direction.ASC));
  }

  /**
   * Create indexes for @SortDefault() on TradeLevelOverrideController#getTradeLevelOverridesCsv()
   */
  private void addGetTradeLevelOverridesCsvIndex() {
    mongoTemplate
        .indexOps(DEAL_EXCEPTION)
        .ensureIndex(
            new Index()
                .named(
                    indexName(
                        EXTERNAL_COMPANY_ID,
                        EXTERNAL_ENTITY_ID,
                        EXTERNAL_PORTFOLIO_ID,
                        EXTERNAL_PORTFOLIO_ITEM_ID))
                .on(EXTERNAL_COMPANY_ID, Direction.ASC)
                .on(EXTERNAL_ENTITY_ID, Direction.ASC)
                .on(EXTERNAL_PORTFOLIO_ID, Direction.ASC)
                .on(EXTERNAL_PORTFOLIO_ITEM_ID, Direction.ASC));
  }

  /**
   * for activeTradeLevelOverrides() validFrom (-1), validities.validTo (-1), recordFrom (-1),
   * recordTo (-1), state (1)
   */
  private void addDateRangeVersionedEntityIndex(String collection) {
    mongoTemplate
        .indexOps(collection)
        .ensureIndex(
            new Index()
                .named(
                    indexName(
                        VALID_FROM,
                        VALIDITIES_VALID_TO,
                        VALIDITIES_RECORD_FROM,
                        VALIDITIES_RECORD_TO,
                        STATE))
                .on(VALID_FROM, Direction.DESC)
                .on(VALIDITIES_VALID_TO, Direction.ASC)
                .on(VALIDITIES_RECORD_FROM, Direction.DESC)
                .on(VALIDITIES_RECORD_TO, Direction.ASC)
                .on(STATE, Direction.ASC));
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
