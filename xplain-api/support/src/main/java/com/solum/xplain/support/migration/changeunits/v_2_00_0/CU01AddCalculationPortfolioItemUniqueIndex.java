package com.solum.xplain.support.migration.changeunits.v_2_00_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.uniqueIndexName;

import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

/**
 * Note. This change unit was originally v1.12.0_04, but that wasn't released in v1.12.0. For
 * clarity and as we're now changing it to only run if an equivalent index doesn't already exist,
 * it's been moved to v2.00.0_01. In some environments, this will result in both changesets having
 * attempted to run, but the second will be a no-op if that is the case.
 */
@RequiredArgsConstructor
@ChangeUnit(order = "v02.00.00_01", id = "v2.00.0_01", author = "xplain")
public class CU01AddCalculationPortfolioItemUniqueIndex {

  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    var hasIndex =
        mongoTemplate.indexOps("calculationPortfolioItem").getIndexInfo().stream()
            .anyMatch(
                index ->
                    index.isIndexForFields(List.of("calculationResultId", "tradeId"))
                        && index.isUnique());

    if (hasIndex) {
      return;
    }

    mongoTemplate
        .indexOps("calculationPortfolioItem")
        .ensureIndex(
            new Index()
                .named(uniqueIndexName("calculationResultId", "tradeId"))
                .on("calculationResultId", Direction.ASC)
                .on("tradeId", Direction.ASC)
                .unique());
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
