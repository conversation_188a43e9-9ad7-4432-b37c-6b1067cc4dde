package com.solum.xplain.support.retention.value;

import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
@ConfigurationProperties("app.retention")
public class RetentionProperties {
  @NotNull private final Integer majorVersionRetentionMonths;
  @NotNull private final Integer minorVersionRetentionMonths;
  @NotNull private final Duration workflowRetentionDuration;

  public DocumentValidities resolveValidities(LocalDateTime date) {
    var validTo = date.toLocalDate().minusMonths(majorVersionRetentionMonths);
    var recordTo = date.minusMonths(minorVersionRetentionMonths);
    return new DocumentValidities(validTo, recordTo);
  }
}
