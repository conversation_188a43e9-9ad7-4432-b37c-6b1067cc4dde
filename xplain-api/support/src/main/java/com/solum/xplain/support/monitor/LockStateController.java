package com.solum.xplain.support.monitor;

import static com.solum.xplain.support.permissions.SupportAuthorities.AUTHORITY_VIEW_LOCK_STATE;

import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/support/locks")
public class LockStateController {
  private final LockStateControllerService service;

  // TODO: add Sort parameter with default sort
  @Operation(
      summary =
          "Get list of exclusive data locks with their most recent state (locked or unlocked)")
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_LOCK_STATE)
  public List<LockStateView> list() {
    return service.list();
  }

  // TODO: add Sort parameter with default sort
  @Operation(summary = "Get list of all exclusive data locks previously obtained and released")
  @GetMapping("/history")
  @PreAuthorize(AUTHORITY_VIEW_LOCK_STATE)
  public List<LockStateView> history() {
    return service.history();
  }
}
