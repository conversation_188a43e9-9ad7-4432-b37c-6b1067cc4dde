package com.solum.xplain.support.migration.changeunits.v_1_11_0;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

@AllArgsConstructor
@ChangeUnit(order = "v01.11.00_01", id = "v1.11.0_01", author = "xplain")
public class CU01AddNewAllowAllCompaniesField {
  private final MongoOperations mongoOperations;

  @Execution
  public void execution() {
    mongoOperations.updateMulti(
        new Query(), Update.update("allowAllCompanies", false), "IpvDataGroup");
  }

  // Remove
  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }
}
