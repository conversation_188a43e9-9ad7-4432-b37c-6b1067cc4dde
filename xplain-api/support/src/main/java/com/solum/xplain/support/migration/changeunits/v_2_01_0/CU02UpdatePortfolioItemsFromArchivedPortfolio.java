package com.solum.xplain.support.migration.changeunits.v_2_01_0;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import io.mongock.api.annotations.*;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

/**
 * Updates All Portfolio Items with the archived date of the Portfolio if the portfolio is archived.
 *
 * <p>It is assumed that portfolio are never brought back to life. Archived portfolio are staying
 * archived forever.
 */
@RequiredArgsConstructor
@ChangeUnit(order = "v02.01.00_02", id = "v2.01.0_02", author = "xplain")
public class CU02UpdatePortfolioItemsFromArchivedPortfolio {

  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    // Before execution not needed
  }

  @Execution
  public void execution() {
    // Collect All Archived Portfolio.
    var archivedPortfolios = new ArrayList<Document>();
    mongoTemplate.executeQuery(
        new Query().addCriteria(where("archived").is(true)), "portfolio", archivedPortfolios::add);

    for (var archivedPortfolio : archivedPortfolios) {
      // Bulk update all related portfolio items with archived date.
      mongoTemplate.updateMulti(
          query(where("portfolioId").is(archivedPortfolio.getObjectId("_id"))),
          new Update()
              .set(
                  "portfolioArchivedAt",
                  archivedPortfolio.toBsonDocument().getDateTime("lastModifiedAt")),
          "portfolioItem");
      mongoTemplate.updateMulti(
          query(where("portfolioId").is(archivedPortfolio.getObjectId("_id"))),
          new Update()
              .set(
                  "portfolioArchivedAt",
                  archivedPortfolio.toBsonDocument().getDateTime("lastModifiedAt")),
          "portfolioItemEntity");
    }
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
