package com.solum.xplain.support.retention;

import static com.solum.xplain.support.permissions.SupportAuthorities.AUTHORITY_RUN_DB_CLEANUP;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.support.retention.value.DocumentValidities;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/support/clean-up")
public class DatabaseCleanUpController {
  private final DatabaseCleanUpControllerService service;

  @Operation(summary = "Start database clean up")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_RUN_DB_CLEANUP)
  public ResponseEntity<DocumentValidities> startCleanUp() {
    var result = service.startCleanUp();
    return ResponseEntity.ok(result);
  }
}
