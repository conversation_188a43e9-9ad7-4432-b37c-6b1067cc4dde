package com.solum.xplain.support.migration.changeunits.v0;

import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.CalculationResultCurves;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.core.common.value.ChartPoint;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

@AllArgsConstructor
@ChangeUnit(order = "045", id = "45", author = "donatasz")
public class ChangeUnit045 {
  static final String OLD_COLLECTION_NAME = "calculationResultCharts";

  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecute() {
    mongoTemplate.find(new Query(), CalculationResultsCharts.class, OLD_COLLECTION_NAME).stream()
        .map(this::convert)
        .filter(Objects::nonNull)
        .forEach(mongoTemplate::save);

    // Delete old collection
    mongoTemplate.dropCollection(OLD_COLLECTION_NAME);
  }

  private CalculationResultCurves convert(CalculationResultsCharts result) {
    var calculationResult = mongoTemplate.findById(result.getId(), CalculationResult.class);
    if (calculationResult == null) {
      return null;
    }

    CalculationResultCurves curves = new CalculationResultCurves();
    curves.setId(result.id);
    curves.setValuationDate(calculationResult.getValuationDate());
    curves.setCurveConfigurationCurves(convert(result.getCurveConfigurationChartData()));
    curves.setFxCurveConfigurationCurves(convert(result.getFxCurveConfigurationChartData()));
    return curves;
  }

  private List<CalibratedCurve> convert(List<CurveChartData> chartData) {
    if (chartData == null) {
      return null;
    }
    return chartData.stream().map(this::convertCurve).toList();
  }

  private CalibratedCurve convertCurve(CurveChartData chartData) {
    CalibratedCurve curve = new CalibratedCurve();
    curve.setName(chartData.getIndexName());
    curve.setYValueType(chartData.getValueType());
    curve.setChartPoints(chartData.getChartPoints());
    return curve;
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }

  @Execution
  public void execute() {
    // Execute not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @Data
  static class CalculationResultsCharts {
    @Id private ObjectId id;
    private List<CurveChartData> curveConfigurationChartData;
    private List<CurveChartData> fxCurveConfigurationChartData;
  }

  @Data
  static class CurveChartData {
    private String indexName;
    private String valueType;
    private List<ChartPoint> chartPoints;
  }
}
