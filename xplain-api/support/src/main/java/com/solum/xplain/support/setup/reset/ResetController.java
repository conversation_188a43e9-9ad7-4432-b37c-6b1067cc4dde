package com.solum.xplain.support.setup.reset;

import static com.solum.xplain.core.authentication.ActivitySessionInterceptor.ACTIVITY_SESSION;
import static com.solum.xplain.core.common.TryResponseEntity.tryResponse;
import static com.solum.xplain.support.SupportClassifiersProvider.RESET_DATASETS;
import static com.solum.xplain.support.permissions.SupportAuthorities.AUTHORITY_RUN_DB_RESET;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.market.validation.ValidClassifier;
import com.solum.xplain.support.setup.reset.dto.DataSetupResponse;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Validated
@RequiredArgsConstructor
@RequestMapping("/support/setup")
@ConditionalOnProperty(prefix = "app.setup.reset", name = "enabled", havingValue = "true")
public class ResetController {
  private final ResetControllerService resetControllerService;

  @Operation(summary = "Resets the database to a known initial dataset")
  @CommonErrors
  @PostMapping("/reset")
  @PreAuthorize(AUTHORITY_RUN_DB_RESET)
  public ResponseEntity<DataSetupResponse> resetDatabase(
      @CookieValue(ACTIVITY_SESSION) String activitySession,
      @RequestParam("dataset") @ValidClassifier(classifierId = RESET_DATASETS) String dataset,
      @RequestHeader(HttpHeaders.AUTHORIZATION) String authorization) {
    return tryResponse(
        resetControllerService.resetDatabase(dataset, authorization, activitySession));
  }

  @Operation(summary = "Erases the database")
  @CommonErrors
  @PostMapping("/erase")
  @PreAuthorize(AUTHORITY_RUN_DB_RESET)
  public ResponseEntity<DataSetupResponse> eraseDatabase() {
    return tryResponse(resetControllerService.eraseDatabase());
  }

  @Operation(summary = "Loads an initial dataset into the database without clearing it down first")
  @CommonErrors
  @PostMapping("/load")
  @PreAuthorize(AUTHORITY_RUN_DB_RESET)
  public ResponseEntity<DataSetupResponse> loadDataset(
      @CookieValue(ACTIVITY_SESSION) String activitySession,
      @RequestParam("dataset") @ValidClassifier(classifierId = RESET_DATASETS) String dataset,
      @RequestHeader(HttpHeaders.AUTHORIZATION) String authorization) {
    return tryResponse(resetControllerService.loadDataset(dataset, authorization, activitySession));
  }
}
