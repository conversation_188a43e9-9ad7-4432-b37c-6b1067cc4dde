package com.solum.xplain.support.migration.changeunits.v_2_02_0;

import com.mongodb.client.result.UpdateResult;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

@AllArgsConstructor
@ChangeUnit(order = "v02.02.00_05a", id = "v2.02.0_05a", author = "xplain")
@Slf4j
public class CU05AddNewNavLevelSettingType {
  private final MongoOperations mongoOperations;

  @Execution
  public void execution() {
    UpdateResult updateResult =
        mongoOperations.updateMulti(
            new Query(), Update.update("navLevel", "TRADE_LEVEL"), "exceptionManagementSettings");
    log.info(
        "Updated {} documents to add new navLevel setting type", updateResult.getModifiedCount());
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }
}
