package com.solum.xplain.calculation.simulation.daterange.integration

import static com.solum.xplain.core.sockets.events.EventType.SIMULATION_PROGRESS_UPDATED

import com.solum.xplain.calculation.CalculationTradesStatistics
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationCalculation
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem
import com.solum.xplain.calculation.simulation.daterange.events.DateRangeSimulationFinishedEvent
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository
import com.solum.xplain.calculation.value.InProgressValuationStatus
import com.solum.xplain.core.sockets.constants.CoreSocketEvents
import com.solum.xplain.extensions.simulations.SimulationType
import com.solum.xplain.valuation.messages.metrics.ValuationResponse
import com.solum.xplain.valuation.messages.metrics.ValuationStatus
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class DateRangeSimulationResponseHandlerTest extends Specification {
  static SIMULATION_ID = "simulationId"
  static CALCULATION_ID = "calculationId"

  DateRangeSimulationCalculationRepository simulationRepository = Mock()
  CalculationTradeCountHolder calculationTradeCountHolder = Mock()
  CalibrationCacheService cacheService = Mock()
  ApplicationEventPublisher eventPublisher = Mock()

  DateRangeSimulationResponseHandler listener = new DateRangeSimulationResponseHandler(simulationRepository, calculationTradeCountHolder, cacheService, eventPublisher)

  def "should handle simulation response"() {
    setup:
    def simulationCalculation = Mock(DateRangeSimulationCalculation)
    simulationCalculation.id >> SIMULATION_ID
    simulationCalculation.tradesStatistics >> new CalculationTradesStatistics(failedCount: 0, totalCount: 1)
    def simulationResponse = new ValuationResponse(
      simulationId: SIMULATION_ID,
      simulationType: SimulationType.DATE_RANGE,
      calculationId: CALCULATION_ID,
      valuationDate: LocalDate.now(),
      status: ValuationStatus.ERROR,
      errorMessage: "ERROR"
      )

    def simulationError = DateRangeSimulationErrorItem.ofCalculationError(SIMULATION_ID, LocalDate.now(), "ERROR")

    when:
    listener.handle(SIMULATION_ID, [simulationResponse])

    then:
    1 * simulationRepository.getSimulation(SIMULATION_ID) >> Either.right(simulationCalculation)
    1 * simulationRepository.saveSimulationErrors(SIMULATION_ID, [simulationError])
    1 * calculationTradeCountHolder.allTradesProcessed(SIMULATION_ID, 1) >> true
    1 * calculationTradeCountHolder.updateFailureCount(SIMULATION_ID, 1) >> 1
    1 * eventPublisher.publishEvent(new DateRangeSimulationFinishedEvent(SIMULATION_ID))

    1 * calculationTradeCountHolder.allTradesProcessed(CALCULATION_ID, 1) >> true
    1 * calculationTradeCountHolder.updateFailureCount(CALCULATION_ID, 1) >> 1
    1 * cacheService.cleanCalculationCache(CALCULATION_ID)
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID)

    1 * calculationTradeCountHolder.remainingUnprocessedTrades(SIMULATION_ID) >> 0
    var status = InProgressValuationStatus.statusOf(SIMULATION_ID, 1, 0)
    eventPublisher.publishEvent(
      CoreSocketEvents.simulationInProgress(SIMULATION_PROGRESS_UPDATED, status))
  }

  def "should handle simulation response without errors"() {
    setup:
    def simulationCalculation = Mock(DateRangeSimulationCalculation)
    simulationCalculation.id >> SIMULATION_ID
    simulationCalculation.tradesStatistics >> new CalculationTradesStatistics(failedCount: 0, totalCount: 1)
    def simulationResponse = new ValuationResponse(
      simulationId: SIMULATION_ID,
      simulationType: SimulationType.DATE_RANGE,
      calculationId: CALCULATION_ID,
      valuationDate: LocalDate.now(),
      status: ValuationStatus.OK,
      )

    when:
    listener.handle(SIMULATION_ID, [simulationResponse])

    then:
    1 * simulationRepository.getSimulation(SIMULATION_ID) >> Either.right(simulationCalculation)
    1 * simulationRepository.saveSimulationErrors(SIMULATION_ID, [])
    1 * calculationTradeCountHolder.allTradesProcessed(SIMULATION_ID, 1) >> true
    1 * eventPublisher.publishEvent(new DateRangeSimulationFinishedEvent(SIMULATION_ID))

    1 * calculationTradeCountHolder.allTradesProcessed(CALCULATION_ID, 1) >> true
    1 * cacheService.cleanCalculationCache(CALCULATION_ID)
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID)

    1 * calculationTradeCountHolder.remainingUnprocessedTrades(SIMULATION_ID) >> 0
    var status = InProgressValuationStatus.statusOf(SIMULATION_ID, 1, 0)
    eventPublisher.publishEvent(
      CoreSocketEvents.simulationInProgress(SIMULATION_PROGRESS_UPDATED, status))
  }
}
