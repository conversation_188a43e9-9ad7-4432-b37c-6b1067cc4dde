package com.solum.xplain.calculation.discounting

import com.solum.xplain.calibration.rates.CalibrationCombinedResultRates
import com.solum.xplain.calibration.rates.CurvesCalibration
import com.solum.xplain.calibration.rates.group.DiscountingGroup
import com.solum.xplain.calibration.rates.group.DiscountingGroups
import com.solum.xplain.calibration.rates.set.CalibrationBundle
import spock.lang.Specification

class ResolvedDiscountingCalibrationBundlesTestGroup extends Specification {

  def "should resolve discount curves"() {
    setup:
    def baseDsc = Mock(DiscountingGroup)
    def bundle = Mock(CalibrationBundle)
    def bundles = [(baseDsc): bundle]

    1 * bundle.discountCurves() >> ["DSC"]

    def group = new ResolvedDiscountingCalibrationBundles(null, bundles)

    when:
    def res = group.discountCurves()

    then:
    res == ["DSC"]
  }

  def "should resolve discountings"() {
    setup:
    def discountings = Mock(DiscountingGroups)

    def group = new ResolvedDiscountingCalibrationBundles(discountings, null)

    when:
    def res = group.discountings()

    then:
    res == discountings
  }

  def "should resolve calibrate"() {
    setup:
    def baseDsc = Mock(DiscountingGroup)
    def bundle = Mock(CalibrationBundle)
    def bundles = [(baseDsc): bundle]

    def expectedRes = Mock(CalibrationCombinedResultRates)
    def calibration = Mock(CurvesCalibration)
    1 * calibration.calibrate(bundle, null, true, false) >> expectedRes

    def group = new ResolvedDiscountingCalibrationBundles(null, bundles)

    when:
    def res = group.calibrate(calibration, false)

    then:
    res == [(baseDsc): expectedRes]
  }
}
