package com.solum.xplain.calculation.curvegroup

import com.opengamma.strata.data.MarketData
import com.solum.xplain.calculation.value.CalculationCalibrationSettings
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import java.time.LocalDate
import java.util.function.UnaryOperator
import spock.lang.Specification

class CalculationCurveGroupDataBuildersHolderTest extends Specification {

  CalculationCurveGroupDataBuilder NON_FX = Mock()
  CalculationCurveGroupDataBuilder FX = Mock()
  CalculationCalibrationSettings SETTINGS = Mock()
  CalculationCurveGroupData RESULT = Mock()

  def "should get builders"() {
    setup:
    def holder = new CalculationCurveGroupDataBuildersHolder(NON_FX, FX, SETTINGS)

    when:
    def result = holder.builders()

    then:
    result == [NON_FX, FX]
  }

  def "should test warnings"() {
    setup:
    def holder = new CalculationCurveGroupDataBuildersHolder(NON_FX, FX, SETTINGS)

    when:
    holder.addWarning(Error.CALCULATION_WARNING.entity("W1"))
    holder.addWarning(Error.CALCULATION_WARNING.entity("W2"))
    holder.addWarning(Error.CALCULATION_WARNING.entity("W2"))
    holder.addWarnings([Error.CALIBRATION_WARNING.entity("W1")])
    holder.addWarnings([Error.CALIBRATION_WARNING.entity("W1"), Error.CALIBRATION_WARNING.entity("W2")])

    then:
    holder.warnings() == [
      Error.CALCULATION_WARNING.entity("W1"),
      Error.CALCULATION_WARNING.entity("W2"),
      Error.CALIBRATION_WARNING.entity("W1"),
      Error.CALIBRATION_WARNING.entity("W2"),
    ]
  }

  def "should test calculate fx"() {
    setup:
    UnaryOperator<ErrorItem> errorConsumer = { e -> e }
    def holder = new CalculationCurveGroupDataBuildersHolder(null, FX, SETTINGS)
    FX.calculate(SETTINGS, MarketData.empty(LocalDate.now()), errorConsumer) >> RESULT

    when:
    def result = holder.calculateFx(MarketData.empty(LocalDate.now()), errorConsumer)

    then:
    result == RESULT
  }

  def "should test calculate fx empty"() {
    setup:
    UnaryOperator<ErrorItem> errorConsumer = { e -> e }
    def holder = new CalculationCurveGroupDataBuildersHolder(null, null, SETTINGS)

    when:
    def result = holder.calculateFx(MarketData.empty(LocalDate.now()), errorConsumer)

    then:
    result == null
  }

  def "should test calculate nonfx"() {
    setup:
    UnaryOperator<ErrorItem> errorConsumer = { e -> e }
    def holder = new CalculationCurveGroupDataBuildersHolder(NON_FX, null, SETTINGS)
    NON_FX.calculate(SETTINGS, MarketData.empty(LocalDate.now()), errorConsumer) >> RESULT

    when:
    def result = holder.calculate(MarketData.empty(LocalDate.now()), errorConsumer)

    then:
    result == RESULT
  }

  def "should fetch discount curves"() {
    setup:
    1 * NON_FX.discountCurves() >> ["DSC"]
    def holder = new CalculationCurveGroupDataBuildersHolder(NON_FX, FX, SETTINGS)

    when:
    def result = holder.discountCurves()

    then:
    result == ["DSC"]
  }

  def "should fetch fx discount curves"() {
    setup:
    1 * FX.discountCurves() >> ["DSC"]

    when:
    def result = new CalculationCurveGroupDataBuildersHolder(NON_FX, FX, SETTINGS).fxDiscountCurves()

    then:
    result == ["DSC"]

    when:
    def emptyResult = new CalculationCurveGroupDataBuildersHolder(NON_FX, null, SETTINGS).fxDiscountCurves()

    then:
    emptyResult == []
  }
}
