package com.solum.xplain.calculation.simulation.daterange

import com.solum.xplain.calculation.CalculationPortfolioValidator
import com.solum.xplain.calculation.simulation.data.SimulationDataFactory
import com.solum.xplain.calculation.simulation.daterange.data.DateRangeSimulationCalculationData
import com.solum.xplain.calculation.simulation.daterange.events.DateRangeSimulationRequestedEvent
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationForm
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.team.UserTeamEntity
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.portfolio.PortfolioBuilder
import com.solum.xplain.core.portfolio.PortfolioViewBuilder
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class DateRangeSimulationsServiceTest extends Specification {

  static VALUATION_START_DATE = LocalDate.now()
  static VALUATION_END_DATE = LocalDate.now().plusDays(1)
  static CURVE_DATE = LocalDate.ofEpochDay(0)
  static STATE_DATE = BitemporalDate.newOfNow()
  static PORTFOLIO_VIEW = PortfolioViewBuilder.of(PortfolioBuilder.portfolio())
  static AUDIT_USER = AuditUser.of(UserBuilder.user())

  AuthenticationContext authenticationContext = Mock()
  SimulationDataFactory calculationDataService = Mock()
  CalculationPortfolioValidator calculationPortfolioValidator = Mock()
  ApplicationEventPublisher eventPublisher = Mock()

  DateRangeSimulationsService service = new DateRangeSimulationsService(authenticationContext, calculationDataService, calculationPortfolioValidator, eventPublisher)

  def "should calculate simulation results"() {
    setup:
    def simulationData = Mock(DateRangeSimulationCalculationData)
    simulationData.simulationId >> ObjectId.get()

    def calculationForm = Mock(DateRangeSimulationCalculationForm)
    calculationForm.getName() >> "Name"
    calculationForm.getValuationStartDate() >> VALUATION_START_DATE
    calculationForm.getValuationEndDate() >> VALUATION_END_DATE
    calculationForm.getCurveDate() >> CURVE_DATE

    def user = UserBuilder.user()
    var event =
      new DateRangeSimulationRequestedEvent(
      "Name",
      VALUATION_START_DATE,
      VALUATION_END_DATE,
      STATE_DATE,
      simulationData,
      AUDIT_USER)
    authenticationContext.currentUser() >> user

    def portfolioView = UserTeamEntity.userEntity(user, PORTFOLIO_VIEW)

    when:
    service.calculateResults(portfolioView, calculationForm, STATE_DATE)

    then:
    1 * calculationPortfolioValidator.validateTrades(PORTFOLIO_VIEW, STATE_DATE) >> Either.right(PORTFOLIO_VIEW)
    1 * calculationDataService.simulationCalculationData(PORTFOLIO_VIEW, VALUATION_START_DATE, CURVE_DATE, STATE_DATE, calculationForm) >> Either.right(simulationData)
    1 * eventPublisher.publishEvent(event)
  }
}
