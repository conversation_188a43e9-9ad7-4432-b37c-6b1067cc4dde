package com.solum.xplain.calculation.simulation.daterange

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.team.EntityTeamFilter.filter
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.events.CalculationDeletedEvent
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.simulation.SimulationControllerService
import com.solum.xplain.calculation.simulation.ccyexposure.entity.CcyExposureSimulationCalculation
import com.solum.xplain.calculation.simulation.ccyexposure.repository.CcyExposureSimulationCalculationRepository
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationCalculation
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.TableFilter
import java.time.LocalDate
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class SimulationControllerServiceTest extends Specification {
  DateRangeSimulationCalculationRepository repository = Mock()
  CcyExposureSimulationCalculationRepository ccyExposureRepository = Mock()
  AuthenticationContext authenticationContext = Mock()
  CalculationResultRepository calculationResultRepository = Mock()
  ApplicationEventPublisher applicationEventPublisher = Mock()

  SimulationControllerService service = new SimulationControllerService(repository, ccyExposureRepository, authenticationContext, calculationResultRepository, applicationEventPublisher)

  def "should get simulation results"() {
    setup:
    def user = UserBuilder.user()
    def scrollRequest = Mock(ScrollRequest)
    def tableFilter = Mock(TableFilter)
    def teamFilter = filter(user)
    1 * authenticationContext.currentUser() >> user
    1 * repository.simulationsScrollable(scrollRequest, tableFilter, teamFilter) >> ScrollableEntry.empty()

    expect:
    service.getDateRangeSimulationCalculations(scrollRequest, tableFilter) == ScrollableEntry.empty()
  }

  def "should delete simulation calculation"() {
    setup:
    def user = UserBuilder.user()
    def teamFilter = filter(user)
    def simulation = Mock(DateRangeSimulationCalculation)
    simulation.getId() >> "id"
    1 * authenticationContext.currentUser() >> user
    1 * repository.getUserSimulation("id", teamFilter) >> right(simulation)
    1 * repository.deleteSimulation("id") >> right(entityId("id"))

    expect:
    service.deleteDateRangeSimulationCalculation("id") == right(entityId("id"))
  }

  def "should delete all simulation"() {
    setup:
    def user = UserBuilder.user()
    def teamFilter = filter(user)
    def simulation = Mock(DateRangeSimulationCalculation)
    simulation.getId() >> "id"
    1 * authenticationContext.currentUser() >> user
    1 * repository.deleteSimulations(teamFilter) >> [entityId("id")]

    expect:
    service.deleteDateRangeSimulations() == [entityId("id")]
  }

  def "should delete currency exposure simulation and results calculation by id"() {
    setup:
    def exposureSimulationId = ObjectId.get().toString()
    def calcResultId = ObjectId.get()
    def simulation = Mock(CcyExposureSimulationCalculation)
    simulation.getId() >> exposureSimulationId
    def user = user()
    def teamFilter = filter(user)

    1 * authenticationContext.currentUser() >> user
    1 * ccyExposureRepository.getUserSimulation(exposureSimulationId, teamFilter) >> right(simulation)
    1 * ccyExposureRepository.deleteSimulation(exposureSimulationId) >> right(exposureSimulationId)
    1 * calculationResultRepository.calculationResultsForCcyExposureSimulationCalculation(exposureSimulationId) >> [new CalculationResult(id: calcResultId)]

    when:
    def result = service.deleteCcyExposureSimulationCalculation(exposureSimulationId)

    then:
    result.isRight()
    def right = result.right().get() as EntityId
    right.id == exposureSimulationId

    and: "cascades to calculation results"
    1 * applicationEventPublisher.publishEvent({ CalculationDeletedEvent it ->
      assert it.calculationId == calcResultId
    })
  }

  def "should return simulation errors"() {
    setup:
    def user = UserBuilder.user()
    def scrollRequest = Mock(ScrollRequest)
    def teamFilter = filter(user)
    def simulation = Mock(DateRangeSimulationCalculation)
    simulation.getId() >> "id"
    1 * authenticationContext.currentUser() >> user
    1 * repository.getUserSimulation("id", teamFilter) >> right(simulation)
    1 * repository.simulationErrorItemsScrollable("id", scrollRequest) >> ScrollableEntry.empty()

    expect:
    service.dateRangeSimulationErrorsScroll("id", scrollRequest) == right(ScrollableEntry.empty())
  }

  def "should return simulation errors csv"() {
    setup:
    def user = UserBuilder.user()
    def teamFilter = filter(user)
    def simulation = Mock(DateRangeSimulationCalculation)
    simulation.getId() >> "id"
    1 * authenticationContext.currentUser() >> user
    1 * repository.getUserSimulation("id", teamFilter) >> right(simulation)
    1 * repository.simulationErrorsStream("id") >> Stream.of(DateRangeSimulationErrorItem.ofCalculationError("ID", LocalDate.ofEpochDay(0), "REASON"))

    when:
    def result = service.dateRangeSimulationErrorsCsv("id")

    then:
    result.isRight()
    def resultString = new String(result.getOrNull().getBytes().byteArray)
    resultString == "Valuation Date,Reason,Description\n1970-01-01,CALCULATION_ERROR,REASON\n"
  }
}
