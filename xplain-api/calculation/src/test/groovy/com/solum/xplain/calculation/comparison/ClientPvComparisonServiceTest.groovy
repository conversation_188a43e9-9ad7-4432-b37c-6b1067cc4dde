package com.solum.xplain.calculation.comparison

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.FxRateProvider
import com.solum.xplain.calculation.CalculationPortfolioItemViewBuilder
import com.solum.xplain.calculation.comparison.entity.ClientPvComparisonResult
import com.solum.xplain.calculation.comparison.entity.ClientPvComparisonResultItem
import com.solum.xplain.calculation.comparison.repository.ClientPvComparisonRepository
import com.solum.xplain.calculation.curves.value.CalculationResultsChartsView
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.value.CalculationResultView
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import spock.lang.Specification

class ClientPvComparisonServiceTest extends Specification {
  ComparisonFxRateProvider fxRateProvider = Mock()
  ClientPvComparisonRepository comparisonRepository = Mock()
  CalculationResultRepository resultRepository = Mock()

  ClientPvComparisonService service = new ClientPvComparisonService(fxRateProvider, comparisonRepository, resultRepository)


  def "should compare items against client pv"() {
    setup:
    def calculationResultId = ObjectId.get()
    var resultItem = CalculationPortfolioItemViewBuilder.ofMetrics()
    1 * resultRepository.calculationItems(calculationResultId.toString()) >> Flux.fromIterable([resultItem])
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.right(new FxRateProvider() {
        @Override
        double fxRate(Currency baseCurrency, Currency counterCurrency) {
          return 10
        }
      }
      )
    resultRepository.calculationCurves(calculationResultId.toString()) >> new CalculationResultsChartsView(
      curveConfigurationChartData: [],
      fxCurveConfigurationChartDataView: []
      )
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compareWithClientPv(Currency.of("EUR"), view)
    result.isRight()
    then:
    1 * comparisonRepository.saveClientPvComparison(_ as List<ClientPvComparisonResultItem>) >> { List<?> items ->
      def results = items[0] as List<ClientPvComparisonResultItem>
      assert results.size() == 1
      assert results[0].view.metricsPresentValue == 10
      assert results[0].view.metricsClientMetricsPresentValue == 130
      assert results[0].view.metricsPVDiffClientPV == -120
      assert results[0].view.absPVDiffClientPV == 120
      assert results[0].view.metricsSens01 == 2.0
      return Mono.empty()
    }

    1 * comparisonRepository.saveClientPvComparisonResult(_ as ClientPvComparisonResult) >> { ClientPvComparisonResult r ->
      r.comparisonCurrency == "EUR"
      r.calculationId == calculationResultId.toString()
    }
  }
}
