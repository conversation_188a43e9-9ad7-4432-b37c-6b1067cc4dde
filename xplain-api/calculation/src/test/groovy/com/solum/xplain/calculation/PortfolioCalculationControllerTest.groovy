package com.solum.xplain.calculation

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.form.PortfolioCalculationForm
import com.solum.xplain.calculation.helpers.MockMvcConfiguration
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.lock.XplainLock
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PortfolioCalculationController])
class PortfolioCalculationControllerTest extends Specification {

  static LocalDate STATE_DATE = LocalDate.now()
  static String PORTFOLIO_ID = "000000000000000000000001"

  @SpringBean
  CalculationResultControllerService service = Mock()
  @SpringBean
  CalculationService calculationService = Mock()
  @SpringBean
  ResourceValidationService resourceValidationService = Mock()
  @SpringBean
  CompanyMarketDataGroupValidator mdValidator = Mock()
  @SpringBean
  CurveGroupRepository curveGroupRepository = Mock()
  @SpringBean
  CurveConfigurationRepository configurationRepository = Mock()
  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  PortfolioRepository portfolioRepository = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should perform portfolio calculation with form #form"() {
    setup:
    calculationService.calculateResults(
    _ as Authentication,
    _ as String, {
      it.priceRequirements() == bidRequirements()
    } as PortfolioCalculationForm,
    _ as BitemporalDate) >> right(entityId("1"))
    curveGroupRepository.getEither(_ as String) >> right(new CurveGroupView())
    portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(new PortfolioView(companyId: "000000000000000000000000"))
    mdValidator.isMdAllowedForCompany("marketDataId", "000000000000000000000000") >> true
    mdValidator.isMdAllowedForCompany(_ as String, _ as String) >> left(OBJECT_NOT_FOUND.entity()) >> false
    configurationRepository.validCurveConfigurationId(_ as String) >> true
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }
    when:
    def results = mockMvc.perform(post("/portfolio/{id}/calculation", PORTFOLIO_ID)
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:

    results != null
    results.getResponse().getStatus() == code
    results.getResponse().getContentAsString().contains(responseBody)
    where:
    form                                                                                                                           | code | responseBody
    form()                                                                                                                         | 200  | "id"
    form(c -> c.remove("calculationCurrency"))                                                                                     | 412  | "NotEmpty.portfolioCalculationForm.calculationCurrency"
    form(c -> c.remove("curveDiscountingForm"))                                                                                    | 412  | "NotNull.portfolioCalculationForm.curveDiscountingForm"
    form(c -> c.remove("valuationDate"))                                                                                           | 412  | "NotNull.portfolioCalculationForm.valuationDate"
    form(c -> c.remove("curveConfiguration"))                                                                                      | 412  | "NotNull.portfolioCalculationForm.curveConfiguration"
    form(c -> c.remove("configurationType"))                                                                                       | 412  | "NotEmpty.portfolioCalculationForm.configurationType"
    form(c -> c.put("configurationType", "random"))                                                                                | 412  | "ValidStringSet.portfolioCalculationForm.configurationType"
    form(c -> c.put("configurationType", "FX_V_IRS"))                                                                              | 412  | "NotNull.portfolioCalculationForm.nonFxCurveConfiguration"
    form(c -> c.put("curveConfiguration", new CalculationConfigForm(null, null)))                                                  | 412  | "NotNull.portfolioCalculationForm.curveConfiguration.configurationId"
    form(c -> c.remove("marketDataSource"))                                                                                        | 412  | "NotEmpty.portfolioCalculationForm.marketDataSource"
    form(c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(null, OIS.name(), USD.getCode(), false)))               | 412  | "NotEmpty.portfolioCalculationForm.curveDiscountingForm.discountingType"
    form(c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(DISCOUNT_EUR.name(), null, USD.getCode(), false)))      | 412  | "NotEmpty.portfolioCalculationForm.curveDiscountingForm.strippingType"
    form(c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), null, false)))         | 412  | "NotEmpty.portfolioCalculationForm.curveDiscountingForm.triangulationCcy"
    form(c -> c.put("curveDiscountingForm", new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), null))) | 200  | "id"
    form(c -> c.put("priceRequirements", new InstrumentPriceRequirementsForm()))                                                   | 200  | "id"
    form(c -> c.put("priceRequirements", new InstrumentPriceRequirementsForm(volsPriceType: InstrumentPriceType.BID_PRICE)))       | 200  | "id"
  }

  def form(Closure c = { a -> a }) {
    [
      valuationDate       : LocalDate.now(),
      curveDate           : LocalDate.now(),
      stateDate           : STATE_DATE,
      calculationCurrency : "EUR",
      curveDiscountingForm: new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), false),
      configurationType   : "SINGLE",
      curveConfiguration  : new CalculationConfigForm("id", null),
      marketDataGroupId   : "marketDataId",
      marketDataSource    : "RAW_PRIMARY",
    ].tap(c)
  }
}
