package com.solum.xplain.calculation.simulation

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.helpers.MockMvcConfiguration
import com.solum.xplain.calculation.simulation.ccyexposure.repository.CcyExposureSimulationCalculationRepository
import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureSimulationCalculationForm
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationForm
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PortfolioSimulationsController])
class PortfolioSimulationsControllerTest extends Specification {

  static String PORTFOLIO_ID = "000000000000000000000001"
  static LocalDate STATE_DATE = LocalDate.ofEpochDay(0)

  @SpringBean
  SimulationsService simulationService = Mock()
  @SpringBean
  ResourceValidationService resourceValidationService = Mock()
  @SpringBean
  CompanyMarketDataGroupValidator mdValidator = Mock()
  @SpringBean
  CurveGroupRepository curveGroupRepository = Mock()
  @SpringBean
  CurveConfigurationRepository configurationRepository = Mock()
  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  PortfolioRepository portfolioRepository = Mock()
  @SpringBean
  DateRangeSimulationCalculationRepository simulationRepository = Mock()
  @SpringBean
  CcyExposureSimulationCalculationRepository ccyExposureSimulationRepository = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should perform portfolio date-range simulation with response #responseBody"() {
    setup:
    simulationService.calculateResults(PORTFOLIO_ID, _ as DateRangeSimulationCalculationForm, _ as BitemporalDate) >> right(entityId("1"))
    curveGroupRepository.getEither(_ as String) >> right(new CurveGroupView())
    portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(new PortfolioView(companyId: "000000000000000000000000"))
    mdValidator.isMdAllowedForCompany("marketDataId", "000000000000000000000000") >> true
    mdValidator.isMdAllowedForCompany(_ as String, _ as String) >> left(OBJECT_NOT_FOUND.entity()) >> false
    configurationRepository.validCurveConfigurationId(_ as String) >> true
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }
    simulationRepository.simulationNameExists("EXISTENT") >> true
    0 * ccyExposureSimulationRepository.simulationNameExists(_)
    when:
    def results = mockMvc.perform(post("/portfolio/{id}/simulations", PORTFOLIO_ID)
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      status == code
      contentAsString.contains(responseBody)
    }
    where:
    form                                                                          | code | responseBody
    form()                                                                        | 200  | "id"
    form(c -> c.remove("name"))                                                   | 412  | "NotEmpty.dateRangeSimulationCalculationForm.name"
    form(c -> c.remove("calculationCurrency"))                                    | 412  | "NotEmpty.dateRangeSimulationCalculationForm.calculationCurrency"
    form(c -> c.remove("curveDiscountingForm"))                                   | 412  | "NotNull.dateRangeSimulationCalculationForm.curveDiscountingForm"
    form(c -> c.remove("valuationStartDate"))                                     | 412  | "NotNull.dateRangeSimulationCalculationForm.valuationStartDate"
    form(c -> c.remove("valuationEndDate"))                                       | 412  | "NotNull.dateRangeSimulationCalculationForm.valuationEndDate"
    form(c -> c.remove("curveConfiguration"))                                     | 412  | "NotNull.dateRangeSimulationCalculationForm.curveConfiguration"
    form(c -> c.remove("configurationType"))                                      | 412  | "NotEmpty.dateRangeSimulationCalculationForm.configurationType"
    form(c -> c.put("configurationType", "random"))                               | 412  | "ValidStringSet.dateRangeSimulationCalculationForm.configurationType"
    form(c -> c.put("configurationType", "FX_V_IRS"))                             | 412  | "NotNull.dateRangeSimulationCalculationForm.nonFxCurveConfiguration"
    form(c -> c.put("curveConfiguration", new CalculationConfigForm(null, null))) | 412  | "NotNull.dateRangeSimulationCalculationForm.curveConfiguration.configurationId"
    form(c -> c.remove("marketDataSource"))                                       | 412  | "NotEmpty.dateRangeSimulationCalculationForm.marketDataSource"
    form(c -> c.put("name", "EXISTENT"))                                          | 412  | "Simulation with this name already exists"
    form(c -> c.put("valuationEndDate", LocalDate.now().minusDays(1)))            | 412  | "Valuation end date must be after valuation start date"
    form(c -> c.put("valuationEndDate", LocalDate.now().plusDays(15)))            | 412  | "Simulation duration must be less than or equal to 14 days"
  }

  def "should perform portfolio ccy-exposure simulation with response #responseBody"() {
    setup:
    simulationService.calculateResults(PORTFOLIO_ID, _ as CcyExposureSimulationCalculationForm, _ as BitemporalDate) >> right(entityId("1"))
    curveGroupRepository.getEither(_ as String) >> right(new CurveGroupView())
    portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(new PortfolioView(companyId: "000000000000000000000000"))
    mdValidator.isMdAllowedForCompany("marketDataId", "000000000000000000000000") >> true
    mdValidator.isMdAllowedForCompany(_ as String, _ as String) >> left(OBJECT_NOT_FOUND.entity()) >> false
    configurationRepository.validCurveConfigurationId(_ as String) >> true
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }
    0 * simulationRepository.simulationNameExists(_)
    ccyExposureSimulationRepository.simulationNameExists("EXISTENT_CCY_EXPOSURE") >> true
    when:
    def results = mockMvc.perform(post("/portfolio/{id}/simulations/ccy-exposure", PORTFOLIO_ID)
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      status == code
      contentAsString.contains(responseBody)
    }
    where:
    form                                                                                      | code | responseBody
    ccyExposureForm()                                                                         | 200  | "id"
    ccyExposureForm(c -> c.remove("name"))                                                    | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.name"
    ccyExposureForm(c -> c.remove("baseCurrency"))                                            | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.baseCurrency"
    ccyExposureForm(c -> c.remove("counterCurrency"))                                         | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.counterCurrency"
    ccyExposureForm(c -> c.remove("calculationCurrency"))                                     | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.calculationCurrency"
    ccyExposureForm(c -> c.remove("curveDiscountingForm"))                                    | 412  | "NotNull.ccyExposureSimulationCalculationForm.curveDiscountingForm"
    ccyExposureForm(c -> c.remove("valuationDate"))                                           | 412  | "NotNull.ccyExposureSimulationCalculationForm.valuationDate"
    ccyExposureForm(c -> c.remove("curveConfiguration"))                                      | 412  | "NotNull.ccyExposureSimulationCalculationForm.curveConfiguration"
    ccyExposureForm(c -> c.remove("configurationType"))                                       | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.configurationType"
    ccyExposureForm(c -> c.put("baseCurrency", "USD"))                                        | 412  | "ValidSimulationCalculationCurrencies.ccyExposureSimulationCalculationForm"
    ccyExposureForm(c -> c.put("configurationType", "random"))                                | 412  | "ValidStringSet.ccyExposureSimulationCalculationForm.configurationType"
    ccyExposureForm(c -> c.put("configurationType", "FX_V_IRS"))                              | 412  | "NotNull.ccyExposureSimulationCalculationForm.nonFxCurveConfiguration"
    ccyExposureForm(c -> c.put("curveConfiguration", new CalculationConfigForm(null, null)))  | 412  | "NotNull.ccyExposureSimulationCalculationForm.curveConfiguration.configurationId"
    ccyExposureForm(c -> c.remove("marketDataSource"))                                        | 412  | "NotEmpty.ccyExposureSimulationCalculationForm.marketDataSource"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.remove("numberShifts"))))      | 412  | "NotNull.ccyExposureSimulationCalculationForm.spotShift.numberShifts"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.put("numberShifts", -1))))     | 412  | "Min.ccyExposureSimulationCalculationForm.spotShift.numberShifts"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.put("numberShifts", 6))))      | 412  | "Max.ccyExposureSimulationCalculationForm.spotShift.numberShifts"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.put("shiftSize", -1))))        | 412  | "Positive.ccyExposureSimulationCalculationForm.spotShift.shiftSize"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.remove("shiftType"))))         | 412  | "NotNull.ccyExposureSimulationCalculationForm.spotShift.shiftType"
    ccyExposureForm(c -> c.put("spotShift", shiftForm(c2 -> c2.put("shiftType", "NA"))))      | 412  | "InvalidFormat"
    ccyExposureForm(c -> c.put("name", "EXISTENT_CCY_EXPOSURE"))                              | 412  | "Simulation with this name already exists"
  }

  def form(Closure c = { a -> a }) {
    [
      name                : "NAME",
      valuationStartDate  : LocalDate.now(),
      valuationEndDate    : LocalDate.now(),
      curveDate           : LocalDate.now(),
      stateDate           : STATE_DATE,
      calculationCurrency : "EUR",
      curveDiscountingForm: new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), false),
      marketDataGroupId   : "marketDataId",
      curveConfiguration  : new CalculationConfigForm("id", null),
      configurationType   : "SINGLE",
      marketDataSource    : "RAW_PRIMARY",
    ].tap(c)
  }

  def ccyExposureForm(Closure c = { a -> a }) {
    [
      spotShift           : shiftForm(),
      volatilityShift     : shiftForm(),
      baseCurrency        : "EUR",
      counterCurrency     : "USD",
      name                : "NAME",
      valuationDate       : LocalDate.now(),
      curveDate           : LocalDate.now(),
      stateDate           : STATE_DATE,
      calculationCurrency : "EUR",
      curveDiscountingForm: new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), false),
      marketDataGroupId   : "marketDataId",
      curveConfiguration  : new CalculationConfigForm("id", null),
      configurationType   : "SINGLE",
      marketDataSource    : "RAW_PRIMARY",
    ].tap(c)
  }

  def shiftForm(Closure c = { a -> a }) {
    [
      numberShifts           : 5,
      shiftSize              : 0.5,
      shiftType              : "ABSOLUTE",
    ].tap(c)
  }
}
