package com.solum.xplain.calculation.integration.cache

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.calibration.volatility.VolatilitiesCalibrationData
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilitySurfaceNodeValue
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurveType
import com.solum.xplain.valuation.messages.calibration.vols.SkewType
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [CacheMapperImpl])
class CacheMapperTest extends Specification {
  @SpringBean
  ReferenceData refData = ReferenceData.standard()
  @Autowired
  CacheMapper mapper

  def "should correctly map OVERNIGHT volatilty surface"() {
    def surface = VolatilitySurfaceBuilder.surfaceWithOnlyAtmNodes()
    surface.setName("EUR ESTR Vols")

    def nodes = [new VolatilitySurfaceNodeValue(expiry: "1Y", tenor: "2Y", value: 1d)]
    VolatilitiesCalibrationData data = VolatilitiesCalibrationData.newOf(surface,
      LocalDate.of(2022, 2, 15),
      nodes,
      [:] as Map<BigDecimal, List<VolatilitySurfaceNodeValue>>)

    def mapped = mapper.fromData(data)
    expect:
    mapped.nodes.size() == 1
    with(mapped.nodes[0]) {
      getValue() == 1d
      getExpiry() == "1Y"
      getExpiryYearFraction() == 1d
      getTenor() == "2Y"
      getTenorYearFraction() == 2d
    }
    mapped.config.getConvention() == "EUR-FIXED-1Y-ESTR-OIS"
    mapped.config.surfaceName == "EUR ESTR Vols"
  }

  def "should correctly map IBOR volatilty surface"() {
    def surface = VolatilitySurfaceBuilder.surfaceWithOnlyAtmNodes()

    def nodes = []
    VolatilitiesCalibrationData data = VolatilitiesCalibrationData.newOf(surface,
      LocalDate.now(),
      nodes,
      [:] as Map<BigDecimal, List<VolatilitySurfaceNodeValue>>)

    def mapped = mapper.fromData(data)
    expect:
    mapped.nodes.size() == 0
    mapped.config.getConvention() == "EUR-FIXED-1Y-EURIBOR-3M"
    mapped.config.surfaceName == "EUR 3M Vols"
  }

  def "should map surface type #surfaceType to skew type #skewType"() {
    expect:
    mapper.resolveType(surfaceType) == skewType

    where:
    surfaceType                     | skewType
    VolatilitySurfaceType.ATM_ONLY  | SkewType.ATM
    VolatilitySurfaceType.MONEYNESS | SkewType.MONEYNESS
    VolatilitySurfaceType.STRIKE    | SkewType.STRIKE
  }

  def "should map curve type #curveType to calibration curve type #calibrationCurveType"() {
    when:
    def curve = CurveBuilder.curveWith({ it.curveType = curveType; return it })
    then:
    mapper.resolveCurveType(curve) == calibrationCurveType

    where:
    curveType                 | calibrationCurveType
    CurveType.INDEX_BASIS     | CalibrationCurveType.INDEX_BASIS
    CurveType.INFLATION_INDEX | CalibrationCurveType.INFLATION
    CurveType.IR_INDEX        | CalibrationCurveType.IR_INDEX
    CurveType.XCCY            | CalibrationCurveType.XCCY
  }
}
