package com.solum.xplain.calculation.integration


import static com.solum.xplain.valuation.messages.trade.constant.ValuationProductType.IRS

import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import com.solum.xplain.valuation.messages.metrics.ValuationResponse
import com.solum.xplain.valuation.messages.metrics.ValuationStatus
import com.solum.xplain.valuation.messages.trade.ValuationCustomTradeField
import com.solum.xplain.valuation.messages.trade.ValuationExternalIdentifier
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import com.solum.xplain.valuation.messages.trade.constant.SwapLegType
import java.time.LocalDate
import org.bson.types.ObjectId

class ValuationResponseBuilder {

  static valuationResponse(calculationId = ObjectId.get().toHexString(), tradeId = "entityId") {
    new ValuationResponse(
      productType: IRS,
      errorMessage: "error",
      status: ValuationStatus.ERROR,
      calculationId: calculationId,
      tradeId: tradeId,
      appVersion: "version",
      tradeDetails: fixedIborSwap()
      )
  }

  static ValuationTradeDetails fixedIborSwap() {
    new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(),
      receiveLeg: iborLeg(),
      businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "EUR"),
      tradeInfoExternalIdentifiers: [new ValuationExternalIdentifier(identifier: "ExtIdentifier", externalSourceId: "ExtSource")],
      tradeInfoCustomFields: [new ValuationCustomTradeField(externalFieldId : "ExtField", value: "value")]
      )
  }

  static ValuationTradeLegDetails iborLeg() {
    new ValuationTradeLegDetails(
      type: SwapLegType.IBOR,
      notional: 1,
      currency: "EUR",
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      initialValue: 1,
      fixingDateOffsetDays: 2,
      index: "EUR-EURIBOR-3M",
      dayCount: "Act/360"
      )
  }

  static ValuationTradeLegDetails fixedLeg() {
    new ValuationTradeLegDetails(
      type: SwapLegType.FIXED,
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      notional: 1,
      currency: "EUR",
      initialValue: 1,
      dayCount: "Act/360"
      )
  }
}
