package com.solum.xplain.calculation

import static CalculationResultStatus.UNSAVED
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.OVERLAY

import com.solum.xplain.calculation.value.CalculationConfigurationData
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = CalculationResult)
class CalculationResultBuilder {
  static EntityReference CURVE_GROUP = EntityReference.newOf("cgId", "CURVE_GROUP")
  static EntityReference CURVE_CONFIG = EntityReference.newOf("ccId", "CURVE_CONFIG")
  static EntityReference FX_CURVE_GROUP = EntityReference.newOf("fxCgId", "FX_CURVE_GROUP")
  static EntityReference FX_CURVE_CONFIG = EntityReference.newOf("fxCcId", "FX_CURVE_CONFIG")

  CalculationResultBuilder() {
    calculationResultStatus(UNSAVED)
    calculationType(PortfolioCalculationType.TRADES)
    curveDate(LocalDate.parse("2016-01-02"))
    valuationDate(LocalDate.parse("2017-01-01"))
    stateDate(LocalDate.parse("2018-01-01"))
    recordDate(LocalDateTime.now())
    reportingCcy("EUR")
    discountingType(CalculationDiscountingType.LOCAL_CURRENCY)
    strippingType(CalculationStrippingType.OIS)
    triangulationCcy("USD")
    configurationData(new CalculationConfigurationData(curveGroup: CURVE_GROUP, curveConfiguration: CURVE_CONFIG))
    fxConfigurationData(new CalculationConfigurationData(curveGroup: FX_CURVE_GROUP, curveConfiguration: FX_CURVE_CONFIG))
    marketData(CalculationResultMarketData.newOf("market", "MARKET_DATA_GROUP", OVERLAY))
    priceRequirements(InstrumentPriceRequirements.bidRequirements())
    dashboardId(null)
  }

  static def calculation(Portfolio portfolio, String tradeId = null, String valuationDate = "2017-01-01", CalculationResultStatus status = UNSAVED, String dashboardId = null) {
    new CalculationResultBuilder()
      .portfolioId(new ObjectId(portfolio.getId()))
      .tradeId(tradeId)
      .valuationDate(LocalDate.parse(valuationDate))
      .calculationResultStatus(status)
      .dashboardId(dashboardId)
      .build()
  }
}
