package com.solum.xplain.calculation.pnlexplain.value

import com.solum.xplain.core.error.ErrorItem
import spock.lang.Specification

class MarketLevelsTest extends Specification {

  def "should return MarketLevels with subtracted values"() {
    setup:
    def marketLevels = new MarketLevels(
      [
        "curve1": [dec(2), dec(4), dec(6)],
        "curve2": [dec(3), dec(2), dec(2)],
      ]
      )
    def otherMarketLevels = new MarketLevels(
      [
        "curve1": [dec(1), dec(2), dec(3)],
        "curve2": [dec(1), dec(2), dec(3)],
      ]
      )

    when:
    def result = marketLevels.subtract(otherMarketLevels)

    then:
    result.isRight()
    def resultMap = ((MarketLevels) result.right().get()).getCurveIdToMarketLevels()
    resultMap.keySet().size() == 2

    resultMap["curve1"] == [dec(1), dec(2), dec(3)]
    resultMap["curve2"] == [dec(2), dec(0), dec(-1)]
  }

  def "should throw exception when other MarketLevels has different number of nodes for curve"() {
    setup:
    def marketLevels = new MarketLevels(
      [
        "curve1": [dec(1), dec(2), dec(3)],
        "curve2": [dec(1), dec(2), dec(3)],
      ]
      )
    def otherMarketLevels = new MarketLevels(
      [
        "curve1": [dec(1), dec(2)],
        "curve2": [dec(1), dec(2), dec(3)],
      ]
      )

    when:
    def result = marketLevels.subtract(otherMarketLevels)

    then:
    result.isLeft()
    ((ErrorItem) result.left().get()).getDescription() == "Both MarketLevels must have the same number of nodes for curve \"curve1\""
  }

  def "should throw exception when other MarketLevels has different number of curves"() {
    setup:
    def marketLevels = new MarketLevels(
      [
        "curve1": [dec(1), dec(2), dec(3)],
        "curve2": [dec(1), dec(2), dec(3)],
      ]
      )
    def otherMarketLevels = new MarketLevels(
      [
        "curve1": [dec(1), dec(2), dec(3)]
      ]
      )

    when:
    def result = marketLevels.subtract(otherMarketLevels)

    then:
    result.isLeft()
    ((ErrorItem) result.left().get()).getDescription() == "Both MarketLevels must have the same list of curves"
  }

  def dec(double v) {
    BigDecimal.valueOf(v)
  }
}
