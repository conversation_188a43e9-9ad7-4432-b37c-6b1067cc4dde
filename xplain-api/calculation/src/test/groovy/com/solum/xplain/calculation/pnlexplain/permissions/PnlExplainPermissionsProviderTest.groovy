package com.solum.xplain.calculation.pnlexplain.permissions

import spock.lang.Specification

class PnlExplainPermissionsProviderTest extends Specification {

  def "should return support module permissions"() {
    setup:
    def provider = new PnlExplainPermissionsProvider()

    def permissions = provider.availablePermissions()

    expect:
    permissions.size() == 2
    permissions[0].name == "RUN_PNL_EXPLAIN_CALCULATION"
    permissions[0].permissionCategory.name() == "PNL_EXPLAIN_CALCULATIONS"
    permissions[1].name == "VIEW_PNL_EXPLAIN_CALCULATION_RESULTS"
    permissions[1].permissionCategory.name() == "PV_CALCULATIONS"
  }
}
