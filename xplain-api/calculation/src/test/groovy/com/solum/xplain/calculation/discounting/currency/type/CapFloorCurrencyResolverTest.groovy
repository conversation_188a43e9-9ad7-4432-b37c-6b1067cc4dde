package com.solum.xplain.calculation.discounting.currency.type

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import spock.lang.Specification

class CapFloorCurrencyResolverTest extends Specification {

  def RESOLVER = new CapFloorCurrencyResolver()

  def "should return correct product type"() {
    expect:
    RESOLVER.productTypes() == [CoreProductType.CAP_FLOOR]
  }

  def "should resolve capfloor currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.capFloor(), null) == Currency.EUR
  }
}
