package com.solum.xplain.calculation.trades

import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.allocationTrade

import com.solum.xplain.calculation.repository.CalculationPortfolioItemRepository
import com.solum.xplain.calculation.repository.filter.SelectedTypesTradesFilter
import com.solum.xplain.core.common.versions.BitemporalDate
import java.time.LocalDate
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import spock.lang.Specification

class SelectedTypesCalculationTest extends Specification {

  def static STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 03))
  def static PORTFOLIOID = ObjectId.get().toHexString()
  def static TRADEMULTI = allocationTrade()

  CalculationPortfolioItemRepository itemRepository = Mock()

  def tradeFilter = new SelectedTypesTradesFilter(PORTFOLIOID, STATE_DATE, [IRS])
  def calculation = new SelectedTypesCalculation(itemRepository, tradeFilter, [IRS])

  def "should resolve product types"() {
    expect:
    calculation.uniqueProductTypes() == Set.of(IRS)
  }

  def "should resolve trades count "() {
    setup:
    1 * itemRepository.portfolioCalculationTradesCount(tradeFilter) >> 2

    expect:
    calculation.tradesCount() == 2
  }

  def "should resolve trades flux"() {
    setup:
    itemRepository.portfolioItemsFlux(tradeFilter) >> Flux.just(TRADEMULTI)

    expect:
    calculation.tradesFlux().blockFirst() == TRADEMULTI
  }

  def "should resolve empty single tradeId"() {
    expect:
    calculation.singleTradeId() == Optional.empty()
    calculation.singleTradeExternalId() == Optional.empty()
  }
}
