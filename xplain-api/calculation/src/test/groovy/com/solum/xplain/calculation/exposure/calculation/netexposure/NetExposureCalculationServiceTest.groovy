package com.solum.xplain.calculation.exposure.calculation.netexposure

import com.solum.xplain.calculation.exposure.CcyExposureCalculationOptions
import com.solum.xplain.calculation.exposure.netexposure.NetExposureCalculationDataService
import com.solum.xplain.calculation.exposure.netexposure.NetExposureCalculationService
import com.solum.xplain.calculation.exposure.schedule.CombinedCashflowSchedule
import com.solum.xplain.calculation.exposure.schedule.CombinedCashflowSchedule.CombinedCashflowBucket
import com.solum.xplain.calculation.value.CalculationResultTotalsForm
import com.solum.xplain.core.ccyexposure.CcyExposureControllerService
import com.solum.xplain.core.ccyexposure.entity.CcyExposure
import com.solum.xplain.core.ccyexposure.value.CashflowView
import com.solum.xplain.shared.utils.filter.TableFilter
import java.time.LocalDate
import org.bson.types.ObjectId
import spock.lang.Specification

class NetExposureCalculationServiceTest extends Specification {

  NetExposureCalculationDataService netExposureCalculationDataService = Mock()
  CcyExposureControllerService ccyExposureControllerService = Mock()
  def calculator = new NetExposureCalculationService(netExposureCalculationDataService, ccyExposureControllerService)

  def "should calculate net exposure metrics"() {
    setup:
    def cf1 = cashflow("2020-01-01", -100.0)
    def cf2 = cashflow("2020-01-04", 100.0)
    def cf3 = cashflow("2020-01-08", 200.0)
    def cf4 = cashflow("2020-01-08", 1200.0)
    def cf5 = cashflow("2020-01-11", 1000.0)
    def externalCcyExposure = new CcyExposure("name", "ccy")
    externalCcyExposure.setId(ObjectId.get().toHexString())
    def externalCcyExposures =  [externalCcyExposure]

    def combinedCashflows = new CombinedCashflowSchedule(
      [
        cashflowBucket(cf1.date, [cf1]),
        cashflowBucket(cf2.date, [cf2]),
        cashflowBucket(cf3.date, [cf3, cf4]),
        cashflowBucket(cf5.date, [cf5])
      ]
      )

    def options = Mock(CcyExposureCalculationOptions)
    def ccy = "EUR"
    options.getExposureCurrency() >> ccy
    def date = LocalDate.now()
    options.getStateDate() >> date
    def exposureIds = [externalCcyExposure.id]
    options.getExposureIds() >> exposureIds

    def tableFilter = TableFilter.emptyTableFilter()
    def totalsForm = new CalculationResultTotalsForm(all: true)

    1 * ccyExposureControllerService.ccyExposuresWithCashflows(ccy, date, exposureIds) >> externalCcyExposures
    1 * netExposureCalculationDataService.generateCombinedCashflowSchedule(externalCcyExposures, options, tableFilter, totalsForm) >> combinedCashflows

    when:
    def metrics = calculator.calculate(options, tableFilter, totalsForm)

    then:
    metrics.size() == 4

    metrics[0].scheduleDate == LocalDate.of(2020, 1, 1)
    metrics[0].exposure == -100.0
    metrics[0].forwardsExposure == -100.0
    metrics[0].optionExposure == -100.0
    metrics[0].netExposure == -300.0
    metrics[0].hedgeRatio == 2.0
    metrics[0].deltaOptions == -100.0
    metrics[0].deltaNetExposure == -300.0
    metrics[0].deltaHedgeRatio == 2.0

    metrics[1].scheduleDate == LocalDate.of(2020, 1, 4)
    metrics[1].exposure == 100.0
    metrics[1].forwardsExposure == 100.0
    metrics[1].optionExposure == 100.0
    metrics[1].netExposure == 300.0
    metrics[1].hedgeRatio == 2.0
    metrics[1].deltaOptions == 100.0
    metrics[1].deltaNetExposure == 300.0
    metrics[1].deltaHedgeRatio == 2.0

    metrics[2].scheduleDate == LocalDate.of(2020, 1, 8)
    metrics[2].exposure == 1400.0
    metrics[2].forwardsExposure == 1400.0
    metrics[2].optionExposure == 1400.0
    metrics[2].netExposure == 4200.0
    metrics[2].hedgeRatio == 2.0
    metrics[2].deltaOptions == 1400.0
    metrics[2].deltaNetExposure == 4200.0
    metrics[2].deltaHedgeRatio == 2.0

    metrics[3].scheduleDate == LocalDate.of(2020, 1, 11)
    metrics[3].exposure == 1000.0
    metrics[3].forwardsExposure == 1000.0
    metrics[3].optionExposure == 1000.0
    metrics[3].netExposure == 3000.0
    metrics[3].hedgeRatio == 2.0
    metrics[3].deltaOptions == 1000.0
    metrics[3].deltaNetExposure == 3000.0
    metrics[3].deltaHedgeRatio == 2.0
  }

  def cashflow(String date, Double amount) {
    new CashflowView(date: LocalDate.parse(date), amount: amount)
  }

  def cashflowBucket(LocalDate scheduleDate, List<CashflowView> cashflows) {
    new CombinedCashflowBucket(
      scheduleDate,
      cashflows,
      cashflows,
      cashflows,
      cashflows
      )
  }
}
