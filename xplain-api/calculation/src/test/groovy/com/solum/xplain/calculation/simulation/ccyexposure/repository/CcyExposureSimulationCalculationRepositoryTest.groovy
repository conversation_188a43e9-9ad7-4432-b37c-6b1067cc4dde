package com.solum.xplain.calculation.simulation.ccyexposure.repository

import static com.solum.xplain.core.portfolio.PortfolioBuilder.portfolio
import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.CalculationResultStatus
import com.solum.xplain.calculation.simulation.ccyexposure.entity.CcyExposureSimulationCalculation
import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureCalculationKey
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType
import com.solum.xplain.calculation.simulation.daterange.SimulationCalculationSample
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CcyExposureSimulationCalculationRepositoryTest extends IntegrationSpecification implements SimulationCalculationSample {
  def MOCK_PORTFOLIO_ID = ObjectId.get().toHexString()

  @Resource
  CcyExposureSimulationCalculationRepository repository
  @Resource
  MongoOperations operations

  def cleanup() {
    operations.remove(new Query(), CcyExposureSimulationCalculation)
    operations.remove(new Query(), CalculationResult)
    operations.remove(new Query(), Portfolio)
  }

  def "should return simulations scrollable when user in team"() {
    setup:
    def portfolio = portfolio()
    portfolio.entity = new CompanyLegalEntityReference(entityId: "entityId", externalEntityId: "externalEntityId", name: "name")
    operations.insert(portfolio)

    def simId = ObjectId.get().toHexString()
    def simulation = ccyExposureSimulation(portfolio.id, simId)
    operations.insert(simulation)

    def spotResult = new CalculationResult()
    spotResult.ccyExposureCalculationKey = new CcyExposureCalculationKey(simId, ShiftDataType.SPOT, 0.5d)
    def volResult = new CalculationResult()
    volResult.ccyExposureCalculationKey = new CcyExposureCalculationKey(simId, ShiftDataType.VOLATILITY, 0.5d)
    def baseResult = new CalculationResult()
    baseResult.ccyExposureCalculationKey = new CcyExposureCalculationKey(simId, null, 0.0d)
    def otherCalculationResult = new CalculationResult()

    operations.insert(spotResult)
    operations.insert(volResult)
    operations.insert(baseResult)
    operations.insert(otherCalculationResult)

    when:
    def result = repository.simulationsScrollable(ScrollRequest.of(0, 10), TableFilter.emptyTableFilter(), EntityTeamFilter.filter(user()))

    then:
    result.content.size() == 1
    with(result.content[0]) {
      id == simulation.id
      name == simulation.name
      calculationResultStatus == CalculationResultStatus.UNSAVED
      portfolioId == simulation.portfolioId.toHexString()
      portfolioExternalId == simulation.portfolioExternalId
      entityExternalId == simulation.entityExternalId
      companyExternalId == simulation.companyExternalId

      baseCalculationResultId != null
      baseCalculationResultId == baseResult.id.toString()

      marketData == simulation.marketData
      fxConfigurationData == simulation.fxConfigurationData
      configurationData == simulation.configurationData
      curveDate == simulation.curveDate
      valuationDate == simulation.valuationDate
      stateDate == simulation.stateDate
      recordDate == simulation.recordDate
      reportingCcy == simulation.reportingCcy
      discountingType == simulation.discountingType
      strippingType == simulation.strippingType
      triangulationCcy == simulation.triangulationCcy

      createdBy == simulation.createdBy
      createdAt == simulation.createdAt
    }
  }
}
