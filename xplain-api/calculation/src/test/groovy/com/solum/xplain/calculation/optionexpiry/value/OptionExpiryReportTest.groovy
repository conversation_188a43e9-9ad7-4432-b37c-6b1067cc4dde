package com.solum.xplain.calculation.optionexpiry.value

import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_BUY

import spock.lang.Specification

class OptionExpiryReportTest extends Specification {

  def "should validate values array input"() {
    when:
    new OptionExpiryReport([1], [2, 3], [(SWAPTION_PAY_BUY): new BigDecimal[][]{}])

    then:
    def ex1 = thrown IllegalArgumentException
    ex1.message == "Values array must have the same size as itm array"

    when:
    new OptionExpiryReport([1], [2, 3], [(SWAPTION_PAY_BUY): new BigDecimal[][]{
        new BigDecimal[]{
          4d
        }, new double[]{
          5d, 6d
        }
      }])

    then:
    def ex2 = thrown IllegalArgumentException
    ex2.message == "Each values sub-array must have the same size as expiry array"
  }
}
