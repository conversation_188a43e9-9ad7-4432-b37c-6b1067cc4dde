package com.solum.xplain.calculation.pnlexplain

import static com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey.configMarketKey
import static com.solum.xplain.core.curvemarket.SingleDateMarketDataExtractionParams.mdParamsWithoutCurves

import com.opengamma.strata.data.MarketData
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.CalculationResultMarketData
import com.solum.xplain.calculation.value.CalculationConfigurationData
import com.solum.xplain.calibration.market.CalibrationMarketDataService
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.error.Error
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.time.LocalDateTime
import spock.lang.Specification

class PnlFxRateProviderTest extends Specification {
  CalibrationMarketDataService mdService = Mock()
  PnlFxRateProvider provider = new PnlFxRateProvider(mdService)

  def "should return fx rate provider for calculation result"() {
    setup:
    def priceRequirements = Mock(InstrumentPriceRequirements)
    def marketData = Mock(MarketData)
    def calculationDate = LocalDateTime.now()
    def valuationDate = LocalDate.now().plusDays(1)
    def calculationResult = new CalculationResult()

    calculationResult.marketData = CalculationResultMarketData.newOf("mdId", "mdName", MarketDataSourceType.RAW_PRIMARY)
    calculationResult.configurationData = new CalculationConfigurationData(EntityReference.newOf("cgId", "cgName"),EntityReference.newOf("ccId", "ccName"))
    calculationResult.stateDate = LocalDate.ofEpochDay(0)
    calculationResult.createdAt = calculationDate
    calculationResult.curveDate = LocalDate.now()
    calculationResult.valuationDate = valuationDate
    calculationResult.priceRequirements = priceRequirements

    def expectedStateKey = mdParamsWithoutCurves(configMarketKey(
      "mdId",
      "ccId",
      MarketDataSourceType.RAW_PRIMARY,
      BitemporalDate.newOf(LocalDate.ofEpochDay(0), calculationDate),
      LocalDate.now(),
      priceRequirements))

    1 * mdService.getCalibrationMD(valuationDate, expectedStateKey) >> Either.right(marketData)

    expect:
    provider.fxRateProvider(calculationResult) == MarketDataFxRateProvider.of(marketData)
  }

  def "should return error when market data not found"() {
    setup:
    def priceRequirements = Mock(InstrumentPriceRequirements)
    def calculationDate = LocalDateTime.now()
    def valuationDate = LocalDate.now().plusDays(1)
    def calculationResult = new CalculationResult()

    calculationResult.marketData = CalculationResultMarketData.newOf("mdId", "mdName", MarketDataSourceType.RAW_PRIMARY)
    calculationResult.configurationData = new CalculationConfigurationData(EntityReference.newOf("cgId", "cgName"),EntityReference.newOf("ccId", "ccName"))
    calculationResult.stateDate = LocalDate.ofEpochDay(0)
    calculationResult.createdAt = calculationDate
    calculationResult.curveDate = LocalDate.now()
    calculationResult.valuationDate = valuationDate
    calculationResult.priceRequirements = priceRequirements

    def expectedStateKey = mdParamsWithoutCurves(configMarketKey(
      "mdId",
      "ccId",
      MarketDataSourceType.RAW_PRIMARY,
      BitemporalDate.newOf(LocalDate.ofEpochDay(0), calculationDate),
      LocalDate.now(),
      priceRequirements))

    1 * mdService.getCalibrationMD(valuationDate, expectedStateKey) >> Either.left(Error.OBJECT_NOT_FOUND.entity("Cannot find fx market data"))

    when:
    provider.fxRateProvider(calculationResult)

    then:
    IllegalArgumentException e = thrown()
    e.getMessage() == "Could not get FX rate provider"
  }
}
