package com.solum.xplain.calculation.discounting

import static PortfolioItemDiscountIndexResolver.fromDiscountSettings
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_12M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_12M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.capFloor
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.cdsTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.creditIndexTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.floatFloatSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fraDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxOptionTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxSingle
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.inflationSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.irsDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.swaptionTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.xccySwap
import static com.solum.xplain.core.settings.value.StrippingCurvePriority.TENOR_3M
import static com.solum.xplain.core.settings.value.StrippingCurvePriority.TENOR_6M

import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecificationBuilder
import com.solum.xplain.calibration.discounting.OisConfigurations
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import spock.lang.Specification

class TradeIndexResolverTest extends Specification {
  def productSettingsResolver = Mock(ProductSettingsResolver)
  def RESOLVER = fromDiscountSettings(OisConfigurations.of(VAL_DT), productSettingsResolver)


  def "should correctly resolve SWAP index with both spread legs"() {
    when:
    def details = floatFloatSwap(VAL_DT)
    details.payLeg.initialValue = 1.0d
    details.receiveLeg.initialValue = 1.0d
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(details)), Set.of(USD_LIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == USD_LIBOR_3M
  }

  def "should correctly resolve swaption index"() {
    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.swaptionTradeDscSpec(swaptionTradeDetails())), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve capfloor index"() {
    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.capFloorTradeDscSpec(capFloor())), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve fxfwd index"() {
    setup:
    def fx = fxSingle(VAL_DT)
    1 * productSettingsResolver.resolveFxCcy(fx.currencyPair()) >> EUR
    1 * productSettingsResolver.resolveFxCurvePriorities() >> [TENOR_3M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.fxForwardDscSpec(fx)), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve fxfwd index no index no xccy no USD then error"() {
    setup:
    def fx = fxSingle(VAL_DT)
    1 * productSettingsResolver.resolveFxCcy(fx.currencyPair()) >> EUR
    1 * productSettingsResolver.resolveFxCurvePriorities() >> [TENOR_3M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.fxForwardDscSpec(fx)), Set.of(GBP_LIBOR_12M))

    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting ccy EUR. Priorities [TENOR_3M]"
  }

  def "should correctly resolve fxopt index"() {
    setup:
    def fxopt = fxOptionTradeDetails(VAL_DT)
    1 * productSettingsResolver.resolveFxCcy(fxopt.currencyPair()) >> EUR
    1 * productSettingsResolver.resolveFxCurvePriorities() >> [TENOR_3M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.fxOptDscSpec(fxopt)), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve cds index"() {
    setup:
    1 * productSettingsResolver.resolveCdsCurvePriorities() >> [TENOR_6M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.cdsDscSpec(cdsTradeDetails())), Set.of(EUR_EURIBOR_12M, EUR_EURIBOR_6M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_6M
  }

  def "should correctly resolve credit index trade index"() {
    setup:
    1 * productSettingsResolver.resolveCdsCurvePriorities() >> [TENOR_3M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.cdxDscSpec(creditIndexTradeDetails())), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should correctly resolve inflation index"() {
    setup:
    1 * productSettingsResolver.resolveInflationCurvePriorities() >> [TENOR_6M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.infTradeDscSpec(inflationSwap(VAL_DT))), Set.of(EUR_EURIBOR_6M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_6M
  }

  def "should correctly resolve XCCY index"() {
    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.xccyTradeDscSpec(xccySwap(VAL_DT))), Set.of(USD_LIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == USD_LIBOR_3M
  }

  def "should correctly resolve fra index"() {

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.fraTradeDscSpec(fraDetails(VAL_DT))), Set.of(EUR_EURIBOR_3M))

    then:
    res.isRight()
    res.getOrNull() == EUR_EURIBOR_3M
  }

  def "should fail to resolve SWAP index when no curve group indices"() {
    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(irsDetails(VAL_DT))), Set.of())


    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting: EUR-EURIBOR-3M"
  }

  def "should fail to resolve SWAP index when no curve group indices explicit index"() {
    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(irsDetails(VAL_DT))), Set.of())

    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting: EUR-EURIBOR-3M"
  }


  def "should fail to resolve CDS index when no curve group indices"() {
    setup:
    1 * productSettingsResolver.resolveCdsCurvePriorities() >> [TENOR_6M]

    when:
    def res = RESOLVER.resolveIndex(new DiscountablePortfolioItem(PortfolioItemDiscountingGroupSpecificationBuilder.cdsDscSpec(cdsTradeDetails())), Set.of(EUR_EURIBOR_12M))

    then:
    res.isLeft()
    def error = res.left().get() as ErrorItem
    error.reason == CALIBRATION_ERROR
    error.description == "Unable to find curve for discounting ccy EUR. Priorities [TENOR_6M]"
  }
}
