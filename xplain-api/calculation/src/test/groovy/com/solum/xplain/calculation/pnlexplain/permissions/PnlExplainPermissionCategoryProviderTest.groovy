package com.solum.xplain.calculation.pnlexplain.permissions

import spock.lang.Specification

class PnlExplainPermissionCategoryProviderTest extends Specification {
  def "should return pnl permission categories"() {
    setup:
    def categories = new PnlExplainPermissionCategoryProvider().permissionCategories()
    expect:
    categories.size() == 1
    categories.containsAll([PnlExplainPermissionCategory.PNL_EXPLAIN_CALCULATIONS])
  }
}
