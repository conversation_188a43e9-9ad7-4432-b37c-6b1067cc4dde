package com.solum.xplain.calculation.curves

import static com.solum.xplain.calculation.curves.value.CalculationConfigurationType.DEFAULT

import com.opengamma.strata.market.ValueType
import com.solum.xplain.calculation.CalculationResultCurves
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calibration.curve.charts.ChartDateType
import com.solum.xplain.calibration.curve.charts.ChartPointGenerator
import com.solum.xplain.calibration.curve.charts.value.CurveChartGeneratorData
import com.solum.xplain.calibration.curve.charts.value.CurveDetails
import com.solum.xplain.calibration.rates.charts.CalibratedCurve
import com.solum.xplain.core.common.value.ChartPoint
import com.solum.xplain.core.curvegroup.curve.entity.CurvePoints
import java.time.LocalDate
import spock.lang.Specification

class CalculationChartGeneratorTest extends Specification {
  static VALUATION_DATE = LocalDate.of(2020, 1, 1)
  static List<Double> SEASONALITY = [0d, 1d]

  ChartPointGenerator chartPointGenerator = Mock()
  CalculationResultRepository repository = Mock()

  CalculationChartGenerator generator = new CalculationChartGenerator(chartPointGenerator, repository)

  def "should generate all charts for calculation"() {
    setup:
    def curveGenerator1 = curveGeneratorData()
    def curveGenerator2 = inflationCurveGeneratorData()

    when:
    def result = generator.generateAllCharts("calculationId", ChartDateType.ACTUAL_DATE)

    then:
    1 * repository.calculationCurves("calculationId") >> new CalculationResultCurves(
      valuationDate: VALUATION_DATE,
      curveConfigurationCurves: [calculationCurve()],
      fxCurveConfigurationCurves: [calculationInflationCurve()]
      )
    1 * chartPointGenerator.generateChartPoints(curveGenerator1, ChartDateType.ACTUAL_DATE) >> CurvePoints.empty("EUR 3M", ValueType.YEAR_FRACTION)
    1 * chartPointGenerator.generateChartPoints(curveGenerator2, ChartDateType.ACTUAL_DATE) >> CurvePoints.empty("GB RPI", ValueType.PRICE_INDEX)

    and:
    result.curveConfigurationChartData.size() == 1
    result.curveConfigurationChartData[0].indexName == "EUR 3M"
    result.curveConfigurationChartData[0].discountingKey == "EUR"
    result.curveConfigurationChartData[0].primary
    result.curveConfigurationChartData[0].valueType == "ZeroRate"
    result.fxCurveConfigurationChartDataView[0].indexName == "GB RPI"
    result.fxCurveConfigurationChartDataView[0].discountingKey == "EUR"
    !result.fxCurveConfigurationChartDataView[0].primary
    result.fxCurveConfigurationChartDataView[0].valueType == "PriceIndex"
  }

  def "should generate all charts for calculation without FX provider"() {
    setup:
    def curveGenerator1 = curveGeneratorData()

    when:
    def result = generator.generateAllCharts("calculationId", ChartDateType.ACTUAL_DATE)

    then:
    1 * repository.calculationCurves("calculationId") >> new CalculationResultCurves(
      valuationDate: VALUATION_DATE,
      curveConfigurationCurves: [calculationCurve()],
      )
    1 * chartPointGenerator.generateChartPoints(curveGenerator1, ChartDateType.ACTUAL_DATE) >> CurvePoints.empty("EUR 3M", ValueType.YEAR_FRACTION)

    and:
    result.curveConfigurationChartData.size() == 1
    result.curveConfigurationChartData[0].indexName == "EUR 3M"
    result.curveConfigurationChartData[0].discountingKey == "EUR"
    result.curveConfigurationChartData[0].primary
    result.curveConfigurationChartData[0].valueType == "ZeroRate"
    result.fxCurveConfigurationChartDataView == null
  }

  def "should generate single curve chart"() {
    setup:
    def curveGenerator1 = curveGeneratorData()

    when:
    def result = generator.generateSingleCurvePoints("calculationId", "EUR 3M", DEFAULT, ChartDateType.ACTUAL_DATE)

    then:
    1 * repository.calculationCurves("calculationId") >> new CalculationResultCurves(
      valuationDate: VALUATION_DATE,
      curveConfigurationCurves: [calculationCurve()],
      )
    1 * chartPointGenerator.generateChartPoints(curveGenerator1, ChartDateType.ACTUAL_DATE) >> CurvePoints.empty("EUR 3M", ValueType.YEAR_FRACTION)

    and:
    result.isRight()
    def curvePoints = result.getOrNull()
    curvePoints.size() == 1
    curvePoints[0].curveName == "EUR 3M"
    curvePoints[0].discountingKey == null
    curvePoints[0].points.isEmpty()
  }


  def "should fallback to precalculated points when missing data"() {
    setup:
    def calculationCurve = new CalibratedCurve(
      name: "EUR 3M",
      discountingKey: "EUR",
      yValueType: "ZeroRate",
      chartPoints: [new ChartPoint(LocalDate.of(2020, 1, 1), 1, 1)]
      )

    when:
    def result = generator.generateSingleCurvePoints("calculationId", "EUR 3M", DEFAULT, ChartDateType.ACTUAL_DATE)
    then:
    1 * repository.calculationCurves("calculationId") >> new CalculationResultCurves(
      valuationDate: VALUATION_DATE,
      curveConfigurationCurves: [calculationCurve],
      )
    0 * chartPointGenerator._

    and:
    result.isRight()
    def curvePoints = result.getOrNull()
    curvePoints.size() == 1
    curvePoints[0].curveName == "EUR 3M"
    curvePoints[0].discountingKey == "EUR"
    curvePoints[0].points.size() == 1
    curvePoints[0].points == calculationCurve.chartPoints
  }

  def calculationCurve() {
    new CalibratedCurve(
      name: "EUR 3M",
      discountingKey: "EUR",
      primary: true,
      xValueType: "YearFraction",
      yValueType: "ZeroRate",
      interpolator: "Linear",
      extrapolatorLeft: "Flat",
      extrapolatorRight: "Flat",
      chartPoints: [new ChartPoint(LocalDate.of(2020, 1, 1), 1, 1)]
      )
  }

  def curveGeneratorData() {
    def curveDetails = new CurveDetails(
      "EUR 3M",
      "EUR",
      ValueType.YEAR_FRACTION,
      ValueType.ZERO_RATE,
      "Linear",
      "Flat",
      "Flat",
      )

    new CurveChartGeneratorData(
      curveDetails,
      null,
      null,
      [new ChartPoint(LocalDate.of(2020, 1, 1), 1, 1)],
      VALUATION_DATE)
  }

  def calculationInflationCurve() {
    new CalibratedCurve(
      name: "GB RPI",
      discountingKey: "EUR",
      xValueType: "Months",
      yValueType: "PriceIndex",
      interpolator: "Linear",
      extrapolatorLeft: "Flat",
      extrapolatorRight: "Flat",
      inflationAdjustmentType: "Relative",
      inflationSeasonalityAdjustment: SEASONALITY,
      chartPoints: [new ChartPoint(LocalDate.of(2021, 1, 1), 1, 2)]
      )
  }

  def inflationCurveGeneratorData() {
    def curveDetails = new CurveDetails(
      "GB RPI",
      "EUR",
      ValueType.MONTHS,
      ValueType.PRICE_INDEX,
      "Linear",
      "Flat",
      "Flat",
      )

    new CurveChartGeneratorData(
      curveDetails,
      "Relative",
      SEASONALITY,
      [new ChartPoint(LocalDate.of(2021, 1, 1), 1, 2)],
      VALUATION_DATE)
  }
}
