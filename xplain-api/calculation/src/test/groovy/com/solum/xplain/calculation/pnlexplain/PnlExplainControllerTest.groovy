package com.solum.xplain.calculation.pnlexplain

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.CalculationResultControllerService
import com.solum.xplain.calculation.CalculationService
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.helpers.MockMvcConfiguration
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculation
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculationResult
import com.solum.xplain.calculation.pnlexplain.value.PnlExplainCalculationForm
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [PnlExplainController])
@TestPropertySource(properties = [
  "app.pnl-explain.enabled=true"
])
class PnlExplainControllerTest extends Specification {

  static String PORTFOLIO_ID = "000000000000000000000001"
  static LocalDate STATE_DATE = LocalDate.ofEpochDay(0)
  static String PNL_EXPLAIN_CALCULATION_ID = "pnlExplainCalculationId"
  static String PNL_EXPLAIN_CALCULATION_ID_2 = "pnlExplainCalculationId2"

  @SpringBean
  PnlExplainControllerService pnlExplainCalculationService = Mock()
  @SpringBean
  PortfolioRepository portfolioRepository = Mock()
  @SpringBean
  ResourceValidationService resourceValidationService = Mock()
  @SpringBean
  CalculationResultControllerService service = Mock()
  @SpringBean
  CalculationService calculationService = Mock()
  @SpringBean
  CompanyMarketDataGroupValidator mdValidator = Mock()
  @SpringBean
  CurveGroupRepository curveGroupRepository = Mock()
  @SpringBean
  CurveConfigurationRepository configurationRepository = Mock()
  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should fetch all pnl explain calculations"() {
    setup:
    pnlExplainCalculationService.getPnlExplainCalculations() >> [pnlCalculation(), pnlCalculation(PNL_EXPLAIN_CALCULATION_ID_2)]

    when:
    def results = mockMvc.perform(get("/portfolio/pnl")
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString().contains(PNL_EXPLAIN_CALCULATION_ID)
    results.getResponse().getContentAsString().contains(PNL_EXPLAIN_CALCULATION_ID_2)
  }

  def "should perform pnl explain calculation with response #responseBody"() {
    setup:
    pnlExplainCalculationService.calculateResults(_ as Authentication, PORTFOLIO_ID, _ as PnlExplainCalculationForm, _ as EntityId) >> right(entityId("1"))
    curveGroupRepository.getEither(_ as String) >> right(new CurveGroupView())
    portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(new PortfolioView(companyId: "000000000000000000000000"))
    mdValidator.isMdAllowedForCompany("marketDataId", "000000000000000000000000") >> true
    mdValidator.isMdAllowedForCompany(_ as String, _ as String) >> left(OBJECT_NOT_FOUND.entity()) >> false
    configurationRepository.validCurveConfigurationId(_ as String) >> true
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }

    when:
    def results = mockMvc.perform(post("/portfolio/{portfolioId}/pnl", PORTFOLIO_ID)
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == code
    results.getResponse().getContentAsString().contains(responseBody)

    where:
    form                                                                                                                     | code | responseBody
    form()                                                                                                                   | 200  | "id"
    form(c -> c.remove("firstValuationDate"))                                                                                | 412  | "NotNull.pnlExplainCalculationForm.firstValuationDate"
    form(c -> c.remove("secondValuationDate"))                                                                               | 412  | "NotNull.pnlExplainCalculationForm.secondValuationDate"
    form(c -> c.remove("curveConfiguration"))                                                                                | 412  | "NotNull.pnlExplainCalculationForm.curveConfiguration"
    form(c -> c.remove("configurationType"))                                                                                 | 412  | "NotEmpty.pnlExplainCalculationForm.configurationType"
    form(c -> c.put("secondValuationDate", LocalDate.now()))                                                                 | 412  | "ValidPnlValuationDates.pnlExplainCalculationForm"
    form(c -> c.put("firstValuationDate", LocalDate.now().plusDays(1)))                                                      | 412  | "ValidPnlValuationDates.pnlExplainCalculationForm"
    form(c -> c.put("firstValuationDate", LocalDate.now().plusDays(2)))                                                      | 412  | "ValidPnlValuationDates.pnlExplainCalculationForm"
    form(c -> c.put("priceRequirements", new InstrumentPriceRequirementsForm(
    curvesPriceType: InstrumentPriceType.BID_PRICE,
    dscCurvesPriceType: InstrumentPriceType.BID_PRICE,
    fxRatesPriceType: InstrumentPriceType.BID_PRICE,
    volsPriceType: InstrumentPriceType.BID_PRICE,
    volsSkewsPriceType: InstrumentPriceType.BID_PRICE
    )))                                                                                                                      | 412  | "ValidMidPriceRequirements.pnlExplainCalculationForm"
    form(c -> c.put("priceRequirements", new InstrumentPriceRequirementsForm(
    curvesPriceType: InstrumentPriceType.MID_PRICE,
    dscCurvesPriceType: InstrumentPriceType.MID_PRICE,
    fxRatesPriceType: InstrumentPriceType.MID_PRICE,
    volsPriceType: InstrumentPriceType.MID_PRICE,
    volsSkewsPriceType: InstrumentPriceType.MID_PRICE
    )))                                                                                                                      | 200  | "id"
  }

  def "should fetch pnl explain calculation by id"() {
    setup:
    pnlExplainCalculationService.getPnlExplainCalculation(PNL_EXPLAIN_CALCULATION_ID) >> pnlCalculation()

    when:
    def results = mockMvc.perform(get("/portfolio/pnl/{pnlExplainCalculationId}", PNL_EXPLAIN_CALCULATION_ID)
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString().contains(PNL_EXPLAIN_CALCULATION_ID)
  }

  def "should delete pnl explain calculation by id"() {
    setup:
    pnlExplainCalculationService.deletePnlExplainCalculation(PNL_EXPLAIN_CALCULATION_ID) >> calc

    when:
    def results = mockMvc.perform(delete("/portfolio/pnl/{pnlExplainCalculationId}", PNL_EXPLAIN_CALCULATION_ID)
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == status
    results.getResponse().getContentAsString().contains(content)

    where:
    calc                                        | status  | content
    right(entityId(PNL_EXPLAIN_CALCULATION_ID)) | 200     | PNL_EXPLAIN_CALCULATION_ID
    left(OBJECT_NOT_FOUND.entity("not found"))  | 422     | OBJECT_NOT_FOUND.toString()
  }

  def "should fetch pnl explain calculation result by id"() {
    setup:
    pnlExplainCalculationService.getResultForPnlCalculation(PNL_EXPLAIN_CALCULATION_ID) >> pnlCalculationResult()

    when:
    def results = mockMvc.perform(get("/portfolio/pnl/{pnlExplainCalculationId}/result", PNL_EXPLAIN_CALCULATION_ID)
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString().contains(PNL_EXPLAIN_CALCULATION_ID)
  }

  def "should fetch pnl explain calculations for portfolio"() {
    setup:
    pnlExplainCalculationService.getPnlExplainCalculationsForPortfolio(PORTFOLIO_ID) >> [pnlCalculation()]

    when:
    def results = mockMvc.perform(get("/portfolio/{portfolioId}/pnl", PORTFOLIO_ID)
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString().contains(PORTFOLIO_ID)
  }

  def form(Closure c = { a -> a }) {
    [
      firstValuationDate  : LocalDate.now(),
      secondValuationDate : LocalDate.now().plusDays(1),
      stateDate           : STATE_DATE,
      calculationCurrency : "EUR",
      curveDiscountingForm: new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), false),
      configurationType   : "SINGLE",
      curveConfiguration  : new CalculationConfigForm("id", null),
      marketDataGroupId   : "marketDataId",
      marketDataSource    : "RAW_PRIMARY",
    ].tap(c)
  }

  def pnlCalculation(String id = PNL_EXPLAIN_CALCULATION_ID) {
    PnlExplainCalculation.builder()
    .id(id)
    .portfolioId(PORTFOLIO_ID)
    .build()
  }

  def pnlCalculationResult() {
    new PnlExplainCalculationResult(
    PNL_EXPLAIN_CALCULATION_ID,
    null,
    null,
    null,
    null)
  }
}
