package com.solum.xplain.calculation.discounting.currency.type

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import spock.lang.Specification

class CreditCurrencyResolverTest extends Specification {
  def RESOLVER = new CreditCurrencyResolver()

  def "should return correct product type"() {
    expect:
    RESOLVER.productTypes() == [CoreProductType.CREDIT_INDEX, CoreProductType.CDS, CoreProductType.CREDIT_INDEX_TRANCHE]
  }

  def "should resolve CREDIT_INDEX currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.creditIndexTradeDetails(), null) == Currency.EUR
  }

  def "should resolve CDS currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.cdsTradeDetails(), null) == Currency.EUR
  }
}
