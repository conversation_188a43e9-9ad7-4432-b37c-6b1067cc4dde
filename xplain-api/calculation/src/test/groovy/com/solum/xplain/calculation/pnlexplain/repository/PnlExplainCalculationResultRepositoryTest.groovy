package com.solum.xplain.calculation.pnlexplain.repository


import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculationResult
import com.solum.xplain.calculation.pnlexplain.value.result.PortfolioItemCalculatedPnlExplainResult
import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class PnlExplainCalculationResultRepositoryTest extends IntegrationSpecification {

  @Resource
  PnlExplainCalculationResultRepository repository
  @Resource
  MongoOperations operations

  def cleanup() {
    operations.dropCollection(PnlExplainCalculationResult)
  }

  def "should get pnl explain result for calculation result"() {
    setup:
    def stateDate = LocalDate.now()
    def secondDate = stateDate.plusDays(1)
    def calcId = "calculation1"
    def calcResult = new PnlExplainCalculationResult(calcId, null, stateDate, secondDate, [result()])
    operations.insert(calcResult)

    when:
    def result = repository.getByPnlExplainCalculationId(calcId)
    def result2 = repository.getByPnlExplainCalculationId("calculation2")

    then:
    result.pnlExplainCalculationId == calcId
    result.tradePnlExplainResults[0].metricsFirstValDateFirstCurveDatePresentValue == 100
    result.valuationDate == stateDate
    result.secondValuationDate == secondDate

    result2 == null
  }

  def "should delete pnl explain result for calculation result"() {
    setup:
    def calcId = "calculation1"
    def calcResult = new PnlExplainCalculationResult(calcId, null, null, null, [result()])
    operations.insert(calcResult)

    def resultBeforeDelete = repository.getByPnlExplainCalculationId(calcId)

    when:
    repository.deleteByPnlExplainCalculationId(calcId)
    def resultAfterDelete = repository.getByPnlExplainCalculationId(calcId)

    then:
    resultBeforeDelete.pnlExplainCalculationId == calcId
    resultAfterDelete == null
  }

  def result() {
    return PortfolioItemCalculatedPnlExplainResult.builder()
      .metricsFirstValDateFirstCurveDatePresentValue(100)
      .build()
  }
}
