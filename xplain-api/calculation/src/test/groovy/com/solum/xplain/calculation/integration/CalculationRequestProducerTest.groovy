package com.solum.xplain.calculation.integration

import com.solum.xplain.calculation.value.TradeCalculationRequest
import com.solum.xplain.core.config.properties.CalculationProperties
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import org.springframework.kafka.core.KafkaTemplate
import spock.lang.Specification

class CalculationRequestProducerTest extends Specification {
  KafkaTemplate<String, ValuationRequest> kafkaTemplate = Mock()
  TradeRequestMapper requestMapper = Mock()
  CalculationProperties calculationProperties = Mock()

  CalculationRequestProducer producer = new CalculationRequestProducer(kafkaTemplate, requestMapper, calculationProperties)

  def "should send request"() {
    setup:
    def valuationRequest = Mock(ValuationRequest)
    def request = Mock(TradeCalculationRequest)

    when:
    producer.sendRequest(request)

    then:
    1 * requestMapper.valuationRequest(request) >> valuationRequest
    1 * calculationProperties.getValuationsTopic() >> "topic"
    1 * kafkaTemplate.send("topic", valuationRequest)
  }
}
