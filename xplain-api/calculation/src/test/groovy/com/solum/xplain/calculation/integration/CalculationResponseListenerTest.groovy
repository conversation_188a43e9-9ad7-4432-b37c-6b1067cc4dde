package com.solum.xplain.calculation.integration

import com.solum.xplain.calculation.simulation.ccyexposure.integration.CcyExposureSimulationResponseHandler
import com.solum.xplain.calculation.simulation.daterange.integration.DateRangeSimulationResponseHandler
import com.solum.xplain.extensions.simulations.SimulationType
import com.solum.xplain.valuation.messages.metrics.ValuationResponse
import spock.lang.Specification

class CalculationResponseListenerTest extends Specification {
  static String SIMULATION_ID = "SIMULATION"
  static String SIMULATION_ID_2 = "SIMULATION_2"
  static String CALCULATION_ID = "CALCULATION"

  CalculationResponseHandler calculationHandler = Mock()
  DateRangeSimulationResponseHandler dateResponseSimulationHandler = Mock()
  CcyExposureSimulationResponseHandler ccyExposureSimulationResponseHandler = Mock()

  def listener = new CalculationResponseListener(calculationHandler, dateResponseSimulationHandler, ccyExposureSimulationResponseHandler)

  def "should handle valuation responses"() {
    setup:
    def response = new ValuationResponse(calculationId: CALCULATION_ID)
    def responses = [response]

    when:
    listener.listen(responses)

    then:
    1 * calculationHandler.handle(CALCULATION_ID, [response])
  }

  def "should handle simulation responses"() {
    setup:
    def response = new ValuationResponse(simulationId: SIMULATION_ID, simulationType: SimulationType.DATE_RANGE)
    def responses = [response]

    when:
    listener.listen(responses)

    then:
    1 * dateResponseSimulationHandler.handle(SIMULATION_ID, [response])
  }

  def "should handle mixed responses date rate"() {
    setup:
    def calculationResponse = new ValuationResponse(calculationId: CALCULATION_ID)
    def simulationResponse = new ValuationResponse(simulationId: SIMULATION_ID, simulationType: SimulationType.DATE_RANGE)
    def ccyExposureSimulationResponse = new ValuationResponse(simulationId: SIMULATION_ID_2, simulationType: SimulationType.CCY_EXPOSURE)
    def responses = [calculationResponse, simulationResponse, ccyExposureSimulationResponse]

    when:
    listener.listen(responses)

    then:
    1 * dateResponseSimulationHandler.handle(SIMULATION_ID, [simulationResponse])
    1 * ccyExposureSimulationResponseHandler.handle(SIMULATION_ID_2, [ccyExposureSimulationResponse])
    1 * calculationHandler.handle(CALCULATION_ID, [calculationResponse])
  }
}
