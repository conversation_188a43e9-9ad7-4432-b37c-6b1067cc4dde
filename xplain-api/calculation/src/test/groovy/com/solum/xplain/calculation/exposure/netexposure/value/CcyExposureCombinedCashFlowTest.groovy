package com.solum.xplain.calculation.exposure.netexposure.value

import com.solum.xplain.core.portfolio.CoreProductType
import java.time.LocalDate
import spock.lang.Specification

class CcyExposureCombinedCashFlowTest extends Specification {

  def "should correctly flatten FX Option CcyExposureCombinedCashFlow"() {
    setup:
    def ccyExposureCombinedCashFlow = new CcyExposureCombinedCashFlow()
    ccyExposureCombinedCashFlow.productType = CoreProductType.FXOPT
    ccyExposureCombinedCashFlow.paymentDate = LocalDate.parse("2020-01-01")
    ccyExposureCombinedCashFlow.notionalNetExposure = 100.0
    ccyExposureCombinedCashFlow.deltaNetExposure = 200.0
    ccyExposureCombinedCashFlow.presentValue = 1555.0

    expect:
    !ccyExposureCombinedCashFlow.isForward()
    ccyExposureCombinedCashFlow.isOption()
    ccyExposureCombinedCashFlow.hasNetExposure()
    ccyExposureCombinedCashFlow.hasDeltaExposure()

    with (ccyExposureCombinedCashFlow.asNetExposure()) { firstCashFlow ->
      firstCashFlow.getAmount() == 100.0
      firstCashFlow.getDate() == LocalDate.parse("2020-01-01")
      firstCashFlow.getPresentValue() == 1555.0
    }

    with (ccyExposureCombinedCashFlow.asDeltaExposure()) { secondCashFlow ->
      secondCashFlow.getAmount() == 200.0
      secondCashFlow.getDate() == LocalDate.parse("2020-01-01")
      secondCashFlow.getPresentValue() == null
    }
  }

  def "should return CcyExposureCalculatedCashFlow (no flattening)"() {
    setup:
    def ccyExposureCombinedCashFlow = new CcyExposureCombinedCashFlow()
    ccyExposureCombinedCashFlow.productType = CoreProductType.FXFWD
    ccyExposureCombinedCashFlow.paymentDate = LocalDate.parse("2020-01-01")
    ccyExposureCombinedCashFlow.notionalNetExposure = 100.0

    expect:
    ccyExposureCombinedCashFlow.isForward()
    !ccyExposureCombinedCashFlow.isOption()
    ccyExposureCombinedCashFlow.hasNetExposure()
    !ccyExposureCombinedCashFlow.hasDeltaExposure()

    with (ccyExposureCombinedCashFlow.asNetExposure()) { firstCashFlow ->
      firstCashFlow.getAmount() == 100.0
      firstCashFlow.getDate() == LocalDate.parse("2020-01-01")
    }
    ccyExposureCombinedCashFlow.asDeltaExposure() == null
  }
}
