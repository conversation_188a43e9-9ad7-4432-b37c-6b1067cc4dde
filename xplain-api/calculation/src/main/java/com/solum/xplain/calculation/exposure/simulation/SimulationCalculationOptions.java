package com.solum.xplain.calculation.exposure.simulation;

import com.solum.xplain.calculation.exposure.CcyExposureCalculationOptions;
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.bson.types.ObjectId;

@Data
public class SimulationCalculationOptions extends CcyExposureCalculationOptions {
  @Valid @NotNull private ShiftDataType shiftDataType;

  public SimulationCalculationOptions copyWithCalculationResultId(ObjectId calculationResultId) {

    SimulationCalculationOptions options = new SimulationCalculationOptions();
    options.setExposureCurrency(exposureCurrency);
    options.setExposureIds(exposureIds);
    options.setStateDate(stateDate);
    options.setHorizonStartDate(horizonStartDate);
    options.setHorizonEndDate(horizonEndDate);
    options.setCcyExposureFrequency(ccyExposureFrequency);
    options.setHedgeProfile(hedgeProfile);
    options.setCalculationResultId(calculationResultId);
    options.setShiftDataType(shiftDataType);
    return options;
  }
}
