package com.solum.xplain.calculation.simulation;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_RUN_PV_CALCULATIONS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CALCULATION_RESULTS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CCY_EXPOSURE_SIM;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureSimulationCalculationView;
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationView;
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationErrorItemView;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/simulations")
public class SimulationCalculationController {

  private final SimulationControllerService service;

  @Operation(summary = "Get date-range simulations")
  @GetMapping()
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CALCULATION_RESULTS)
  public ScrollableEntry<DateRangeSimulationCalculationView> getSimulations(
      ScrollRequest scrollRequest, TableFilter tableFilter) {
    return service.getDateRangeSimulationCalculations(scrollRequest, tableFilter);
  }

  @Operation(summary = "Get ccy exposure simulations")
  @GetMapping("/ccy-exposure")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE_SIM)
  public ScrollableEntry<CcyExposureSimulationCalculationView> getCcyExposureSimulations(
      ScrollRequest scrollRequest, TableFilter tableFilter) {
    return service.getCcyExposureSimulationCalculations(scrollRequest, tableFilter);
  }

  @Operation(summary = "Delete ccy exposure simulation")
  @DeleteMapping("/ccy-exposure/{exposureSimulationId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_PV_CALCULATIONS)
  public ResponseEntity<EntityId> deleteExposureSimulation(
      @ValidObjectId @PathVariable String exposureSimulationId) {
    return eitherErrorItemResponse(
        service.deleteCcyExposureSimulationCalculation(exposureSimulationId));
  }

  @Operation(summary = "Delete all date-range simulations")
  @DeleteMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_PV_CALCULATIONS)
  public List<EntityId> deleteAllSimulation() {
    return service.deleteDateRangeSimulations();
  }

  @Operation(summary = "Delete date-range simulation")
  @DeleteMapping("/{simulationId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_PV_CALCULATIONS)
  public ResponseEntity<EntityId> deleteSimulation(
      @ValidObjectId @PathVariable String simulationId) {
    return eitherErrorItemResponse(service.deleteDateRangeSimulationCalculation(simulationId));
  }

  @Operation(summary = "Get date-range simulation error logs")
  @GetMapping("/{simulationId}/logs")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE_SIM)
  public ResponseEntity<ScrollableEntry<DateRangeSimulationErrorItemView>> getSimulationLogs(
      @ValidObjectId @PathVariable String simulationId, ScrollRequest scrollRequest) {
    return eitherErrorItemResponse(
        service.dateRangeSimulationErrorsScroll(simulationId, scrollRequest));
  }

  @Operation(summary = "Get date-range simulation error logs csv")
  @GetMapping("/{simulationId}/logs/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CCY_EXPOSURE_SIM)
  public ResponseEntity<ByteArrayResource> getSimulationLogsCsv(
      @ValidObjectId @PathVariable String simulationId) {
    return eitherErrorItemFileResponse(service.dateRangeSimulationErrorsCsv(simulationId));
  }
}
