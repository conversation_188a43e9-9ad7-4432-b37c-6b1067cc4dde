package com.solum.xplain.calculation.form;

import static org.mapstruct.factory.Mappers.getMapper;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.validation.BaseCalculationFormGroupProvider;
import com.solum.xplain.calculation.validation.ValidCalculationMarketDataGroup;
import com.solum.xplain.calculation.value.CalculationDiscounting;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.CalculationDiscountingForm;
import com.solum.xplain.core.company.form.FxVsIrsConfigurationGroup;
import com.solum.xplain.core.company.form.SingleConfigurationGroup;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsMapper;
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@ValidCalculationMarketDataGroup
@GroupSequenceProvider(BaseCalculationFormGroupProvider.class)
public abstract class BaseCalculationForm implements CalculationForm {

  @NotNull
  @Schema(description = "State date", example = "2023-01-31")
  private LocalDate stateDate;

  @ValidStringSet(CurrenciesSupplier.class)
  @NotEmpty
  @Schema(description = "Calculation currency", example = "EUR")
  private String calculationCurrency;

  @NotNull
  @Valid
  @Schema(description = "Discounting configuration")
  private CalculationDiscountingForm curveDiscountingForm;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "curveConfigurationType")
  @Schema(
      description = "Curves configuration type",
      allowableValues = {"SINGLE", "FX_V_IRS"})
  private String configurationType;

  @Valid
  @NotNull
  @Schema(description = "Curves configuration")
  private CalculationConfigForm curveConfiguration;

  @Valid
  @NotNull(groups = FxVsIrsConfigurationGroup.class)
  @Null(groups = SingleConfigurationGroup.class)
  @Schema(description = "Curves configuration for non-fx products (if FX_V_IRS type is used)")
  private CalculationConfigForm nonFxCurveConfiguration;

  @NotEmpty
  @Schema(description = "Market data group id")
  private String marketDataGroupId;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "marketDataSourceType")
  @Schema(
      description = "Market data type",
      allowableValues = {
        "RAW_PRIMARY",
        "RAW_SECONDARY",
        "PRELIMINARY_PRIMARY",
        "PRELIMINARY_SECONDARY",
        "OVERLAY"
      })
  private String marketDataSource;

  @ValidObjectId
  @Schema(description = "Trade id to valuate, all trades if not provided", nullable = true)
  private String tradeId;

  @Schema(
      description = "Market data price type requirements, default BID_PRICE for all",
      nullable = true)
  private InstrumentPriceRequirementsForm priceRequirements;

  @Override
  public MarketDataSourceType marketDataSource() {
    return MarketDataSourceType.valueOf(marketDataSource);
  }

  @Override
  public CalculationDiscounting calculationDiscounting() {
    return new CalculationDiscounting(
        curveDiscountingForm.discountingType(),
        curveDiscountingForm.strippingType(),
        curveDiscountingForm.triangulationCcy(),
        BooleanUtils.isTrue(curveDiscountingForm.getUseCsaDiscounting()));
  }

  @Override
  public CurveConfigurationType configurationType() {
    return CurveConfigurationType.valueOf(configurationType);
  }

  @Override
  public CalculationConfigForm calculationConfiguration() {
    return ObjectUtils.firstNonNull(nonFxCurveConfiguration, curveConfiguration);
  }

  @Override
  public CalculationConfigForm fxCalculationConfiguration() {
    return curveConfiguration;
  }

  @Override
  public Currency reportingCurrency() {
    return Currency.of(calculationCurrency);
  }

  @Override
  public PortfolioCalculationType calculationType() {
    return PortfolioCalculationType.TRADES;
  }

  @Override
  public Optional<String> tradeId() {
    return Optional.ofNullable(tradeId);
  }

  @Override
  public InstrumentPriceRequirements priceRequirements() {
    return getMapper(InstrumentPriceRequirementsMapper.class).fromForm(priceRequirements);
  }
}
