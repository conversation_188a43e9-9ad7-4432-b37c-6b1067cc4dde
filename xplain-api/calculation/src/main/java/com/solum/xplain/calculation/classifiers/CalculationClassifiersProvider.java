package com.solum.xplain.calculation.classifiers;

import static com.solum.xplain.core.classifiers.ClassifierUtils.enumClassifier;
import static com.solum.xplain.core.classifiers.ClassifierUtils.sortById;

import com.solum.xplain.calculation.CalculationResultScope;
import com.solum.xplain.calculation.CalculationResultStatus;
import com.solum.xplain.calculation.curves.value.CalculationConfigurationType;
import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CalculationClassifiersProvider implements ClassifiersProvider {

  @Override
  public List<Classifier> classifiers() {
    return List.of(calculationResultStatus(), calculationResultScope(), configurationType());
  }

  @Override
  public int sortOrder() {
    return 1;
  }

  private Classifier calculationResultStatus() {
    return sortById(
        enumClassifier(
            "calculationResultStatus",
            CalculationResultStatus.class,
            CalculationResultStatus::name,
            CalculationResultStatus::getLabel));
  }

  private Classifier calculationResultScope() {
    return enumClassifier(
        "calculationResultScope",
        CalculationResultScope.class,
        CalculationResultScope::name,
        CalculationResultScope::getLabel);
  }

  private Classifier configurationType() {
    return sortById(
        enumClassifier(
            "configurationType",
            CalculationConfigurationType.class,
            CalculationConfigurationType::name));
  }
}
