package com.solum.xplain.calculation.curvegroup;

import static java.util.stream.Collectors.toMap;

import com.solum.xplain.calibration.rates.CalibrationCombinedResultRates;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.lang.NonNull;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveGroupDiscountRatesResult {
  private final Map<DiscountingGroup, CalibrationCombinedResultRates> rates;
  private final List<CalibratedCurve> calculatedCurves;
  private final boolean sensitivitiesSupported;

  public static <T extends DiscountingGroup> CurveGroupDiscountRatesResult newOf(
      @NonNull Map<T, CalibrationCombinedResultRates> resultMap) {
    var normalized =
        resultMap.entrySet().stream()
            .collect(toMap(c -> (DiscountingGroup) c.getKey(), Map.Entry::getValue));
    var charts =
        resultMap.entrySet().stream()
            .map(e -> e.getValue().calibratedCurves(e.getKey()))
            .flatMap(Collection::stream)
            .toList();

    return new CurveGroupDiscountRatesResult(normalized, charts, true);
  }
}
