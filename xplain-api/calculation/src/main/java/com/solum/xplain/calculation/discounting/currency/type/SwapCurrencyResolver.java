package com.solum.xplain.calculation.discounting.currency.type;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.discounting.currency.DiscountCurrencyResolver;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class SwapCurrencyResolver implements DiscountCurrencyResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.IRS, CoreProductType.INFLATION, CoreProductType.SWAPTION);
  }

  @Override
  public Currency resolveCurrency(
      TradeDetails tradeDetails, ProductSettingsResolver settingsResolver) {
    return tradeDetails
        .legsStream()
        .map(TradeLegDetails::currency)
        .distinct()
        .findAny()
        .orElseThrow(
            () -> new IllegalArgumentException("Invalid Swap trade, currency is missing!"));
  }
}
