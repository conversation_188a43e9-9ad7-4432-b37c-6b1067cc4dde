package com.solum.xplain.calculation.integration;

import static com.solum.xplain.core.portfolio.trade.HedgeTradeDetails.IS_POTENTIAL_HEDGE_CUSTOM_FIELD_ID;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.value.TradeCalculationRequest;
import com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.CreditTradeDetails;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.LoanNoteTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.TradeDetailsResolver;
import com.solum.xplain.valuation.messages.trade.ValuationCreditTradeDetails;
import com.solum.xplain.valuation.messages.trade.ValuationHedgeTradeDetails;
import com.solum.xplain.valuation.messages.trade.ValuationLoanNoteTradeDetails;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails;
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo;
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails;
import com.solum.xplain.valuation.messages.trade.constant.ValuationProductType;
import jakarta.annotation.Nullable;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(imports = Currency.class)
public abstract class TradeRequestMapper {

  @Autowired TradeDetailsResolver tradeDetailsResolver;

  ValuationRequest valuationRequest(TradeCalculationRequest request) {
    var portfolioItem = request.getPortfolioItem();
    var tradeDetails = portfolioItem.getTradeDetails();
    var customFieldDetails = portfolioItem.getCustomFields();
    var externalIdentifiers = portfolioItem.getExternalIdentifiers();
    var clientPv =
        portfolioItem.getClientMetrics() == null
            ? null
            : portfolioItem.getClientMetrics().getPresentValue();
    var tradeUnderlying =
        tradeDetailsResolver.resolveUnderlying(portfolioItem.getProductType(), tradeDetails);
    var valuationRequest = new ValuationRequest();

    valuationRequest.setCalculationId(request.getCalculationId());
    valuationRequest.setSimulationId(request.getSimulationId());
    valuationRequest.setSimulationType(request.getSimulationType());
    valuationRequest.setValuationDate(request.getValuationDate());
    valuationRequest.setTradeId(portfolioItem.getEntityId());
    valuationRequest.setExternalTradeId(portfolioItem.getExternalTradeId());
    valuationRequest.setPortfolioId(portfolioItem.getPortfolioId().toHexString());
    valuationRequest.setDescription(portfolioItem.getDescription());

    valuationRequest.setTradeDetails(
        fromTradeDetails(
            tradeDetails,
            portfolioItem.getProductType(),
            clientPv,
            tradeUnderlying,
            customFieldDetails,
            externalIdentifiers));
    valuationRequest.setProductType(fromProductType(portfolioItem.getProductType()));

    valuationRequest.setReportingCurrency(request.getReportingCcy().getCode());
    valuationRequest.setCalculateSensitivities(request.isCalculateSensitivities());
    request
        .itemDiscounting()
        .ifPresent(
            discounting -> {
              valuationRequest.setDiscountingKey(discounting.key());
              valuationRequest.setDiscountingCcy(discounting.getCurrency().getCode());
            });

    return valuationRequest;
  }

  @Mapping(
      target = "creditTradeDetails",
      expression =
          "java(creditTradeDetails(tradeDetails.getCreditTradeDetails(), tradeDetails.tradeCurrency(), productType))")
  @Mapping(
      target = "info",
      expression =
          "java(fromTradeInfo(tradeDetails.getInfo(), clientPv, xplainResolvedTradeUnderlying))")
  @Mapping(
      target = "hedgeTradeDetails",
      expression = "java(hedgeTradeDetails(productType, customFieldDetails))")
  @Mapping(target = "tradeInfoCustomFields", source = "customFieldDetails")
  @Mapping(target = "tradeInfoExternalIdentifiers", source = "tradeInfoExternalIdentifiers")
  abstract ValuationTradeDetails fromTradeDetails(
      TradeDetails tradeDetails,
      ProductType productType,
      Double clientPv,
      String xplainResolvedTradeUnderlying,
      List<CustomTradeField> customFieldDetails,
      List<ExternalIdentifier> tradeInfoExternalIdentifiers);

  @Mapping(target = "yieldConvention", expression = "java(yieldConvention(details))")
  abstract ValuationLoanNoteTradeDetails fromLoanDetails(LoanNoteTradeDetails details);

  String yieldConvention(LoanNoteTradeDetails details) {
    return BondCurveConventions.findBondCurveYieldConventionByName(details.getReference())
        .map(Enum::name)
        .orElseThrow();
  }

  abstract ValuationTradeLegDetails fromLegDetails(TradeLegDetails legDetails);

  abstract ValuationTradeInfo fromTradeInfo(
      TradeInfoDetails infoDetails, Double clientPv, String xplainResolvedTradeUnderlying);

  ValuationProductType fromProductType(ProductType productType) {
    return ValuationProductType.valueOf(productType.name());
  }

  abstract ValuationCreditTradeDetails creditTradeDetails(
      CreditTradeDetails details, String creditCurveName);

  ValuationCreditTradeDetails creditTradeDetails(
      CreditTradeDetails details, Currency currency, ProductType productType) {
    if (ObjectUtils.allNotNull(details, currency, productType)) {
      var creditCurveName = details.legalEntityStandardId(currency, productType).getValue();
      return creditTradeDetails(details, creditCurveName);
    }
    return creditTradeDetails(details, null);
  }

  /**
   * Returns hedge trade details for a trade - potential hedge flag and the collar id. Used as Java
   * expression in {@link #fromTradeDetails(TradeDetails, ProductType, Double, String, List)}
   * mapping.
   *
   * <p>TODO SXSD-8822: Note that this relies on custom fields with magic identifiers.
   *
   * @param productType trade product type
   * @param customFieldDetails custom fields from the trade record
   * @return the hedge trade details
   */
  @SuppressWarnings("unused")
  ValuationHedgeTradeDetails hedgeTradeDetails(
      ProductType productType, List<CustomTradeField> customFieldDetails) {
    if (productType != CoreProductType.FXOPT
        && productType != CoreProductType.FXFWD
        && productType != CoreProductType.FXSWAP
        && productType != CoreProductType.FXCOLLAR) {
      return null;
    }

    ValuationHedgeTradeDetails hedgeTradeDetails = new ValuationHedgeTradeDetails();

    hedgeTradeDetails.setPotentialHedge(isPotentialHedge(customFieldDetails));
    return hedgeTradeDetails;
  }

  private @Nullable Boolean isPotentialHedge(@Nullable List<CustomTradeField> customFieldDetails) {
    // TODO (TECH DEBT): SXSD-8822 - find a better solution than custom fields with magic
    // identifiers.
    return customFieldDetails != null
        ? customFieldDetails.stream()
            .filter(field -> field.getExternalFieldId().equals(IS_POTENTIAL_HEDGE_CUSTOM_FIELD_ID))
            .map(CustomTradeField::getValue)
            .findFirst()
            .map(Boolean::parseBoolean)
            .orElse(null)
        : null;
  }
}
