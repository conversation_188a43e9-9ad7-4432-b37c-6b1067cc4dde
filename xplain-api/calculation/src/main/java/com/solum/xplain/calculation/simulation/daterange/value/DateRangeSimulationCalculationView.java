package com.solum.xplain.calculation.simulation.daterange.value;

import com.solum.xplain.calculation.CalculationResultMarketData;
import com.solum.xplain.calculation.CalculationResultStatus;
import com.solum.xplain.calculation.CalculationTradesStatistics;
import com.solum.xplain.calculation.value.CalculationConfigurationData;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class DateRangeSimulationCalculationView {

  private String id;
  private String name;

  private CalculationResultStatus calculationResultStatus;
  private CalculationTradesStatistics tradesStatistics;

  private String portfolioId;
  private String portfolioExternalId;
  private String companyExternalId;
  private String entityExternalId;

  private CalculationResultMarketData marketData;
  private CalculationConfigurationData fxConfigurationData;
  private CalculationConfigurationData configurationData;
  private LocalDate curveDate;
  private LocalDate valuationStartDate;
  private LocalDate valuationEndDate;
  private LocalDate stateDate;
  private LocalDateTime recordDate;
  private String reportingCcy;
  private CalculationDiscountingType discountingType;
  private CalculationStrippingType strippingType;
  private String triangulationCcy;

  private AuditUser createdBy;
  private LocalDateTime createdAt;
}
