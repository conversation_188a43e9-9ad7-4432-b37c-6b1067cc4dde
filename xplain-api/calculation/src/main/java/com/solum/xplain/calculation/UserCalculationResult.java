package com.solum.xplain.calculation;

import com.solum.xplain.calculation.value.CalculationResultView;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import io.atlassian.fugue.Either;

public class UserCalculationResult {
  private final UserTeamEntity<PortfolioView> userPortfolio;
  private final CalculationResultView view;

  public UserCalculationResult(
      UserTeamEntity<PortfolioView> userPortfolio, CalculationResultView view) {
    this.userPortfolio = userPortfolio;
    this.view = view;
  }

  public static UserCalculationResult userCalculationResult(
      XplainPrincipal user, CalculationResultView view, PortfolioView portfolioView) {
    return new UserCalculationResult(UserTeamEntity.userEntity(user, portfolioView), view);
  }

  public Either<ErrorItem, UserCalculationResult> allowTeamOnly() {
    return userPortfolio.allowTeamsOnly().map(u -> this);
  }

  public CalculationResultView getView() {
    return view;
  }

  public UserTeamEntity<PortfolioView> getUserPortfolio() {
    return userPortfolio;
  }
}
