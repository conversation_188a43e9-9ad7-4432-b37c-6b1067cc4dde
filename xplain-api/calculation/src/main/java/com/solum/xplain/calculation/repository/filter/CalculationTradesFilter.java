package com.solum.xplain.calculation.repository.filter;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.latestItemsCriteria;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.lang.NonNull;

@EqualsAndHashCode
@ToString
public abstract class CalculationTradesFilter implements TradeFilter {

  @NonNull private final String portfolioId;
  @NonNull private final BitemporalDate stateDate;
  @NonNull private final List<ProductType> productTypes;

  protected CalculationTradesFilter(
      @NonNull String portfolioId, @NonNull BitemporalDate stateDate) {
    this(portfolioId, stateDate, SUPPORTED_TYPES);
  }

  protected CalculationTradesFilter(
      @NonNull String portfolioId,
      @NonNull BitemporalDate stateDate,
      @NonNull List<ProductType> productTypes) {
    this.portfolioId = portfolioId;
    this.stateDate = stateDate;
    this.productTypes = productTypes;
  }

  public Criteria tradesCriteria() {
    var baseCriteria =
        latestItemsCriteria(
            where(PortfolioItem.Fields.portfolioId)
                .is(new ObjectId(portfolioId))
                .and(VersionedTradeEntity.Fields.productType)
                .in(productTypes.stream().filter(SUPPORTED_TYPES::contains).toList()),
            stateDate,
            active());
    return buildCriteria(baseCriteria);
  }

  abstract Criteria buildCriteria(Criteria baseCriteria);
}
