package com.solum.xplain.calculation.discounting.tradeinfosubsets;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.CreditTradeDetailsCurveNameResolver;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import io.atlassian.fugue.Either;
import java.util.Collections;
import java.util.Set;
import lombok.Data;

@Data
public class CdxPortfolioItemDiscountingGroupSpecification
    implements PortfolioItemDiscountingGroupSpecification {

  private final String currency;
  private final String creditReference;

  private final String csaDiscountingGroup;

  @Override
  public CoreProductType productType() {
    return CoreProductType.CREDIT_INDEX;
  }

  @Override
  public ClearingHouse clearingHouse() {
    return ClearingHouse.NONE;
  }

  @Override
  public Currency resolveCurrency(ProductSettingsResolver settingsResolver) {
    return Currency.of(currency);
  }

  @Override
  public Currency tradeCurrency() {
    return Currency.of(currency);
  }

  @Override
  public boolean isOffshore() {
    return false;
  }

  @Override
  public CurrencyPair currencyPair() {
    return null;
  }

  @Override
  public Either<ErrorItem, FloatingRateIndex> resolveIndex(
      Set<FloatingRateIndex> availableIndices,
      ProductSettingsResolver productSettingsResolver,
      DiscountingIndexResolver discountingIndexResolver) {
    Currency tradeCcy = Currency.of(currency);
    return discountingIndexResolver.resolveDiscountingIndexFromCcy(
        tradeCcy, productSettingsResolver.resolveCdsCurvePriorities(), availableIndices);
  }

  @Override
  public Set<FloatingRateIndex> tradeRateIndices() {
    return Collections.emptySet();
  }

  public String creditCurveName() {
    return CreditTradeDetailsCurveNameResolver.curveName(
        currency, productType(), creditReference, null, null, null);
  }
}
