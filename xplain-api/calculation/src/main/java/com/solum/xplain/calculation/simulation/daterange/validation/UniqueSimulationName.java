package com.solum.xplain.calculation.simulation.daterange.validation;

import com.solum.xplain.extensions.simulations.SimulationType;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueSimulationNameValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface UniqueSimulationName {
  String message() default
      "{com.solum.xplain.api.calculation.simulation.validation.UniqueSimulationName.message}";

  SimulationType type() default SimulationType.DATE_RANGE;

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
