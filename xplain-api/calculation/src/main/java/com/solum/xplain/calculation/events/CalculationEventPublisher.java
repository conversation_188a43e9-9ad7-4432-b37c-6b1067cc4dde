package com.solum.xplain.calculation.events;

import com.solum.xplain.core.calculationapi.CalculationEventProducer;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class CalculationEventPublisher implements CalculationEventProducer {

  private final ApplicationEventPublisher publisher;

  @Override
  public void publishDeleteEvent(String calculationId) {
    publisher.publishEvent(new CalculationDeletedEvent(calculationId));
  }

  @Override
  public void publishCancelEvent(
      String portfolioId,
      ObjectId calculationId,
      String dashboardId,
      String pnlExplainCalculationId) {
    publisher.publishEvent(
        CalculationCanceledEvent.newOf(
            calculationId, portfolioId, dashboardId, pnlExplainCalculationId, null));
  }
}
