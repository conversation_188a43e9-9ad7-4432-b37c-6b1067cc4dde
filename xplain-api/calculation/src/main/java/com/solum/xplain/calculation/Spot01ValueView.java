package com.solum.xplain.calculation;

import com.opengamma.strata.basics.currency.CurrencyPair;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class Spot01ValueView {
  @Schema(description = "Spot 01 value currency pair")
  private String currencyPair;

  @Schema(description = "Aggregated Spot01 value for given currency")
  private BigDecimal spot01;

  @Schema(description = "Fx Spot rate for given currency pair")
  private BigDecimal fxSpot;

  @Schema(description = "Base currency for given currency pair")
  public String getCurrency() {
    return CurrencyPair.parse(currencyPair).getBase().toString();
  }
}
