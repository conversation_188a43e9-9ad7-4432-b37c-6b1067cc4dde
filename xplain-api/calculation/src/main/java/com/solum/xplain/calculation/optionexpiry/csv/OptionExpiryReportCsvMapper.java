package com.solum.xplain.calculation.optionexpiry.csv;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReport;
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class OptionExpiryReportCsvMapper {

  private static final String ITM_EXP_COLUMN = "ITM\\EXP";
  private final OptionExpiryReport expiryReport;
  private final OptionExpiryReportType type;

  public CsvOutputFile toCsv() {
    return new CsvOutputFile(headers(), rows());
  }

  private List<String> headers() {
    var builder = ImmutableList.<String>builder();
    builder.add(ITM_EXP_COLUMN);
    builder.addAll(expiryReport.getExpiry().stream().map(BigDecimal::toString).toList());
    return builder.build();
  }

  private List<CsvRow> rows() {
    var builder = ImmutableList.<CsvRow>builder();
    for (int i = 0; i < expiryReport.getValues().get(type).length; i++) {
      var itm = expiryReport.getItm().get(i);
      builder.add(row(itm, expiryReport.getValues().get(type)[i]));
    }
    return builder.build();
  }

  private CsvRow row(BigDecimal itm, BigDecimal[] value) {
    var headers = headers();
    var builder = ImmutableList.<CsvField>builder();
    builder.add(new CsvField(headers.get(0), itm));
    for (int i = 0; i < value.length; i++) {
      builder.add(new CsvField(headers.get(i + 1), value[i]));
    }
    return new CsvRow(builder.build());
  }
}
