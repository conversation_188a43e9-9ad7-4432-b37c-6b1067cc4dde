package com.solum.xplain.calculation.simulation.daterange.csv;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvRow;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DateRangeSimulationErrorCsvMapper {
  private static final String VALUATION_DATE_FIELD = "Valuation Date";
  private static final String REASON_FIELD = "Reason";
  private static final String DESCRIPTION_FIELD = "Description";

  public static final List<String> HEADER =
      List.of(VALUATION_DATE_FIELD, REASON_FIELD, DESCRIPTION_FIELD);

  public static CsvRow toCsvFields(DateRangeSimulationErrorItem simulationError) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();

    builder.add(new CsvField(VALUATION_DATE_FIELD, simulationError.getValuationDate()));
    builder.add(new CsvField(REASON_FIELD, simulationError.getReason()));
    builder.add(new CsvField(DESCRIPTION_FIELD, simulationError.getDescription()));
    return new CsvRow(builder.build());
  }
}
