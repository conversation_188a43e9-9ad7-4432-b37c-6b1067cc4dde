package com.solum.xplain.calculation.pnlexplain.permissions;

import com.solum.xplain.calculation.pnlexplain.PnlExplainEnabled;
import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.provider.PermissionCategoryProvider;
import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@PnlExplainEnabled
public class PnlExplainPermissionCategoryProvider implements PermissionCategoryProvider {

  @Override
  public List<PermissionCategory> permissionCategories() {
    return Arrays.asList(PnlExplainPermissionCategory.values());
  }
}
