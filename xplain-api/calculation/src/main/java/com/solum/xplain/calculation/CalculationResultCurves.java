package com.solum.xplain.calculation;

import static com.solum.xplain.calculation.CalculationResultCurves.COLLECTION_NAME;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static org.apache.commons.collections4.ListUtils.emptyIfNull;

import com.solum.xplain.calculation.curves.value.CalculationConfigurationType;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = COLLECTION_NAME)
@FieldNameConstants
public class CalculationResultCurves {
  private static final String CURVE_NOT_FOUND = "Curve with name %s not found";

  public static final String COLLECTION_NAME = "calculationResultsCurves";

  @Id private ObjectId id;
  private LocalDate valuationDate;
  private List<CalibratedCurve> curveConfigurationCurves;
  private List<CalibratedCurve> fxCurveConfigurationCurves;

  public Either<ErrorItem, List<CalibratedCurve>> curveData(
      CalculationConfigurationType type, String curveName) {
    var curves =
        calculationResultChartData(type).stream()
            .filter(c -> StringUtils.equals(c.getName(), curveName))
            .toList();
    return curves.isEmpty()
        ? left(OBJECT_NOT_FOUND.entity(String.format(CURVE_NOT_FOUND, curveName)))
        : right(curves);
  }

  private List<CalibratedCurve> calculationResultChartData(CalculationConfigurationType type) {
    return switch (type) {
      case FX -> emptyIfNull(fxCurveConfigurationCurves);
      case DEFAULT -> curveConfigurationCurves;
    };
  }
}
