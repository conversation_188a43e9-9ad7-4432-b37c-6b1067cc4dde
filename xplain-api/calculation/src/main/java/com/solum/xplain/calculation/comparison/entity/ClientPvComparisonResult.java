package com.solum.xplain.calculation.comparison.entity;

import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Data
@FieldNameConstants
public class ClientPvComparisonResult {
  @Id private ObjectId id;
  private String comparisonCurrency;
  private ObjectId correlationId;
  private String calculationId;
  private LocalDateTime createdAt;
  private List<CalibratedCurve> comparisonDifferenceChartData;

  public static ClientPvComparisonResult of(
      String comparisonCurrency, ObjectId correlationId, String calculationId) {
    ClientPvComparisonResult comparisonResult = new ClientPvComparisonResult();
    comparisonResult.comparisonCurrency = comparisonCurrency;
    comparisonResult.correlationId = correlationId;
    comparisonResult.calculationId = calculationId;
    comparisonResult.createdAt = LocalDateTime.now();
    return comparisonResult;
  }
}
