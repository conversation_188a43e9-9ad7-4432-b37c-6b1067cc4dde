package com.solum.xplain.calculation.events;

import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.sockets.constants.CoreSocketEvents;
import com.solum.xplain.core.sockets.events.EventType;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class CalculationsEventListener {
  private final ApplicationEventPublisher eventPublisher;
  private final PortfolioRepository portfolioRepository;

  @EventListener
  public void handleCalculationEvent(CalculationEvent event) {
    eventPublisher.publishEvent(
        CoreSocketEvents.global(EventType.VALUATIONS_UPDATED, event.getCalculationId()));
  }

  @EventListener
  public void onEvent(TradesCalculationRequestedEvent event) {
    portfolioRepository.updateAsStartedCalculation(
        event.portfolioId(), event.getCurrentUser(), event.valuationDate());
  }
}
