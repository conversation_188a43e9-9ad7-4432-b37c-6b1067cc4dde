package com.solum.xplain.calculation.value;

import static com.google.common.collect.ImmutableSortedSet.toImmutableSortedSet;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CashFlowsView {
  private Set<String> currencies;
  private List<DiscountCcyCashFlows> values;

  public static CashFlowsView of(List<DiscountCcyCashFlows> values) {
    return new CashFlowsView(
        values.stream()
            .flatMap(v -> v.getCashFlowValues().stream().map(CashFlowValueView::getCurrency))
            .collect(toImmutableSortedSet(Comparator.naturalOrder())),
        values);
  }
}
