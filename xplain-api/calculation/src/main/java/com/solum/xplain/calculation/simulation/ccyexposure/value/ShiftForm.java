package com.solum.xplain.calculation.simulation.ccyexposure.value;

import com.opengamma.strata.market.ShiftType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class ShiftForm {

  @NotNull
  @Max(5)
  @Min(0)
  Integer numberShifts;

  @NotNull @Positive BigDecimal shiftSize;

  @NotNull ShiftType shiftType;
}
