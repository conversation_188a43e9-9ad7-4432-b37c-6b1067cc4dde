package com.solum.xplain.calculation.simulation.daterange.events;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static java.time.temporal.ChronoUnit.DAYS;

import com.solum.xplain.calculation.PortfolioCalculationData;
import com.solum.xplain.calculation.integration.CalculationRequestProducer;
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder;
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService;
import com.solum.xplain.calculation.simulation.daterange.data.DateRangeSimulationCalculationData;
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem;
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository;
import com.solum.xplain.calculation.value.TradeCalculationRequest;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.extensions.simulations.SimulationType;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@AllArgsConstructor
public class DateRangeSimulationCalculationExecutor {

  private final CalculationTradeCountHolder calculationTradeCountHolder;
  private final DateRangeSimulationCalculationRepository dateRangeSimulationCalculationRepository;
  private final CalibrationCacheService cacheService;
  private final CalculationRequestProducer requestProducer;

  @Async
  @EventListener
  public void process(DateRangeSimulationRequestedEvent event) {
    var startDate = event.getValuationStartDate();
    var endDate = event.getValuationEndDate();
    var tradesCount = calculationCount(event);
    var simulationId = event.getSimulationData().getSimulationId();
    var simulationCount = simulationCount(event, tradesCount);

    var simulation =
        event
            .getSimulationData()
            .performed(
                event.getName(), simulationCount, event.getCurrentUser(), startDate, endDate);
    dateRangeSimulationCalculationRepository.saveSimulation(simulation);
    calculationTradeCountHolder.registerCalculation(simulationId.toHexString(), simulationCount);

    Stream.iterate(startDate, date -> date.plusDays(1))
        .limit(daysBetweenInclusive(startDate, endDate))
        .forEach(date -> calculateDailyValue(date, event.getSimulationData(), tradesCount));
  }

  private void calculateDailyValue(
      LocalDate valuationDate,
      DateRangeSimulationCalculationData simulationData,
      Long tradesCount) {
    var simulationId = simulationData.getSimulationId().toHexString();
    var calibrationErrors = new CopyOnWriteArrayList<DateRangeSimulationErrorItem>();
    var errorConsumer = errorConsumer(calibrationErrors, simulationId, valuationDate);
    var portfolioData = simulationData.calculationData(valuationDate, errorConsumer);
    var calculationId = portfolioData.getCalculationId().toHexString();

    dateRangeSimulationCalculationRepository.saveSimulationErrors(simulationId, calibrationErrors);
    calculationTradeCountHolder.registerCalculation(calculationId, tradesCount);
    cacheService.cacheResults(calculationId, portfolioData);

    portfolioData
        .getCalculationTrades()
        .tradesFlux()
        .window(1000)
        .flatMap(Flux::collectList)
        .map(items -> createValuationRequests(simulationId, items, portfolioData))
        .then()
        .block();
  }

  private Mono<Void> createValuationRequests(
      String simulationId, List<PortfolioItem> items, PortfolioCalculationData data) {
    items.stream()
        .map(i -> calculationRequest(simulationId, i, data))
        .forEach(requestProducer::sendRequest);
    return Mono.empty();
  }

  private TradeCalculationRequest calculationRequest(
      String simulationId, PortfolioItem portfolioItem, PortfolioCalculationData data) {
    var calculationId = data.getCalculationId().toHexString();
    var calculationData = data.result(portfolioItem.getProductType());
    return TradeCalculationRequest.newOfSimulation(
        simulationId,
        SimulationType.DATE_RANGE,
        calculationId,
        data.valuationDate(),
        data.reportingCcy(),
        portfolioItem,
        calculationData);
  }

  private UnaryOperator<ErrorItem> errorConsumer(
      List<DateRangeSimulationErrorItem> errors, String simulationId, LocalDate valuationDate) {
    return error -> {
      if (error.getReason() == CALIBRATION_ERROR) {
        var simulationErrorItem =
            DateRangeSimulationErrorItem.ofCalibrationError(
                simulationId, valuationDate, error.getDescription());
        errors.add(simulationErrorItem);
      }
      return error;
    };
  }

  private Long calculationCount(DateRangeSimulationRequestedEvent event) {
    var simulationData = event.getSimulationData();
    return simulationData.getCalculationTrades().tradesCount();
  }

  private Long simulationCount(DateRangeSimulationRequestedEvent event, Long singleDayTradeCount) {
    var duration = daysBetweenInclusive(event.getValuationStartDate(), event.getValuationEndDate());
    return duration * singleDayTradeCount;
  }

  private long daysBetweenInclusive(LocalDate startDate, LocalDate endDate) {
    return DAYS.between(startDate, endDate) + 1;
  }
}
