package com.solum.xplain.calculation.pnlexplain;

import static java.util.stream.Collectors.toCollection;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculationResult;
import com.solum.xplain.calculation.pnlexplain.repository.PnlExplainCalculationRepository;
import com.solum.xplain.calculation.pnlexplain.repository.PnlExplainCalculationResultRepository;
import com.solum.xplain.calculation.pnlexplain.value.PnlExplainTradeResultMetrics;
import com.solum.xplain.calculation.pnlexplain.value.PortfolioItemCalculatedPnlExplainResultMapper;
import com.solum.xplain.calculation.pnlexplain.value.result.IntradayPnlExplain01Result;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlExplainCarryResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlExplainVegaResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlPortfolioItemCalculationResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlSens01Results;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlTradeValuationResults;
import com.solum.xplain.calculation.pnlexplain.value.result.PortfolioItemCalculatedPnlExplainResult;
import com.solum.xplain.calculation.repository.CalculationResultRepository;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PnlExplainCalculationService {

  private final Logger LOG = LoggerFactory.getLogger(PnlExplainCalculationService.class);
  private static final int PNL_PORTFOLIO_CALCULATIONS_TOTAL = 5;

  private final PnlExplainCalculationRepository pnlExplainCalculationRepository;
  private final CalculationResultRepository calculationResultRepository;
  private final PnlExplainCalculationResultRepository pnlExplainCalculationResultRepository;
  private final PnlExplainMetricCalculationService pnlExplainMetricCalculationService;
  private final PortfolioItemCalculatedPnlExplainResultMapper mapper;

  public void calculate(String pnlExplainCalculationId) {
    PnlPortfolioTradeCalculationResults pnlExplainPortfolioCalculationResults =
        getPnlCalculationPortfolioCalculationResults(pnlExplainCalculationId);

    List<PortfolioItemCalculatedPnlExplainResult> calculationResults =
        performPnlCalculation(pnlExplainPortfolioCalculationResults);

    LocalDateTime recordDate = LocalDateTime.now();
    LocalDate stateDate =
        pnlExplainPortfolioCalculationResults.firstValDateFirstCurveDateResults().getStateDate();
    LocalDate secondStateDate =
        pnlExplainPortfolioCalculationResults.secondValDateSecondCurveDateResults().getStateDate();

    PnlExplainCalculationResult pnlExplainCalculationResult =
        new PnlExplainCalculationResult(
            pnlExplainCalculationId, recordDate, stateDate, secondStateDate, calculationResults);

    pnlExplainCalculationResultRepository.insert(pnlExplainCalculationResult);

    LOG.info("Finished pnl explain calculation");
    pnlExplainCalculationRepository.findAndUpdateStatusAsSavedById(pnlExplainCalculationId);
  }

  // extract calculation results from pnl explain portfolio valuations and sort by valuation date
  // then by curve date
  private List<CalculationResult> getPnlValuationResults(String pnlExplainCalculationId) {
    return calculationResultRepository
        .calculationResultsForPnlExplainCalculation(pnlExplainCalculationId)
        .stream()
        .sorted(
            Comparator.comparing(CalculationResult::getValuationDate)
                .thenComparing(CalculationResult::getCurveDate))
        .collect(toCollection(ArrayList::new));
  }

  private PnlPortfolioTradeCalculationResults getPnlCalculationPortfolioCalculationResults(
      String pnlExplainCalculationId) {
    List<CalculationResult> pnlExplainCalculationResults =
        getPnlValuationResults(pnlExplainCalculationId);

    // remove calculation result from list where it contains "(Vega)" in the name
    CalculationResult vega =
        pnlExplainCalculationResults.stream()
            .filter(result -> result.getName().contains("Vega"))
            .findFirst()
            .orElseThrow(
                () ->
                    new RuntimeException(
                        "Vega result not found for pnl explain calculation: "
                            + pnlExplainCalculationId));

    pnlExplainCalculationResults.remove(vega);
    return PnlPortfolioTradeCalculationResults.of(pnlExplainCalculationResults, vega);
  }

  public PnlExplainCalculationResult getResultByPnlCalculation(String pnlExplainCalculationId) {
    return pnlExplainCalculationResultRepository.getByPnlExplainCalculationId(
        pnlExplainCalculationId);
  }

  private List<PortfolioItemCalculatedPnlExplainResult> performPnlCalculation(
      PnlPortfolioTradeCalculationResults pnlExplainPortfolioCalculationResults) {

    PnlTradeValuationResults pnlTradeValuationResults =
        new PnlTradeValuationResults(
            getTradeIdCalculatedResults(
                pnlExplainPortfolioCalculationResults.firstValDateFirstCurveDateResults().getId()),
            getTradeIdCalculatedResults(
                pnlExplainPortfolioCalculationResults.firstValDateSecondCurveDateResults().getId()),
            getTradeIdCalculatedResults(
                pnlExplainPortfolioCalculationResults.secondValDateFirstCurveDateResults().getId()),
            getTradeIdCalculatedResults(
                pnlExplainPortfolioCalculationResults
                    .secondValDateSecondCurveDateResults()
                    .getId()),
            getTradeIdCalculatedResults(
                pnlExplainPortfolioCalculationResults.vegaExceptionResult().getId()));

    // first vd, first cd
    CalculationResult firstValDateFirstCurveDateCalculationResult =
        pnlExplainPortfolioCalculationResults.firstValDateFirstCurveDateResults();
    // second vd, second cd
    CalculationResult secondValDateSecondCurveDateCalculationResult =
        pnlExplainPortfolioCalculationResults.secondValDateSecondCurveDateResults();

    PnlExplainCarryResult carryResults =
        pnlExplainMetricCalculationService.getPnlExplainCarryResults(pnlTradeValuationResults);

    PnlExplainVegaResult vegaResults =
        pnlExplainMetricCalculationService.getPnlExplainVegaResults(pnlTradeValuationResults);

    PnlSens01Results sens01Results =
        pnlExplainMetricCalculationService.pnlSens01Results(
            firstValDateFirstCurveDateCalculationResult,
            secondValDateSecondCurveDateCalculationResult,
            pnlTradeValuationResults);

    // TODO: assumed same trades exist across results, but trades tomorrow may not exist today, and
    //  vice versa
    List<String> trades =
        pnlTradeValuationResults.firstValDateFirstCurveDateResults().keySet().stream().toList();

    return getTradeIdCalculatedResults(
        trades, pnlTradeValuationResults, carryResults, vegaResults, sens01Results);
  }

  private List<PortfolioItemCalculatedPnlExplainResult> getTradeIdCalculatedResults(
      List<String> trades,
      PnlTradeValuationResults pnlTradeValuationResults,
      PnlExplainCarryResult carryResults,
      PnlExplainVegaResult vegaResults,
      PnlSens01Results sens01Results) {

    return trades.stream()
        .map(
            tradeId -> {
              PnlPortfolioItemCalculationResult firstValDateFirstCurveDateTradeResult =
                  pnlTradeValuationResults.firstValDateFirstCurveDateResults().get(tradeId);
              PnlPortfolioItemCalculationResult secondValDateSecondCurveDateTradeResult =
                  pnlTradeValuationResults.secondValDateSecondCurveDateResults().get(tradeId);
              PnlPortfolioItemCalculationResult firstValDateSecondCurveDateTradeResult =
                  pnlTradeValuationResults.firstValDateSecondCurveDateResults().get(tradeId);
              PnlPortfolioItemCalculationResult secondValDateFirstCurveDateTradeResult =
                  pnlTradeValuationResults.secondValDateFirstCurveDateResults().get(tradeId);

              PnlExplainTradeResultMetrics metrics;
              // for successfully calculated trades
              var firstValDateFirstCurveDateTradeResultPv =
                  firstValDateFirstCurveDateTradeResult.getMetricsPresentValuePayLegCurrency();
              var secondValDateSecondCurveDateTradeResultPv =
                  secondValDateSecondCurveDateTradeResult.getMetricsPresentValuePayLegCurrency();

              if (allNotNull(
                  firstValDateFirstCurveDateTradeResultPv,
                  secondValDateSecondCurveDateTradeResultPv)) {
                var actualPnl =
                    secondValDateSecondCurveDateTradeResult.getMetricsPresentValuePayLegCurrency()
                        - firstValDateFirstCurveDateTradeResult
                            .getMetricsPresentValuePayLegCurrency();
                var carry = getTradeMetricResult(carryResults.tradeCarryResults(), tradeId);
                var vega = getTradeMetricResult(vegaResults.tradeVegaResults(), tradeId);
                var dv01 = getSens01TradeResult(sens01Results.getDv01Result(), tradeId);
                var br01 = getSens01TradeResult(sens01Results.getBr01Result(), tradeId);
                var inf01 = getSens01TradeResult(sens01Results.getInf01Result(), tradeId);
                var cs01 = getSens01TradeResult(sens01Results.getCs01Result(), tradeId);
                var spotFx = getSens01TradeResult(sens01Results.getSpot01Result(), tradeId);
                var unexplainedPnl =
                    getUnexplainedPnl(actualPnl, carry, vega, dv01, br01, inf01, cs01, spotFx);

                // calculation of the combined inf01 and cs01 for a single column
                Double inf01cs01 =
                    (inf01 == null && cs01 == null)
                        ? null
                        : ((inf01 == null ? 0.0d : inf01) + (cs01 == null ? 0.0d : cs01));

                metrics =
                    metricsBuilder(
                            firstValDateFirstCurveDateTradeResult,
                            secondValDateSecondCurveDateTradeResult,
                            firstValDateSecondCurveDateTradeResult,
                            secondValDateFirstCurveDateTradeResult)
                        .metricsProfitAndLoss(actualPnl)
                        .metricsCarry(carry)
                        .metricsVega(vega)
                        .metricsIntradayPnlResultDv01(dv01)
                        .metricsIntradayPnlResultBr01(br01)
                        .metricsIntradayPnlResultInf01(inf01)
                        .metricsIntradayPnlResultCs01(cs01)
                        .metricsIntradayPnLResultsInfCs01(inf01cs01)
                        .metricsIntradayPnlResultSpot01(spotFx)
                        .metricsUnexplainedPnl(unexplainedPnl)
                        .build();
              } else {
                metrics = PnlExplainTradeResultMetrics.builder().build();
              }

              return mapper.map(firstValDateFirstCurveDateTradeResult, metrics);
            })
        .toList();
  }

  private PnlExplainTradeResultMetrics.PnlExplainTradeResultMetricsBuilder metricsBuilder(
      PnlPortfolioItemCalculationResult firstValDateFirstCurveDateTradeResult,
      PnlPortfolioItemCalculationResult secondValDateSecondCurveDateTradeResult,
      PnlPortfolioItemCalculationResult firstValDateSecondCurveDateTradeResult,
      PnlPortfolioItemCalculationResult secondValDateFirstCurveDateTradeResult) {
    return PnlExplainTradeResultMetrics.builder()
        .metricsFirstValDateFirstCurveDatePresentValue(
            firstValDateFirstCurveDateTradeResult.getMetricsPresentValuePayLegCurrency())
        .metricsFirstValDateFirstCurveDateImpliedVol(
            firstValDateFirstCurveDateTradeResult.getMetricsBreakevenImpliedVol())
        .metricsFirstValDateFirstCurveDateVega(
            firstValDateFirstCurveDateTradeResult.getMetricsPvVegaLocalCcy())
        .metricsSecondValDateSecondCurveDatePresentValue(
            secondValDateSecondCurveDateTradeResult.getMetricsPresentValuePayLegCurrency())
        .metricsSecondValDateSecondCurveDatePresentValueExcludingCashflows(
            secondValDateSecondCurveDateTradeResult
                .getMetricsPresentValuePayLegCurrencyExclCashflows())
        .metricsSecondValDateSecondCurveDateCashflows(
            secondValDateSecondCurveDateTradeResult.getMetricsCFTZeroNet())
        .metricsSecondValDateSecondCurveDateImpliedVol(
            secondValDateSecondCurveDateTradeResult.getMetricsBreakevenImpliedVol())
        .metricsSecondValDateSecondCurveDateVega(
            secondValDateSecondCurveDateTradeResult.getMetricsPvVegaLocalCcy())
        .metricsFirstValDateSecondCurveDatePresentValue(
            firstValDateSecondCurveDateTradeResult.getMetricsPresentValuePayLegCurrency())
        .metricsSecondValDateFirstCurveDatePresentValue(
            secondValDateFirstCurveDateTradeResult.getMetricsPresentValuePayLegCurrency());
  }

  private Double getUnexplainedPnl(
      Double actualPnl,
      Double carry,
      Double vega,
      Double dv01,
      Double br01,
      Double inf01,
      Double cs01,
      Double spotFx) {

    Double explainedPnl =
        Stream.of(carry, vega, dv01, br01, inf01, cs01, spotFx)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    if (actualPnl != null) {
      return actualPnl - explainedPnl;
    }
    return null;
  }

  private Double getSens01TradeResult(
      Either<ErrorItem, IntradayPnlExplain01Result> sens01Result, String tradeId) {

    return sens01Result.fold(
        l -> null,
        r -> {
          var delta = r.tradeDeltas().get(tradeId);
          return delta == null ? null : delta.doubleValue();
        });
  }

  private Double getTradeMetricResult(Map<String, BigDecimal> metricTradeResults, String tradeId) {
    var metrics = metricTradeResults.get(tradeId);
    return metrics == null ? null : metrics.doubleValue();
  }

  private Map<String, PnlPortfolioItemCalculationResult> getTradeIdCalculatedResults(
      ObjectId calculationResultId) {
    return calculationResultRepository
        .calculationResultPnlExplainItems(calculationResultId)
        .stream()
        .collect(
            Collectors.toMap(
                PnlPortfolioItemCalculationResult::getTradeInfoExternalTradeId,
                Function.identity()));
  }

  private record PnlPortfolioTradeCalculationResults(
      List<CalculationResult> calculationResults, CalculationResult vegaExceptionResult) {

    public static PnlPortfolioTradeCalculationResults of(
        List<CalculationResult> calculationResults, CalculationResult vegaResult) {
      return new PnlPortfolioTradeCalculationResults(calculationResults, vegaResult);
    }

    public CalculationResult firstValDateFirstCurveDateResults() {
      return calculationResults.get(0);
    }

    public CalculationResult firstValDateSecondCurveDateResults() {
      return calculationResults.get(1);
    }

    public CalculationResult secondValDateFirstCurveDateResults() {
      return calculationResults.get(2);
    }

    public CalculationResult secondValDateSecondCurveDateResults() {
      return calculationResults.get(3);
    }
  }
}
