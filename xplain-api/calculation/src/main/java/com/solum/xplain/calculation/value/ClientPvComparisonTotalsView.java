package com.solum.xplain.calculation.value;

import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientPvComparisonTotalsView {
  private String id;
  private Integer tradesCount;
  private Integer excludedTradesCount;
  private Double metricsClientMetricsPresentValue;
  private Double metricsPresentValue;
  private Double metricsPVDiffClientPV;
  private Double absPVDiffClientPV;
  private List<ClientPvComparisonTotalsView> groupedValues;

  public static ClientPvComparisonTotalsView empty() {
    return new ClientPvComparisonTotalsView(null, 0, 0, 0D, 0D, 0D, 0D, Collections.emptyList());
  }

  public ClientPvComparisonTotalsView withGroupedValues(List<ClientPvComparisonTotalsView> totals) {
    return new ClientPvComparisonTotalsView(
        id,
        tradesCount,
        excludedTradesCount,
        metricsClientMetricsPresentValue,
        metricsPresentValue,
        metricsPVDiffClientPV,
        absPVDiffClientPV,
        totals);
  }
}
