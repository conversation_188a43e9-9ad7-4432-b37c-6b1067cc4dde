package com.solum.xplain.calculation.simulation.daterange.validation;

import com.solum.xplain.calculation.simulation.ccyexposure.repository.CcyExposureSimulationCalculationRepository;
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository;
import com.solum.xplain.extensions.simulations.SimulationType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class UniqueSimulationNameValidator
    implements ConstraintValidator<UniqueSimulationName, String> {

  private final DateRangeSimulationCalculationRepository simulationRepository;
  private final CcyExposureSimulationCalculationRepository
      ccyExposureSimulationCalculationRepository;

  private SimulationType simulationType;

  public UniqueSimulationNameValidator(
      DateRangeSimulationCalculationRepository simulationRepository,
      CcyExposureSimulationCalculationRepository ccyExposureSimulationCalculationRepository) {
    this.simulationRepository = simulationRepository;
    this.ccyExposureSimulationCalculationRepository = ccyExposureSimulationCalculationRepository;
  }

  @Override
  public void initialize(UniqueSimulationName constraint) {
    simulationType = constraint.type();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || value.isEmpty()) {
      return true;
    }
    return simulationType == SimulationType.DATE_RANGE
        ? !simulationRepository.simulationNameExists(value)
        : !ccyExposureSimulationCalculationRepository.simulationNameExists(value);
  }
}
