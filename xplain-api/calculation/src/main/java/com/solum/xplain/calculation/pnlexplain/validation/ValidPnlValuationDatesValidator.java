package com.solum.xplain.calculation.pnlexplain.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.calculation.pnlexplain.value.PnlExplainCalculationForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidPnlValuationDatesValidator
    implements ConstraintValidator<ValidPnlValuationDates, PnlExplainCalculationForm> {

  public boolean isValid(PnlExplainCalculationForm form, ConstraintValidatorContext context) {
    if (form != null && allNotNull(form.getFirstValuationDate(), form.getSecondValuationDate())) {

      return form.getSecondValuationDate().isAfter(form.getFirstValuationDate());
    }
    return true;
  }
}
