package com.solum.xplain.calculation.comparison.repository;

import static com.solum.xplain.calculation.CalculationAggregationUtils.mapGroupedFields;
import static com.solum.xplain.calculation.CalculationAggregationUtils.toGroupedValuesField;
import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE_BATCH_1000;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.MongoVariables.VALUE_PREFIX;
import static java.util.Optional.ofNullable;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.ROOT;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.fields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.mapItemsOf;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.solum.xplain.calculation.CalculationResultProjections;
import com.solum.xplain.calculation.comparison.entity.ComparisonResult;
import com.solum.xplain.calculation.comparison.entity.ComparisonResultItem;
import com.solum.xplain.calculation.value.CalculationItemComparisonView;
import com.solum.xplain.calculation.value.CalculationPortfolioItemView;
import com.solum.xplain.calculation.value.CalculationTotalsComparisonView;
import com.solum.xplain.calculation.value.ComparisonResultView;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.ReactiveMongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class CalculationComparisonRepository {
  private static final String PV_COMPARISON_NOT_FOUND = "Calculation comparison not found";

  private final ReactiveMongoOperations reactiveMongoOperations;
  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;

  public Mono<Void> saveComparison(List<ComparisonResultItem> items) {
    return reactiveMongoOperations.insertAll(items).then();
  }

  public void saveComparisonResult(ComparisonResult comparisonResult) {
    mongoOperations.insert(comparisonResult);
  }

  public Either<ErrorItem, ComparisonResultView> comparisonResultView(String comparisonId) {
    var result =
        mongoOperations
            .aggregate(
                newAggregation(
                    ComparisonResult.class,
                    match(
                        where(ComparisonResult.Fields.correlationId)
                            .is(new ObjectId(comparisonId)))),
                ComparisonResultView.class)
            .getUniqueMappedResult();
    return result == null
        ? Either.left(OBJECT_NOT_FOUND.entity(PV_COMPARISON_NOT_FOUND))
        : Either.right(result);
  }

  public ScrollableEntry<CalculationItemComparisonView> comparisonEntries(
      String comparisonId,
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    long count =
        mongoOperations.count(
            Query.query(
                where(ComparisonResultItem.Fields.correlationId)
                    .is(new ObjectId(comparisonId))
                    .andOperator(
                        tableFilter.criteria(
                            CalculationItemComparisonView.class,
                            conversionService,
                            ComparisonResultItem.Fields.view))),
            ComparisonResultItem.class);
    if (count == 0) {
      return ScrollableEntry.empty();
    } else {
      return scrollableComparisonEntries(comparisonId, tableFilter, scrollRequest, groupRequest);
    }
  }

  public Either<ErrorItem, CalculationTotalsComparisonView> comparedCalculationResultTotals(
      String calculationResultId, TableFilter tableFilter, GroupRequest groupRequest) {

    ImmutableList.Builder<AggregationOperation> builder =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(ComparisonResultItem.Fields.correlationId)
                        .is(new ObjectId(calculationResultId))));

    builder.add(
        match(
            tableFilter.criteria(
                CalculationItemComparisonView.class,
                conversionService,
                ComparisonResultItem.Fields.view)));
    builder.add(comparisonResultItemMetricsProjection());
    builder.add(match(where(CalculationItemComparisonView.Fields.comparable).is(true)));
    List<AggregationOperation> operations = builder.build();

    try {
      CalculationTotalsComparisonView totals =
          calculateTotals(operations)
              .map(t -> t.withTotals(calculateComparableTotals(operations, groupRequest)))
              .orElse(CalculationTotalsComparisonView.empty());
      return Either.right(totals);
    } catch (IllegalArgumentException e) {
      return Either.left(new ErrorItem(Error.VALIDATION_ERROR, e.getMessage()));
    }
  }

  private Optional<CalculationTotalsComparisonView> calculateTotals(
      List<AggregationOperation> operations) {

    GroupOperation groupOperation =
        group()
            .count()
            .as(CalculationTotalsComparisonView.Fields.tradesCount)
            .sum(CalculationPortfolioItemView.Fields.metricsPresentValue)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValue)
            .sum(CalculationItemComparisonView.Fields.metricsPresentValueN1)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValueN1)
            .sum(CalculationItemComparisonView.Fields.metricsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv)
            .sum(CalculationItemComparisonView.Fields.metricsAbsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv);

    CalculationTotalsComparisonView totalsComparisonView =
        mongoOperations
            .aggregate(
                newAggregation(
                    ComparisonResultItem.class,
                    ImmutableList.<AggregationOperation>builder()
                        .addAll(operations)
                        .add(groupOperation)
                        .build()),
                CalculationTotalsComparisonView.class)
            .getUniqueMappedResult();

    return ofNullable(totalsComparisonView);
  }

  private List<CalculationTotalsComparisonView> calculateComparableTotals(
      List<AggregationOperation> operations, GroupRequest request) {
    return mongoOperations
        .aggregate(
            newAggregation(
                ComparisonResultItem.class,
                ImmutableList.<AggregationOperation>builder()
                    .addAll(operations)
                    .addAll(getComparisonGroupByOperations(request))
                    .add(totalsComparisonProjection(request.getRowGroupCols()))
                    .add(sort(Sort.by(UNDERSCORE_ID)))
                    .build()),
            CalculationTotalsComparisonView.class)
        .getMappedResults();
  }

  private List<AggregationOperation> getComparisonGroupByOperations(GroupRequest request) {

    List<AggregationOperation> operations = new ArrayList<>();
    operations.add(
        group(fields(request.getRowGroupCols().toArray(String[]::new)))
            .count()
            .as(CalculationTotalsComparisonView.Fields.tradesCount)
            .sum(CalculationPortfolioItemView.Fields.metricsPresentValue)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValue)
            .sum(CalculationItemComparisonView.Fields.metricsPresentValueN1)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValueN1)
            .sum(CalculationItemComparisonView.Fields.metricsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv)
            .sum(CalculationItemComparisonView.Fields.metricsAbsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv));

    List<String> fields = request.getRowGroupCols();
    return getComparisonGroupByOperations(operations, fields);
  }

  private List<AggregationOperation> getComparisonGroupByOperations(
      List<AggregationOperation> operations, List<String> groupFields) {

    List<String> groupBy = groupFields.subList(0, groupFields.size() - 1);
    if (groupBy.isEmpty()) {
      return operations;
    }

    operations.add(
        group(groupBy.toArray(String[]::new))
            .sum(CalculationTotalsComparisonView.Fields.tradesCount)
            .as(CalculationTotalsComparisonView.Fields.tradesCount)
            .sum(CalculationTotalsComparisonView.Fields.metricsPresentValue)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValue)
            .sum(CalculationTotalsComparisonView.Fields.metricsPresentValueN1)
            .as(CalculationTotalsComparisonView.Fields.metricsPresentValueN1)
            .sum(CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv)
            .sum(CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv)
            .as(CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv)
            .push(ROOT)
            .as(CalculationTotalsComparisonView.Fields.groupedValues));
    return getComparisonGroupByOperations(operations, groupBy);
  }

  private ProjectionOperation totalsComparisonProjection(List<String> fields) {
    List<String> fieldNames = ImmutableList.copyOf(fields);
    ProjectionOperation operation =
        project(
            UNDERSCORE_ID,
            CalculationTotalsComparisonView.Fields.tradesCount,
            CalculationTotalsComparisonView.Fields.metricsPresentValue,
            CalculationTotalsComparisonView.Fields.metricsPresentValueN1,
            CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv,
            CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv);

    String parentName = CalculationTotalsComparisonView.Fields.groupedValues;
    if (fieldNames.size() > 1) {
      operation =
          operation
              .and(
                  mapItemsOf(parentName)
                      .as(CalculationTotalsComparisonView.Fields.groupedValues)
                      .andApply(
                          context ->
                              toComparisionDocument(
                                  fieldNames,
                                  1,
                                  propertyName(
                                      CalculationTotalsComparisonView.Fields.groupedValues,
                                      CalculationTotalsComparisonView.Fields.groupedValues),
                                  context)))
              .as(CalculationTotalsComparisonView.Fields.groupedValues);
    }

    return operation;
  }

  private Document toComparisionDocument(
      List<String> fieldNames, int index, String parentName, AggregationOperationContext context) {
    String fieldName = fieldNames.get(index);
    Document document =
        new Document()
            .append(
                UNDERSCORE_ID,
                VALUE_PREFIX
                    + propertyName(
                        CalculationTotalsComparisonView.Fields.groupedValues,
                        UNDERSCORE_ID,
                        fieldName))
            .append(
                CalculationTotalsComparisonView.Fields.tradesCount,
                toGroupedValuesField(CalculationTotalsComparisonView.Fields.tradesCount))
            .append(
                CalculationTotalsComparisonView.Fields.metricsPresentValue,
                toGroupedValuesField(CalculationTotalsComparisonView.Fields.metricsPresentValue))
            .append(
                CalculationTotalsComparisonView.Fields.metricsPresentValueN1,
                toGroupedValuesField(CalculationTotalsComparisonView.Fields.metricsPresentValueN1))
            .append(
                CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv,
                toGroupedValuesField(CalculationTotalsComparisonView.Fields.metricsPVN1DiffPv))
            .append(
                CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv,
                toGroupedValuesField(CalculationTotalsComparisonView.Fields.metricsAbsPVN1DiffPv));

    final int newIndex = ++index;
    if (newIndex < fieldNames.size()) {
      document.append(
          CalculationTotalsComparisonView.Fields.groupedValues,
          mapItemsOf(parentName)
              .as(CalculationTotalsComparisonView.Fields.groupedValues)
              .andApply(
                  ctx ->
                      toComparisionDocument(
                          fieldNames,
                          newIndex,
                          propertyName(
                              parentName, CalculationTotalsComparisonView.Fields.groupedValues),
                          ctx))
              .toDocument(context));
    }
    return document;
  }

  private ScrollableEntry<CalculationItemComparisonView> scrollableComparisonEntries(
      String comparisonId,
      TableFilter tableFilter,
      ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    Fields groupIdFields = groupRequest.groupIdFields();

    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(ComparisonResultItem.Fields.correlationId)
                        .is(new ObjectId(comparisonId))),
                match(
                    tableFilter.criteria(
                        CalculationItemComparisonView.class,
                        conversionService,
                        ComparisonResultItem.Fields.view)),
                CalculationResultProjections.comparisonResultViewProjection(
                    ComparisonResultItem.Fields.view),
                match(
                    groupRequest.criteria(CalculationPortfolioItemView.class, conversionService)));

    if (!groupIdFields.asList().isEmpty()) {
      operations.add(
          group(groupIdFields)
              .sum(CalculationPortfolioItemView.Fields.metricsPresentValue)
              .as(CalculationPortfolioItemView.Fields.metricsPresentValue)
              .sum(CalculationItemComparisonView.Fields.metricsSens01)
              .as(CalculationItemComparisonView.Fields.metricsSens01)
              .sum(CalculationPortfolioItemView.Fields.metricsPv01)
              .as(CalculationPortfolioItemView.Fields.metricsPv01)
              .sum(CalculationItemComparisonView.Fields.metricsPresentValueN1)
              .as(CalculationItemComparisonView.Fields.metricsPresentValueN1)
              .sum(CalculationItemComparisonView.Fields.metricsSens01N1)
              .as(CalculationItemComparisonView.Fields.metricsSens01N1)
              .max(CalculationItemComparisonView.Fields.comparable)
              .as(CalculationItemComparisonView.Fields.comparable));
      operations.add(mapGroupedFields(groupIdFields));
    }

    operations.addAll(
        new ScrollSortOperations(scrollRequest, PortfolioItemFlatView.Fields.tradeId)
            .withGroupRequest(groupRequest)
            .build());

    List<CalculationItemComparisonView> items =
        mongoOperations
            .aggregate(
                newAggregation(ComparisonResultItem.class, operations.build()),
                CalculationItemComparisonView.class)
            .getMappedResults();
    return ScrollableEntry.of(items, scrollRequest);
  }

  public Stream<CalculationItemComparisonView> comparisonEntriesForExportStream(
      String comparisonId, Sort sort, TableFilter tableFilter) {
    List<AggregationOperation> operations =
        Lists.newArrayList(
            match(where(ComparisonResultItem.Fields.correlationId).is(new ObjectId(comparisonId))),
            match(
                tableFilter.criteria(
                    CalculationItemComparisonView.class,
                    conversionService,
                    ComparisonResultItem.Fields.view)),
            CalculationResultProjections.comparisonResultViewProjection(
                ComparisonResultItem.Fields.view));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    }

    return mongoOperations.aggregateStream(
        newAggregation(ComparisonResultItem.class, operations)
            .withOptions(ALLOW_DISK_USE_BATCH_1000),
        CalculationItemComparisonView.class);
  }

  private ProjectionOperation comparisonResultItemMetricsProjection() {

    final String parent = ComparisonResultItem.Fields.view;
    return project()
        .and(propertyName(parent, CalculationPortfolioItemView.Fields.metricsPresentValue))
        .as(CalculationPortfolioItemView.Fields.metricsPresentValue)
        .and(propertyName(parent, CalculationItemComparisonView.Fields.metricsPresentValueN1))
        .as(CalculationItemComparisonView.Fields.metricsPresentValueN1)
        .and(propertyName(parent, CalculationItemComparisonView.Fields.metricsPVN1DiffPv))
        .as(CalculationItemComparisonView.Fields.metricsPVN1DiffPv)
        .and(propertyName(parent, CalculationItemComparisonView.Fields.metricsAbsPVN1DiffPv))
        .as(CalculationItemComparisonView.Fields.metricsAbsPVN1DiffPv)
        .and(propertyName(parent, CalculationItemComparisonView.Fields.comparable))
        .as(CalculationItemComparisonView.Fields.comparable)
        .and(propertyName(parent, PortfolioItemFlatView.Fields.tradeInfoCounterparty))
        .as(PortfolioItemFlatView.Fields.tradeInfoCounterparty)
        .and(propertyName(parent, PortfolioItemFlatView.Fields.tradeInfoTradeType))
        .as(PortfolioItemFlatView.Fields.tradeInfoTradeType)
        .and(propertyName(parent, PortfolioItemFlatView.Fields.tradeInfoExternalTradeId))
        .as(PortfolioItemFlatView.Fields.tradeInfoExternalTradeId)
        .and(propertyName(parent, PortfolioItemFlatView.Fields.currencies))
        .as(PortfolioItemFlatView.Fields.currencies);
  }
}
