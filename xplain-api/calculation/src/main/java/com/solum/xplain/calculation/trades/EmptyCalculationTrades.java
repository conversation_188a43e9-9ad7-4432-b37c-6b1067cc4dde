package com.solum.xplain.calculation.trades;

import com.solum.xplain.calculation.discounting.tradeinfosubsets.GenericPortfolioItemDiscountingCriteria;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.product.ProductType;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import reactor.core.publisher.Flux;

/** Empty calculation trade implementation. */
public enum EmptyCalculationTrades implements CalculationTrades {
  INSTANCE;

  @Override
  public Set<ProductType> uniqueProductTypes() {
    return Set.of();
  }

  @Override
  public Stream<PortfolioItem> tradesStream() {
    return Stream.empty();
  }

  @Override
  public Stream<GenericPortfolioItemDiscountingCriteria> tradeDiscountingCriteriaStream() {
    return Stream.empty();
  }

  @Override
  public long tradesCount() {
    return 0;
  }

  @Override
  public Flux<PortfolioItem> tradesFlux() {
    return Flux.empty();
  }

  @Override
  public Optional<String> singleTradeId() {
    return Optional.empty();
  }

  @Override
  public Optional<String> singleTradeExternalId() {
    return Optional.empty();
  }
}
