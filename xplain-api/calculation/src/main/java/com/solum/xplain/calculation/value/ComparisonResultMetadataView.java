package com.solum.xplain.calculation.value;

import com.solum.xplain.calculation.curves.value.CalculationResultChartDataView;
import java.util.List;
import lombok.Data;

@Data
public class ComparisonResultMetadataView {

  private final String comparisonCurrency;
  private final CalculationResultView calculationResult;
  private final CalculationResultView against1calculationResult;
  private final List<CalculationResultChartDataView> curveConfigurationDifferenceChartPoints;
  private final List<CalculationResultChartDataView> fxCurveConfigurationDifferenceChartPoints;

  public ComparisonResultMetadataView(
      String comparisonCurrency,
      CalculationResultView calculationResult,
      CalculationResultView against1calculationResult,
      List<CalculationResultChartDataView> curveConfigurationDifferenceChartPoints,
      List<CalculationResultChartDataView> fxCurveConfigurationDifferenceChartPoints) {
    this.comparisonCurrency = comparisonCurrency;
    this.calculationResult = calculationResult;
    this.against1calculationResult = against1calculationResult;
    this.curveConfigurationDifferenceChartPoints = curveConfigurationDifferenceChartPoints;
    this.fxCurveConfigurationDifferenceChartPoints = fxCurveConfigurationDifferenceChartPoints;
  }
}
