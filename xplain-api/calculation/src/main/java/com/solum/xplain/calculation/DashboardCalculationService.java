package com.solum.xplain.calculation;

import com.solum.xplain.calculation.form.DashboardCalculationForm;
import com.solum.xplain.calculation.repository.CalculationPortfolioItemRepository;
import com.solum.xplain.calculation.repository.filter.AllTradesFilter;
import com.solum.xplain.core.calculationapi.DashboardCalculation;
import com.solum.xplain.core.common.AuditContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.function.UnaryOperator;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class DashboardCalculationService implements DashboardCalculation {

  private static final String PORTFOLIO_CALCULATION_NAME = "Dashboard %s %s %s";
  private final CalculationService calculationService;
  private final CalculationPortfolioItemRepository itemRepository;

  @Override
  public Either<ErrorItem, EntityId> calculate(
      String dashboardId,
      LocalDate valuationDate,
      AuditUser createdBy,
      CompanyLegalEntityValuationSettingsView settings,
      PortfolioCondensedView portfolio,
      MarketDataSourceType sourceType,
      BitemporalDate stateDate,
      List<ProductType> productTypes) {
    var auditContext = AuditContext.contextNow(createdBy);
    var form =
        DashboardCalculationForm.builder()
            .valuationSettings(settings)
            .marketDataSource(sourceType)
            .productTypes(productTypes)
            .build();
    return calculationService.calculateDashboardResults(
        portfolio,
        valuationDate,
        stateDate,
        form,
        dashboardEnhancementFn(dashboardId, valuationDate, auditContext, settings, portfolio));
  }

  @Override
  public List<ProductType> productTypesForDashboardCalculation(
      String portfolioId, BitemporalDate stateDate, List<ProductType> productTypes) {
    return itemRepository.uniqueProductTypes(new AllTradesFilter(portfolioId, stateDate)).stream()
        .filter(productTypes::contains)
        .toList();
  }

  private UnaryOperator<CalculationResult> dashboardEnhancementFn(
      String dashboardId,
      LocalDate valuationDate,
      AuditContext auditContext,
      CompanyLegalEntityValuationSettingsView settings,
      PortfolioCondensedView portfolio) {
    return result -> {
      result.setDashboardId(dashboardId);
      result.setCreatedBy(auditContext.user());
      result.setCreatedAt(auditContext.now());
      result.setName(dashboardCalculationName(valuationDate, settings, portfolio));
      return result;
    };
  }

  private String dashboardCalculationName(
      LocalDate valuationDate,
      CompanyLegalEntityValuationSettingsView settings,
      PortfolioCondensedView portfolio) {
    return String.format(
        PORTFOLIO_CALCULATION_NAME,
        settings.getMarketDataGroupName(),
        valuationDate,
        portfolio.getExternalPortfolioId());
  }
}
