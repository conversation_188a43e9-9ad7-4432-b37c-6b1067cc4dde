package com.solum.xplain.calculation.events;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.types.ObjectId;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CalculationDeletedEvent extends CalculationEvent {

  public CalculationDeletedEvent(ObjectId calculationId) {
    super(calculationId);
  }

  public CalculationDeletedEvent(String calculationId) {
    super(new ObjectId(calculationId));
  }
}
