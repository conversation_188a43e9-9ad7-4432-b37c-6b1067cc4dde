package com.solum.xplain.calculation;

import static com.solum.xplain.core.utils.ComparisonUtils.safeAbs;
import static com.solum.xplain.core.utils.ComparisonUtils.safeDiff;
import static com.solum.xplain.core.utils.ComparisonUtils.safeDiv;
import static java.util.Objects.requireNonNull;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.value.CalculationItemComparisonView;
import com.solum.xplain.calculation.value.CompareItem;
import java.util.List;
import java.util.Map;

public class CalculationResultCompare {
  private final Currency comparisonCurrency;
  private final List<CompareItem> original;
  private final Map<String, CompareItem> against1;

  public CalculationResultCompare(
      Currency comparisonCurrency, List<CompareItem> original, Map<String, CompareItem> against1) {
    this.comparisonCurrency = requireNonNull(comparisonCurrency);
    this.original = requireNonNull(original);
    this.against1 = requireNonNull(against1);
  }

  public List<CalculationItemComparisonView> calculateResults() {
    return original.stream().map(this::calculateResult).toList();
  }

  private CalculationItemComparisonView calculateResult(CompareItem original) {
    CompareItem n1 = against1.get(original.getTradeId());
    Double metricsPresentValue = original.getMetricsPresentValue(comparisonCurrency);
    Double metricsSens01 = original.getMetricsSens01();
    CalculationItemComparisonView view = new CalculationItemComparisonView();
    view.setMetricsPresentValue(metricsPresentValue);
    view.setMetricsSens01(metricsSens01);

    Double metricsPresentValueN1;
    Double metricsDv01N1;
    Double metricsSens01N1;

    boolean comparable = false;
    if (isComparable(original, n1)) {
      metricsPresentValueN1 = n1.getMetricsPresentValue(comparisonCurrency);
      metricsDv01N1 = n1.getMetricsDv01();
      metricsSens01N1 = n1.getMetricsSens01();
      view.setMetricsPresentValueN1(metricsPresentValueN1);
      view.setMetricsDv01N1(metricsDv01N1);
      view.setMetricsSens01N1(metricsSens01N1);

      final Double metricsPVN1DiffPv = safeDiff(metricsPresentValueN1, metricsPresentValue);
      view.setMetricsPVN1DiffPv(metricsPVN1DiffPv);
      view.setMetricsAbsPVN1DiffPv(safeAbs(metricsPVN1DiffPv));

      final Double metricsPVN1DiffPVDivPV = safeDiv(metricsPVN1DiffPv, metricsPresentValue);
      view.setMetricsPVN1DiffPVDivPV(metricsPVN1DiffPVDivPV);
      view.setMetricsAbsPVN1DiffPVDivPV(safeAbs(metricsPVN1DiffPVDivPV));

      final Double metricsPVN1DiffPVDivDV01 = safeDiv(metricsPVN1DiffPv, original.getMetricsDv01());
      view.setMetricsPVN1DiffPVDivDV01(metricsPVN1DiffPVDivDV01);
      view.setMetricsAbsPVN1DiffPVDivDV01(safeAbs(metricsPVN1DiffPVDivDV01));

      final Double metricsPVN1DiffPVDivSens01 = safeDiv(metricsPVN1DiffPv, metricsSens01);
      view.setMetricsPVN1DiffPVDivSens01(metricsPVN1DiffPVDivSens01);
      view.setMetricsAbsPVN1DiffPVDivSens01(safeAbs(metricsPVN1DiffPVDivSens01));

      comparable = true;
    }
    view.setComparable(comparable);
    return CalculationResultCopier.INSTANCE.copy(original.getItem(), view);
  }

  private boolean isComparable(CompareItem original, CompareItem against) {
    return against != null && CompareItem.comparable(original, against);
  }
}
