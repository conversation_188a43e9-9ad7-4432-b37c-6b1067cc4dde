package com.solum.xplain.calculation.curvegroup;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.InterpolatedNodalCurve;
import com.opengamma.strata.pricer.fxopt.BlackFxOptionVolatilities;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.value.CalculationConfigurationData;
import com.solum.xplain.calibration.capfloor.CapletFloorletVolatilityCalibrationResults;
import com.solum.xplain.calibration.rates.RatesCalibrationResult;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.calibration.rates.group.DiscountingGroups;
import com.solum.xplain.calibration.volatility.VolatilitiesCalibrationData;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CalculationCurveGroupData {

  private final CurveConfigurationInstrumentResolver curveConfig;
  private final CurveGroup curveGroup;
  private final MarketData marketData;

  private final List<Curve> curves;
  private final List<CreditCurve> creditCurves;
  private final List<VolatilitySurface> volatilitySurfaces;
  private final CurveGroupFxVolatility fxVolatility;

  private final Map<String, RatesCalibrationResult> rates;
  private final Map<String, InterpolatedNodalCurve> bondCurves;
  private final Map<String, CalculationCreditCalibrationResult> creditRates;
  private final Map<String, CapletFloorletVolatilityCalibrationResults> caplets;
  private final Map<RateIndex, VolatilitiesCalibrationData> volatilities;
  private final Map<CurrencyPair, BlackFxOptionVolatilities> fxOptionVols;
  private final List<CalibratedCurve> calculationResultChartData;

  private final DiscountingGroups<PortfolioItemDiscountingGroupSpecification> discountingGroups;
  private final boolean sensitivitiesSupported;

  public CalculationConfigurationData toConfigurationData() {
    var data = new CalculationConfigurationData();
    data.setCurveConfiguration(EntityReference.newOf(curveConfig.getId(), curveConfig.getName()));
    data.setCurveGroup(EntityReference.newOf(curveGroup.getId(), curveGroup.getName()));
    return data;
  }

  /** Override @Data default because it makes debugging hell */
  public String toString() {
    return "CalculationCurveGroupData(" + curveConfig.getName() + ", " + curveGroup.getName() + ")";
  }
}
