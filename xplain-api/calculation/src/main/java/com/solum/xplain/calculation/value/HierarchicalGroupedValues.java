package com.solum.xplain.calculation.value;

import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsLast;

import java.util.Comparator;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class HierarchicalGroupedValues extends GroupedValues {

  private String id;
  private List<HierarchicalGroupedValues> groupedValues;

  public void sort() {
    if (groupedValues != null) {
      groupedValues.sort(
          Comparator.comparing(HierarchicalGroupedValues::getId, nullsLast(naturalOrder())));
      groupedValues.forEach(HierarchicalGroupedValues::sort);
    }
  }
}
