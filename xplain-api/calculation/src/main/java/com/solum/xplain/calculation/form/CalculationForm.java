package com.solum.xplain.calculation.form;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.value.CalculationDiscounting;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import java.util.Optional;

public interface CalculationForm {

  String getMarketDataGroupId();

  MarketDataSourceType marketDataSource();

  CalculationDiscounting calculationDiscounting();

  CurveConfigurationType configurationType();

  CalculationConfigForm calculationConfiguration();

  CalculationConfigForm fxCalculationConfiguration();

  Currency reportingCurrency();

  PortfolioCalculationType calculationType();

  InstrumentPriceRequirements priceRequirements();

  default Optional<String> tradeId() {
    return Optional.empty();
  }

  default Optional<List<ProductType>> productTypes() {
    return Optional.empty();
  }

  default boolean onlyOnboardingTrades() {
    return false;
  }
}
