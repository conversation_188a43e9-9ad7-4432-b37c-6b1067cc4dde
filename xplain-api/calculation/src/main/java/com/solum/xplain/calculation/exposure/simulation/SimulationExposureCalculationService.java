package com.solum.xplain.calculation.exposure.simulation;

import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.exposure.CcyExposureCalculationOptions;
import com.solum.xplain.calculation.exposure.netexposure.NetExposureBucketCalculationMetrics;
import com.solum.xplain.calculation.exposure.netexposure.NetExposureCalculationService;
import com.solum.xplain.calculation.repository.CalculationResultRepository;
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType;
import com.solum.xplain.calculation.value.CalculationResultTotalsForm;
import com.solum.xplain.core.ccyexposure.CcyExposureControllerService;
import com.solum.xplain.core.ccyexposure.value.CcyExposureWithCashflows;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SimulationExposureCalculationService {

  private final NetExposureCalculationService netExposureCalculationService;
  private final NetExposureSimulationCalculationService netExposureSimulationCalculationService;
  private final CalculationResultRepository calculationResultRepository;
  private final CcyExposureControllerService ccyExposureControllerService;

  public List<NetExposureSimulationShiftResults> calculate(
      String simulationId,
      SimulationCalculationOptions options,
      TableFilter tableFilter,
      CalculationResultTotalsForm totalsForm) {

    Map<BigDecimal, CalculationResult> calculationResultsByShiftSize =
        calculationResultsByShiftSize(simulationId, options);

    List<NetExposureBucketCalculationMetrics> baseCalculationResult =
        netExposureCalculationService.calculate(options, tableFilter, totalsForm);

    return performCalculationsAsync(
        baseCalculationResult, options, tableFilter, totalsForm, calculationResultsByShiftSize);
  }

  private Map<BigDecimal, CalculationResult> calculationResultsByShiftSize(
      String simulationId, SimulationCalculationOptions options) {

    ShiftDataType shiftDataType = options.getShiftDataType();

    return calculationResultRepository
        .calculationResultsForSimulationShiftDataType(simulationId, shiftDataType)
        .filter(c -> c.getCcyExposureCalculationKey().shiftSize() != null)
        .collect(
            Collectors.toMap(
                c -> c.getCcyExposureCalculationKey().shiftSize(), Function.identity()));
  }

  /**
   * Asynchronously calculates the net exposure for each shift simulation calculation result, then
   * performs a join() to wait for all calculations to complete, and returns the results.
   *
   * @param baseCalculationResult net exposure results for the base calculation
   * @param options simulation calculation options
   * @param tableFilter table filter
   * @param totalsForm calculation result totals form
   * @param calculationResultByShiftSize map of shift size to calculation result
   * @return list of net exposure results for each shift simulation calculation result
   */
  private List<NetExposureSimulationShiftResults> performCalculationsAsync(
      List<NetExposureBucketCalculationMetrics> baseCalculationResult,
      SimulationCalculationOptions options,
      TableFilter tableFilter,
      CalculationResultTotalsForm totalsForm,
      Map<BigDecimal, CalculationResult> calculationResultByShiftSize) {

    List<CcyExposureWithCashflows> externalCcyExposureWithCashflows =
        fetchExternalCcyExposures(options);
    List<NetExposureSimulationShiftResults> results = new ArrayList<>();

    List<CompletableFuture<NetExposureSimulationShiftResults>> resultsFutures =
        calculationResultByShiftSize.entrySet().stream()
            .map(
                r -> {
                  // calculate net exposure on the shift simulation calculation result
                  ObjectId shiftCalculationResultId = r.getValue().getId();
                  SimulationCalculationOptions copyOptions =
                      options.copyWithCalculationResultId(shiftCalculationResultId);
                  return netExposureSimulationCalculationService.calculateAsync(
                      externalCcyExposureWithCashflows,
                      baseCalculationResult,
                      r.getKey(),
                      copyOptions,
                      tableFilter,
                      totalsForm);
                })
            .toList();

    CompletableFuture<Void> allOf =
        CompletableFuture.allOf(resultsFutures.toArray(new CompletableFuture[0]))
            .whenComplete(
                (v, th) ->
                    resultsFutures.forEach(
                        r -> results.add(r.getNow(NetExposureSimulationShiftResults.empty()))));

    allOf.join();

    ArrayList<NetExposureSimulationShiftResults> resultsByShiftSize = new ArrayList<>(results);

    // add base calculation result
    resultsByShiftSize.add(
        NetExposureSimulationShiftResults.of(BigDecimal.ZERO, baseCalculationResult));

    // sort by shift size
    return resultsByShiftSize.stream()
        .sorted(Comparator.comparing(NetExposureSimulationShiftResults::shiftSize))
        .toList();
  }

  private List<CcyExposureWithCashflows> fetchExternalCcyExposures(
      CcyExposureCalculationOptions options) {
    return ccyExposureControllerService.ccyExposuresWithCashflows(
        options.getExposureCurrency(), options.getStateDate(), options.getExposureIds());
  }
}
