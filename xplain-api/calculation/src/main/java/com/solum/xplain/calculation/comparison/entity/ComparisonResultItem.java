package com.solum.xplain.calculation.comparison.entity;

import com.solum.xplain.calculation.value.CalculationItemComparisonView;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@FieldNameConstants
@Document(collation = "en")
public class ComparisonResultItem {
  @Id private ObjectId id;

  private LocalDateTime createdAt;

  private ObjectId correlationId;

  private CalculationItemComparisonView view;

  public static ComparisonResultItem newOf(
      CalculationItemComparisonView view, ObjectId correlationId) {
    ComparisonResultItem item = new ComparisonResultItem();
    item.correlationId = correlationId;
    item.view = view;
    item.createdAt = LocalDateTime.now();
    return item;
  }
}
