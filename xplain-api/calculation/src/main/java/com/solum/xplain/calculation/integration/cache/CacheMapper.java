package com.solum.xplain.calculation.integration.cache;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.product.swap.type.FixedFloatSwapConvention;
import com.solum.xplain.calibration.volatility.VolatilitiesCalibrationData;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilitySurfaceNodeValue;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurveType;
import com.solum.xplain.valuation.messages.calibration.vols.SkewType;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceConfig;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceForCalculation;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper
public abstract class CacheMapper {
  @Autowired ReferenceData referenceData;

  @Mapping(target = "config", source = "surface")
  @Mapping(target = "nodes", expression = "java(nodes(data.getNodes(), data))")
  @Mapping(target = "skews", expression = "java(convertMap(data.getSkews(), data))")
  public abstract SurfaceForCalculation fromData(VolatilitiesCalibrationData data);

  @Mapping(target = "surfaceName", source = "name")
  @Mapping(target = "convention", source = "swapConvention")
  public abstract SurfaceConfig toConfig(VolatilitySurface surface);

  @Mapping(
      target = "expiryYearFraction",
      expression = "java(value.parsedVolExpiry(date, convention, referenceData))")
  @Mapping(target = "tenorYearFraction", expression = "java(value.parsedTenor())")
  public abstract SurfaceNode node(
      VolatilitySurfaceNodeValue value, FixedFloatSwapConvention convention, LocalDate date);

  List<SurfaceNode> nodes(
      List<VolatilitySurfaceNodeValue> nodeValues, VolatilitiesCalibrationData data) {
    var convention = FixedFloatSwapConvention.of(data.getSurface().getSwapConvention());
    return nodeValues.stream().map(n -> node(n, convention, data.getValuationDate())).toList();
  }

  Map<BigDecimal, List<SurfaceNode>> convertMap(
      Map<BigDecimal, List<VolatilitySurfaceNodeValue>> map, VolatilitiesCalibrationData data) {
    return map.entrySet().stream()
        .collect(Collectors.toMap(Map.Entry::getKey, v -> nodes(v.getValue(), data)));
  }

  SkewType resolveType(VolatilitySurfaceType type) {
    return switch (type) {
      case MONEYNESS -> SkewType.MONEYNESS;
      case ATM_ONLY -> SkewType.ATM;
      case STRIKE -> SkewType.STRIKE;
    };
  }

  @Mapping(target = "type", expression = "java(resolveCurveType(curve))")
  public abstract CalibrationCurve toCalibrationCurve(Curve curve);

  @Mapping(target = "type", constant = "CREDIT")
  public abstract CalibrationCurve toCalibrationCurve(CreditCurve curve);

  CalibrationCurveType resolveCurveType(Curve curve) {
    return switch (curve.getCurveType()) {
      case IR_INDEX -> CalibrationCurveType.IR_INDEX;
      case INDEX_BASIS -> CalibrationCurveType.INDEX_BASIS;
      case XCCY -> CalibrationCurveType.XCCY;
      case INFLATION_INDEX -> CalibrationCurveType.INFLATION;
    };
  }
}
