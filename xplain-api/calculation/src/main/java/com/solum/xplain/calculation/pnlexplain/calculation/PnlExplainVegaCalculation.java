package com.solum.xplain.calculation.pnlexplain.calculation;

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;
import static java.math.MathContext.DECIMAL64;
import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.calculation.pnlexplain.value.result.PnlExplainVegaResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlPortfolioItemCalculationResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlTradeValuationResults;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;

/** Performs the calculation of the "vega" component of PnL Explain for each trade. */
@AllArgsConstructor
public class PnlExplainVegaCalculation {

  private static final Logger LOG = getLogger(PnlExplainVegaCalculation.class);
  private static final BigDecimal VEGA_SCALING_FACTOR = BigDecimal.valueOf(10_000d);
  private static final BigDecimal FX_OPT_VEGA_SCALING_FACTOR = BigDecimal.valueOf(100d);

  private final PnlTradeValuationResults pnlTradeValuationResults;

  /**
   * Performs vega calculation for all trades
   *
   * @return PnlExplainVegaResult which holds carry values for all trades
   */
  public PnlExplainVegaResult calculate() {

    // only trades with a vega value will be included in the result
    List<String> tradeIds =
        pnlTradeValuationResults.firstValDateFirstCurveDateResults().keySet().stream()
            .filter(
                tradeId ->
                    pnlTradeValuationResults
                            .firstValDateFirstCurveDateResults()
                            .get(tradeId)
                            .getMetricsPvVegaLocalCcy()
                        != null)
            .toList();

    // TODO pass through error consumer for audit entries for failed trades

    Map<String, BigDecimal> tradeVegaResults = new HashMap<>();
    tradeIds.forEach(
        tradeId -> {
          boolean tradeIsFxOpt =
              pnlTradeValuationResults
                      .firstValDateFirstCurveDateResults()
                      .get(tradeId)
                      .getTradeInfoTradeType()
                  == CoreProductType.FXOPT;

          var firstOrderVegaEither = getTradeFirstOrderVega(tradeId, tradeIsFxOpt);

          if (firstOrderVegaEither.isRight()) {
            var firstOrderVega = firstOrderVegaEither.right().get();

            var secondOrderVegaEither = getTradeSecondOrderVega(tradeId, tradeIsFxOpt);

            if (secondOrderVegaEither.isRight()) {
              // carry A + carry B
              var tradeVega = firstOrderVega.add(secondOrderVegaEither.right().get());
              var scaledVega =
                  tradeIsFxOpt
                      ? tradeVega.multiply(FX_OPT_VEGA_SCALING_FACTOR)
                      : tradeVega.multiply(VEGA_SCALING_FACTOR);
              tradeVegaResults.put(tradeId, scaledVega);
            }
          }
        });

    return new PnlExplainVegaResult(tradeVegaResults);
  }

  /** Calculates first order vega for given trade */
  private Either<ErrorItem, BigDecimal> getTradeFirstOrderVega(
      String tradeId, boolean tradeIsFxOpt) {
    // => (impliedVolVd1Cd2 - impliedVolVd1Cd1) * vegaVd1Cd1

    var impliedVolVd1Cd2 =
        getTradeValue(
            tradeIsFxOpt
                ? pnlTradeValuationResults.vegaCalculationResults()
                : pnlTradeValuationResults.firstValDateSecondCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsBreakevenImpliedVol,
            PnlPortfolioItemCalculationResult.Fields.metricsBreakevenImpliedVol);

    if (impliedVolVd1Cd2.isLeft()) {
      return impliedVolVd1Cd2;
    }

    var impliedVolVd1Cd1 =
        getTradeValue(
            pnlTradeValuationResults.firstValDateFirstCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsBreakevenImpliedVol,
            PnlPortfolioItemCalculationResult.Fields.metricsBreakevenImpliedVol);

    if (impliedVolVd1Cd1.isLeft()) {
      return impliedVolVd1Cd1;
    }

    var vegaVd1Cd1 =
        getTradeValue(
            pnlTradeValuationResults.firstValDateFirstCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsPvVegaLocalCcy,
            PnlPortfolioItemCalculationResult.Fields.metricsPvVegaLocalCcy);

    return vegaVd1Cd1.fold(
        Either::left,
        r ->
            right(
                impliedVolVd1Cd2
                    .right()
                    .get()
                    .subtract(impliedVolVd1Cd1.right().get())
                    .multiply(r, DECIMAL64)));
  }

  /** Calculates second order vega for given trade */
  private Either<ErrorItem, BigDecimal> getTradeSecondOrderVega(
      String tradeId, boolean tradeIsFxOpt) {
    // => ((vegaVd2Cd2 - vegaVd1Cd1) / 2) * (impliedVolVd1Cd2 - impliedVolVd1Cd1)

    var impliedVolVd1Cd2 =
        getTradeValue(
            tradeIsFxOpt
                ? pnlTradeValuationResults.vegaCalculationResults()
                : pnlTradeValuationResults.firstValDateSecondCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsBreakevenImpliedVol,
            PnlPortfolioItemCalculationResult.Fields.metricsBreakevenImpliedVol);

    if (impliedVolVd1Cd2.isLeft()) {
      return impliedVolVd1Cd2;
    }

    var impliedVolVd1Cd1 =
        getTradeValue(
            pnlTradeValuationResults.firstValDateFirstCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsBreakevenImpliedVol,
            PnlPortfolioItemCalculationResult.Fields.metricsBreakevenImpliedVol);

    if (impliedVolVd1Cd1.isLeft()) {
      return impliedVolVd1Cd1;
    }

    var impliedVolDiff = impliedVolVd1Cd2.right().get().subtract(impliedVolVd1Cd1.right().get());

    var vegaVd2Cd2 =
        getTradeValue(
            pnlTradeValuationResults.secondValDateSecondCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsPvVegaLocalCcy,
            PnlPortfolioItemCalculationResult.Fields.metricsPvVegaLocalCcy);

    if (vegaVd2Cd2.isLeft()) {
      return vegaVd2Cd2;
    }

    var vegaVd1Cd1 =
        getTradeValue(
            pnlTradeValuationResults.firstValDateFirstCurveDateResults(),
            tradeId,
            PnlPortfolioItemCalculationResult::getMetricsPvVegaLocalCcy,
            PnlPortfolioItemCalculationResult.Fields.metricsPvVegaLocalCcy);

    return vegaVd1Cd1.fold(
        Either::left,
        r -> {
          var vegaDiff = vegaVd2Cd2.right().get().subtract(r);
          return right(
              vegaDiff.divide(BigDecimal.valueOf(2.0d), DECIMAL64).multiply(impliedVolDiff));
        });
  }

  private Either<ErrorItem, BigDecimal> getTradeValue(
      Map<String, PnlPortfolioItemCalculationResult> resultsCollection,
      String tradeId,
      Function<PnlPortfolioItemCalculationResult, Double> valueFn,
      String valueName) {

    var tradeResult = resultsCollection.get(tradeId);

    if (tradeResult != null) {
      var value = valueFn.apply(tradeResult);
      if (value == null) {
        String nullValueErrorMessage = format("Trade %s %s is null", tradeId, valueName);
        LOG.debug(nullValueErrorMessage);
        return left(CALCULATION_ERROR.entity(nullValueErrorMessage));
      }
      return right(BigDecimal.valueOf(value));
    }

    String errorMessage = format("Missing calculation for trade %s", tradeId);
    LOG.info(errorMessage);
    return left(CALCULATION_ERROR.entity(errorMessage));
  }
}
