package com.solum.xplain.calculation;

import static com.solum.xplain.core.utils.ComparisonUtils.safeAbs;
import static com.solum.xplain.core.utils.ComparisonUtils.safeDiff;
import static java.util.Objects.requireNonNull;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.value.CalculationItemClientPvComparisonView;
import com.solum.xplain.calculation.value.CompareItem;
import java.util.List;

public class CalculationResultClientPvCompare {

  private final Currency comparisonCurrency;
  private final List<CompareItem> original;

  public CalculationResultClientPvCompare(Currency comparisonCurrency, List<CompareItem> original) {
    this.comparisonCurrency = requireNonNull(comparisonCurrency);
    this.original = requireNonNull(original);
  }

  public List<CalculationItemClientPvComparisonView> calculateResults() {
    return original.stream().map(this::calculateResult).toList();
  }

  private CalculationItemClientPvComparisonView calculateResult(CompareItem original) {
    Double clientPv = original.getMetricsClientMetricsPresentValue(comparisonCurrency);
    Double metricsPresentValue = original.getMetricsPresentValue(comparisonCurrency);
    Double metricsPvDiffClientPV = safeDiff(metricsPresentValue, clientPv);
    Double absPVDiffClientPV = safeAbs(metricsPvDiffClientPV);
    Double metricsSens01 = original.getMetricsSens01();

    return CalculationResultCopier.INSTANCE.copy(
        original.getItem(),
        clientPv,
        metricsPresentValue,
        metricsPvDiffClientPV,
        absPVDiffClientPV,
        metricsSens01);
  }
}
