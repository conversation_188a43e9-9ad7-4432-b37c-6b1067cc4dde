#!/bin/bash

# <PERSON><PERSON>t fixes the out-of-sync local MongoDB replica set,
# when MongoDB instance is no longer primary (e.g. after container recreation)
# with errors like: Command failed with error 13436 (NotPrimaryOrSecondary): 'node is not in primary or recovering state'
#
# Before running the script make sure MongoDB container is up and running.
# Usage: ./mongoReplSetFixPrimary.sh

docker exec -it solum-xplain-api-solum_mongo-1 bash -c "mongo --eval '
cfg = rs.conf();
cfg.members[0].host=\"$(docker ps -aqf "name=solum-xplain-api-solum_mongo-1"):27017\";
rs.reconfig(cfg, {force : true})
cfg;
'" || docker exec -it solum_mongo bash -c "mongo --eval '
cfg = rs.conf();
cfg.members[0].host=\"$(docker ps -aqf "name=solum_mongo"):27017\";
rs.reconfig(cfg, {force : true})
cfg;
'"
