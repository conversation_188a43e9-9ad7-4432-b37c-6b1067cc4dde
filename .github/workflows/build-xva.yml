name: Xplain XVA PROXY build
on:
  workflow_dispatch:
  push:
    branches:
      - 'SXSD-**'
      - 'release-**'
      - 'main'
    tags:
      - 'v*'
    paths:
      # Main Code
      - 'xplain-xva/**'
      # Configs
      - 'build.gradle'
      - 'dependencies.gradle'
      - 'gradle.properties'
      - 'gradle/gradle-wrapper.properties'
      # Pipeline changes
      - '.github/workflows/build-xva.yml'
      - '.github/actions/build-java/action.yml'
      - '.github/actions/build-docker/action.yml'
permissions:
  contents: read
  checks: write
  id-token: write
env:
  AWS_REGION: 'eu-west-2'
  EKS_CLUSTER_NAME: 'xplain'
  PLATFORM_DIR: "platform"
  PLATFORM_BRANCH: "main"
  PROJECT: "solum-xplain-xva-proxy"
  XVA_RELEASE_S3_BUCKET: "release-xva.xplainfinancial.com"
  MODULE_NAME: "xplain-xva:app"
  SONAR_MODULE: "xplain-xva"

jobs:
  configure:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      branch: ${{ steps.version.outputs.branch }}
      env: ${{ steps.deployment.outputs.environment }}
      xva-version: ${{ steps.xva-version.outputs.xvaVersion }}
    steps:
      - uses: actions/checkout@v4
      - name: Checkout platform repository
        uses: actions/checkout@v4
        with:
          path: ${{ env.PLATFORM_DIR }}
          ref: ${{ env.PLATFORM_BRANCH }}
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./platform/.github/actions/common/resolve-version
        id: version
        with:
          version-file-path: "gradle.properties"
      - uses: ./platform/.github/actions/common/resolve-env
        id: deployment
        name: "Resolve deployment env"
      - name: Read XVA version
        id: xva-version
        uses: christian-draeger/read-properties@1.1.1
        with:
          path: "xplain-xva/xva.properties"
          properties: 'xvaVersion'

  build:
    runs-on: ubuntu-latest
    needs: configure
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-java
        with:
          github-token: ${{ secrets.PAT_GITHUB_TOKEN }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          module-name: ${{ env.MODULE_NAME }}
          sonar-module: ${{ env.SONAR_MODULE }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-token: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4.2.1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          mask-aws-account-id: true
          aws-region: ${{ env.AWS_REGION }}
      - name: "Download Julia artifacts"
        run: aws s3 sync s3://${{ env.XVA_RELEASE_S3_BUCKET }}/${{ needs.configure.outputs.xva-version }}/XVA_compiled_for_linux XVA_compiled_for_linux/

      - uses: ./.github/actions/build-docker
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          github-token: ${{ secrets.PAT_GITHUB_TOKEN }}
          branch: ${{ needs.configure.outputs.branch }}
          image-tag: ${{ needs.configure.outputs.version }}
          module-name: ${{ env.MODULE_NAME }}

  deploy:
    runs-on: self-hosted
    needs: [ build, configure ]
    if: ${{ needs.configure.outputs.env }}
    steps:
      - uses: actions/checkout@v4
      - name: Checkout platform repository
        uses: actions/checkout@v4
        with:
          path: ${{ env.PLATFORM_DIR }}
          ref: ${{ env.PLATFORM_BRANCH }}
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./platform/.github/actions/common/deploy
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          cluster-name: ${{ env.EKS_CLUSTER_NAME }}
          environment: ${{ needs.configure.outputs.env }}
          project: ${{ env.PROJECT }}
          image-tag: ${{ needs.configure.outputs.version }}
