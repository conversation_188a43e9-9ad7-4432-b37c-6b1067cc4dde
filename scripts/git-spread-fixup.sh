#!/bin/bash

# This gets the list of files changed in the last commit, resets the last commit,
# stages the changes for each file, and then creates a fixup commit for each file.
# Finally, it rebases the fixup commits onto the earliest commit in the list of unique commit hashes.
# It's useful if you have done a load of fixes in response to a review, and then want to apply
# them to the appropriate commits so that the history makes sense.

ROOT=$(git rev-parse --show-toplevel)
pushd $ROOT

# Get the list of files changed in the last commit
files=$(git diff --name-only HEAD~1 HEAD)
git reset --soft HEAD~1
git reset

# Initialize an empty set to hold the unique commit hashes
declare -A commit_hashes

# Create a fixup commit for each file
for file in $files; do
  git add $file

 TARGET_COMMIT_HASH=$(git log -1 --pretty=format:%H -- $file)
  git commit --fixup $TARGET_COMMIT_HASH
  commit_hashes[$TARGET_COMMIT_HASH]=1
done

echo Commit hashes: ${!commit_hashes[@]}
earliest_commit=$(git rev-list --date-order --reverse ${!commit_hashes[@]} ^refs/remotes/origin/main | head -n 1)
echo earliest: $earliest_commit

read -p "Continue with rebase --interactive --autosquash? [y/n] " -n 1 -r
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
  echo
  echo "Aborting"
  exit 1
fi

git rebase -i --autosquash ${earliest_commit}~1

popd
