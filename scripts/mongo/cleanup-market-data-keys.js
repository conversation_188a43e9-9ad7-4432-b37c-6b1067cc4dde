// Take a backup of the marketDataKeyEntity collection before starting: (15s)
db.marketDataKeyEntity.aggregate([{$out: "marketDataKeyEntityBackup"}]);
// Take a backup of the marketDataKey collection before starting: (15s)
db.marketDataKey.aggregate([{$out: "marketDataKeyBackup"}]);
// Restore instructions - if necessary:
// db.marketDataKeyEntityBackup.aggregate([{$out: "marketDataKeyEntity"}]);
// db.marketDataKeyBackup.aggregate([{$out: "marketDataKey"}]);

// First of all clear out the redundant no-ticker versions. (2s)
db.marketDataKeyEntity.updateMany({}, {$pull: {versions: {"value.providerTickers": {$exists: false}}}});

// Now keep only the latest record date for each valid from date (1.5s)
db.marketDataKeyEntity.updateMany({}, [{
  $set: {
    versions: {
      $reduce: {
        input: { // take the sorted array of minor versions with most recent first for each major version
          $sortArray: {
            input: "$versions",
            sortBy: {
              validFrom: 1,
              recordFrom: -1
            }
          }
        },
        initialValue: [],
        in: {
          $cond: {
            if: { // check if the valid from date has already been added to the array
              $in: [
                "$$this.validFrom",
                "$$value.validFrom"
              ]
            },
            then: "$$value", // latest minor version for valid from date has already been added
            else: { // valid from date has not been added, add the latest minor version to the array
              $concatArrays: [
                "$$value",
                [
                  "$$this"
                ]
              ]
            }
          }
        }
      }
    }
  }
}]);

// Now squash anything with the same consecutive value (4s)
db.marketDataKeyEntity.updateMany({}, [{
  $set: {
    versions: {
      $reduce: {
        input: "$versions",
        initialValue: [],
        in: {
          $cond: {
            if: { // check if the value has changed
              $let: {
                // thisValue is the current version value, prevValue is the previous version value
                vars: {
                  thisValue: "$$this.value",
                  prevValue: {
                    $last: "$$value.value"
                  }
                },
                in: {
                  $let: {
                    // thisValueArray is the current version value converted to a k/v array with providerTickers sorted and all keys sorted
                    // prevValueArray is the current version value converted to a k/v array with providerTickers sorted and all keys sorted
                    // we need to convert the objects to arrays so they can be compared by MongoDB
                    vars: {
                      thisValueArray: {
                        $sortArray: {
                          input: {
                            $objectToArray: {
                              $mergeObjects: [
                                "$$thisValue",
                                {
                                  providerTickers: {
                                    $map: {
                                      input: {
                                        $sortArray: {
                                          input: "$$thisValue.providerTickers",
                                          sortBy: {
                                            code: 1,
                                            bidAskType: 1
                                          }
                                        }
                                      },
                                      as: "ticker",
                                      in: {
                                        $sortArray: {
                                          input: {
                                            $objectToArray: "$$ticker"
                                          },
                                          sortBy: {
                                            k: 1
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              ]
                            }
                          },
                          sortBy: {
                            k: 1
                          }
                        }
                      },
                      prevValueArray: {
                        $sortArray: {
                          input: {
                            $objectToArray: {
                              $mergeObjects: [
                                "$$prevValue",
                                {
                                  providerTickers: {
                                    $map: {
                                      input: {
                                        $sortArray: {
                                          input: "$$prevValue.providerTickers",
                                          sortBy: {
                                            code: 1,
                                            bidAskType: 1
                                          }
                                        }
                                      },
                                      as: "ticker",
                                      in: {
                                        $sortArray: {
                                          input: {
                                            $objectToArray: "$$ticker"
                                          },
                                          sortBy: {
                                            k: 1
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              ]
                            }
                          },
                          sortBy: {
                            k: 1
                          }
                        }
                      }
                    },
                    in: {
                      $eq: [
                        "$$thisValueArray",
                        "$$prevValueArray"
                      ]
                    }
                  }
                }
              }
            },
            then: "$$value", // value has not changed, don't add the major version to the array
            else: { // value has changed, add the major version to the array
              $concatArrays: [
                "$$value",
                [
                  "$$this"
                ]
              ]
            }
          }
        }
      }
    }
  }
}]);

// Now generate the new marketDataKey collection, one record per version (4s)
db.marketDataKeyEntity.aggregate([{
  $set: {
    versions: {
      $reduce: {
        input: { // start with the most recent major version first
          $sortArray: {
            input: "$versions",
            sortBy: {
              validFrom: -1
            }
          }
        },
        initialValue: [],
        in: {
          $concatArrays: [
            "$$value",
            [
              {
                $mergeObjects: [
                  "$$this",
                  {
                    recordTo: ISODate("9999-12-31T00:00:00.000Z"),
                    validTo: { // calculate this using the more recent major version for this MDK if it exists
                      $ifNull: [
                        {
                          $last: "$$value.validFrom"
                        },
                        ISODate("9999-12-31T00:00:00.000Z")
                      ]
                    }
                  }
                ]
              }
            ]
          ]
        }
      }
    }
  }
}, {
  "$unwind": "$versions" // Create one record per version
}, {
  "$sort": { // Reorder them back into the correct date order
    "semanticId": 1,
    "versions.validFrom": 1
  }
}, {
  "$project": { // Remap to match the expected data model for the read view
    _id: 0,
    _class: "com.solum.xplain.core.market.MarketDataKey",
    // mapping from DefaultEmbeddedVersionEntityToViewConverter#generateViewsFromCalculatedVersionRows
    entityId: {$toString: "$_id"},
    version: "$versions.validFrom",
    validFrom: "$versions.validFrom",
    recordFrom: "$versions.recordFrom",
    state: "$versions.state",
    comment: "$versions.comment",
    modifiedBy: "$versions.createdBy",
    modifiedAt: "$versions.recordFrom",
    validities: [
      {
        validTo: "$versions.validTo",
        recordFrom: "$versions.recordFrom",
        recordTo: "$versions.recordTo"
      }
    ],
    // mapping from MarketDataKeyMapper#generateRead
    key: "$semanticId",
    name: "$versions.value.name",
    assetGroup: "$versions.value.assetGroup",
    instrumentType: "$versions.value.instrumentType",
    providerTickers: "$versions.value.providerTickers"
  }
}, {
  $out: "marketDataKey" // Replace the collection with the new records, keeping indexes
}]);
